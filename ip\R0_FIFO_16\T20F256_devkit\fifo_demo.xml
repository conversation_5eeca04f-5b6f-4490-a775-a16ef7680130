<?xml version="1.0" encoding="UTF-8"?>
<efx:project name="fifo_demo" description="" last_change_date="Wed August 11 2021 14:10:01" location="/projects/SWIP/syng/efinity/2021.M.200/project/0811_fifo/ip/abc/T20F256_devkit" sw_version="2021.M.200" last_run_state="pass" last_run_tool="efx_pgm" last_run_flow="bitstream" config_result_in_sync="true" design_ood="sync" place_ood="sync" route_ood="sync" xmlns:efx="http://www.efinixinc.com/enf_proj" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.efinixinc.com/enf_proj enf_proj.xsd">
    <efx:device_info>
        <efx:family name="Trion"/>
        <efx:device name="T20F256"/>
        <efx:timing_model name="C4"/>
    </efx:device_info>
    <efx:design_info def_veri_version="verilog_2k" def_vhdl_version="vhdl_2008">
        <efx:top_module name="fifo_demo_top"/>
        <efx:design_file name="fifo_demo_top.v" version="default" library="default"/>
        <efx:design_file name="R0_FIFO_16.sv" version="default" library="default"/>
        <efx:design_file name="efx_symmetric_width_fifo_top.sv" version="default" library="default"/>
        <efx:top_vhdl_arch name=""/>
    </efx:design_info>
    <efx:constraint_info>
        <efx:sdc_file name="fifo_demo_T20.sdc"/>
        <efx:inter_file name=""/>
    </efx:constraint_info>
    <efx:sim_info/>
    <efx:misc_info/>
    <efx:ip_info/>
    <efx:synthesis tool_name="efx_map">
        <efx:param name="work_dir" value="work_syn" value_type="e_string"/>
        <efx:param name="write_efx_verilog" value="on" value_type="e_bool"/>
        <efx:param name="mode" value="speed" value_type="e_option"/>
        <efx:param name="max_ram" value="-1" value_type="e_integer"/>
        <efx:param name="max_mult" value="-1" value_type="e_integer"/>
        <efx:param name="infer-clk-enable" value="3" value_type="e_option"/>
        <efx:param name="infer-sync-set-reset" value="1" value_type="e_option"/>
        <efx:param name="fanout-limit" value="0" value_type="e_integer"/>
        <efx:param name="bram_output_regs_packing" value="1" value_type="e_option"/>
        <efx:param name="retiming" value="1" value_type="e_option"/>
        <efx:param name="seq_opt" value="1" value_type="e_option"/>
        <efx:param name="blast_const_operand_adders" value="1" value_type="e_option"/>
        <efx:param name="mult_input_regs_packing" value="1" value_type="e_option"/>
        <efx:param name="mult_output_regs_packing" value="1" value_type="e_option"/>
    </efx:synthesis>
    <efx:place_and_route tool_name="efx_pnr">
        <efx:param name="work_dir" value="work_pnr" value_type="e_string"/>
        <efx:param name="verbose" value="off" value_type="e_bool"/>
        <efx:param name="load_delaym" value="on" value_type="e_bool"/>
        <efx:param name="optimization_level" value="NULL" value_type="e_option"/>
        <efx:param name="seed" value="1" value_type="e_integer"/>
    </efx:place_and_route>
    <efx:bitstream_generation tool_name="efx_pgm">
        <efx:param name="mode" value="active" value_type="e_option"/>
        <efx:param name="width" value="1" value_type="e_option"/>
        <efx:param name="cold_boot" value="off" value_type="e_bool"/>
        <efx:param name="cascade" value="off" value_type="e_option"/>
        <efx:param name="enable_roms" value="smart" value_type="e_option"/>
        <efx:param name="spi_low_power_mode" value="on" value_type="e_bool"/>
        <efx:param name="io_weak_pullup" value="on" value_type="e_bool"/>
        <efx:param name="oscillator_clock_divider" value="DIV8" value_type="e_option"/>
        <efx:param name="enable_crc_check" value="on" value_type="e_bool"/>
    </efx:bitstream_generation>
    <efx:debugger>
        <efx:param name="work_dir" value="work_dbg" value_type="e_string"/>
        <efx:param name="auto_instantiation" value="off" value_type="e_bool"/>
        <efx:param name="profile" value="NONE" value_type="e_string"/>
    </efx:debugger>
</efx:project>
