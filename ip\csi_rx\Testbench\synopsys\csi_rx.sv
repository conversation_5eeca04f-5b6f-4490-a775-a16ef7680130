// =============================================================================
// Generated by efx_ipmgr
// Version: 2025.**********
// IP Version: 5.12
// =============================================================================

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2025 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//                                                                       
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//                                                                       
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.                                                
//
////////////////////////////////////////////////////////////////////////////////

`define IP_UUID _449714fdae91449eb701c8d91e94b26b
`define IP_NAME_CONCAT(a,b) a``b
`define IP_MODULE_NAME(name) `IP_NAME_CONCAT(name,`IP_UUID)
module csi_rx
(
    input reset_n,
    input clk,
    input reset_byte_HS_n,
    input clk_byte_HS,
    input reset_pixel_n,
    input clk_pixel,
    input Rx_LP_CLK_P,
    input Rx_LP_CLK_N,
    output Rx_HS_enable_C,
    output LVDS_termen_C,
    input [0:0] Rx_LP_D_P,
    input [0:0] Rx_LP_D_N,
    input [7:0] Rx_HS_D_0,
    input [7:0] Rx_HS_D_1,
    input [7:0] Rx_HS_D_2,
    input [7:0] Rx_HS_D_3,
    input [7:0] Rx_HS_D_4,
    input [7:0] Rx_HS_D_5,
    input [7:0] Rx_HS_D_6,
    input [7:0] Rx_HS_D_7,
    output [0:0] Rx_HS_enable_D,
    output [0:0] LVDS_termen_D,
    output [0:0] fifo_rd_enable,
    input [0:0] fifo_rd_empty,
    output [0:0] DLY_enable_D,
    output [0:0] DLY_inc_D,
    input [0:0] u_dly_enable_D,
    output vsync_vc1,
    output vsync_vc15,
    output vsync_vc12,
    output vsync_vc9,
    output vsync_vc7,
    output vsync_vc14,
    output vsync_vc13,
    output vsync_vc11,
    output vsync_vc10,
    output vsync_vc8,
    output vsync_vc6,
    output vsync_vc4,
    output vsync_vc0,
    output vsync_vc5,
    output irq,
    output pixel_data_valid,
    output [63:0] pixel_data,
    output [3:0] pixel_per_clk,
    output [5:0] datatype,
    output [15:0] shortpkt_data_field,
    output [15:0] word_count,
    output [1:0] vcx,
    output [1:0] vc,
    output hsync_vc3,
    output hsync_vc2,
    output hsync_vc8,
    output hsync_vc12,
    output hsync_vc7,
    output hsync_vc10,
    output hsync_vc1,
    output hsync_vc0,
    output hsync_vc13,
    output hsync_vc4,
    output hsync_vc11,
    output hsync_vc6,
    output hsync_vc9,
    output hsync_vc15,
    output hsync_vc14,
    output hsync_vc5,
    input axi_rready,
    output axi_rvalid,
    output [31:0] axi_rdata,
    output axi_arready,
    input axi_arvalid,
    input [5:0] axi_araddr,
    input axi_bready,
    output axi_bvalid,
    output axi_wready,
    input axi_wvalid,
    input [31:0] axi_wdata,
    output vsync_vc3,
    output vsync_vc2,
    output axi_awready,
    input [0:0] u_dly_inc_D,
    input axi_clk,
    input axi_reset_n,
    input [5:0] axi_awaddr,
    input axi_awvalid
);
`IP_MODULE_NAME(efx_csi2_rx)
#(
    .PACK_TYPE (15),
    .tLPX_NS (50),
    .tINIT_NS (1000),
    .tCLK_TERM_EN_NS (38),
    .tD_TERM_EN_NS (35),
    .tHS_SETTLE_NS (85),
    .tHS_PREPARE_ZERO_NS (145),
    .NUM_DATA_LANE (1),
    .ASYNC_STAGE (2),
    .HS_BYTECLK_MHZ (187),
    .CLOCK_FREQ_MHZ (50),
    .DPHY_CLOCK_MODE ("Discontinuous"),
    .PIXEL_FIFO_DEPTH (1024),
    .AREGISTER (8),
    .ENABLE_USER_DESKEWCAL (1'b0),
    .FRAME_MODE ("GENERIC"),
    .ENABLE_VCX (1'b0)
)
u_efx_csi2_rx
(
    .reset_n ( reset_n ),
    .clk ( clk ),
    .reset_byte_HS_n ( reset_byte_HS_n ),
    .clk_byte_HS ( clk_byte_HS ),
    .reset_pixel_n ( reset_pixel_n ),
    .clk_pixel ( clk_pixel ),
    .Rx_LP_CLK_P ( Rx_LP_CLK_P ),
    .Rx_LP_CLK_N ( Rx_LP_CLK_N ),
    .Rx_HS_enable_C ( Rx_HS_enable_C ),
    .LVDS_termen_C ( LVDS_termen_C ),
    .Rx_LP_D_P ( Rx_LP_D_P ),
    .Rx_LP_D_N ( Rx_LP_D_N ),
    .Rx_HS_D_0 ( Rx_HS_D_0 ),
    .Rx_HS_D_1 ( Rx_HS_D_1 ),
    .Rx_HS_D_2 ( Rx_HS_D_2 ),
    .Rx_HS_D_3 ( Rx_HS_D_3 ),
    .Rx_HS_D_4 ( Rx_HS_D_4 ),
    .Rx_HS_D_5 ( Rx_HS_D_5 ),
    .Rx_HS_D_6 ( Rx_HS_D_6 ),
    .Rx_HS_D_7 ( Rx_HS_D_7 ),
    .Rx_HS_enable_D ( Rx_HS_enable_D ),
    .LVDS_termen_D ( LVDS_termen_D ),
    .fifo_rd_enable ( fifo_rd_enable ),
    .fifo_rd_empty ( fifo_rd_empty ),
    .DLY_enable_D ( DLY_enable_D ),
    .DLY_inc_D ( DLY_inc_D ),
    .u_dly_enable_D ( u_dly_enable_D ),
    .vsync_vc1 ( vsync_vc1 ),
    .vsync_vc15 ( vsync_vc15 ),
    .vsync_vc12 ( vsync_vc12 ),
    .vsync_vc9 ( vsync_vc9 ),
    .vsync_vc7 ( vsync_vc7 ),
    .vsync_vc14 ( vsync_vc14 ),
    .vsync_vc13 ( vsync_vc13 ),
    .vsync_vc11 ( vsync_vc11 ),
    .vsync_vc10 ( vsync_vc10 ),
    .vsync_vc8 ( vsync_vc8 ),
    .vsync_vc6 ( vsync_vc6 ),
    .vsync_vc4 ( vsync_vc4 ),
    .vsync_vc0 ( vsync_vc0 ),
    .vsync_vc5 ( vsync_vc5 ),
    .irq ( irq ),
    .pixel_data_valid ( pixel_data_valid ),
    .pixel_data ( pixel_data ),
    .pixel_per_clk ( pixel_per_clk ),
    .datatype ( datatype ),
    .shortpkt_data_field ( shortpkt_data_field ),
    .word_count ( word_count ),
    .vcx ( vcx ),
    .vc ( vc ),
    .hsync_vc3 ( hsync_vc3 ),
    .hsync_vc2 ( hsync_vc2 ),
    .hsync_vc8 ( hsync_vc8 ),
    .hsync_vc12 ( hsync_vc12 ),
    .hsync_vc7 ( hsync_vc7 ),
    .hsync_vc10 ( hsync_vc10 ),
    .hsync_vc1 ( hsync_vc1 ),
    .hsync_vc0 ( hsync_vc0 ),
    .hsync_vc13 ( hsync_vc13 ),
    .hsync_vc4 ( hsync_vc4 ),
    .hsync_vc11 ( hsync_vc11 ),
    .hsync_vc6 ( hsync_vc6 ),
    .hsync_vc9 ( hsync_vc9 ),
    .hsync_vc15 ( hsync_vc15 ),
    .hsync_vc14 ( hsync_vc14 ),
    .hsync_vc5 ( hsync_vc5 ),
    .axi_rready ( axi_rready ),
    .axi_rvalid ( axi_rvalid ),
    .axi_rdata ( axi_rdata ),
    .axi_arready ( axi_arready ),
    .axi_arvalid ( axi_arvalid ),
    .axi_araddr ( axi_araddr ),
    .axi_bready ( axi_bready ),
    .axi_bvalid ( axi_bvalid ),
    .axi_wready ( axi_wready ),
    .axi_wvalid ( axi_wvalid ),
    .axi_wdata ( axi_wdata ),
    .vsync_vc3 ( vsync_vc3 ),
    .vsync_vc2 ( vsync_vc2 ),
    .axi_awready ( axi_awready ),
    .u_dly_inc_D ( u_dly_inc_D ),
    .axi_clk ( axi_clk ),
    .axi_reset_n ( axi_reset_n ),
    .axi_awaddr ( axi_awaddr ),
    .axi_awvalid ( axi_awvalid )
);
endmodule


// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_asyncreg) #(
    parameter ASYNC_STAGE = 2,
    parameter WIDTH = 4,
    parameter ACTIVE_LOW = 1, // 0 - Active high reset, 1 - Active low reset
    parameter RST_VALUE = 0 
) (
    input  wire             clk,
    input  wire             reset_n,
    input  wire [WIDTH-1:0] d_i,
    output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] async_reg[ASYNC_STAGE-1:0];

assign d_o = async_reg[ASYNC_STAGE-1];

genvar i;
generate
   if (ACTIVE_LOW == 1) begin: active_low
      if (RST_VALUE == 0) begin: low_reset_0
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: low_reset_1
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
   else begin: active_high
      if (RST_VALUE == 0) begin: high_reset_0
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: high_reset_1
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
endgenerate

endmodule


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
ldXoODZEiSr0TnTH12qQJbLLM+/7Tlqn72B5Qv50jiFHk/Jp/hE04aOsHIPbMo3J
9307OKr45A3ZlHdU6XOWSoyAsGW/VDc7fCLjz3AviONReNVx/tAcgKflpGB5xLzU
VmQSq/K2u4smLLvKEUP+pZwKO+5jazilhsXtMlHyHCw=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 37024 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
sjnJxE7ezz02d+eI2pMJ++xR96+SKsHyt6EjG97jjFzczEGny3q7ntDpZTeRmg+5
nmz77skHS/NOUgGEbTDQ4c/1X9wqHRW3dCxZO1DU9ZXcrfUvu+UuxswwvWfrA7Dx
8Ls8QY5FFtF+JywXXE/zF93BBEhncCiuRSomlGCvGqg=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 2384 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
sGMmo70s2EWHXiYoJp9nlXga6+/m4xluuuei63/uZK0gHrzk3Jae6Nsk41+1yHcp
Kw3kUC7+XjVqCKi4/5shIj2oqbYLD8Hjs9de9ZQxCcPI0d8UJfRl7yXpqtxywrnV
ACGJQOLP1lycuN6DA3GRfyAP7YLVdqyql6RCmnY3fx0=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 4672 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
oFON8Ka4xA4IgRzgNp5rpRs/X8Af92SOZwJwnP/eiGEE1qhY85lQX7rsXBJZNTNp
KvWNA72E4/AiguK4XwgjsG+Q5gzEFTLP4PHxGO4poa6RX/C2nWt7Nijt1aBBAuDv
YJ6/E6+N/QA11tZeUUb+MmRl+1oOsisXkx9SrE3aW20=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 11552 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
SMQb8XNuzBeHhgUN6YMZy+BslQcWbjozsVDrnHT8QvtZjIVB26OGMkkqZjMi7aTC
n4v/jEKUB3ZW+QZeaLQJy2c1G9dST/d07Mi/G4usWq/KSTDFZoICFh1PlPTkQcSS
QGX40A9mK8+BRGXsTgKjpKLxkJ1boGIoTt6mnw+UiUQ=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 31328 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
kIW2fcK9WqzB/a0W2jKfWxB2tM3+o8T/bvi4//c03RNQk06hdd/2aNY11fMwzzjF
knB4wQ6DmTNqAgDayJ3rOzmB945wpvrv62a4VHQ69zCUpUNe+VUOeSkG0qcYOAnD
uvpenU1s14kkZtjfuXLzjHroijkL0KVGbpSgspfLyZg=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 6640 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
AYAlMbSSexkM9VYk/PtLASzmggXXFyuNQ91qEj9CaElKzbtQyyqgYTOFP4vn3Mw8
NedCp/z2HiOl/9Yi9lODt/fe3ZeWTmRQ4/OFob+zgQBKdezDSDBbHapl8KhRdCJQ
0GGVzZ/QMXFoJVyKrqFv3Q8FCyeJgPLJ8YodLzy+RkE=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 4960 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
AIRnPhknCTVpknIqTkemXvM73dHa28mU6RkeWDvUBx4K3Fk46BRKOzgKPN7dhOY7
cpaxZAFi3dkM+Mj12JR7JQ9buGfjoWAMAxKBjora1HAm4CKGIhKCRmBAlGxA7LFE
q4Z2ZdC+hih7nmLtbqvj6OONSgoher/6On5/XargNs0=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 6288 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
p67b7YwKALMwaqsteF4ZgBN6ZtaBokxM7I+blF7mZGagRKGb50gnbL4PofdTGVk2
6pEo30cLYBlIAaeM6ThZN/5VQIoNAwSO3RV8DpXyxaU5UHgMRmsOEp2H0yDyRABv
G/4cnlBaCERVJAnECdBn6/HDRYrOVlfQjzqQV9onZXU=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 2496 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
WdVIYsE42OwNed8suxiW4VUIkGoby3rs1TY6KBPB0zMJzrifwMVYbiJB0GUVyQcX
ZHwZ/pK9sjPTRTlzRE6xKZeItAWs+4oPiGfk3fMCDJfCdJ57c9jarXRXRlmbLoX4
rMa7bE8D0pbLqigNojpeJJtFhCzNtX/+KxLctSDmgjM=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 54720 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
iUXU/PvwJoaGiBJPnhhp8tnPwuxsrQmeM6RE00LmPd5JJa7BBjL6jLsROa5IXp0K
4gRAG1emwNtcy/gJgIrzOSJHz60GxcLhiQ3mpLZvzKblAWCbNV1PlsFXvYzGz1Ic
y37fT8u9lpkvO0+ftDqxDi8j8HpbRjQC8xOgXEwnZb8=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 9568 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
SCoU0VVEJe8cYCdIN+Lj6itQy6BjHBlsvfHt8eQb4MNve7ajZSLhCZ6ecBRM/Owv
bvS3DByxpLpaMW4AE8cOCMxSCNrZm7garBdRbwA8R+241fINguYutPm174uUsHHx
3DqtIaRESZQ3hZdAHaum2hNu4avyhkShmUxWTSIkUfg=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 26016 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
IztNmV+Bisomid4V1wUTOLRGlRfhRnBOF+V4B/loUVwEzhnQsixyWH3Xtgf3idM/
TJyWmHM26bMgERrR9oM49+9MzAscbpNt58rJOcEfApW90niWH9RaoPqb4aJ4MAmE
Z+q+wqAE1k3o6Dd5N1p8T63GexOm4uAR0waDl1UWQu0=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 28624 )
`pragma protect data_block
BR01aH8jCF9+h+B7uyUmsgubLg/LIBhzC57nBo93HyQKXk679vM2VFk69lpLITW9
CBc+Mv+aSca2IAZX27w7ujB6RqMLzDh1SwInPengVaZCbIQg9BQWICFgdF1hU5wo
MEK2bHaOXANB2+uxliafikUfoy1GS0jr6dSdySdc1aBpfpDluEaf0tBXKaQ1a0o6
2xhwJ1h0q4wvBekgR8iHDvPdouxAV6dXIETCHfI7kgAI7Rj5d/UUFKCXL1VY9RtS
o9E0f+cYk3gEJ01Yc4rZF1OCPP1aLjqmX/Xg/ILNDsG2JNlZYDXfKYVTiTETq5UU
sB8tMuw4Mbzb4DYi4cgEtinHnJOvybqpC2U8+DeTlx672cHEKH3j0j2z/vHQLnoB
mQsaBDTcS52LvJyQzNI1fO8HjhHDg6EUxtLIY54G7sAuty371eCV8jvNwwPhar0e
m88cZi8YhXw52602K8D4f3wu4Fk1XUFddJ9W+XYNkI0f1zEItG1D1FUaeiTDdT2X
O3skB6s2GX4gLX0tXIpo4UoBjrFwmrK1db8KCo1ylyD6td6ADrFy4TEkPRmNYd2L
8yK03PJ2iBTkG81qOVC5b9+ddoLUvcyXr7Yl8MPdEb+LhYr/siEYbmAbOoOfbbRn
paz2FbA9lnYTwJhJ85MrCzVDPt0NyiEUH5tjUv8KbDuN7Hmq27ZqjFyaMKni++CV
286QMbxDuwZkHoXtLhjnZmmgwZckXntMqiQk20hO2D3ytWFRRz7ztVtON92suyBt
3FMNT+A/V1KaQMGcr7QOWotxk2yyhx1u6rCfA0pXn/XyM5qUDH2OLTFVptbY4ckZ
hMaUhATBFK/6bCrsPHqtXRFR8ulvtaEOAMRKLmjouRIOOgltLJ4zOoJNIko+m3NR
D9MuZx7XcO6emBJzzkQSd+LZmfAekTNr3UtEbaT37Gqqm7N6Eb4MtUZlBBoxY+7f
0N0bGEs2vsq4fBtMWP2v6qEctUvlYEE+********************************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`pragma protect end_protected

//pragma protect end



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_csi2_rx) #(
    parameter tLPX_NS = 50,
    parameter tINIT_NS = 100000,
    parameter tCLK_TERM_EN_NS = 38,
    parameter tD_TERM_EN_NS = 35,
    parameter tHS_SETTLE_NS = 85,
    parameter tHS_PREPARE_ZERO_NS = 145,
    parameter NUM_DATA_LANE = 4,
    parameter HS_BYTECLK_MHZ = 187,
    parameter CLOCK_FREQ_MHZ = 100,
    parameter DPHY_CLOCK_MODE = "Continuous",  //"Continuous", "Discontinuous"
    parameter PIXEL_FIFO_DEPTH = 1024,
    parameter AREGISTER = 8,
    parameter ENABLE_USER_DESKEWCAL = 0,
    parameter ENABLE_VCX = 0,
    parameter FRAME_MODE = "GENERIC",    //1-ACCURATE, 0-GENERIC
    parameter ASYNC_STAGE = 2,
    parameter PACK_TYPE = 4'b1111
)(
    input logic           reset_n,
    input logic           clk,				//100Mhz
    input logic           reset_byte_HS_n,
    input logic           clk_byte_HS,
    input logic           reset_pixel_n,
    input logic           clk_pixel,
    // LVDS clock lane   
    input logic           Rx_LP_CLK_P, 
	input logic           Rx_LP_CLK_N,
    output logic          Rx_HS_enable_C, 
	output logic          LVDS_termen_C,
	
    // LVDS RX data lane
    input logic  [NUM_DATA_LANE-1:0]      Rx_LP_D_P, 
	input logic  [NUM_DATA_LANE-1:0]      Rx_LP_D_N,
    input logic  [7:0]                    Rx_HS_D_0,
    input logic  [7:0]                    Rx_HS_D_1,
    input logic  [7:0]                    Rx_HS_D_2,
    input logic  [7:0]                    Rx_HS_D_3,
    input logic  [7:0]                    Rx_HS_D_4,
    input logic  [7:0]                    Rx_HS_D_5,
    input logic  [7:0]                    Rx_HS_D_6,
    input logic  [7:0]                    Rx_HS_D_7,
    // control signal to LVDS IO
    output logic [NUM_DATA_LANE-1:0]      Rx_HS_enable_D, 
	output logic [NUM_DATA_LANE-1:0]      LVDS_termen_D,
	output logic [NUM_DATA_LANE-1:0]      fifo_rd_enable,                            
	input  logic [NUM_DATA_LANE-1:0]      fifo_rd_empty,
    output logic [NUM_DATA_LANE-1:0]      DLY_enable_D,
	output logic [NUM_DATA_LANE-1:0]      DLY_inc_D,
	input  logic [NUM_DATA_LANE-1:0]      u_dly_enable_D, //user control the IO delay
	input  logic [NUM_DATA_LANE-1:0]      u_dly_inc_D, //user control the IO delay

    //AXI4-Lite Interface
    input                 axi_clk,
    input                 axi_reset_n,
    input          [5:0]  axi_awaddr,//Write Address. byte address.
    input                 axi_awvalid,//Write address valid.
    output logic          axi_awready,//Write address ready.
    input          [31:0] axi_wdata,//Write data bus.
    input                 axi_wvalid,//Write valid.
    output logic          axi_wready,//Write ready.
                          
    output logic          axi_bvalid,//Write response valid.
    input                 axi_bready,//Response ready.      
    input          [5:0]  axi_araddr,//Read address. byte address.
    input                 axi_arvalid,//Read address valid.
    output logic          axi_arready,//Read address ready.
    output logic   [31:0] axi_rdata,//Read data.
    output logic          axi_rvalid,//Read valid.
    input                 axi_rready,//Read ready.
	
    output logic          hsync_vc0,
    output logic          hsync_vc1,
    output logic          hsync_vc2,
    output logic          hsync_vc3,
    output logic          vsync_vc0,
    output logic          vsync_vc1,
    output logic          vsync_vc2,
    output logic          vsync_vc3,
    
    output logic          hsync_vc4,
    output logic          hsync_vc5,
    output logic          hsync_vc6,
    output logic          hsync_vc7,
    output logic          hsync_vc8,
    output logic          hsync_vc9,
    output logic          hsync_vc10,
    output logic          hsync_vc11,
    output logic          hsync_vc12,
    output logic          hsync_vc13,
    output logic          hsync_vc14,
    output logic          hsync_vc15,
    output logic          vsync_vc4,
    output logic          vsync_vc5,
    output logic          vsync_vc6,
    output logic          vsync_vc7,
    output logic          vsync_vc8,
    output logic          vsync_vc9,
    output logic          vsync_vc10,
    output logic          vsync_vc11,
    output logic          vsync_vc12,
    output logic          vsync_vc13,
    output logic          vsync_vc14,
    output logic          vsync_vc15,
    
    output logic [1:0]    vc,
    output logic [1:0]    vcx,
    output logic [15:0]   word_count,
    output logic [15:0]   shortpkt_data_field,
    output logic [5:0]    datatype,
    output logic [3:0]    pixel_per_clk,
    output logic [63:0]   pixel_data,
    output logic          pixel_data_valid,
`ifdef MIPI_CSI2_RX_DEBUG
    input  logic [31:0]   mipi_debug_in,
    output logic [31:0]   mipi_debug_out,
`endif
`ifdef MIPI_CSI2_RX_PIXEL_SIDEBAND
    output logic [15:0]   pixel_line_num,
    output logic [15:0]   pixel_frame_num,
    output logic [5:0]    pixel_datatype,
    output logic [15:0]   pixel_wordcount,
    output logic [1:0]    pixel_vc,
    output logic [1:0]    pixel_vcx,
`endif
    output logic          irq
    
);

logic [7:0] RxDataHS_0, RxDataHS_1, RxDataHS_2, RxDataHS_3, RxDataHS_4, RxDataHS_5, RxDataHS_6, RxDataHS_7;
logic RxValidHS_0, RxValidHS_1, RxValidHS_2, RxValidHS_3, RxValidHS_4, RxValidHS_5, RxValidHS_6, RxValidHS_7;
// logic [NUM_DATA_LANE-1:0][7:0] RxDataHS;
logic [NUM_DATA_LANE-1:0] RxValidHS, RxSyncHS;
logic RxUlpsClkNot, RxUlpsActiveClkNot;
logic [NUM_DATA_LANE-1:0] RxErrEsc, RxErrControl, RxErrSotSyncHS;
logic [NUM_DATA_LANE-1:0] RxUlpsEsc, RxUlpsActiveNot, RxSkewCalHS, RxStopState; 

generate
if (NUM_DATA_LANE == 1) begin
// assign RxDataHS[0] = RxDataHS_0;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = 1'b0;
assign RxValidHS_2 = 1'b0;
assign RxValidHS_3 = 1'b0;
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end             
else if (NUM_DATA_LANE == 2) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = 1'b0;
assign RxValidHS_3 = 1'b0;
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end
else if (NUM_DATA_LANE == 4) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
// assign RxDataHS[2] = RxDataHS_2;
// assign RxDataHS[3] = RxDataHS_3;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = RxValidHS[2];
assign RxValidHS_3 = RxValidHS[3];
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end
else if (NUM_DATA_LANE == 8) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
// assign RxDataHS[2] = RxDataHS_2;
// assign RxDataHS[3] = RxDataHS_3;
// assign RxDataHS[4] = RxDataHS_4;
// assign RxDataHS[5] = RxDataHS_5;
// assign RxDataHS[6] = RxDataHS_6;
// assign RxDataHS[7] = RxDataHS_7;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = RxValidHS[2];
assign RxValidHS_3 = RxValidHS[3];
assign RxValidHS_4 = RxValidHS[4];
assign RxValidHS_5 = RxValidHS[5];
assign RxValidHS_6 = RxValidHS[6];
assign RxValidHS_7 = RxValidHS[7];
end                              
endgenerate

`IP_MODULE_NAME(efx_dphy_rx) #(
    .tLPX_NS              (tLPX_NS),
    .tCLK_TERM_EN_NS      (tCLK_TERM_EN_NS),
    .tD_TERM_EN_NS        (tD_TERM_EN_NS),
    .tHS_SETTLE_NS        (tHS_SETTLE_NS),
    .tHS_PREPARE_ZERO_NS  (tHS_PREPARE_ZERO_NS),
    .HS_BYTECLK_MHZ       (HS_BYTECLK_MHZ),
    .CLOCK_FREQ_MHZ       (CLOCK_FREQ_MHZ),
    .NUM_DATA_LANE        (NUM_DATA_LANE),
    .ENABLE_USER_DESKEWCAL(ENABLE_USER_DESKEWCAL),
    .DPHY_CLOCK_MODE      (DPHY_CLOCK_MODE)
) dphy_rx_inst (
    .reset_n              (reset_n),
    .clk                  (clk),				
    .reset_byte_HS_n      (reset_byte_HS_n),
    .clk_byte_HS          (clk_byte_HS),
    //To LVDS clock lane   
    .Rx_LP_CLK_P          (Rx_LP_CLK_P), 
	.Rx_LP_CLK_N          (Rx_LP_CLK_N),
    .Rx_HS_enable_C       (Rx_HS_enable_C), 
	.LVDS_termen_C        (LVDS_termen_C), 
	
	//ULPS clock
	.RxUlpsClkNot         (RxUlpsClkNot),
	.RxUlpsActiveClkNot   (RxUlpsActiveClkNot),
	
	//To LVDS data lane 0
	.Rx_LP_D_P            (Rx_LP_D_P     ),
	.Rx_LP_D_N            (Rx_LP_D_N     ),
	.Rx_HS_D_0            (Rx_HS_D_0     ),
	.Rx_HS_D_1            (Rx_HS_D_1     ),
	.Rx_HS_D_2            (Rx_HS_D_2     ),
	.Rx_HS_D_3            (Rx_HS_D_3     ),
	.Rx_HS_D_4            (Rx_HS_D_4     ),
	.Rx_HS_D_5            (Rx_HS_D_5     ),
	.Rx_HS_D_6            (Rx_HS_D_6     ),
	.Rx_HS_D_7            (Rx_HS_D_7     ),
	.Rx_HS_enable_D       (Rx_HS_enable_D),
	.LVDS_termen_D        (LVDS_termen_D ),
	.fifo_rd_enable       (fifo_rd_enable),
	.fifo_rd_empty        (fifo_rd_empty ),
	.DLY_enable_D         (DLY_enable_D  ),
	.DLY_inc_D            (DLY_inc_D     ),
	.u_dly_enable_D       (u_dly_enable_D),
	.u_dly_inc_D          (u_dly_inc_D),	                   
	//To CSI2 lane 0      
    .RxUlpsEsc            (RxUlpsEsc),
    .RxUlpsActiveNot      (RxUlpsActiveNot),
    .RxErrEsc             (RxErrEsc),
    .RxErrControl         (RxErrControl),
    .RxErrSotSyncHS       (RxErrSotSyncHS),
    .RxDataHS_0           (RxDataHS_0), 
    .RxDataHS_1           (RxDataHS_1),
    .RxDataHS_2           (RxDataHS_2), 
    .RxDataHS_3           (RxDataHS_3),
    .RxDataHS_4           (RxDataHS_4), 
    .RxDataHS_5           (RxDataHS_5),
    .RxDataHS_6           (RxDataHS_6), 
    .RxDataHS_7           (RxDataHS_7),
    .RxValidHS            (RxValidHS), 
    .RxActiveHS           (),
    .RxSyncHS             (RxSyncHS),
    .RxSkewCalHS          (RxSkewCalHS),
    .RxStopState          (RxStopState),
    //LPDT mode only supported in DSI
    .RxLPDTEsc            (),
    .RxValidEsc           (),
    .RxDataEsc_0          (),
    .RxDataEsc_1          (),
    .RxDataEsc_2          (),
    .RxDataEsc_3          (),
    .RxDataEsc_4          (),
    .RxDataEsc_5          (),
    .RxDataEsc_6          (),
    .RxDataEsc_7          ()
);

`IP_MODULE_NAME(efx_csi2_rx_top) #(
    .HS_DATA_WIDTH         (8),
    .tINIT_NS              (tINIT_NS),
    .CLOCK_FREQ_MHZ        (CLOCK_FREQ_MHZ),
    .NUM_DATA_LANE         (NUM_DATA_LANE),
    .PACK_TYPE             (PACK_TYPE),
    .AREGISTER             (AREGISTER),
    .ENABLE_VCX            (ENABLE_VCX),
    .FRAME_MODE            (FRAME_MODE),
    .ASYNC_STAGE            (ASYNC_STAGE),
    .PIXEL_FIFO_DEPTH      (PIXEL_FIFO_DEPTH)
) csi2_rx_top_inst (
    .reset_n              (reset_n),
    .clk                  (clk),				
    .reset_byte_HS_n      (reset_byte_HS_n),
    .clk_byte_HS          (clk_byte_HS),
    .reset_pixel_n        (reset_pixel_n),
    .clk_pixel            (clk_pixel),
    .axi_clk              (axi_clk),
    .axi_reset_n          (axi_reset_n),
    .axi_awaddr           (axi_awaddr),
    .axi_awvalid          (axi_awvalid),
    .axi_awready          (axi_awready),
    .axi_wdata            (axi_wdata),
    .axi_wvalid           (axi_wvalid),
    .axi_wready           (axi_wready),                        
    .axi_bvalid           (axi_bvalid),
    .axi_bready           (axi_bready),
    .axi_araddr           (axi_araddr),
    .axi_arvalid          (axi_arvalid),
    .axi_arready          (axi_arready),
    .axi_rdata            (axi_rdata),
    .axi_rvalid           (axi_rvalid),
    .axi_rready           (axi_rready),
    
	.RxUlpsClkNot         (RxUlpsClkNot),
	.RxUlpsActiveClkNot   (RxUlpsActiveClkNot),
    .RxErrEsc             (RxErrEsc),
    .RxClkEsc             ({NUM_DATA_LANE{1'b0}}),
    .RxErrControl         (RxErrControl),
    .RxErrSotSyncHS       (RxErrSotSyncHS),
    .RxUlpsEsc            (RxUlpsEsc),
    .RxUlpsActiveNot      (RxUlpsActiveNot),
    .RxSkewCalHS          (RxSkewCalHS),
    .RxStopState          (RxStopState),
    .RxSyncHS             (RxSyncHS),
    .RxDataHS0            (RxDataHS_0),
    .RxDataHS1            (RxDataHS_1),  
    .RxDataHS2            (RxDataHS_2),
    .RxDataHS3            (RxDataHS_3),
    .RxDataHS4            (RxDataHS_4),
    .RxDataHS5            (RxDataHS_5),
    .RxDataHS6            (RxDataHS_6),
    .RxDataHS7            (RxDataHS_7),
    .RxValidHS0           (RxValidHS_0),
    .RxValidHS1           (RxValidHS_1),
    .RxValidHS2           (RxValidHS_2),
    .RxValidHS3           (RxValidHS_3),
    .RxValidHS4           (RxValidHS_4),
    .RxValidHS5           (RxValidHS_5),
    .RxValidHS6           (RxValidHS_6),
    .RxValidHS7           (RxValidHS_7),
    
    .hsync_vc0            (hsync_vc0),
    .hsync_vc1            (hsync_vc1),
    .hsync_vc2            (hsync_vc2),
    .hsync_vc3            (hsync_vc3),
    .vsync_vc0            (vsync_vc0),
    .vsync_vc1            (vsync_vc1),
    .vsync_vc2            (vsync_vc2),
    .vsync_vc3            (vsync_vc3), 
                          
    .hsync_vc4            (hsync_vc4),
    .hsync_vc5            (hsync_vc5),
    .hsync_vc6            (hsync_vc6),
    .hsync_vc7            (hsync_vc7),
    .hsync_vc8            (hsync_vc8),
    .hsync_vc9            (hsync_vc9),
    .hsync_vc10           (hsync_vc10),
    .hsync_vc11           (hsync_vc11),
    .hsync_vc12           (hsync_vc12),
    .hsync_vc13           (hsync_vc13),
    .hsync_vc14           (hsync_vc14),
    .hsync_vc15           (hsync_vc15),
    .vsync_vc4            (vsync_vc4),
    .vsync_vc5            (vsync_vc5),
    .vsync_vc6            (vsync_vc6),
    .vsync_vc7            (vsync_vc7),
    .vsync_vc8            (vsync_vc8),
    .vsync_vc9            (vsync_vc9),
    .vsync_vc10           (vsync_vc10),
    .vsync_vc11           (vsync_vc11),
    .vsync_vc12           (vsync_vc12),
    .vsync_vc13           (vsync_vc13),
    .vsync_vc14           (vsync_vc14),
    .vsync_vc15           (vsync_vc15),
    .vc                   (vc),
    .vcx                  (vcx),
    .word_count           (word_count),
    .shortpkt_data_field  (shortpkt_data_field),
    .datatype             (datatype),  
    .pixel_per_clk        (pixel_per_clk),
    .pixel_data           (pixel_data), 
    .pixel_data_valid     (pixel_data_valid),
`ifdef MIPI_CSI2_RX_DEBUG
    .mipi_debug_in        (mipi_debug_in    ),
    .mipi_debug_out       (mipi_debug_out   ),
`endif 
`ifdef MIPI_CSI2_RX_PIXEL_SIDEBAND
    .pixel_line_num       (pixel_line_num   ),
    .pixel_frame_num      (pixel_frame_num  ),
    .pixel_datatype       (pixel_datatype   ),
    .pixel_wordcount      (pixel_wordcount  ),
    .pixel_vc             (pixel_vc         ),
    .pixel_vcx            (pixel_vcx        ),
`endif 
    .irq                  (irq)
);


endmodule


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
fIbKzZ6y8cb0GKv/OgCJ1y24E4+IT3h2BSK2wsJ3DeQFgVQLaewEYDM+gqk/jQEA
7rqrVifhcIZgX3xoOGcHCd9kCNMOffJOMHapHVAFaV9qGnmrhaTry0E5z4b9gBQ2
omLLbyhYc1YPtZtTa1/ihXdkNPEiZDkg5FSQy5ntB5U=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 3568 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
W3v9MbvjPwzyNfjkTxNuToW6hpxYAb9+LYaThD7uCpnM1aTioS+ymZekca1okTLj
eDamgTz7qZai62nX4ndDFuF4PmjylmEM8QSZ3HKA5afFfUSGKeg6bgF/NJbqcr03
jGMxMnP7dwUzz/NMDv2MaNb/wUsPwKA7lgBAmpSsWEk=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 10032 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
qPevvrxtjt++loNZIwisehiDcP+Yff7thTN0c5xh77v6yBt3c14FwgzFx+mmUZQc
ZwXBp9p4q069ls/4trLQx0I6owa/Z2C3Sa+Lza9DFNflh/oxvpVcD2wx50tau5KW
2/OUHxCqCXGWtTUWyBpe0EUO+g6J9xQO/aG3PE98Ak4=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 8800 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
a7a0xjWp4hW7MShn4IjhsekSxTxW9pLqFv2pfa94FEpud8RLogo/sqAKAETwMXVS
IHlX/A6nDgMZfbsGn95cqmWTiTfuLUOIJqvDT3l5WX4TUTWZjDHStNexJW2EqYS0
hAOI3MfpI9rG8b5U7EiCST6K19OeEpJVKwq4eO+Az14=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 14000 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
SJ2o9F4mPtu8A/kqmGCbVBc1/THGlw/DgmwATW7j3/Xcj99vWBDbdLk3WJYrdJpG
LAkv3rnUUHlZEUivnm4ZyVSonH7Z4VEnMQYYSOKFVk8nZRsOcfOwJE9P7r+m9ONB
i/nr0GxA+fo4wpVB6GV+AaevJ+Ic1VVpPcCWFKcGpFE=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 832 )
`pragma protect data_block
PA/MkQc509i0ABoQvUYY3CIlS+90fXSsVFD1igwEXI8nkAmgDx7fvt6XRbcW701c
h6wAuDGfPhgFvvI+NtKmU54KMiKvzENdbF7kX5T+Nfk94FicYB5xupZP8ce1uwEE
Yar06t1nINy6yoD/BZs9oMTIKq63rRr9K74UYbpGQ23YIddthrIaraPFRp5xQ90M
LBXLvAYOXuS36gcRY7xH2V+w8PqK+M8aD1sPEtNcCeOfzeCxv7KOdSw/8omQNz+G
pEuqVlICJ6l0jb2rqE+tnnmgT05mJuSfnzOqxTg5NO/jGrXajPjMYGgsTzr8RzcT
bO2GFKQDA2L7DnwjOUKBRtbKzc5KaDV2M6seV/xfz/4W685P1t0w9iKsLxufSB8p
dnAV44q3zKYHSW5o7Aah96GIUuCHUFmyCCsHJ3Req2YW2ML3Yf6CFmSXrZ717j4z
z1qVk8ul5ME7ndbwDzGlE8BFUYLySgTunpty9PU+JHBuo9u8P+20VLquSU7J32uq
TZ0jr9Qr6fCtWj+4os1Q2vz97FjsyIEnPQFIXy6DVeUbQYZ5FGZpyJ2sN3F65oYA
LjT2dwn1hlFuQ70ArN4kFdaJkhD2hOcFS0lGVFxMNkZcfKlKDwHmptUC78jCymSu
xUZ0t1ilJEcREussXF2kqeWP0I6Ua7TDmExDA2wrncxjfhn4KnrFjP9u3Pv7XJs6
xqRHKPKXVa33CBXeApMi8FapDmjvLDNP/FKZ2HZna4lqNkJ/Gmcb3mqS7fR/hyqw
PycYl5tRM4IkNO70xz7ByCH1P7r455Nbc49Wb8JdoBn0wZ9oz5Nr0pdsDZA4ISGt
xXFecSPW7zue9LRUqRCZQqmP+4nmwW8mHveXOR3IFUkGerTAkNne1cj9+XJbYgw1
1d6s4pMb+Yme3LEhb/71FJayyUI9TRYAxG+rENUYnC7WNim/vm1stnnwLTpEB8F6
4242vc4UGx3u+d7PUAizavoKNWy2RKRXcB+1z5IblDTdATtis1TvCsWHcEqKOBjL
cDxXPieQ3ohj3IMXgngc8dWYVa6vJwseMfVfpdOMDtULWL3ekI/J1PmhVGwQKPBP
1hr5Qqi0nsQc9nXupCP+OQ==
`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Synopsys" , key_keyname = "SNPS-VCS-RSA-2"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 128 )
`pragma protect key_block
NzC9jUWRN0Jsj7P0ruQv7u9hP/kgPWRLHeg70iEDoplxBaUu38Hz1+fTvR2AS7DN
ZIBmgAbvOAMPfgk49av/14+Xuqy2h5HsFRy2cuaFk96dw9w3fDn+Seiwi4pxXrGW
qyx4xnMl8BVHF6MCJ40eWB3qlS4seUJJNyFkD6P1BZI=
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 6336 )
`pragma protect data_block
nF8TZgJUFuCi7w1seYCWOLQaKli8nLYZLy28JCuhprD42IQJvwJj0hiWsauw8Fgj
cdhcXe3pIDGOcx0PJx2+31rGYRxVlKl0/Au1QP1M7Yj+NJOOagKDWbtvLDp5WVS4
tWMPPaFqU+GxtrnqeNaRKqtSote6NNxs8rTMpwYNvaiA2TQpdG7eSLGoqjAf4mwP
OYrJHW5vvijeHePcZmeYPH3ZG9shaZO+KE0IRse6Nx8S/WT5CYNVuCtVi3CXSS1D
nOVaLkDSTLHu9/***************************+bYfJ2ZBrtWmIN2uA1eOw8t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`pragma protect end_protected

//pragma protect end


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_top) # (
    parameter FAMILY             = "TRION",       // New Param
    parameter SYNC_CLK           = 0,
    parameter BYPASS_RESET_SYNC  = 0,             // New Param
    parameter SYNC_STAGE         = 2,             // New Param
    parameter MODE               = "STANDARD",
    parameter DEPTH              = 512,           // Reverted (Equivalent to WDATA_DEPTH) 
    parameter DATA_WIDTH         = 32,            // Reverted (Equivalent to WDATA_WIDTH)
    parameter PIPELINE_REG       = 1,             // Reverted (By default is ON)
    parameter OPTIONAL_FLAGS     = 1,             // Reverted
    parameter OUTPUT_REG         = 0,
    parameter PROGRAMMABLE_FULL  = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_FULL_ASSERT   = 27,
    parameter PROG_FULL_NEGATE   = 23,
    parameter PROGRAMMABLE_EMPTY = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_EMPTY_ASSERT  = 5,
    parameter PROG_EMPTY_NEGATE  = 7,
    parameter ALMOST_FLAG        = OPTIONAL_FLAGS,
    parameter HANDSHAKE_FLAG     = OPTIONAL_FLAGS,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter WADDR_WIDTH        = depth2width(DEPTH),
    parameter RDATA_WIDTH        = rdwidthcompute(ASYM_WIDTH_RATIO,DATA_WIDTH),
    parameter RD_DEPTH           = rddepthcompute(DEPTH,DATA_WIDTH,RDATA_WIDTH),
    parameter RADDR_WIDTH        = depth2width(RD_DEPTH),
    parameter ENDIANESS		      = 0,
    parameter OVERFLOW_PROTECT   = 1,
    parameter UNDERFLOW_PROTECT  = 1,
    parameter RAM_STYLE          = "block_ram"

)(
    input  wire                   a_rst_i,
    input  wire                   a_wr_rst_i,
    input  wire                   a_rd_rst_i,
    input  wire                   clk_i,
    input  wire                   wr_clk_i,
    input  wire                   rd_clk_i,
    input  wire                   wr_en_i,
    input  wire                   rd_en_i,
    input  wire [DATA_WIDTH-1:0]  wdata,
    output wire                   almost_full_o,
    output wire                   prog_full_o,
    output wire                   full_o,
    output wire                   overflow_o,
    output wire                   wr_ack_o,
    output wire [WADDR_WIDTH :0]  datacount_o,
    output wire [WADDR_WIDTH :0]  wr_datacount_o,
    output wire                   empty_o,
    output wire                   almost_empty_o,
    output wire                   prog_empty_o,
    output wire                   underflow_o,
    output wire                   rd_valid_o,
    output wire [RDATA_WIDTH-1:0] rdata,
    output wire [RADDR_WIDTH :0]  rd_datacount_o,
    output wire                   rst_busy
); 

localparam WR_DEPTH      = DEPTH;
localparam WDATA_WIDTH   = DATA_WIDTH;
localparam RAM_MUX_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? 32 :
                           (RDATA_WIDTH <= WDATA_WIDTH/16) ? 16 :
                           (RDATA_WIDTH <= WDATA_WIDTH/8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH/4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH/2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH)    ? 1  :
                           (RDATA_WIDTH <= WDATA_WIDTH*2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH*4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH*8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH*16) ? 16 : 32;           

wire                   wr_rst_int;
wire                   rd_rst_int;
wire                   wr_en_int;
wire                   rd_en_int;
wire [WADDR_WIDTH-1:0] waddr;
wire [RADDR_WIDTH-1:0] raddr;
wire                   wr_clk_int;
wire                   rd_clk_int;
wire [WADDR_WIDTH :0]  wr_datacount_int;
wire [RADDR_WIDTH :0]  rd_datacount_int;

generate
    if (ASYM_WIDTH_RATIO == 4) begin
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
            assign datacount_o    = wr_datacount_int;
            assign wr_datacount_o = {(WADDR_WIDTH+1){1'b0}};
            assign rd_datacount_o = {(RADDR_WIDTH+1){1'b0}};
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
            assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
            assign wr_datacount_o = wr_datacount_int;
            assign rd_datacount_o = rd_datacount_int;
        end
    end
    else begin
        assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
        assign wr_datacount_o = wr_datacount_int;
        assign rd_datacount_o = rd_datacount_int;
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
        end
    end    

    if (!SYNC_CLK) begin
        //(* async_reg = "true" *) reg [1:0] wr_rst;
        //(* async_reg = "true" *) reg [1:0] rd_rst;
        //
        //always @ (posedge wr_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        wr_rst <= 2'b11;
        //    else 
        //        wr_rst <= {wr_rst[0],1'b0};            
        //end
        //
        //always @ (posedge rd_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        rd_rst <= 2'b11;
        //    else 
        //        rd_rst <= {rd_rst[0],1'b0};            
        //end
        

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_wr_rst_i;
            assign rd_rst_int = a_rd_rst_i; 
            assign rst_busy   = 1'b0;
        end 
        else begin
            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_wr_rst (
               .clk   (wr_clk_int),
               .reset (a_rst_i),
               .d_o   (wr_rst_int)
            );

            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_rd_rst (
               .clk   (rd_clk_int),
               .reset (a_rst_i),
               .d_o   (rd_rst_int)
            );
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end

    end
    else begin
        //(* async_reg = "true" *) reg [1:0] a_rst;
        //
        //always @ (posedge clk_i or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        a_rst <= 2'b11;
        //    else 
        //        a_rst <= {a_rst[0],1'b0};            
        //end
        wire   a_rst;

        `IP_MODULE_NAME(efx_resetsync) #(
           .ACTIVE_LOW (0)
        ) efx_resetsync_a_rst (
           .clk   (clk_i),
           .reset (a_rst_i),
           .d_o   (a_rst)
        );

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_rst_i;
            assign rd_rst_int = a_rst_i;
            assign rst_busy   = 1'b0;
        end
        else begin
            assign wr_rst_int = a_rst;
            assign rd_rst_int = a_rst;   
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end 
endgenerate

`IP_MODULE_NAME(efx_fifo_ram) # (
    .FAMILY        (FAMILY),
    .WR_DEPTH      (WR_DEPTH),
    .RD_DEPTH      (RD_DEPTH),
    .WDATA_WIDTH   (WDATA_WIDTH),
    .RDATA_WIDTH   (RDATA_WIDTH),
    .WADDR_WIDTH   (WADDR_WIDTH),
    .RADDR_WIDTH   (RADDR_WIDTH),
    .OUTPUT_REG    (OUTPUT_REG),
    .RAM_MUX_RATIO (RAM_MUX_RATIO),
    .ENDIANESS	    (ENDIANESS),
    .RAM_STYLE     (RAM_STYLE)
) xefx_fifo_ram (
    .wdata (wdata), 
    .waddr (waddr),
    .raddr (raddr), 
    .we    (wr_en_int), 
    .re    (rd_en_int),
    .wclk  (wr_clk_int),
    .rclk  (rd_clk_int),
    .rdata (rdata)
);

`IP_MODULE_NAME(efx_fifo_ctl) # (
    .SYNC_CLK           (SYNC_CLK),
    .SYNC_STAGE         (SYNC_STAGE),
    .MODE               (MODE),
    .WR_DEPTH           (WR_DEPTH),
    .WADDR_WIDTH        (WADDR_WIDTH),
    .RADDR_WIDTH        (RADDR_WIDTH),
    .ASYM_WIDTH_RATIO   (ASYM_WIDTH_RATIO),
    .RAM_MUX_RATIO      (RAM_MUX_RATIO),
    .PIPELINE_REG       (PIPELINE_REG),
    .ALMOST_FLAG        (ALMOST_FLAG),
    .PROGRAMMABLE_FULL  (PROGRAMMABLE_FULL),
    .PROG_FULL_ASSERT   (PROG_FULL_ASSERT),
    .PROG_FULL_NEGATE   (PROG_FULL_NEGATE),
    .PROGRAMMABLE_EMPTY (PROGRAMMABLE_EMPTY),
    .PROG_EMPTY_ASSERT  (PROG_EMPTY_ASSERT),
    .PROG_EMPTY_NEGATE  (PROG_EMPTY_NEGATE),
    .OUTPUT_REG         (OUTPUT_REG),
    .HANDSHAKE_FLAG     (HANDSHAKE_FLAG),
    .OVERFLOW_PROTECT   (OVERFLOW_PROTECT),
    .UNDERFLOW_PROTECT  (UNDERFLOW_PROTECT)
) xefx_fifo_ctl (
    .wr_rst          (wr_rst_int),
    .rd_rst          (rd_rst_int),
    .wclk            (wr_clk_int),
    .rclk            (rd_clk_int),
    .we              (wr_en_i),
    .re              (rd_en_i),
    .wr_full         (full_o),
    .wr_ack          (wr_ack_o),
    .rd_empty        (empty_o),
    .wr_almost_full  (almost_full_o),
    .rd_almost_empty (almost_empty_o),
    .wr_prog_full    (prog_full_o),
    .rd_prog_empty   (prog_empty_o),
    .wr_en_int       (wr_en_int),
    .rd_en_int       (rd_en_int),
    .waddr           (waddr),
    .raddr           (raddr),
    .wr_datacount    (wr_datacount_int),
    .rd_datacount    (rd_datacount_int),
    .rd_vld          (rd_valid_o),
    .wr_overflow     (overflow_o),
    .rd_underflow    (underflow_o)
);

function integer depth2width;
input [31:0] depth;
begin : fnDepth2Width
    if (depth > 1) begin
        depth = depth - 1;
        for (depth2width=0; depth>0; depth2width = depth2width + 1)
            depth = depth>>1;
        end
    else
       depth2width = 0;
end
endfunction 

function integer width2depth;
input [31:0] width;
begin : fnWidth2Depth
    width2depth = width**2;
end
endfunction

function integer rdwidthcompute;
input [31:0] asym_option;
input [31:0] wr_width;
begin : RdWidthCompute
    rdwidthcompute = (asym_option==0)? wr_width/16 :
                     (asym_option==1)? wr_width/8  :
                     (asym_option==2)? wr_width/4  :
                     (asym_option==3)? wr_width/2  :
                     (asym_option==4)? wr_width/1  :
                     (asym_option==5)? wr_width*2  :
                     (asym_option==6)? wr_width*4  :
                     (asym_option==7)? wr_width*8  :
                     (asym_option==8)? wr_width*16 : wr_width/1;
end
endfunction

function integer rddepthcompute;
input [31:0] wr_depth;
input [31:0] wr_width;
input [31:0] rd_width;
begin : RdDepthCompute
    rddepthcompute = (wr_depth * wr_width) / rd_width;
end
endfunction

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ram) #(
    parameter FAMILY        = "TRION",
    parameter WR_DEPTH      = 512,
    parameter RD_DEPTH      = 512,
    parameter WDATA_WIDTH   = 8,
    parameter RDATA_WIDTH   = 8,
    parameter WADDR_WIDTH   = 9,
    parameter RADDR_WIDTH   = 9,
    parameter OUTPUT_REG    = 1,
    parameter RAM_MUX_RATIO = 4,
    parameter ENDIANESS 	 = 0, //0: Big endian (default)   1: Little endian 
    parameter RAM_STYLE     = "block_ram"
) (
    input  wire                     wclk,
    input  wire                     rclk,
    input  wire                     we,
    input  wire                     re,
    input  wire [(WDATA_WIDTH-1):0] wdata,
    input  wire [(WADDR_WIDTH-1):0] waddr,
    input  wire [(RADDR_WIDTH-1):0] raddr,
    output wire [(RDATA_WIDTH-1):0] rdata
);

localparam MEM_DEPTH         = (WR_DEPTH > RD_DEPTH) ? WR_DEPTH : RD_DEPTH;
localparam MEM_DATA_WIDTH    = (WDATA_WIDTH > RDATA_WIDTH) ? RDATA_WIDTH : WDATA_WIDTH;   
localparam LSB_WIDTH         = (WADDR_WIDTH > RADDR_WIDTH) ? (WADDR_WIDTH - RADDR_WIDTH) : (RADDR_WIDTH - WADDR_WIDTH);
localparam RDATA_WDATA_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? "ONE_THIRTYTWO" :
                               (RDATA_WIDTH <= WDATA_WIDTH/16) ? "ONE_SIXTEENTH" :
                               (RDATA_WIDTH <= WDATA_WIDTH/8)  ? "ONE_EIGHTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/4)  ? "ONE_FOURTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/2)  ? "ONE_HALF"      :
                               (RDATA_WIDTH <= WDATA_WIDTH)    ? "ONE"           :
                               (RDATA_WIDTH <= WDATA_WIDTH*2)  ? "TWO_TIMES"     :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "FOUR_TIMES"    :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "EIGHT_TIMES"   :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "SIXTEEN_TIMES" : "THIRTYTWO_TIMES";

(* syn_ramstyle = RAM_STYLE *) reg [MEM_DATA_WIDTH-1:0] ram[MEM_DEPTH-1:0];
reg [RDATA_WIDTH-1:0]    r_rdata_1P;
reg [RDATA_WIDTH-1:0]    r_rdata_2P;

wire                     re_int;

generate
    if (FAMILY == "TRION") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                end
                r_rdata_2P <= r_rdata_1P;
            end
        end
        
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            if (ENDIANESS == 0) begin 
            	integer i;
            	always @ (posedge wclk) begin
                	for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                    end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                	end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
        end
        
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  ||RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
            //integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end 
            end
            else begin //endianess == 1
            	always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end
            end
        end
        if (OUTPUT_REG) begin
            assign re_int = re;
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end
    end
    else begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                    r_rdata_2P <= r_rdata_1P;
                end
            end
        end 
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            //integer i;
            if (ENDIANESS == 0) begin
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
            
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
        end
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  || RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
        	//integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
            
            else begin //endianess ==1
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
        end
        // NIC
        //if (MODE == "STANDARD") begin     
        //    if (OUTPUT_REG) begin
        //        reg re_r;
        //        always @ (posedge rclk) begin
        //            re_r <= re;
        //        end
        //        assign re_int = re | re_r;        
        //        assign rdata  = r_rdata_2P;            
        //    end
        //    else begin
        //        assign re_int = re;
        //        assign rdata  = r_rdata_1P;
        //    end
        //end
        //else begin
        //    assign re_int = re;
        //    assign rdata  = r_rdata_1P;
        //end    
        if (OUTPUT_REG) begin
            reg re_r;
            always @ (posedge rclk) begin
                re_r <= re;
            end
            assign re_int = re | re_r;        
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end    
    end
endgenerate

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2021 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   gray2bin.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Gray to Binary Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_gray2bin)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] bin_o,
 // input
 input [WIDTH-1:0] gray_i);

//---------------------------------------------------------------------
// Recursive Module
// Description: reduction xor
generate
   if (WIDTH > 1) begin
      wire [1:0] bin_1;
      assign bin_1 = {gray_i[WIDTH-1], gray_i[WIDTH-1]^gray_i[WIDTH-2]};
      if (WIDTH == 2) begin
	 assign bin_o = bin_1;
      end
      else begin
	 assign bin_o[WIDTH-1] = bin_1[1];
	 `IP_MODULE_NAME(efx_fifo_gray2bin) #(.WIDTH(WIDTH-1)) u_gray2bin (.bin_o(bin_o[WIDTH-2:0]), .gray_i({bin_1[0], gray_i[WIDTH-3:0]}));
      end
   end
   else /* if (WIDTH == 1) */
     assign bin_o = gray_i;
endgenerate
   
endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   pipe_reg.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Parallel Pipelining Shift Register
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_datasync) #(
    parameter STAGE = 32,
    parameter WIDTH = 4
) (
 input  wire             clk_i,
 input  wire [WIDTH-1:0] d_i,
 output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] pipe_reg [STAGE-1:0];
integer i;

always @(posedge clk_i) begin
  for (i=STAGE-1; i>0; i = i - 1) begin
    pipe_reg[i] <= pipe_reg[i-1];
  end
  pipe_reg[0] <= d_i;
end
assign d_o = pipe_reg[STAGE-1];
   

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_resetsync) #(
    parameter ASYNC_STAGE = 2,
    parameter ACTIVE_LOW = 1
) (
    input  wire             clk,
    input  wire             reset,
    output wire             d_o
);


generate
   if (ACTIVE_LOW == 1) begin: active_low
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (1),
         .RST_VALUE  (0)
      ) efx_resetsync_active_low (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b1),
         .d_o     (d_o)
      );
   end
   else begin: active_high
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (0),
         .RST_VALUE  (1)
      ) efx_resetsync_active_high (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b0),
         .d_o     (d_o)
      );
   end
endgenerate

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ctl) # (
    parameter SYNC_CLK           = 1,
    parameter SYNC_STAGE         = 2,
    parameter MODE               = "STANDARD",
    parameter WR_DEPTH           = 512,
    parameter WADDR_WIDTH        = 9,
    parameter RADDR_WIDTH        = 9,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter RAM_MUX_RATIO      = 1,
    parameter PIPELINE_REG       = 1,
    parameter ALMOST_FLAG        = 1,
    parameter PROGRAMMABLE_FULL  = "NONE",
    parameter PROG_FULL_ASSERT   = 0,
    parameter PROG_FULL_NEGATE   = 0,
    parameter PROGRAMMABLE_EMPTY = "NONE",
    parameter PROG_EMPTY_ASSERT  = 0,
    parameter PROG_EMPTY_NEGATE  = 0,
    parameter OUTPUT_REG         = 0,
    parameter HANDSHAKE_FLAG     = 1, 
    parameter OVERFLOW_PROTECT   = 0,
    parameter UNDERFLOW_PROTECT  = 0
)(
    input   wire                   wr_rst,
    input   wire                   rd_rst,
    input   wire                   wclk,
    input   wire                   rclk,
    input   wire                   we,
    input   wire                   re,
    output  wire                   wr_full,
    output  reg                    wr_ack,
    output  wire                   wr_almost_full,
    output  wire                   rd_empty,
    output  wire                   rd_almost_empty,
    output  wire                   wr_prog_full,
    output  wire                   rd_prog_empty,
    output  wire                   wr_en_int,
    output  wire                   rd_en_int,
    output  wire [WADDR_WIDTH-1:0] waddr,
    output  wire [RADDR_WIDTH-1:0] raddr,
    output  wire [WADDR_WIDTH:0]   wr_datacount,
    output  wire [RADDR_WIDTH:0]   rd_datacount,
    output  wire                   rd_vld,
    output  reg                    wr_overflow,
    output  reg                    rd_underflow
);

reg  [WADDR_WIDTH:0] waddr_cntr;
reg  [WADDR_WIDTH:0] waddr_cntr_r;
reg  [RADDR_WIDTH:0] raddr_cntr;
reg                  rd_valid;

wire [WADDR_WIDTH:0] waddr_int;
wire [RADDR_WIDTH:0] raddr_int;
wire                 rd_empty_int;
wire [WADDR_WIDTH:0] wr_datacount_int;
wire [RADDR_WIDTH:0] rd_datacount_int;

assign waddr     = waddr_cntr[WADDR_WIDTH-1:0];
// NIC
wire [RADDR_WIDTH:0] ram_raddr;
assign raddr     = (MODE == "FWFT") ? ram_raddr[RADDR_WIDTH-1:0] : raddr_cntr[RADDR_WIDTH-1:0];
//assign raddr     = raddr_cntr[RADDR_WIDTH-1:0];
//assign wr_en_int = we & ~wr_full;
assign wr_en_int = OVERFLOW_PROTECT ? we & ~wr_full : we;

assign wr_datacount  = wr_datacount_int;
assign rd_datacount  = ASYM_WIDTH_RATIO == 4 && SYNC_CLK ? wr_datacount_int : rd_datacount_int;


generate
    if (MODE == "FWFT") begin    
        // NIC
        //assign rd_en_int     = (~rd_empty_int & rd_empty) | (re & ~rd_empty_int);
        //assign rd_empty      = rd_empty_fwft;

        assign rd_en_int     = 1'b1;
        //assign rd_empty      = rd_empty_int;
        
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        init_set <= 1'b1;
        //    end
        //    else if (~init_set & rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //    else if (~rd_empty_int) begin
        //        init_set <= 1'b0;            
        //    end
        //    else if (rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //end
        // NIC 
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        rd_empty_fwft <= 1'b1;
        //    end
        //    else if (rd_en_int) begin
        //        rd_empty_fwft <= 1'b0;            
        //    end
        //    else if (re) begin
        //        rd_empty_fwft <= 1'b1;            
        //    end
        //end          
        
        //if (FAMILY == "TRION") begin
        if (OUTPUT_REG) begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid <= 1'b0;
                end
                else begin
                    rd_valid <= ~rd_empty;
                end
            end
            assign rd_vld = rd_valid;                
        end    
        else begin
            assign rd_vld = ~rd_empty;                
        end
        
        assign rd_empty = rd_empty_int;
    end
    else begin
        assign rd_en_int     = UNDERFLOW_PROTECT ? re & ~rd_empty_int : re;
        assign rd_empty      = rd_empty_int;
        
        if (OUTPUT_REG) begin
            reg rd_valid_r;
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid_r <= 'h0;
                    rd_valid   <= 'h0;
                end
                else begin
                    {rd_valid,rd_valid_r} <= {rd_valid_r,rd_en_int};
                end
            end
            assign rd_vld = rd_valid;            
        end
        else begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid  <= 'h0;
                end
                else begin
                    rd_valid <= rd_en_int;
                end
            end
            assign rd_vld = rd_valid;
        end
    end
    
    if (ALMOST_FLAG) begin
        assign wr_almost_full   = wr_datacount >= WR_DEPTH-1;
        assign rd_almost_empty  = rd_datacount <= 'd1;
    end
    else begin
        assign wr_almost_full   = 1'b0;
        assign rd_almost_empty  = 1'b0;        
    end
    
    if (PROGRAMMABLE_FULL == "STATIC_SINGLE") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_datacount >= PROG_FULL_ASSERT;
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end  
    end
    else if (PROGRAMMABLE_FULL == "STATIC_DUAL") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_prog_full_int ? wr_datacount >= PROG_FULL_NEGATE : wr_datacount >= PROG_FULL_ASSERT;   

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end              
    end
    else begin
        assign wr_prog_full  = 1'b0; 
    end
        
    if (PROGRAMMABLE_EMPTY == "STATIC_SINGLE") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty  = rd_datacount <= PROG_EMPTY_ASSERT;
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end
    end
    else if (PROGRAMMABLE_EMPTY == "STATIC_DUAL") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty = rd_prog_empty_int ? (rd_datacount <= PROG_EMPTY_NEGATE) : (rd_datacount <= PROG_EMPTY_ASSERT);
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end  
    end
    else begin
        assign rd_prog_empty  = 1'b0; 
    end
    
    if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_ack <= 1'b0;
            end
            else begin
                // NIC
                //wr_ack <= wr_en_int & ~wr_overflow;
                wr_ack <= OVERFLOW_PROTECT ? wr_en_int & ~wr_overflow : wr_en_int;
            end
        end
    end

    if (OVERFLOW_PROTECT) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else if (we && wr_full) begin
                wr_overflow <= 1'b1;
            end
            else begin
                wr_overflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else begin
                wr_overflow <= we && wr_full ? 1'b1 : wr_overflow;
            end
        end
    end

    if (UNDERFLOW_PROTECT) begin     
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else if (re && rd_empty) begin
                rd_underflow <= 1'b1;
            end
            else begin
                rd_underflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else begin
                rd_underflow <= re && rd_empty ? 1'b1 : rd_underflow;
            end
        end
    end
    
    localparam RATIO_WIDTH = (RADDR_WIDTH >= WADDR_WIDTH)? RADDR_WIDTH - WADDR_WIDTH : WADDR_WIDTH - RADDR_WIDTH;
    
    if (ASYM_WIDTH_RATIO < 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:RATIO_WIDTH]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:RATIO_WIDTH];
        assign wr_datacount_int = waddr_cntr - (raddr_int/RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int*RAM_MUX_RATIO)-raddr_cntr;
    end
    // NIC
    else if (ASYM_WIDTH_RATIO == 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:0]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - raddr_int;
        assign rd_datacount_int = waddr_int - raddr_cntr;
    end
    else begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:RATIO_WIDTH] == raddr_int[RADDR_WIDTH-1:0]);
        // NIC
        //assign rd_empty_int     = (waddr_int- raddr_cntr*RAM_MUX_RATIO) < RAM_MUX_RATIO;
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:RATIO_WIDTH] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - (raddr_int*RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int/RAM_MUX_RATIO)-raddr_cntr;
    end
endgenerate

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr   <= 'h0;
    end
    else if (wr_en_int) begin
        waddr_cntr   <= waddr_cntr + 1'b1;
    end
end

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr_r  <= 'h0;
    end
    else begin
        waddr_cntr_r  <= waddr_cntr;
    end
end

always @ (posedge rclk or posedge rd_rst) begin
    if (rd_rst) begin
        raddr_cntr   <= 'h0;
    end
    // NIC
    //else if (rd_en_int) begin
    else begin
        //raddr_cntr   <= raddr_cntr + 1'b1;
        //raddr_cntr   <= raddr_cntr + (re & ~rd_empty_int);
        raddr_cntr   <= ram_raddr;
    end
end
// NIC
assign ram_raddr = raddr_cntr + (UNDERFLOW_PROTECT ? re & ~rd_empty_int : re);


generate
    if (SYNC_CLK) begin : sync_clk
       if (MODE == "FWFT") begin
          assign waddr_int = waddr_cntr_r;
          assign raddr_int        = raddr_cntr;
       end
       else begin
          assign waddr_int        = waddr_cntr;
          assign raddr_int        = raddr_cntr;
       end
    end
    else begin : async_clk
        reg  [RADDR_WIDTH:0] raddr_cntr_gry_r;
        reg  [WADDR_WIDTH:0] waddr_cntr_gry_r;
        
        wire [RADDR_WIDTH:0] raddr_cntr_gry;
        wire [RADDR_WIDTH:0] raddr_cntr_gry_sync;
        wire [RADDR_WIDTH:0] raddr_cntr_sync_g2b;
        wire [WADDR_WIDTH:0] waddr_cntr_gry;
        wire [WADDR_WIDTH:0] waddr_cntr_gry_sync;
        wire [WADDR_WIDTH:0] waddr_cntr_sync_g2b;

        if (PIPELINE_REG) begin
            reg  [RADDR_WIDTH:0] raddr_cntr_sync_g2b_r;    
            reg  [WADDR_WIDTH:0] waddr_cntr_sync_g2b_r;        
        
            assign waddr_int        = waddr_cntr_sync_g2b_r;
            assign raddr_int        = raddr_cntr_sync_g2b_r;
            
            always @ (posedge wclk or posedge wr_rst) begin
                if (wr_rst) begin
                    raddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    raddr_cntr_sync_g2b_r <= raddr_cntr_sync_g2b;
                end
            end
            
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    waddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    waddr_cntr_sync_g2b_r <= waddr_cntr_sync_g2b;
                end
            end
        end
        else begin
            assign waddr_int        = waddr_cntr_sync_g2b;
            assign raddr_int        = raddr_cntr_sync_g2b;        
        end
      
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                raddr_cntr_gry_r <= 'h0;
            end
            else begin
                raddr_cntr_gry_r <= raddr_cntr_gry;
            end
        end
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_bin2gray    (.bin_i(raddr_cntr), .gray_o(raddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (RADDR_WIDTH+1), .ACTIVE_LOW(0)) xrd2wr_addr_sync   (.clk(wclk), .reset_n(wr_rst), .d_i(raddr_cntr_gry_r), .d_o(raddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_gray2bin    (.gray_i(raddr_cntr_gry_sync), .bin_o(raddr_cntr_sync_g2b));
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                waddr_cntr_gry_r <= 'h0;
            end
            else begin
                waddr_cntr_gry_r <= waddr_cntr_gry;
            end
        end        
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_bin2gray  (.bin_i(waddr_cntr), .gray_o(waddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (WADDR_WIDTH+1), .ACTIVE_LOW(0)) wr2rd_addr_sync (.clk(rclk), .reset_n(rd_rst), .d_i(waddr_cntr_gry_r), .d_o(waddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_gray2bin  (.gray_i(waddr_cntr_gry_sync), .bin_o(waddr_cntr_sync_g2b));        

    end
endgenerate
endmodule


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   bin2gray.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Binary to Gray Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_bin2gray)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] gray_o,
 // input
 input [WIDTH-1:0] bin_i
 );

//---------------------------------------------------------------------
// Function :   bit_xor
// Description: reduction xor
function bit_xor (
  input [31:0] nex_bit,
  input [31:0] curr_bit,
  input [WIDTH-1:0] xor_in);
  begin : fn_bit_xor
    bit_xor = xor_in[nex_bit] ^ xor_in[curr_bit];
  end
endfunction

// Convert Binary to Gray, bit by bit
generate 
begin
  genvar bit_idx;
  for(bit_idx=0; bit_idx<WIDTH-1; bit_idx=bit_idx+1) begin : gBinBits
    assign gray_o[bit_idx] = bit_xor(bit_idx+1, bit_idx, bin_i);
  end
  assign   gray_o[WIDTH-1] = bin_i[WIDTH-1];
end
endgenerate

endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////

`undef IP_UUID
`undef IP_NAME_CONCAT
`undef IP_MODULE_NAME
