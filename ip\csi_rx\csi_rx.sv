// =============================================================================
// Generated by efx_ipmgr
// Version: 2025.**********
// IP Version: 5.12
// =============================================================================

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2025 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//                                                                       
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//                                                                       
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.                                                
//
////////////////////////////////////////////////////////////////////////////////

`define IP_UUID _449714fdae91449eb701c8d91e94b26b
`define IP_NAME_CONCAT(a,b) a``b
`define IP_MODULE_NAME(name) `IP_NAME_CONCAT(name,`IP_UUID)
module csi_rx
(
    input reset_n,
    input clk,
    input reset_byte_HS_n,
    input clk_byte_HS,
    input reset_pixel_n,
    input clk_pixel,
    input Rx_LP_CLK_P,
    input Rx_LP_CLK_N,
    output Rx_HS_enable_C,
    output LVDS_termen_C,
    input [0:0] Rx_LP_D_P,
    input [0:0] Rx_LP_D_N,
    input [7:0] Rx_HS_D_0,
    input [7:0] Rx_HS_D_1,
    input [7:0] Rx_HS_D_2,
    input [7:0] Rx_HS_D_3,
    input [7:0] Rx_HS_D_4,
    input [7:0] Rx_HS_D_5,
    input [7:0] Rx_HS_D_6,
    input [7:0] Rx_HS_D_7,
    output [0:0] Rx_HS_enable_D,
    output [0:0] LVDS_termen_D,
    output [0:0] fifo_rd_enable,
    input [0:0] fifo_rd_empty,
    output [0:0] DLY_enable_D,
    output [0:0] DLY_inc_D,
    input [0:0] u_dly_enable_D,
    output vsync_vc1,
    output vsync_vc15,
    output vsync_vc12,
    output vsync_vc9,
    output vsync_vc7,
    output vsync_vc14,
    output vsync_vc13,
    output vsync_vc11,
    output vsync_vc10,
    output vsync_vc8,
    output vsync_vc6,
    output vsync_vc4,
    output vsync_vc0,
    output vsync_vc5,
    output irq,
    output pixel_data_valid,
    output [63:0] pixel_data,
    output [3:0] pixel_per_clk,
    output [5:0] datatype,
    output [15:0] shortpkt_data_field,
    output [15:0] word_count,
    output [1:0] vcx,
    output [1:0] vc,
    output hsync_vc3,
    output hsync_vc2,
    output hsync_vc8,
    output hsync_vc12,
    output hsync_vc7,
    output hsync_vc10,
    output hsync_vc1,
    output hsync_vc0,
    output hsync_vc13,
    output hsync_vc4,
    output hsync_vc11,
    output hsync_vc6,
    output hsync_vc9,
    output hsync_vc15,
    output hsync_vc14,
    output hsync_vc5,
    input axi_rready,
    output axi_rvalid,
    output [31:0] axi_rdata,
    output axi_arready,
    input axi_arvalid,
    input [5:0] axi_araddr,
    input axi_bready,
    output axi_bvalid,
    output axi_wready,
    input axi_wvalid,
    input [31:0] axi_wdata,
    output vsync_vc3,
    output vsync_vc2,
    output axi_awready,
    input [0:0] u_dly_inc_D,
    input axi_clk,
    input axi_reset_n,
    input [5:0] axi_awaddr,
    input axi_awvalid
);
`IP_MODULE_NAME(efx_csi2_rx)
#(
    .PACK_TYPE (15),
    .tLPX_NS (50),
    .tINIT_NS (1000),
    .tCLK_TERM_EN_NS (38),
    .tD_TERM_EN_NS (35),
    .tHS_SETTLE_NS (85),
    .tHS_PREPARE_ZERO_NS (145),
    .NUM_DATA_LANE (1),
    .ASYNC_STAGE (2),
    .HS_BYTECLK_MHZ (187),
    .CLOCK_FREQ_MHZ (50),
    .DPHY_CLOCK_MODE ("Discontinuous"),
    .PIXEL_FIFO_DEPTH (1024),
    .AREGISTER (8),
    .ENABLE_USER_DESKEWCAL (1'b0),
    .FRAME_MODE ("GENERIC"),
    .ENABLE_VCX (1'b0)
)
u_efx_csi2_rx
(
    .reset_n ( reset_n ),
    .clk ( clk ),
    .reset_byte_HS_n ( reset_byte_HS_n ),
    .clk_byte_HS ( clk_byte_HS ),
    .reset_pixel_n ( reset_pixel_n ),
    .clk_pixel ( clk_pixel ),
    .Rx_LP_CLK_P ( Rx_LP_CLK_P ),
    .Rx_LP_CLK_N ( Rx_LP_CLK_N ),
    .Rx_HS_enable_C ( Rx_HS_enable_C ),
    .LVDS_termen_C ( LVDS_termen_C ),
    .Rx_LP_D_P ( Rx_LP_D_P ),
    .Rx_LP_D_N ( Rx_LP_D_N ),
    .Rx_HS_D_0 ( Rx_HS_D_0 ),
    .Rx_HS_D_1 ( Rx_HS_D_1 ),
    .Rx_HS_D_2 ( Rx_HS_D_2 ),
    .Rx_HS_D_3 ( Rx_HS_D_3 ),
    .Rx_HS_D_4 ( Rx_HS_D_4 ),
    .Rx_HS_D_5 ( Rx_HS_D_5 ),
    .Rx_HS_D_6 ( Rx_HS_D_6 ),
    .Rx_HS_D_7 ( Rx_HS_D_7 ),
    .Rx_HS_enable_D ( Rx_HS_enable_D ),
    .LVDS_termen_D ( LVDS_termen_D ),
    .fifo_rd_enable ( fifo_rd_enable ),
    .fifo_rd_empty ( fifo_rd_empty ),
    .DLY_enable_D ( DLY_enable_D ),
    .DLY_inc_D ( DLY_inc_D ),
    .u_dly_enable_D ( u_dly_enable_D ),
    .vsync_vc1 ( vsync_vc1 ),
    .vsync_vc15 ( vsync_vc15 ),
    .vsync_vc12 ( vsync_vc12 ),
    .vsync_vc9 ( vsync_vc9 ),
    .vsync_vc7 ( vsync_vc7 ),
    .vsync_vc14 ( vsync_vc14 ),
    .vsync_vc13 ( vsync_vc13 ),
    .vsync_vc11 ( vsync_vc11 ),
    .vsync_vc10 ( vsync_vc10 ),
    .vsync_vc8 ( vsync_vc8 ),
    .vsync_vc6 ( vsync_vc6 ),
    .vsync_vc4 ( vsync_vc4 ),
    .vsync_vc0 ( vsync_vc0 ),
    .vsync_vc5 ( vsync_vc5 ),
    .irq ( irq ),
    .pixel_data_valid ( pixel_data_valid ),
    .pixel_data ( pixel_data ),
    .pixel_per_clk ( pixel_per_clk ),
    .datatype ( datatype ),
    .shortpkt_data_field ( shortpkt_data_field ),
    .word_count ( word_count ),
    .vcx ( vcx ),
    .vc ( vc ),
    .hsync_vc3 ( hsync_vc3 ),
    .hsync_vc2 ( hsync_vc2 ),
    .hsync_vc8 ( hsync_vc8 ),
    .hsync_vc12 ( hsync_vc12 ),
    .hsync_vc7 ( hsync_vc7 ),
    .hsync_vc10 ( hsync_vc10 ),
    .hsync_vc1 ( hsync_vc1 ),
    .hsync_vc0 ( hsync_vc0 ),
    .hsync_vc13 ( hsync_vc13 ),
    .hsync_vc4 ( hsync_vc4 ),
    .hsync_vc11 ( hsync_vc11 ),
    .hsync_vc6 ( hsync_vc6 ),
    .hsync_vc9 ( hsync_vc9 ),
    .hsync_vc15 ( hsync_vc15 ),
    .hsync_vc14 ( hsync_vc14 ),
    .hsync_vc5 ( hsync_vc5 ),
    .axi_rready ( axi_rready ),
    .axi_rvalid ( axi_rvalid ),
    .axi_rdata ( axi_rdata ),
    .axi_arready ( axi_arready ),
    .axi_arvalid ( axi_arvalid ),
    .axi_araddr ( axi_araddr ),
    .axi_bready ( axi_bready ),
    .axi_bvalid ( axi_bvalid ),
    .axi_wready ( axi_wready ),
    .axi_wvalid ( axi_wvalid ),
    .axi_wdata ( axi_wdata ),
    .vsync_vc3 ( vsync_vc3 ),
    .vsync_vc2 ( vsync_vc2 ),
    .axi_awready ( axi_awready ),
    .u_dly_inc_D ( u_dly_inc_D ),
    .axi_clk ( axi_clk ),
    .axi_reset_n ( axi_reset_n ),
    .axi_awaddr ( axi_awaddr ),
    .axi_awvalid ( axi_awvalid )
);
endmodule


// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_asyncreg) #(
    parameter ASYNC_STAGE = 2,
    parameter WIDTH = 4,
    parameter ACTIVE_LOW = 1, // 0 - Active high reset, 1 - Active low reset
    parameter RST_VALUE = 0 
) (
    input  wire             clk,
    input  wire             reset_n,
    input  wire [WIDTH-1:0] d_i,
    output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] async_reg[ASYNC_STAGE-1:0];

assign d_o = async_reg[ASYNC_STAGE-1];

genvar i;
generate
   if (ACTIVE_LOW == 1) begin: active_low
      if (RST_VALUE == 0) begin: low_reset_0
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: low_reset_1
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
   else begin: active_high
      if (RST_VALUE == 0) begin: high_reset_0
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: high_reset_1
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
endgenerate

endmodule


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
mdUzKnqqQS3V+vcAl7ucwGPY1/sQbBtRy5zVLpnaAN33ZZGE9gDSuKpC6FI+7An7
R7rstGX5PSAIgBk9AKm2sdXx4wZ6YzIRi1gLvj6yfNKPPS6U58D0vhJlUQFqDWyi
c3AGqpKIFjHpZysEeazN1je2m5HN3UGdP5R9orYcLcAi+1KGITt8GSbiOlKTgz4M
AGDn/5vVykEeiA4wDBHK1Tu3lbfcgC3VxR5o9PKzCNKTGRq+8pVkfiDYlLAkLR/e
IC/W5OhoMfHYVvfhpMnj0mh5QT+jDF9fDMlCe8a4pzDecOzZtLNenypT2S05bM6P
Ab5dYvVq7stZ6thF94d6mg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 37024 )
`pragma protect data_block
ABhduqu30M8mIPkEB1htVx+VwsqGeYa+hHLSk6fGIBNbGd5Lx7R6f0l8MbtBxkib
eUKDGPaWVkGuhG8mqzAj/sGQr6JjaYLDCjxjxUoiYIiLgkZYVaTlcMab/5/GTRg8
99fNUwtVnNRys/aEd0VQrWVczoHW3b9Rs7MK9R4OncZCkwnYZCLrgBfGU1TXFrq4
y0+0LWpeazP9ac0WvqGqIz7nCygDuO3topQbjNZ393AFlhlgzJ3BN1Egd6N81f0e
6rb2BkvbTjzyWwgBPFAzSLH9cNWhn4StWfc2L3WGOFeQOADL48ONv7/bAm50OapC
027mIK6YNLoduwlzikaNFjgD3jIATH3ZQTG/NbFsJ6tT38no3LAooHS4AFWl8C+h
x7Uzu1g83NGU93JzNYNo8wLUbsaDkRL/wHpYFuKA6pClox7l3PLtH5B8AnCBb+pQ
XT5wq6B6HueXq3DExipZ+r+H1NxoeKT+edKkzgdD9umj1/g382373sOjzBAXUMvl
UKGr7T8csSJKflNeQAWEj3N3zy1Tqhwces0Z729Qq3nj3+dDVY14milXCu2xz90y
49z0mqvccCvx9MejntZV8oymGyyAjEehh3dCPvrNvBUYCIqvLBFBQ4gkplSV5WrJ
Lb0QKU3sdjm2V0b4/VYvRefnuDZYmXG9yT/UnJTXHktq0l+N47ZdhbXJx76gYqfk
bF87Tsgr2N+S/UXW/uq2PNOKaDS0DxpqWtamBpVDtZIecpyUnlezigw5sBQAXLfC
nMtSSMWO47Y5h1VY3HtrNpOr9AjicHSitptykyVWfQ8jv8xQTXa6Yh8ZKkQuuL/T
5R9wpfbi0B4XQJuETNAtqB28cJ7sBpfCWOo7v9qFKCMEILWM6T4PlLHLrcMnLTtr
uVimQgZXTaTNr/BTUjpo9fdNm+j2PVzQ0ocOFGcAUoGZKoWXj4ZAot/lxcIZsJBE
GCvvkgLhhPsqeugdUXBRdp+o6FMFOC/ZIMPWDNIey5R81ZhGJXG2rNTUzyhuN4ao
dG0r5/Yfzbzc5Uhu7a2ANfSIZawLv2X5KpXPbtBS1aqOnt6BJOOn7El6X7LqsSt3
ZbC7cE5H/TtCv3DHioQ5oZE46GNMcsbQN+IjGz+GYINoMKSCF0iOBncUB1sMo+jb
Ki1ZzCIRE1yEqPF914q2O0F2RyTYw86GB7eGTFe8lr4gBTMbHVWOfR8z6Z8d9oiA
iuuMxJ0MsPoDmRe66+Ly1O4+YDK6fA19mXjfFYTpxa/nXASu6KptISUFqOEfIb8D
5tbl6lmUL8RdgUJ9xhld7dCAJ9NebsFoHySUczjjNlLMrkpViu9RVNQxQclS/Exw
BXcrlCwUEBmfsCx18FO8eOP67sla2EwrMmbxzegNZpKGIKdAC8oAWF03qsaQtQxa
V/Z3hp26Fl50h1ePvnUkeuzPgPo1/QSvYj8ObNeic5A28aQWM0kDqEoEMq7rRBL4
pc7X44aqzGClCIn/r2t7IQilbjF7RBeoDmV174DPz/gbdW9FQ65AFHA75/XI+JDq
R3biXCgoHhBbPrNYnj5qSY85VyI5Hqu5PifbV+qByAexTrXTa8V37Tu22jqL8b74
hZIkBP1FPDxfcUVyXPej9CWL3WDlrrOp78fg6QWzF7mRP2cOTMoVeZKBzpV0OMEQ
6JFL+PGEtWOjyyuoNZNXdUlaBqdqKJQG/+/0JS5rvW/e2q0nqDEhwVrKGyA8GfUQ
9wmX0N3Lqtx7PfsTR/gNmrwie9ADN7ZgY1Bfi7n4rS/fSuQ4rS/hoMypaONO2pOs
DvpCYfehJerfAOYVX3XJhUGfcjeUU3nTL/wxxQWO9CdIpQmuZzKhZObUHV5+Ar9p
H9UDnROlnmBrLZoTJBFXUq7dgWN+KyZIIYX4DonGShjvXOMN1/JeCty57UHservu
pRp8MZwXaP5kuA4ctRrSKVhW5EH6h2EekqcXTbHoWesaMq9rVbxKV52VDCd+6KS6
Jvx5VONd476P01e8/orNEPL9Ol4sedvA1AkpUdLfmrLfH6fo3vA3fD11otzMES26
H51+I2hQhcqtqrDZ8L0qLwNkQBQYx8TEIJu6XT7jmtKb5oB3xSaB57WJk4nqE/NQ
AkxO2ZSiMsiqcPc1Ktz/HpvOXUTBupvn9u/CvcjxYZ/Xx2OTg5aWRdx7SlmN3t8j
M2UmaVaH0kQDM3bN7JjfXSV5F3ixN/NZuO7z3VrB8CnZhWhQPLy4ClVYvy3PuZpJ
p1jGQT31wxnFt7C7F7cXwL1NO4L7xUKVFkl53etw/7fyXK2jBaVz/sVhltYscj6Z
jyjIrLSjpLrIhUHjCiVV/2tEnRbJiBahlaq+qxNuM/Lk4iRdL5H7EUV1l/jggIi9
JdwyuEICT9u0UA/3Zv+QX5w37fO0UEGUnXgviRiU71Mmg9FPR9RgN4Q4RggEgaFv
8MUULQvbWYeKUupeog9PqL+7cHin9iTxg+mfhgF7oG64nwHaBG5qaso7G1AzNB1r
hQO+/PkWgE/jvii2DDjQNIY/0T4p7bRqfibmzTxcy84sg7ROGKHm1//JWnuuX80F
p8puQP99yRbyCfjavHw0On8NECqHmarOrFiPsDikepiCSMib+s1xCEqJjqoHwm8d
pfAqN3OJfPTIcqxHT+zj1SD4D+oJvI+jpzIJadD/bFu/4CudRmOYjeyB5Y35qK6z
NDgDLDDpyx4lhebk15ZTRpyN/X6W6TL5u6zfpqH0TIa6zwML2bxpYaZkx2ak7ctO
/uRw4a7TYTGpdtPRBvtieg2fcIf/IpNmbuJztdfMMK9pfw/0PgvvaGCTynfXMp0U
vQOoy/tn3rK0/0yUwfdqSxnwqv6W2m1uGUxgKR5fQXJihZyPY0WcYtp1LlnJGReU
RWA4vM/PnDlrR3Ff4zCq7u2krtlaH99a/5oByyylqmf/1dNu66qjoRpTm30K4Y75
l8/nyLZmXSUClsN1GCMwxKfusyZuQ5LnHkXD115+ooz4CDh/eP5lvulZGbZQsVDR
pBnieQH0+MZzUnQIn6HmJwYfJFEpcYdVqBgAYhRhhRVWkrnq2pE/eRvSXoZtaUgi
CuA7pbJDfoSHzqgKWsVdt4MF0xmac0f512XHoBv/QNKrLu2/F6e9d0C/GhX11ORp
J5b+Pm2ZmiVyGe1+DN1t/CaCfruE6wEo18n6PP24NwKFU1yrZ34+kmRkOz2k3GlA
B+cE6A3VP3tUFBAv+VxeR5VIVWAoub2F+qJbCNyfhI4y6YXJSpIpwz9Uyq/MW8bI
JsbxWwXAHJuP+0lhOdNRcevEFAKbLiZSHIOM4imznXSDCzGU3qXoerwHiPyz3QdC
4wfcWkeiejRAkia/i/OIQVPG1WuyV6AyVmwojZdeqd9+av0BeZrq0Go1wjM9qQ1+
OjObURdh3rx61E6X9uJwbQgmqYAvq48UzeBjUcJnZfH+9pD5+JdGnUN1JCvgIPpV
wvfhZUbluN65Ki07W+iYghrvykylKEivk2FkG6OBK3YfwwWQGB73mChYLVe0h4+L
ZTG0pJPwqgjpTos0ncZwBtfkWoEQ9GU7rIIy9cEFTaHoHgPd3hZfJYjFHAbL2jIK
g0zO8eGUOK/2S/z40OeL9pr7frbpNeniujq+/1bRqL5XLD+8J+ZY61bAbCmj8hAR
pD2WydYdG6QQNn6eqt7u5mVzcyOPsy865tVRaRVHz3VGDZQtjjnX4jmZ+F4kurBd
JBJxRoZTpfTIOXFWuBQlHR4/I8cSLVW+7AZKJb32+VdoF7tpmoTawnyeH0oazv8/
RBtkmsWyE6BR3LYeF5RXH5yTl4i3OGkEAxkSZUMxVWE/kUxLtAxJFalCObyeuBR9
a5u1rgf1pyNpfZd/5u63kacSD2aMQXMleIJKgu1GhjYvMB2+RoVbUIDAGBdKLoJt
if62f1tSFxOAzFNbQ8/2caXD5NrJQl8qIWPUexXegv0tWzR6x9SzppJedRBMJPaW
3uVb0by1/Y0dIeCMpQhcrhoEsA67yUu1q5ouUgOmMqZq8hoFTG6rG0qGj3CwAvL3
iIf7Omv99D2QdqlPfkSMJYAMUPB8Rez94OkBk+ce4nhJwIg0/UVlJENApvyuZQLg
tPX0Frwp/OnNlcZURWzb0ae1Am4/QgZkDmwkvrVkz9HxG9M5GsFWmXbz9sYwIbcS
d0alKssdPFOdjvS1HHULAIytTkkEjhBzGLlqCDpRJOObD7ogQCLJfH7zyF9SlPmT
lnvh6j2mHwNiAoSiDCNSJlduZem0tlwqPfAtyb7ibHpn/5qZ+8M+XpklY+Vh1pwC
+0jQYcZSLohhRiBjLgYc1q/829oBAOR64KuVxOnY3G+q8YohlAeXkkMDMxc2zpw8
DR0KXOmvz2zF708rPjuHnpjPAhbBJnewzi85LRJZvyQKbuh8i2C2GwSWLWVk9Vl2
L3pSlbp/jLJ6sE/+cd6yZ2Ld4SNexkENIC6pmNIyCKJy7Fb9i7Rzsn7mV86eHQGa
Be/+jnEb8CEse5l1Sl2KnhG3AIF8jwmJZQWEjzkTHNMRF9jnLEsblo2DT2R2lTdd
JX7ieG44bUW7sVo7nwbXkJ2pAfYy7BA89RS4AuucSoxevKZMi4fOM29cBdxJDzBE
l0bSETtYYT5YjUD/VSIbUnXueSnvxCaD93q+WC+aLDl/e5TqCrgczO6EtI7a0C0N
8Nl1QCYY964hVdD/g0V2Qd6c36lxk7sekAdn5b4Wo3fsjUCmN/2+xNWpKbEDev3m
VGKOxKrv/W5LUUjQr7UBXnCKGwgzFdZj9XKHrxOmqDu5AGL9WfLKbpTuee92tp0P
u8gxhYXZKbBXKuvD9nJJ7LKcVcCgF1ZR7wHUSh7qfXkO99/YMbk7kVMNB8aaRmqB
n81qsoBSV6i3g0IERivbayJFIqP2Gb/rAp+PunY2W4sRXjObpboNtHqcfLs1D0os
MOch/dFA5cmBf5FdH1ZVh9dyYXgXoTZ8jTjtrL58hkme2maIIPOwzPnmhE4K9iPV
1QTSW06bj1zfwqVj+o8WZ+rNeGz0CGviN1oEfK7TYnn9c4zeZqXkAL6BZ35H0fgM
A6Pz68rcC1mBhLxEsEcupRc2OZRLPH0lcL1UmhYFYPrIMUu61E9D0E1Q9otxV1EP
tOjfii2A/B2eEXnr7QU6ksDvjEhH0bmNotcjQhCJwUkbZ52al8okBAAJ+1oZncC/
zRv8QfrGqW9PQM9KQlWgzH+EhuaPEHCnQehtngXZzy5w6euudw1MBqRfJAmEAppa
4MRQSCubsn8NYZlml8zhHfzkMhhxRsp5ZSEbTgCXT9Ld3B8kSw52GeXr26nPo/1f
otz0FKTo5xASO7nfORSzBZ34RGY161L7NJwOlQupnTJr7UXsj2UbA+fNaZg/BxqM
0+5uxS5eVn2nFlIg1YvbKUct6lDQ38W06ZtkkYLTHrlsFBqCH01G7ZpQOqMJHV/C
2tdRa2/s2EKaJIgkn1iEByCTdr95lTuMNEqsbGnufH8lZsfhnTNrFKFAfIrV8OHe
8ww4D757qP5waKxbnDYTIVHBs4A6HSakD+LBqowOg5Q60yHBccx+5L6iakEqZo+C
br9FFVZEm2LFzQpK9skR0ZhhzgEQyr3G/8oynfGVFcs0d+S6hMiRUjfjj+JoCduq
MrlyUQUac3lk8CwoIv6gF70TFyhLB0aVihOuGbvGPJV/w/rw0fecM6SJ4b7w2tJ5
nia+DK0eQtllAIjgxvBxr1tfLF6KVtFJ+F/03LvRtTrv79XsoO4uNyE26G9VEFyq
te/ITjc6rzA5xJktKl+W25+87m444jfbmIrgeBSeNY9eYtJvTCwYCLwG2sU/Jd8M
joP5VfYHfqLtQ7ZLyMF/3PTUlK5U4ikxp8pd8FCIgICfvnhpD0vpZ/yP8lGh5IVh
jv3e2bPkd0qESB/okZQtFrTq3JEtrJjHNURGMHTaxw5LukAdtGH9LB0EAjY0noyp
BMb/7Q3MznBs/5/INTOs636v5Wnei+i6/+fluIZ3bM9MK0YPkwG3AxkLn9cmSVIT
fyXTDFuHCkwrYNg5c7yx6k85WcFltkTwz3KO+X4fLzNxH+0XDrtNUUe7gXj/ta5k
6eXcp2BHf+6w1xe28guToS+0t4hWLCif9Yot0j6XvNmyo9NGotZqJlv5zy14atiI
m9eCfu1U7vBsUznt4FFDSKxfcN/Y6Wq3XbnR0AHBRcoJ94CFiJ1pDwDUwDXKA4cL
G8SCiKotrHPJYYJa3fK7gUZZ0uXJaY4ZPMrOAcp0LRJ3+Nxl5kYgCXy5Y6kJejCj
kiAStDwfFGdM0xnATRMf5y1RQdNF5n+PzDGOtRC96iLQ8T1zstB8fucdrrx7H8UG
HnA9Zhp8idEtj1ybKqT8/drKpiXalkvOy4BEdB8OR8mRAhVxDmiPj90fYV2768WA
HyJmVtYANbJlEvYd29HsfuvCllZyaaMWykfhdikyKAVjPk6XwEU6ad2F+SAi2m1p
+PWvITbE99HuolKU3jfNhHOIlLYl8g5j6TCeOkynXIUGx9lnn98qCpVeNwWtpfF3
OLLgCKFmI1Rp57EEY8Cj7vb9OS4vZIs9vkosomZoF1QtQLpD7GyloieKi7ESBNpK
xU1ucSfDD9lnDTBsLXqk0Rnzi4Jv862gvzxFOK441LCv+v4iOpg1KXJ3CMMmLR6X
jFdNjzLqCoQrCA7ihPLIyG4NeSD/V5NPn9xaMGDOGqZF5nP4xty7tzp4AAJHS/fW
+GCFSs633znScDncv87sLgsNOf0+1aydUjcVhV0rGvUiFCNIsdaxH+b6kkOp+WDD
k9DHFuk9WuUBi644PuzbevUyQhkRSAsFnvUoarhrPzIcP3jYuTsjDhVDdpWX0BMV
D+KZdb9MXMVS7Uuk9p/uOhX/RhTsfKDOnDsSvRQZtmjchaoeABoY6U9RMxCSzIgs
5mzirLaOhe0w5ofU3z8OXxcN4+yfGrG2vw6F3dtTLmuCvcQbeP9pXH8nkaSGXQCP
aiq7buG/FBOvZLQ/Bw0ibsCaQPHrmeUQM9Mr6+aJDd9/aphMODigT8ovFb1kVGmQ
MQhunRWIX68wPRe0owBEcxV7BT/ha011w5Hji4uBiTcK0jN88fXPhAZvvezOE+mM
3brxEVcQKsd9o0ZjItntvn09tWd8HYvN/kv0PiWOrwgHbl+9Ht//JZjAbVfExos7
+yhVtTxuHqVaighV3p3Tlry+IBG/uhiAwI6qlVk5vhjCGqcbLLRC3tnijiQpVEEk
bzHMWW3bz2CEg8qn9pMV7grr82tAVlJEYiSqYCklhY6qe/wWrUr7e4uplipXvsnK
dxfkauHIWiZi+lIErG+Kq2ZIXZBQ434w2WqawB0xCnUTjPaklFEvhVduwfhkw7Sj
2u8E+iLBGzRvb6mPyT4Q8oxeFSeLf9ChIAfQkYfuaRS64m28fWK7Kzw5ocqni0kn
Gt6itw6s7V9zWVNxyNyjUPGaFqi7vd9m/CrLrv2ke2MokD0QkB5v33HpSuCX0D1d
A3hrmd0e0McNr0eve8+XTEwxT54JRks4rx2Gfo1X13Q2zrqJ+kwR/jszAJsZanh/
Ftc0+poOyK6on5Oz99lmAYSAj+4L9TB2d/9kKNTCtrwmHk2SaQHhMg3/Di9Rq6XG
LpAPmuVFFw9fVHoZ8Sl8lgIBvfllzY//m6DwDwIsa0/0tpCEpLkesHt9qR6sNdxD
MGQaaGhe0oX8rsvuNi2AKfhnIQQUywhfTORRcxHyF51we8vZDcKuK/YoDU22x1b6
b+OLi/AXzH29GI/t3gwQ6cZMjOor6zMjA30X6npi5kuMgxnqZ80uxyeAptqfEPwS
IqAF5nqThs7RiY8aTHNaqVt/23ABEYJnIOEBSONMV1okUjVJqizGCIIvJTPa4Lit
YdlVZfzNldUxzlly6yLs1oiSvILKmYnYDCHjjSuFbOgdBsar0W68mOLR1FRjw6/y
uVdrV7pMhqRM6dMHSK6Y0fTwtnA+8yYpN0tuA1ujZKbwBFXy+QTYqs+9ggD8iSda
Deajb6jvtjAZxDxhoKg719aH0xk9f1cpasr3V1eaTYhoicYC+o7D0n4nnXmSsRdp
ddeyM4CotdQTg0db2mzRunfYHz5nUWt/JL7rj68fVF1xDINNhd0EgfXvuc4ySsrB
RDzMXgQuI9TdugDr+Ef9+WoykIP0jk+61QRam0rZnOFY6hJjvja4v28A+U+bnZmz
0gDK+aLqs7bPbopz4D6ny8oIp2YVB71QBdwc0wWX4XREnAmz3ciojtPYCaBq/hF8
QM/5MMIdRIVbNTVzDpKPyxAqY/+Dkd9a06lRpLwQfwAVggGINSA2cd5GBvQAnYBa
9edEH7blpmvXTU81uRe74O0zqZc65D8Zg+C8d6nZMoFUeX/PcZzEK+8Bs2ETZ0KY
ENQYOSclh50BwwJO4SDk8kvqRp9RpzxtdKKMYdY2tN7af2KNg5wlAOeGQ/YawLS2
+jdhEvJr19AV6NHUIKRGEpTNUpbVtVSYmYRcANJRAhi1gYwgQYPlyZ80Nx1uMBn6
enhdTNHBekugqpNELU+Jo0A8S5JEkILsH4Xye8JyOdHNz7GyXbOz8BeOLSnD4pkd
oq/D9k7TcWoqXZMc6Yv/0u8KiMCo+LaF6XRGObFz/3P11hJYHKCZPBn7dfLE4xt0
x4HxDi7KiMSUk2nsG4t82YmGKk710uW4iO09oX2wHE0XWokl8LpF9W2H6OMBz59E
fktY953LzdzRJvDeM5/R0gcINPnoZjJBP3egKg6tuOm9a2x6lJXcKfhhYwChlvio
cfiqMNVWclSap1MXj8fybCtBsk8/TVEJqxm7PRnEBJ6MLo0dq4LJEFk3RPKM4Jid
XuhAXJPUKLKuLVfjluHIMnbEzSgCPuNeA/0JPVJPVShAMeYm1tv7GuV+ZEMZ6vy0
dnrJryHHXNKxSQLFT73Trumja+aTtXFfPcKJc42KtEO9CFLNsCA3vit4SQt3P/Ze
dQGcPgNwejm/2V7xqKcGSEBvSME70K55h2FgXUcZZ6PuRZVEQtiYYmxm6P7CyIL6
w4hdyf8xctwdHMWkntsJFA8X/39XbqA4VA8c+gHHpTxGLnFS6Jzz8mPI0nP9tnMr
XDi9oGmC+V1X75U6gJGgNpeuGwCZ4LIRzWC9Imay4C7TO8Ujyl/7b6NOP4i96wN8
IdvPM8K+ahKFpBVXB90fhzeP09G4jK94oOxfu3StJxd1RbDd4N3V7viPINTwQdot
JejCzl7fAI4b3hZ/TxgfMrYQixK9SCD+cCWERhebplCo8Tzq0zldsI81iIOF3JIA
JMx8UImdjAnQ3e4XdsewdrT8RVItl9UXLQBWdESDh2Uyb3365Eh55LUQaaDN5dBq
9hf5KxnTRCZXQwFaCQ1DDX+4wEXbsb/il1zhbwuemr8QW37DFqEQZ9CEOImoAiLJ
7x945PmvAvppPr68ekoq/cHhtiqfMvHoZiujU97y2fh5+RrYNy0dHgyqv4/AdWzI
mAOqiIhD6vuNH9b7Ma2TUS6Cs/+YEZtN1MAzTT5KFQnNEfqgEvtyGkmaiv/ieQP3
/Z9E9Eswn+4UN0x5Q99SGKKtwmvhlqYREwWuzGwiNscsEt0XbgpmUscFpZVnSReA
S8BeMcQNHsd1QcHPJpye77N8S7yop608Ok9nRx2X4LQtDhhlgjgL3+mpFTpWxPcd
SacwmxV/W0sFtBPmQKu1YMHyuTvRy5b91pr4QQ/iBOmOkO8hp7jDlKogSx/0fI4w
9bDWH32AGJCdZQ4hQ7YsPuSR3A18lqWLYYv7/XEe03RbzRm9+es6rTP0Z28aV/sL
Ys5oBCIsfQTWOlwDD1M6p91Yu8JHDHVlhkQdNSreO88KSKvBC3E4EFo5/DAdybVw
a0rlhn1ARZdlAgCnxi6ZTOE2qG5D38ozO94wsndEsknpofDYSSuBDNagRO/V3W8m
2sMQYgr/DPTMON/zPsJBNfrZ7a1ze8OTEkaLKpI+aLYTB36mcGH5gEMaspgCzPKb
gKVBCF5UVZtYf+MEdIkbWYV/sgUDyis9/i7V13Qi5MMSk5K2ytfCc2M9Z6c/Eldt
al5MoSlZlts9RIOEB158gAnk/NPYWNrKKQiDxyuHdpAfpmqxGfJuRVu/oIWnlwk7
MStUAAxksRXT7pRm2LCEvrTirPQpoc9kqoSrTXB1etQemr+wX4yK7ZreN4qJZJQO
Rv0wb8aY6Oghcr/snZNhg/I5afVW2XgCtYD3oBqGUSsK61bMX87Dz68eDwwXHw79
sXG6lbYq78fm61uQIOV2wuS+b5lHqDsZWwsEaAGm9SsSPIyDV2HJuVew6/WN0uWz
dd93OoUj8pZdoFXGal9u90nvf1LME+dxzBxBzuMLFDrlNKEjXtk+XF398Kf0hJd1
/SsCwG7eTaMxgHYysug3w3yrY8LDTNigZMe2IwKuUHEdP7Tt0hVmqH7ivkSby+1v
tY6SKcAGgsjfsrTxFMWiO0pFJTpSRbINKaG6oy+N11BRMnagxTUOjM2JcxT56C78
028AZXkOMllynOTa2gbBWJRMSgMGBOF4TORHRkYmQvLGQOq2houbpjTKWFBqP9DS
/lNL3rjHFhW1+HinQVTCDe5f/qvVWbYyv9HeHYf8Hd05DtmZGLQrFaFxV5SlR7fK
5GFt65iP6W+1bg7Kfl8vAF7yuqYwwXWGpAQXla4GVO/3Kmw4BT0H1CAIanz9/EbP
WYWYqvdIehT8SoAW0ZKuDiyYesP6zkKgkUb+4vvz0csF7aWi334cO3CzUcBr/BzH
p0HatMOH05uFZ5uxNTUgKUdw5XG/dAsXcChtFVSIw/pT93KIZsYnzlJ38Tnd36s9
K7PsWIdPf9lUs4R44Y/MmiEKTYP8Lu2rq3AhfIkj60r0o33DJ2+AwJoI8jhLeoU1
FiVPUx/gOPGjYIjbfj5EFe1gp5dC+/yEM8/EgIOvJ10ncJR32T7ypwjOTg3lhcPu
S8YL/ZmAJgo8FXwHesuLwcw6wgVT+Uh/mtDUuitiFK8O5TN/GNC6qcg2LFQwq6xi
Pss0tsu+Q4PJ290M5U3xamrbhZv352gPZd48nVNVhPdfXupCS0PS4QG6oHeVNQeh
VaN4paVIvr4Kcp5dKOU2xgU08CreSetu7cXwfRB1x5gWghRqAJd5JF8K7t4v4duF
Y0KZE8ooj1YbGJUPBJqjKxmqR10vR8aDPv3K7oHTHkkMhFjIiClOyzs9vcrxnO5g
raERxkn9PjPE1iVLEMf0PkhNHYwcg26jde257SmNvyewUIPCDfs1OXBI/APhzM2e
pdYKvNS8508oELOtC5XgL8RJKMiAwXjYKZeenKEsvCx6Fh6oV48K11ADn1qveV3Q
pc+wUk7fiicTEo14qvJ46o16a3VG5mZtMknWFWHTov00lkMh0kbOPxEHxhTRcA5j
GRKQbuPqb2x0jUwcUYvpzMZvJRQdtQ2M+I7Lt5K5ovS6iVoab8tx6EzZenLkYSgq
RUF/3i+philtaQWZ2tYx7OgPPmCrwuNCfCga6ICYLEro1x7gKkgBtoSSr4pwGfVW
sAf6tuDqLsdlg7WrJlcZNUS70gH8QWYUreIHF99qncPhnltTAdIyzfKzr4NAyYSz
e+PXtTgfEMOu+FJ2fVDOp60iCYehJDAgnvrDJbQhLduNJMueav5VzYTM4gzwAgkB
VsO5ib8ZbttWOepXUqvp8yoM/afy+ahJ6xMxSia8c+i9liF3u6WuhNLudiluyNHP
AvR50xRqPznXBE+Ce64janZmiMGmXEkg8GQrGhSGfCE/oWKkjUryz1YygSYfBXwH
8Zd+JvvteY0aPPgoVOWRxNqRuMKDxddae8Mt/MwRZOUyNQzAUv3BZ5Uvq7cxcoJ6
pu8448rA9CzLmdMMOA8aD9F2RzS+RsNNLdrj+/4x6VV8UQyw+cHfTve0KNghw9lq
DHE+z4vc9AYpmDz2mCxgTkoe6hJrr5SgCpZoY08GPmpf00b4ipyneK/ZV8Lrk5QN
LW1NG5LZVMU5q6wz1cvpnX8HzJjJAq6qyHcCoZZ0wlFkKlKVjENzMdwb+pZAjGSS
yzx6GI0SvJYxnMoGhiTIxJS8efahA6gu3Ak2kiAWk+rywtAv75GPMq/Aw8P/KiDJ
GQaHKExhcNR6+KqBs7i83f6i4ErdI/igUK2PnEvrnrq5rCMyMfyqAEv+3CMCAdth
tHRn1cOZK5Ho9Q7eiDLK9nKE/lsRfhUAwnYjuVIQhhu8dUuLJXu3yw6PYMoPXyIy
A3689YMEOQQtj6qkGLv2x1JI5ut7GxPsHUnN/KX84vu2WOn0uMq2JbPMER1Pu1if
5NcdpZf+tgL5SGsjY8ULS4ClMd9yLBWGdKK0Aodm6bGsciir4Szu32qx6DGGhBp0
KMramIlslw5jRdnwGQ0hG+MDim/Hd/bxH9EPYelOJ/Ho0oYVUK3cWyg/rto4yPWL
oQt2jdCrErT9nQst/xCc1enqnTK4dJV1rq91Zqd7t8Oyu+nmKm9LI7qzBdTrYtHB
8eXFMhuPWerx/QDURGhuvCkZpxPm7LR4eazHazlfqkl3NWaR5K+Z20dFkzeYyhnV
xlxVhLdpj3wJIj/D8I2lCLJcPXN0LsCkfouP6731lBIGZIN7c42sDuuHZtRFujyg
Hv6TcRNxKurxfznR8TAh6+qGTzUJJn1yM41lIlMTO1kFIE3Vxk164xvxxO28TKBp
e4i3Dqb/rf5/m2fKajwm5giwnUjIVYgHoATR9cimlyAgDTRtrL1LSG0/OWh3MOYa
WFglDNGJ4leIfgTEAI/SK8Hup6FSairMqCWwImeaVpOWYTiH35QAKORAdNLiPDLl
5Tq1VvDjH5sVHZEtMtbcJirQfNHaLOaXTZChoHPWSl+Twux6OL4DzrKSsLOeGPEM
o8PBkd4QhRKOP5zTcOKY8q58iIXHL78g2nFhA2weWtcAEaKLFGzWwJxkv4l21XAq
SPQUYEGPIlv1gPBB8bM4pUdeJl9qjm9bFp8rZyxmuSjkeyBu111eeY/E+hj2YQDa
muJ6hdDzxiTdoe3MMb/yJfqmgd4e9FF72tC+ZYGUnHcdjl1qn4PB9uxIf34d5S7S
XHP5SGcHxUcm4oZWyw+tDbsV6lJ32vu+qmj3mErCiOjbznEwkNdVFtpfli/z/kGo
BOYLBo/Ehk00sykwdsDzOnlti34cbQKqvrdEcD/CjT3nFpjbWz2z2z5JeBkGG3+/
ca0/UkNquTuQPUiAvrvu0C9YLxKwlTcsjJYYc1a9bZn2jX6/p2Wl1DQ3c/HI9CQs
1B1FE3xV2nwB5Q5qzpq5pJGHoKtSz9rKnP7/0+s3iSoodjgud5CgRGZmRh/KqnXW
U9+MTN4ehJpQasYkbp8vXtX+9mT2GDCRwVlN+Yi6VgGzEMkZAh0qWTmcNfhv90E+
p0xsnoPCAOC404RFB0U6w+SNDN8pdYk5UOf6Ik+Joh2f1nTyE9oflY/dXFmd49eD
7PntcPaKc1QGtZ0tX6mKEH+CIcZdNjQk2TmIyH6Pdfw640g9Tap3ftX+9S+Wl+jA
V8QQNZwfaazyfbYyMGLGNTzxmYFjBlml1YShEVIpLv+VxU/rHNb4JYaRQTEdwb+8
Jf2nVu+GvwUl1fepUVNaJ90fcYuGJvuC1mQ3pdNsU4CuFmxK+Nz8zUnLTsVJA/W2
F1cO7gt+uktkU1zzAZvvum9Cqga0LpeRpS6R2Co8pOLtfSfalbeFJvYzE9oTSLvw
G1rzM44cnKYFY2Ilv3fLICoC0JasfWuLPh5YtQhB0Zsxpdb+pIWTe8sDGgoX/i5m
G1Z84J5LfHqlacVy5HV9foz4P+oao9CJ1gcIRKW4sA+H3q/YMIzGWUj11qioSOHj
6cnsV5naFQBXMwhP4UusbxaXg2gO4Oh6tKPdekZbfjSqXC1FMHz4wC09SjnL+XOB
Q0zgvvLm9r71V7gg8VPbWYF/JXn0dJ8x7aR9bPEgMFgc8m19yg6f0loWxHp1Umzi
0AgNRigbNCAZuP6PYYLVeetLx+9lKskadBmmeyc1O2WQNZjDTZu5PRcz+y0SYbQ/
kXI78ODsoIIZmJc9ffhkbOGAvdd7CbK2Uo5Cz19S2TyieKrj4iH76pwWOPiTjrED
Rm0FPxmMkJ8XnxFQqyVvz3DiyP1wF3PzhJbY0vGB7jDv4z9OpvE4GU7hJ21HJ0PC
aAOAjT8io33Syk/Vwe2oEhNoY7L7L2H3Jym/33OUdE4N+ApOnifJapSpdr7/sLui
ljT95dc7D3rcb+L+LxXXUfQ3kwV9X8GWSr5qFJrZT/EuBs/PYLYDsAErB9G1W1p5
ENmniHUB+kMyTjl99q0nOfxV7yPzg4i7s99r1zFGbkctoIgzMFqxqyWNJS/HSUCp
nSmmpcCpB9ro7LgFq/meAE9DV/+cRdRKL2+Vy/QKxk/R/skmZCAVSiNSYv1FtCOG
gbpKFJAMBNuMmqmRHkiXjEdmQkCRvDwk/dOEn/bQwJt/IufnDWzTypu95spLWbWd
5l0mZg668dRVzcLJR1VxpvwornnLsccaFqKNdIbUUI4dmw7Q3mZ9Nim+2jwXynQR
pYSkWs6eB7n7e5ozZQim6VGwth1fB+QJao04i6M2nadqIa+H3agr3/36QNudRpDL
bGRQpAKJKLvrGDvvrbS9tNQsduonJJl8vBLkJhgzixU9Vc6xXCoYCSIE60eQ1ivq
L6PnFCb0H+ST5m4ZoQRTYtd0h62MvAwAaxmPDluHcj8llhkaW7yvsERIy1HodEaw
+2ZiF8cuSdmO6T0w8MvAPLZwvrJoXv6YICPPEOCkgJ76N4z4Ue/bxp0nApaz8O31
Yy5H70YcY1WIA2zjpfAVQbkM5QOVQc2X6hBSPDXZesmV/6UWZWXJHj5nNb7s5unq
KeC6HyHl3Hj7Nalu4N+hLBv5S7r6PbOIzpnwOSzd5BHd5mXd/Auvh8US40h8kOb8
wZoHKoifm9j0A+kzA48Bi4Qwq8ANlGI/hEoKyxuUykcbbaTblE+EGlkATxd6zp/N
qQMGpPW/kqG7RW7D4JHCakvdYiEgzvbrW0RZGPK6yh2poVX3io3eUOyq6h09h131
H2t1qEff3s9EiCWq4TmPZNMkJJ7PSOxztf/Mfni7xSu3AQsijgv0/jyjms/SY7vk
fYhY30NYymS6DEqxE9TFzmAhUkZ/T3urAiBqXjcOfinK+71mldgE7SvFmyC9OBT2
8u7DIBJrqstJSkWBqCE83uWh0YqTku+HF9tPb+gFgvLAD/Lge9wCPHg+0r0Gp/v+
BMwSHUmhIKRypIdyMzw+hpx1pnv9ZKfxHjN7W6Q2UZP+1kEhQhdEsy95A0nAP0eV
eL4X4N94pWeoKh2hY1jtkoV9CehMaJ/CwBQlqMkC0E9s4pCuouvhINcHBCpvcCaG
zbmYjKRRCUbkITOGGtEpnhd5I588m9mF6ACoHK/Dtw4YyBj9kzveAMiP+Av0Q1aL
G0b8ttMK+L6mSF3NM7VUX8B6HwVZ0iwOsLS2nZfdSoHjZBqPKAMtay6xZv85xOcL
BA1x9tILhQiqxca6NmzXR8+7tHBd1hYFpzYibsa1U5+zNMFOtasRtMOWGgSjK/jo
7wc9KPH7n7Cz6Fqe0B1sTsL+WcV0dN2wgQZ/tBgLOYk++HBGdeE2rz8WWKKHW9c9
HSBdvGXOG/TdqGB20e4zgKqTly5mYPTtdiewBOm29F92t6aAtKnK3IQfh88vvFvu
WN4RysYlu14UTVaKAQavNaSg0f8WY5lBuq/iZAI9WEYDT/410RqgOcsahelYqfjk
QhroJwwWM+2GLwBmJ4VWMf6yvUqI/jpZL0nClMIv5F2a4k3VRDNYRV94tNZs5BQg
azFOt0s/hMuAnjxHhf2EitAjKjmVFotAWP6AZN7Gk1KCRRMgrmnOn5nAH5duAyat
DGnS0PT01Q9uQl4lJgGzAh5/9ly8BOib9iX+JdigI+Crk2z2QkpuHZd52IrBmilD
l3me8Df4BSR6xvooOTVR7DyyEZzkmXLyCB+LulrmjXe9riIYGCnc72vhhqD1lqaC
Gm3lsGHKGAuhCntGpj8nu4vXi6w4uFYrhs175YapWOWcgJ4zTth3NyTrgGVnafU/
C1YAF8pt1v1gBv+1bVrWyfVnIkkaboVQasUH1H+4Ph/7s7kdv7o9+TUVjT0/zLUH
8jF5NfaoTJaIQkiP7xBHBXk4gjw9iObjky0Aehayfr8M6W1GIqF8fZkDXpnEho+s
hdDcirSurD5u8cqpHZud4L55eqR72qvQGDqpzKDlFoaEt7PIjCb1XMCosn4b9yCA
B0J2ogg/zATbe26PT8tj+8Hp8ywJxFOk0XZa1jRY6ilIhtaJEKZJ/Ifrg6rmr73Y
mXEvItJ5ox3g3lwqKCQuTr5jE4tirpD+3FetA8BmiqeXWdEqO99k+TIjduvVedZ2
bzXicq4f+lVP8Ns+MbJaHH1/8VW8WCv5yzr8HdB1g46MrZbSc/vFao1XvnYmIZXt
CoR0jgtoS2mNaEM88bXv11EwQAb36gnfQRU26NdlbyiMxmS4FcU1dxOxgTzwWwBI
FcroXaWtrlqUdHQ7wVZCtJ82RnwGrEEDWqquj455cLPrClPfwa5aK1FwRoKu1nQb
c6JMTbCOnY67rgQLpbNe/M0hhaPhA1mPqXhvEwUVvJhEriiZqFnbgxSlM0Tnv65p
0oBMW+5C3LyJjKmveUXpcIzfzMHIM3RHeH9QQfuY7WX0ZZrTvI/UxNtmZDtxiUzK
J5wQIFTUKKlL40MahaQNZ1anUjRc+xQKjzQ6hNKpcaHS4ffinynllJyWVUDk/ES3
olcyWq6nJ8MBHs3XjS5Qb1g3cd/6vUgw6Djl9L5PWz7Mx8/TUvJ813OWlTqKWbTK
KzPHjmqjdqYLlesxi4YrTnazZSTxU+YtALSAZ6Bq4Jb/RdZPeuXefgGED0ussZ3k
6nCus8ljqledR+a+2Z9TGBq4+qPyIfI+4OkIzmlS2f8lC8u6StYgBOPI4yNwtASN
LV7segdKJGQj/sY9CkrlTicXPpJR5/RyJHWof3kYlUBeYMA8MV4muHZORVtE5h0o
d4v5I+3RcO7sDA5MORj6nsfC6cKeVCYx0XRRIPGOaApAQWqMb8AerUL8hFX2lv5D
4x60ntmWqLd9fpM7GdfScxxge+2387YN3rFAygPqJLcjQVyckBy+q6zDOTVbdCEH
DO5oOaVpPLl9yavZcyzD+TkDUOpBbqYABnIs2QtS38KQhDVK4OX2KdCwFXsimU+A
jWuFrsaWxhQ9xk8xIRE7IRWosp5M2RuAEA4c0PxTTmpzGrUi0fB2yQfqtB9O1o6j
f1lOJT7DIx+ah+t1KEbsju0cxEoNP7rMlCFe1CQIRiryMTj6ZfNBPzZBEG+tHN+b
829a2V8VWY28IJflhIkpaxMVE0yAOAzsZjOhaTAjCGRgZT0baYaNWlkcu6eJs+FF
CZ3Agcr+ZOoJIy27DCizEy5oh+V4zOpkX7IbYy/J44OhqE2pSYWcb8BfNfYoUP0L
wrIR4ZXXr2WnVw5LxSv5/vxxtsZBlHrJuE7CVaLkujQKlmCqJcg63JWRQKvpJn0r
7stuw1cKpidLozy+NINdN/2vhINwso3aKHilk5lwESj9ieF/Qmd3qN3/Jo93WSOU
47xJTNZC1TcVz5mYyh4+d86HdXMCNp1ZDHXJP2CduC/rPB4vj2tZS/jAYv4f0wpu
Wrv4e8KxuSVbfMuau1YpGSzOGOBzy8TjqipaAURHYqZstXHLZb9RXhWqfGgtLazy
rn7yBPLOxqUvNVLc4lOHJ359AMpJVsLMLAlmdGqhAoVMMOO/auLzQLDSQuIdskhQ
wVeNXhr4no1B3LEg63jEcwqvpcpFQfJxoirEwJBxyKhDjidBkgqWvdeVLluR4JSC
uiayCxGeSbDFXgY6jbWcIqyZZES3t7TXahf02q5bdSoCmaqyhIZT0+bEUorZRjkh
lRmwE5sVQ0EQ9a2VrMAWfT26whiRZk8IPpOrKI9Dy0V3u6iqUK9q3qLToBjHExJo
ATiBcZPbyg636sgXw2H9n7WGHEZpPNURbm4GJ5jqValap9MKMmfu32UaaPWa1nv2
xfbWA2WEfoj1WkaQ98hsKWqdCu9A73HgUrYdx0AcmDHSGbzeAnsg6rP9i0e5nV5Y
dieIFNey6AgBHaq/KSWnWrcfFzK/fphURTQDnSQk9kG44GsZELIna4l39nR8jXiT
t8xH2FFCmBnWFyQ0Hm7Ou+uMlKYkdChiIMI9nbybSmMqQwHNmaJrtS5QFhQWXAe2
BxooSRzf24LAM4SUcKqVlg/fvW1hMSLMX16HqiREv6SMuvoVpHsNT8roTcuNbtcx
/vUUfLrtwPzzroROEpAWpeXRJ2myHVSjRg2ihycO2oQv4ohkOMUi9gPnGmXqwRiX
Y/P8Gipck2VFslqmpJkAG5W3Qt7SCyCOE9PcxwDoHUhQtW+xXGWW/l5vNcWZtQJV
Vq5YyTS4w0B5dvgF2Bj4EO0O2/Z07wH5QBuIygJQAnqLpyRqd6MvUJ5eDuaIN0HC
HnbeNdnVI1UNRl3diTQMYWrjR3VnX3XNTqmYGzMuDlD97eBp+YcsVR81LIV4uQDf
UyQXUs1T2o6n1gs/6yV2T0icXWqJ6fJNbrbh/rONDIrj4GPqR1l+9MwI3U078QU/
E1rHsTGat3NmtFPDB0Qut0hpIJ3rTrAqLLcvv61lxlK7m7ZxRRqcSALElF3M9vtv
eqWFjOY5JjgqbZIqKxcdqcDrAqXNA4mX5YQcSc27nyJ9oNzVJilrKK7wbfmAugK4
zMPmk+WRcpHSj2PNaaS8H6gK7/KuAHzfYHSt9kwnnry15Az4szBqpJYIIbeICuw4
Vhr0APT4KqF46tvIm0jK468myp1aKDsvDB6FcGFslYFJbXeHWCocoe5A7z803mMC
NLp8Aeaj2pBv6uL5Vd3NDnyZOlLzK0mynSHhfD2vmuSx+bSLp+sWrNsuTjJ+rPhn
F69dH52i3Q7FGTWr59wMwXD/ltkxyH4T1D8YLRIISxA03xt3kFK/KyYQqfB8lYnG
UkTkQHYqydTaE+AtQgA2ni/P6fUjS070sBtDb3so7SN8e+hCFlZCcECEYVe8JfF4
fou6ifTSAABDBIuTXDmfkc9WMQW3GgukfJ2PuCKqZr0NkvOzVweJUDp2h3Nwivvn
0CzpzoeIJQqX787H3TB7DExvidvgPJgtuXe+fZfqoBZH3Wilm516TBSmwmBmLLhI
TAsj28Wv0U37plAnPES8EGFiTX+P3Qy9mHrVB+m2MivoCCx3bUeieflie4q+jTDq
yy2w+/UFZqGBNzNLGAu13600o69lMoRPPnj4c4xsmYKwdajkC8V2ORIvyfYcharI
kFSyODJQ+ryN0izfKb4vMFH0sEg82MvSdq2DxvqxoRTFfNejy+rhMlqUWmmm/aGi
s0VCs08L4E/7A5a7XxVKy7fu4onai0kzEqmbnl1enxTRHsuphKYCrGCpGXXLexQ8
fYfMk59a8wc3ey0VlRKNEXXR0xTd1MQjwSRwfqZbGbQxOD4auK70n2w4UfCZcGNj
G0VOX36e3zMZIu5N9lIbQM7g0RzsFIdBCE3bKvF8UnvZ6IQRzSYTpYuoPiLQnzGI
1e7D4ODeIwmV69DC7HF1LVKxTAGhP+x6d3u4sCXlPGeJfMdgOrXf82CIm1Z1T/YK
obcQwA1MQvfTv6yeBR2zRFegZE9qSs87dALmL6Dmv5eizJ8Fe2HWjobVFCo5SYbx
TCcARBarRXegv3STAxZtkqLQxnXtWMuffaqAOV0zzI2OCnbm0nb2Uc8slPRVXW6A
Nl5c8iJdZ0LuNXw9lUl8v1EER3qL4WGKmWsIdP9AuVY8ikwdQGKYFJbD/fkKSwDw
ynRLKBaNGf6VjVadBd+6ZP7CKwNyT/fo1Sz/pB0h9W1/RetO+Zuq9k2y6EWf8Hvq
OpdCOJNgb4LUGltOLtogj+mEL0IazG4VkMxNUlkVQVRwnIZKPNVdhH1OXh9jMhjm
L8/HAiW+yvhvdwrEtOLhMJH5POFZ5TroHqtJ7QL4wD6cElN6+u2KrTkUNVGXlXtW
g0JIZfo+2zsEEKv3PlTFPacZw4j24cfmK8/YCGcuvhdcTVFe9Ju1JlXaUfli36Xd
gWyTndraOjFottLRXC3ukyiVWhF2WbNY/TUvn0zhAghd4kQseTRjtr9zgqhIUenT
hYF+iBrSDJXBR2mgh5K67vZNSos0X2uH9J8dg/t4QMet5vMi81zT3d0IgLI/8aXg
sOxaLHXbNq1vabQxY4HiNk0R0StQfm6/6ftM+St7l0tsTQRtgZTjRl40DlYtyeYX
9wFci6ZqNWoezfkPBlOci2R/FB9ukV9EaS28uEBZptLBwx/NpNzyHs32PXnTkN+e
DFBeQMP5qvdzOdMLSpcYovv03ElQuEPuh+HCy+S8EVvlufVlODynaj4kwz34epvU
8OyaW6EBqdi+off6FzP9TRwB6Kf0jx4u0x8r9hk6keULw0fVETwqlaY4RIxHePyZ
lTjn8cBoZg1AskQlQz217m24LxNQd/0K1ZnUrc1ubcu2ZT8o+gnyEtah8E7hd4Lk
irgoilpAPov8Y5dh21U5difAsRMfVuuPe5GIVmJmH0EHXHXEiIVd+rJQrVlXrC6+
RsbquaNk540WABpwQz3CUDOJCszoNJXMuyPuwcnV6xwR27WLlI0h9gNOceoA987b
hxKTtjr6QhB7inp6Qtekc2fhJxmRbo9iEnNe4Z9nsGM5ZnISPLA04SKqaGndw0Ax
8GOToPKfa+IVRiYk7Aq5tgOcsWod5jTtuhj62POD4QCLrf24G3aBheb8zU/xjLaF
8I4YyIjwnJMMwddWszaLRWRfVZcDfVPMkm/p1Jg46XYv0RhR4LGzF0VhtALVNy9X
ByDo2+dTfiYQG+0x5p9WQ2iU3yo2k0qWiEimRuhYgUHGeS9JLSyJ3o1q7IbluVEv
ibaxgPbci21mK7vK469TdJ9DXhVwWWIZKit2utTvC2/5qLGqGJAIPFOvKwbjcbeb
JoGe1fdDL7Uz1EDgvRLq8QhdFeunD7T6BE0ATv5VbuYwVu4Q1VLFhbNDHvU/yJgx
DC4rAV+Prs9WqqlyOP9eWzWDItmnO/fSqR55CXYob4Ci3l0dsJUxzPt+UJfKBGCb
cao19ZkMBYNJ1EDRohwJ67w3VgZyJS16kkuod09DXdke12Baiof7INGJeJf0iaSy
rcigB7sDMTvOh6G1U9t96kiIqXPLhLfHap56CjBxFV66rUtcamJS0+PDfiMPjAks
vTE9o64v6L2FBWYg51WQXx+dwA/46jG9HqP08aoocT9i+HR96gV92llEgn7fOBOf
W56JAzbm0DZCkRLS7ni1iexBsvu0YmqTyFksVpcJC1hds9nh8v0wJdql3usjWxga
PrgRE7DmqmseHgnQJf9utRu+NEbl68IXdteoOD4Qpbdd9hxWT6QMUZrbqU3qfVhW
mrkPVKKvg2QSIOLOKTz86hlFVdduGFNiKPsOWF7ec5eGa6fIzVP9SbmV7HC2ZpgV
5LJ6rL8SnwmL+mbF77ej1F4Cii7nj3n2KEe+BULMyK6URtLXQnM2HeEPMFkn+svZ
cVCE2QR3IREV4g86N7uWE4jBIClDCWMM39FPQ1MA4FWV2zbtUTmyIioo5X+wQ6hp
ze2N5/LmH7BlFKXzhslskGkN5dIJNeCcmikX8E15ddlg91E8mDaHuq/jagcfHR1/
pthiRYQ/TFUY5AiWM24VDQc1cBX3zJIuOuYC0y+pN21QKmI78E6UEn2bnqqr75ar
hrgZ8TMy7H2UAqtDbbIqwyrSQlNF1WXFHp+xISrVVuR7TEV/KrJ6atG/q3l18BWK
WH8lXRU2YSkUIUnVRa93LsKYbtbR0AXZIdtVg0G1hf58slJxDSMqvMCq78O3g+/H
SM8jelcJpUfIHjB7B+FTaT39MPozU5xpY6zS4JW6BSutBwRK18im9fgD+GqNtGUf
XSiWoPaDHsKroyh3UI9trDbQWm84DSHeUZ9uJ4oJHYeG3jMmXbN9lyDNj3DF2BNP
3d1AuFEHF4rTfRtGZe97SKMlSp8J0luw/7MJUx10f1jEcbzdXeO/DPuZHGz1pCYg
TKm8fgP+KoV/PKv35iko2nsN7o+jQZkgiJyorpy3y3eUqlrjtz2tJgfAwN7JZUdc
7OLlO9YwmdnmROGSzabxS8ZWT8IW0CXp74e/k4kj6l3UiKT2DWwui2tZS83Wh5j+
SpZhIjLA6NORbYTuh9aegYV1+s4UgilFtJx9jNlwKfiKU4HLGbeJxQh4vurxcyVJ
bVEr8EyqD9MNLbo/StRYSzVpgJa4CuAZ3dowN4OY9FD7Tjr2SO64EBRt6Ac2vof7
WzPp3wuBsjuQOCqCc2vkLUxYZ6R+vvo3EKaPmbf/JY70Oggble1yVC3Xz7RUFf0j
tZUVURDMZcL439fv/GZBtvQLwCDaiMnZH9ZPem5MwU7SOXf5cXM9J/ZYz87uY0CQ
kFPnrkhxrtAeAV3O7KRIlA+oBNOsE/aliWYOpRpD5TqNfUly69cYGxrNfJFi92YI
wdJwZeaRbr8d/iFSq3MflA+8eXRUgRqPKD/Cw0f7qGIHcurCLloi0a+QBCu5dZeK
eHeRR7p6QbWHOhm4RTg2I2Pn6UIx2Qjlsq34z6kl4EMjDm6oSbm5kX4iLKxiMsiA
xYRDeIqK6aoS10juG9/tFaQtrsMNZOfi8PvrhypRV2Mwr6p8TCs0kjOB2oIZCE+L
ZlwWIpjXAJG+6Go7HendXocMY2/1rPma4SCzfRImw2IYzfFBvbWEWZosmWTLnNWD
1LJfFABps3A8n2gCgusIzwZbYGp1GHqKO04KJ/A9aqVOwH7799an9LUN8aNkmkgg
8xMjarpVYoldCeIPbWgPMJr2l0n4OXo2ZB4ulK2OwdEYWoHFr18tNI6X3+LgXx9Z
5zyxF+0R4rzvmO+YeZULYRvvVTmFug8DuXawK0R5q+xpZy2OUXAjS5/xusS7BPo2
3majWTG4bSUtG3fvgST4Il9G0WhesWX9dmtayhciB+8+P5YPs+hv0n7p7jgzpOFr
Rx4uyuoqWmpPJkv5dZGE0GfLP83ps/a4GVsCLeWN/Apbx20v8TW1QSb+LEmEf+iw
tCpF58lIW/sU+ow8gA46DW3GI0kiLpow4dZJ1ROBfF5xaVWdlzlZOVLzE4IZVBMi
KvfHxErmZ+1UUlKEzUt5AyhvU1Rob/yCc5k7hddwfZIWcwDd6BkxOqSCLW0PYO4q
FRUuc+BeFamGbmrSvcQWlJBFOdS5xPeXgVLa0r09F2wYTsl1NUk+rw3hfGa26vni
wUJ7q2NZqmYoWDK003oRCv8IaKc8tKzVT1AywUhhHI8CquOIq6e6IJcMg0/3fd7E
YtTJM3uUjxrCX4+kTeN1Vi+a2F09PpFX70IctgKw1gbpnxb1vwZmUe/7cLx8rNQx
XksRX5ujLD87lMdn4oZJjTzjh43IiGmT/1AKZbfodC6Raw22MMBBVOw9OQBsAZz0
FDOeXmCif/UfVG8D6vzptJnXHxBXHUfCTYWeWJPeHVaoWjVL22m/B1Y0UmcBcOrc
9EVIJr6vnGt5D0xrKU20KqA2UEyDY6ns/0WVYOtsf1O2KARUxmK+X7Jw3WMSkQCT
X47XD0tz5LoCZooqBto5ZkXYx1TfHWyyUVu0DGMNcEjYEzwGYu3HNa+FMACdU7/T
xnOHaPHKOu74upSpKhsta2peN1dDz4i0SeTO5NJ16I6gIbLcOTxm+PuH5nZk5DFJ
lkyW9ugBS3V8N6I3LDV0qe4Mn0Baw7I/UXImVK7ahxme6Rp/WNAMvxmlnATvcb7q
PtSqIgVNBNC5bB/mhsL8Czlazb2CcoUYsp2pnZ9SAFbVRmWeQiAuktWojScZAbUh
LYdLHMYL2Fo8OsoqSPH9OgUMyfjb/Qtet+OEpfYHD32RKCpJtbZ4+DqJxy9cEJu1
Hfe+FitoJ6vXj4OLJxs4mMKvSISX/UhPiygfTvJgSrwIOxiICa7GRTM+5CiOPBme
9HBnUOsUj1/g7UALcMHEagPQh9K6WwGwSHInErWf3EwhnD7oTo6dUseCdwUWxq42
VhwZuw04QucPhyV8d/9H++QgiEE5AO9yVzaLd4UW+lnkZC0ZfmcsIfn/QFAletmN
A5ffYR/47RfASTrkXgfWJQWHLSpAb+FpOx26rTtblj2/abVbx6RIKQJ0ARdfEiWJ
BPTSQ2yDsufdIqTtmpSQay5BhzeL8mmTvhDkT9pED2JcOievKV96Hxt385M4aLYQ
tjfoLGLxiYH6Kg0srwXmuoLzKJcVCijqUhNjrlILcLQ78svHwoisK91UEnyWPw8A
fm5rPVU4dxNTL/7WRzrfY+97K54wVchEHLynH0LZaXOaKbM2KEwaH/Kp4KFPWDGR
2u3NXPe8WhicMls7bD9TDEMHIYhmMZg2U9hnCLe1AGFeVj2EJhNypey+TsljL2Ye
XmPFeP164e8GAQAXF/XNjo7Jhi9ZtYTFlfRMIPN37yutLALPDUEFbn+hVlhe/9VD
J5Xt7ubqqcBgGIZcJn1ewBq1YQYCiAAPwVILRgRvmFMQiN0FIpbe74Ib9q8dhxF0
OiCar2inhyCvtJMZqo+mmNuwlgq0wUgnHUBZsEDAoWtbtRD2oHu6h/kJ9BJpgxJ0
dkXclVwW0EFWsTWc5/J8TNPvsH0QjsJxilX+m9T4bdUoF6YVL8RBjG3bssDOO1NU
8CnWgdZmPSc/Ovwq0cAbYlD1JlOMXPRGZ/Y5ASPb+9WhmUfdco7D4sdhMgAUMdAh
nwzhWYtdIsh7QUBmui+NPZeFNK0oVpzq4HHK7RRXXHLbCt089TDLWKJvBYUpfZWt
VmyHmd8zStMS8NU75t5nBIwj6HS5LTkzFAgbmVqAz1Z8ZlLQyP/BRJK6PXv9t0YA
IkzEP0TMh3zMMUmnaMnB0uc5KDRB+1rTdlvaxpa5a+hX1DhCR/WUGPDssBeoIwUE
9XNRw3kYMdeL+6TsiTosnWgzMjsaX4oXw6L7ZiU+m1slOA9CcGRsDDMiOcHDmvAq
vnPXQ2NkQKTB/DsBj7jMT1kNZwpGrWjBBzFz301ucsHi/svE4RG/CQyDhSb9sUj4
pK+Nd+Cthhh3hCnrPrtn3kVnGSTlSExSy7hu5gclAsSzUviBz7mM3ChoZcnwP51r
SSpCM0EiXHh57KhlG6HmLYpavLhpxbnXEtjJpP97gCQrBgKsVptmlZa+WDny1y+N
MTlmLMkyuuCD4eEfs6oYiqLu0l0jHcpGeBEMUDbYaCA9yQ77MYzBoDH9Uy2eAxc9
Tq9fVIN+afYT/9G7+W0BuvGkHO7PdC8K28XFdSuqY4KDjl89IbemDEg7PLwS/qSu
zgy6jJhstWsWgNeAp47u5Ca6Eb+PPL2Iyu5BhlE8bjyKT2XG1tplSKPvQdckelaZ
yX6VOndiyRJJmEFkyLqeQcNJjqOfkqfZz3OD1srJjqRbgGjwFx4E8wt0VGkLaTWM
nME2NC0/dx2HRwP+jPF2LmSN6WlT77hJ0t0obeYnJRCVz6nlhx1hi2Q++GjaxqHt
tS/eEReFb7Ue9VzL6PTEdktueHVmX9Q5HWNkJoDZIu9ey1mGG/+9AdNSlAXkQDln
SnRUK72SFpTAiQvnmVgGbZHhv14JZgirtAkQDdSWDs+BPWwp8OKCgE95m0ZDI8TZ
XLOfnf+hr/LSt0qqQkB2wEo6FJaBAMnWIGJ0S7cqTPLYpAz921M7mxEisFxjgvl9
nVklZSebi6SA7/yEZ1TcLZtaOVoqjLGFM2OSPPMpTnix9HyfUEYtjg7pyXZTwYD3
CIpQF11vzlp5kpAaF4nD9iRGkUvjowuPRaoTRqkqPkOwdVGd6iztX2uQ5JnBUJ/Q
4Pm8ny+NdEc03XBmqdYKrcTJvWJLDnB4VWDOG4rtFqwA1hbIH1XhyYW/YHohBndz
FMMW9EFijOwiWiAb88PENQ8uUuumBdB9L2wr/gakytAMyuttUHximKIeO1ODmdBS
kX6L+MP42V5rLcaxuLeeYFc6IaPAXnRVQeQrNmjyKP4Z0Eu8aSseWPFmGKKTT+pT
E35tTBFqNv9lR56zXmtn7MSG67xteXRkj7cDUrwD4S7tFJ45guyAmtKQDpykURXu
Xe8Af7XsSP/Og4gVHZ4XiOhIusxL3aUUPmmSWlmG3hd3paMr+0JmKPq7LscfWGW4
VGu9PCG7fvbhwFVPkfbqBu7VKk3inRk3MWkr+tdE7fZ5SDxshw8p5gC9fRAm7UNA
A5g9eSoQDfnaPJu+bRRKhihlNCuBYooWOhmyxr+94CVJLfhn6WuSV3o7pqMDOv+K
ojtpM0oIbUkkpjoxHbuB5k6jH1aQJv4ZvqPXiVlagCAI6xDJv8CDd068pbGqGrH9
AuFWs9DAnXcI3HlvNGkeWZsqqtO0/vmpIf8P+f0tZjOSyr/JfOdxix5b7ZAJ/9fI
aBjWzcfqVUrUwhck3Yxldit/o0qBuj+LaKC0FS3m5GK0HyXpp9H1haVgOUVLDPer
yBaUaXqUtNE+CdBDzEzgFiG6f+eLJdkkzd+nPqiusptbYOtpgDHD4/XVWMW5oYXr
Bc1Dha3mBhVIvQsyeHMkUpvbYDmXpB+coEHblc1feAl0Id9if/jtkmZblEz8Qqew
ciJ8ntK0hVjEcDT2KC1HlsYIVYIyBKwLzbum4Ct1NR3f4m4CdrfhUQ0l2gUMswDu
ct1lRvwzX8iyB6lli/9I2lUsLIUvUhkedLNWMrYuxaGrcDwXiwOUxbg39oY8j87n
mBLn1JmEAMTlji1UAADhA/94nmFcXBUOfYF1sBvUCueRmYvVD0jSCmS4EvnummUJ
aEng+37uKssMtCCuF5yEQScs1bb1+3NuA1G6H4DhZCHK9Cfv8foXjzW86kSt9G7m
hMY8wPwcDQTY6VF2HHbLUF+t6jbVLbrPUXhTF7WBwiDwEknh7xEOPICaoh6QF7Ij
F7WO/sOqGcEprmBv5meyZ94L//YMj3W2PbUC6kCrPadI9gqA0veIRy2g01+N1Dz3
O3Oovafj8i+XgIFZ8WQAW/ny7OJcAesDrbZ9is7f6JlK3lHdi03RgG8fkHqlVM8/
AUIbfmL3ybRieivElimQkaF4F7tsLs80MICSv2IOzrfv7EI52bNLuyge75D3B7rn
9NleDPMitK/huqGampC+Vt/PlCdIOZu4bgnLsSjbe7c4jn9GA6BKREWCa15GE1Fb
hrW49z38unwRQjvBGCBJ/+L/73IXup33mQjEywMiT2/HHtcZ5EjphGygEmVk4C4q
+qvv0rhXCfQg8RZOics4L9T+dMLvz4xfxqo0uNhNW/yQ2nCPyeKg6vWyQDqGINuQ
SvXVvgrmAwNmd7I0u2DaVZar1zPMSRMhHM12azFa1nVAkE50v3dqzyTvU6+9IE+5
npgcvNioN/Pn8esgZj5R06o9bwIkD4OLIjmDSPDgo+fPdvtQubgZuif0WhzvcAtw
xIqwfLSj/MjIHg78f/2j1UpWmeadBzOi5SjVY4Xpq63PG6fvRdWp3ixqI7pmFbbr
Wlpb1wmXoM3cO80bwJqK0eoBfFhkr1XwYl7xWwLYo+L7bToSvqq+loFxSyKvnKsD
rciO6oXHW2hM8s12mF8ErgdYMpzF63ieqo9EN4CLbdpRBufT7ua3jnSIncJKl0is
iMsKOreS9EDM4yobH87m95o+E+phKOugxRcO3yRi2m7O/dvZxDaXaFVXC/itia2v
PFWekWJokyuxlvfl812gbO2IBIfKfr4pll6c98eDAHCiAV9GJ3sUjOflacsjahHy
RK6k9pkc+FtTGd2j3CFmcm/aUiyYKOoM79tgOC1u778T5rXnbjjLloINiPjyPF3n
TsP8M5EWcC4VG2KiOyFXAQ4pOWh99+kRBB8AJNmNn4IukDq8l6B1nUN+lvFtT/tD
9HxM2oIhEPmbJWubv8W+DsPcdfgsSfAl/lh7AlMkUzykSSZiFICVk54bVCfLcdZs
2CTRx6TbU344vPsmCmyKdAelu9L9ww8D1NENp2+fb/XHUO1pk9tqfWmSqq9QIdyn
LHupOqnebbBdAYxBDjPMMGpOcTaSkrme2JSPHLJUeqb3MQeY0KgxbPLCj2jqvBai
xoSDqSj6uQYlDvXQCCyWyU7X2zjTHpkxvpTyQ65dYc4J2AGcR1XCAs4XbQLfhRH8
+SVFIEaTB9D/FbHpiWMV62Fw/7hJatqFfX+VFt6ZYzpTdrSA1lM/oCOvTxcqQ3UQ
2p7YD81pAXLq9xC6qa5EwbyA8F5LDMhe6mMv7Lt5NWDAh3FRGqko7rsDXY7lJ+B3
krhQ3yk0xsX3sKEJckBJ3Qg0TxE+ysx5xgtNRBnu/O11gRAImzlSIRg2Ec/2+6CC
wzDQUwvXq3NpgEgFNVixB181xgEDQo0yFtPXayZVD56lgwfY1gcfTW0E+mIS0ogS
QORWhmGOaPuOb9RDzvRSLCjRzBjb++k1/XIAY7eGbfkH5vtukDQ3MBdagcQ+htY9
cqsmpNQPYFSe+Ze51AbbqbNMgNRWgU9TXigXXjDUq1h5ZPx0xuORpj91j9u5HXzj
9ftloJB7+CujRH0bIctBZ+HlNj7Uc6OA8wdPa1eWGUwvA9NVhDXRiMc4ezFjPi0a
pjJEtbGBlYRl0Nk6/DI5jvFSC6Au7535CyaV8PbBV9t8DxjsgFRpY4qjibbXwfz2
acdHQchmoSwV/g22kLGDyz0myOyLWh/X+aR1ibMlsUfCmWcwvD1aXiVymv65K8e2
oMVtLDyVXNT1Ks/f38UcxX5nzLbW4sV5rqLYxfgJO5IbbEIEku8niSfx8MhsCCgW
u2HURMYXxyz4X9jDSs7/AhCHkZ6XGgPLP9h5UnoegYiGS1QgfFPL8H5NCxXCuhxc
iz+2ucE9fXSrUza7jKJdPWFpYY5KzkTsgYDFKzURVgoxkUP/nlE90RODeGTjh6e+
/EiAjaSD0Vj9yTZfABd9PacD/vt9UEGK9yBXk1RmSQqrKgppKtvwbKz90n74iEvF
c/D5P0QduEeqaG3BNdpR6zUJ33UMVxy47evjuH7xd+IVzDafwZCL06WQL9797UMn
ADd7k3zAV24XFZaxHTnQLlKeqdspAhfT8KPX+h5gyDfdD0TnT73Z0PGWfnbSNQAn
Nfo1/sYexGJzyRWlfmEnvaUsT+PnQz/7ZKsQMUNzSZkN6rJwl1oa/s1SSlj4J5i5
lVF2e/ZABbTOuiYIGAVpwm9tjOKxD8Z09SBgL6ixluWtGB0YBMPlm3B62+RcTsYA
GpJEQ5D5QEfe0MTBQeYIkZ+dYfw026REpYLeR/FCDhkmrtSFsiUMGEEO7ll45dXn
BX7N+pWcjh2ntj3/yeiBeiyIooAGU1B+TveHs/ebxS63Vzsnu2vraeWnZVL3Tq6h
dAe4gKkSW+AQ2Q+xhRhilXjgoiqI6/15oBk2cljfodKTYJLqLFSaQzixNo9qhncX
GcNgJIWvwG7HsRZl6jPQ1tMZQjQJK1Eqq3SGVtZ07p0W2Mg0tcYdHwJYroOgLMkB
cnm2RwGfYZgVFwdXqQQoAFw7g31qB5qLV2EeZZ4MNLLpIgJOjy5Db2OC7nK8rNXm
FFfoKUCLYiKD8cKoJmRXRpyJTPoe1iUFzp21kK7I1Utc2h/AhGYdQrQ1KYG+0f77
wQdLf9bwTL7An3z+qKH8flk6X1G9BqoTBKvl4fRjjaou/dDZP1EmzmCioBjtJ/hR
5IsnLrYafysxOv7JX5Cl4vgDYd1H9du7timvVg6O1XbgAK3cGL0WIijXnYX2rLBz
thAqZJbr3vpdBB8XkLjXDACvIUIpUa8oSpV8qAaFs97JgnnSB3Ew8erfWNcBXRHG
rJdgeEqdxEZe87lCvwDUgQc5thivLsv2nYWpHj/BQflwuz/7YluOmf+QiwaXTLBb
rwSE/ygLbq6p/mTMJngIkrviZn+WclFZV8DvMZSPWNHU/br/jXwvpjYE/xGd1W/8
pir9mupQ0V2iBJG2wv74bnB2/tSUcKlErzPhxmHCcSKAP13pVPiRh9H7x3yMbKOb
RdG04rroCPnwDK1gsXHbhIYZl2HaQvKgwH4rwO02TE0PTRAebjhbA5F0utD6PekF
acCJlO6ME212EWxiO7f7PXEUR+9JwlQKUeQZeUR99EwB1b6Mg9qxoZ0L3WL0mkkg
6Vk+CsDKJOV7+uF+KyFZho6GiY5FMCvqJwG+69s6ACK6TAK8PDkiusYH/mYbOGsZ
Mu3vx2N3XOqU0MzXFSqJxT+HRZBQzpS4oMoDgfdkdOr2L0rodswmiUM5el/0jWYG
rZ1OsQ7Y8tzp0bcNiAuGiA2j+hApm6/rmiNC9GT1SL6uNUvaXkxvbSp/Apz8cbHT
tV3kxXNG2tlZK+IDhv9ZmGGEwbMsudBv4HxI+Ba0oRa+2hED+IeXiFd0M75FA9K8
X4bPeDCztLrbXzI0gpyPo2oOEJk1Pc7adLdMxausvUmmja5QyufyfSLGGeFzcgHN
JXdpQdM7lJud6q4E6eF9mHYGhX09dBH+0cawU+yNjI2J22jtmQaDRZErogBC/PE8
Qu0b9rnXGiUMDC+NJ3H6gfW2TsPpqS8+5H+lcjPhn3Pao9Pf80kMgBP56DSFr3Ug
O9ZAujbg+9wRspseFQDoqUarclsWESiPYSLUFJJ6AH8qaiPz2eRDufmv2J5BNxYG
AJ1TdUAhC8gqNCnlvOEi2mUZDchjPIOfjSMKHudyKj+2/wbkfbQFMbEesHFmmbAo
MrlK0X0FLhS8/s7GiYO2/zefEnF0f0s4eJq34E19OsZK1J2gHiCh/twjpZbvyv1u
aHjkySp2YQEN1jfu4L3QxEuI0b2YqrVXq6wOijOdrZlVlfVaMrBejb2OajVPfGql
CWsYKT51SweKuwwua/AWxZZttTZUjT1yYv7BOb+XDG6uEd52fFMoVHJjJwITnnvG
dyRnIDz9MGgLkjR1ll1m3FX+ikHKoN/PymJJLq6OOl38kH0JWAJWIp+VbOAeLzXc
XvUFIzDcCFJjB8Xrbu5hHgz5qKcsV+YrvfIdsZIU2yrd2x2OIjJI69sav7Ase2F+
m5Oaqo8cX5h6M0WQvRsddky+LwbZae1Je8ZVjX6YGLCGo18B31Y1fI1Im0xV6VD9
/OUkZQW0FCtPxenOVsPMCxaHIYXjo6YQ5nBOOKnppvg4o+sjRLtehfINjJpK6V69
WO0DHEBv03hnDeVc1p3BzIGzEsux6kHXxAdkli1yDoCoY8P3C9DUWVK+t+ZxbgHz
81DPKGEiLac+/BQWiMi7EHsPiO3qR2WDgz+GLZWi2bAy9ObmRM+Ikxdgt3qYYZHQ
qT1gvaBSyunIYHcBL5T5IwYjvkG6+J1odmSH+p5fjQ6f/kqPcFz6geFzDXNtMLt6
Ntlk/Z/3kO2R1zFWbY8516h/2DKBFiENoLLrqi9TQlEaO9y8nBaATphYT/anpKh9
HHJvgjubana++InZ+CRqFrKlty6gZy+ieiTwmVxwDJuNbfgGZcoEe6DL9AekKSpp
P4ngY1FBx2KA3C4k3e2aqTlfY42T2tnKVWSbePBXX/2OCv1v2TQ5YcSC5MrDrSUQ
6NbUvpvd/eTqF3Qlf+jNGbX/WRulZbljcmZToNoew6KH7QsXhDG4ifpTwfQ50TQj
v9z42w18N7szTv69fzsyGK9QX8HplbisXGL/iME/AbhIkUNDBFMTa7DUzDdqjtez
jHTkaYsrN0fmRsW0i/lyJdoGmU9JoUt7T8EbpkgmEYCO7o+KePhZol48wo72IAyC
z05xErJrvHO/8oGrsvyN4ulpms3AunrbrBB3q9+Gxv6mCjWglvjIFs0CwXIWTulw
mcGUmjR6kQF0ESTS/G3Rswb2avehrS8i34Lcm2l7tBfID18ojf5Rht8M25di8ygq
x1m+Pj1N/KvoHoJew1Wc/aVzpp/isLyKm1ZCz8BHiqzJlVdfAsj5kl46eDlvIgbc
xT2/s0dgGtPCHSypjGGfu4A8OQ8QnCuN/gMBSRxesYiY+uVxsTUlNGyGOJMR9urB
Rp6iwNRLV7MX7yMo5o1JKksMP9mylgB2cxYJu8IxhZR9+/hcrcKQw6nE5eUbKkz5
zifLZOzCPQyqF8Okw35Ng5VXHqA+hGANx5s7xs0zmblPmLUOAMUAjOydJr0XTikr
1G4V9tkuw03RThz08r1D3ZlMRUvALksH95p+vES2/UKsgVzUI64zFDyi88AWmJN5
qhpSz+lMdT8nnqJcnUrbk9rb4XduTXyNJBz3NAE0oLxzIbykqRtEdR3lPoX/vmFF
woYOs6hVxY3kr1isUD19GWasdXwBEXMoD8F23koVgCcniS1mU/mcGYr66atRqr9J
4WGT2y9T5iOHP1pw3jeY2whTqEUTCK5MssvzP3gSDFaI8K1R0/+5WRmwXIXqKctd
kpeKLTPVwOCAT3qZMx8+WOIMlO5GNtO2Gp+TT1ZFna9iwtDkb4BWlV8JDtcgVo8t
ZvL4rVoM95qAiq6PHZBgkYntaO0m00VFNCuy7mQVzdyz3k4M4NNBaLy0yDL1pvUw
LyGUsCua1BUT9jnx6vhFT3zVZuO573GsdyybZvdfHF5dRJbFKYKIA7lnTTJCWI1F
5tHueOlqtHAFoGq76AAGgfjZwW33P03B4b/DTdu1X6U2YKaps/CuSpkt2JqwImxj
zYJAWy5nxzFZDBiCw0y4sgFVhJtw66UDhXofIBs8Ft1zj3booxbljDz2Cyzjel5m
dbXEF6klBizakXJH17ftdEQiDreJACRr2Wxfgy15o1cXN3NvA0a1G2r4iSdGHaUB
K31SdA0uezsWYpK2jcAXZRnLSC1er6z1Lx6ll93f79ItxzfrfREJO/dsb3/Y5mI9
dY7DYbdN2yK6FqrCs0q+QUu84Z6XbELAngfwQENYXziHrbKcez8tRi3brJH3NHM4
s2T0pLKV9xPdFL4f7bjoQ5Uj1aoXxm6fUZFv/z6y8OHCQx0hmd9AyB5LnY86nS5K
Ozp2Ll3pbV+9DclGrvIAj/G1D3HUjg2LSrReep2uEIS/F4H01xk/inva13PkMHW0
E/TsvznnPIOYpYNSrf+NTlb7sOaseTxj3AZsWGQc7RiuWFEAZyhcWraH14Vyf7xo
Vbb7BNTQkj5mwSKJ+Zks9IcM47AHygaRhSl6F9bw8K8luKhgBlag5Vg7X2SRMI00
36mIdbdNz4Cx27JTFc09Xl6j6cW7PLkH00CmBFU09TvaxjOJP1DwdHOTCZLRPcDC
+TddCld4A7tGa3zijqNbowuX0n7nALcW50k1wNlrImXW2aw84n0Zq7qipvI0Udou
cNNQiyO9imUkMvQ8Slfk0uzjGdQqk+rAnENX0JdoEBbq7aIvSUYyhzoVw8G08hcq
DSL49QNwMykNG462uwi7MGO0YFoCqcPzCuHUtmRsVYeZ/fYJ/uc/l3lavhkPe1gw
FLCfNA5n+QWFof/BA8x2wwakk5eCYwkyFEDkaqobmwMnzEvWrrSBC+2VhnPFrstX
gLUgEEiZsq3hOA5CzRTRBhxjRipDZ+X8bC8ilvllpOS3SEIpNIfc+F/yCrwwJj2H
Kazjw9qEFSH9qejSWQEI7Mh8rIe2SVWR/2/50RRToVr+aJKtkpoaGqxjVmsuyu5s
qXvn1Fbq1tZYnkXzxA7FwsvAiP3Jd2T3fH3n6yGGgZHcLq/elsmOiBGm0IWsNekv
txDjN6NDzJqF/5EN7KHyZ3qr2XvAJu+L5zqF4qqzZh3qeoZ2vhDV4iF1eVpvUEfP
mV4O5i1JZxMR/V92esY/KGggJh9QnkmpS1WsDZrqsHHSQycjbaxE0R4wfxuCqq7e
Y4eK+WJVYGuOydN/jAaLyCTeyG6ny3h9MGSw6x0lmZXRhBkTBmOAjhsX/LagmvQ0
4iqlzXfsAwLGFzJrb3JBisO05RRtNzkX9Wha+ft35X479NkEU3ysi7zS6Oa51S0E
AgJRw1bEfT5xsehxwCfDu5Uz483g8kh1d+bXeQJcDKP2lOCGzLIVqlg0QIn5GLPV
ex2l4MVDmHpmrp0H/GoD/yj6Ex/X64JMKcKyLhwLfL2aZSG/zvkT3yEOXOba8icf
55YHpLeAu5rlwz0uotmngn8Pw3OGuIC/s79jdwqAw2Q85S6LAbbfvHdPruN7kbMB
mTVQaX1bmQH7Tkb+7EgbgCtWq9TEGtJzU3t8ENPwJzXiVsfUs0yJEj3kOwROmjb0
Vxzi4l4wGImxgRERx35Q8OWPLoEwsC7UaiaNWrQarJM+av2+IN3Wgt8ZUkEiZ/Iu
Hpu5+TTSrDCK5pZoFtfJDFaJ+iIMvqMDAcHAIT0TGD/4m7Sb/x9gLtLbvgcLB/po
2xrCGzt8ngUq9jzVAPdUhVDOmU9ifUsrNYT+SektAeUtmtevXCyw5UIPVLXhX3Rd
RTlYLnN7hHxalNldEpBt7pC4M7fGoujpc+hGoZUspjY0W0s+lBuEvyXW6C8rSyCB
41tza/yBrsRELgpsIvzO0mCoi+RaAw7slDNjsvU9JHyCajwKrkQIySyoon+5/8qH
EmhBff39DOgE5+g5PPcHXeAkp7oU46LuWVvTie6Pyq8WxuJrQ5JLk6uBWwEMA37H
QJP0SnkJx2riRI8JZTnChNLDzIyzbrwcc6TzMoogUvYUMWLJQaPnio8XFutbnvGE
I2Pi5GXxHadBS6YIwqw0sXaMEg4Tunutoq8NxMT/yOOJ7OcoIxFeR5dfeVUwz5Kz
HWLlq9YR62oPjeUkBR4PSJj0Htcdr2cwFfHsDtxKauQvS9OCP+7H/e2GyCFmHgBZ
PSFPz1FBJPCCxZM1NfsJIHh51fuQNfia9HA9Q10K8pVCDRrVyW8I/PXbQSAD9LgW
EDfZ0QkJJ9urAqJ8naHbrT28kA3/2jh/VsWE3P798gpW4uEVnUEjWkkEaKpU/3L8
D6hjIuctAiG/T16AVOveJ57dyW1tmlLNAZ0ihsfGZMfTKTZqkulf+SFeGmg4pCbg
UpytvYaesjaUuCqVgxOwBP5NyUcPc1zEImN7iscglbamXBQF34nnPV2BR5cMAkeh
IAr7t0J5wFS5Ax/o0sHvao53zyk2ThTRqJh8UPomLW+J3DWvNeoxERqWKiqEB58h
/vn5VyE0yLPDCsGZslPv216pynKu8PQpNPJBTr+t/CVuqP2icSMbeWoNLS9SnFqL
S3GCu18DgR1InOAxqa06Q9J7iT2kde7RT+kSx/uRI11+U9gVOTnIw2n00o6gNZ7v
uEO42Q+VEdbGf82yCbuC16hS4CXgp1c/OKZpcf6a3u1zB++vNJdm3nK+6Y1a7a0I
GUzoJr2TgCEITJoZNJn8CkwQSspUuV5mfpnISHXXAnLiPLLwPIB6rsKGGi61Vhw+
SjtdPEdXy9jhENS74sGgmiUuXaix1QgXehWnBapA2QYZ48tBk03EUhTcYILhh1Jt
3/MupTlvGI48TfKaY6ohzKzCIvvEkoQk4JUjo0LrWHXHMkODDKDaRr7HynemPpYJ
9gZ9T5G98DNZn116d6gNDkjAUu7lwUD+DsOox8MFewwproxrjQE3iT2DzwIMk/B6
ytBIaziBHLw/lxHyzNVduEdEDy9DXX2nFc/wTbLcvSxv77iXdAz5B1l3Y2/N2OM+
r+6IAZCjAiIL/0SMh+5PCRLyFmIwLLplPgZIC+6Lg+uKQFr6A5p2ke18penRJ/Mz
SRCfcKUcFvtZUsBP7+x9OIiQeGxYp1m8pP17iMlxty6KyaooKp1hcGjogiN5+c3/
r1D79DYH160qgNcOCAtMntG+LRHW/iujPHYWUKBPUvyp28Ulh/hrVwu7DAap+Xhr
1YmxeeQZRUzFte9i1xVEtn1NrhdU0YdNvub1zRJkCrC9Es6pZ/993euUSfPjndLo
YqqyFViTvZYa2plCIZPGm5ghSto7pntqCCPJHBBkAde+DLO5hUhJXERNqRzHM3C7
7d2XvbNR2h4EcjUMEGOrAlTV9MPCTXM5d+s/ndGP1SPrecJVReb+Kq1oxJlgnHMp
zOxbQbuQpxN7zvdLCczr5FOMwSv315LWG0zvI6miYV7YPWKi9weAm+9nOEEa64Jo
cQZ12kvtNC5gAetzeJL8/OAwPyYd6Frtsoxb5ldMjgID6d6a6Cm53MlbRsKGIbn8
Jxd9mdQaeamMmQPs0uK51UifjDW4ESnGtDOJbJRA1jZO4Kbjlf3tjw2P2YJvZ25H
jEFTot6H2xz8++6ep/vfDSOhz47YEFKGBnlK5gQps9T4iC35KsSaRf1zPH4L1A7Q
VQxOeL7ppokw/0leQsjADtVaoq782Rb3S7EioMmJvbrtX3qLsrD/AbyUHtUwzMbV
uiX1MuNmRCvo/bmRMWWyzOe9ODhBpuzOAzfneox1wJ90mOtOnWdvTZvgXX1EkYq6
swzCUl/+JXWZ7lSmRYPnPbkllvOcNhaK3zJifh4aDXelLY8qc4bPZrEMaojCpipf
Xd5sMjUDns7WJIcc5Z1CkTxi+p3Ft3bkKjgaios96yxFPpFFTy1vN8KUo7ZqjdA8
qeiNJP5LPGZbIGr5wmC1CV5rVDOKXTqNTf6V7kT8TU+MwwuCcKXwa0ml8g7haqR+
jBCRz/llI8A2GUNDijxZrv/xFoPgGwuiWrQKrvxEykmqe2POyglVAQKDp1tfCjsd
MToX/nkBQxGuTrr9gqb71GMckM8RaLCD4oegLHZseyHFHa+AfZtQAgj5mOohlZku
PB0nzNtybdGMfdxv7h1SxuAOiFopCEFqK161wgWEIvcFtGFmnAMcdjM7t2DH7aI8
zXDMfOjSpBRJQ5xG+c0vQ1RnD5cP0rM0t0sSVEZVV56MpKTf0QG8/dNKNsN2rnvN
GoaPrkXktp0LsMbQ0rkCv34EJRm738GWfhXTNIJL7PfT9uGpp/Bv6Xb/mEblQEQU
eUH+7Qa6vf8rkCcF8CYSMec+QCJDdstHIQO1P9fFfiYhkyTWH9rCtHK+76TeMw6Z
b5RP1PE8ZJPtq97fwJr88VWiWNtAouJ6Y3SLQT50dbpyauuHI2R7SRDVPu0PQtfP
fCtT0kXzmb61wNh52y73vkwVZP7oVvKm0dlhb/f7MiZO3djSvfcCu2mUHU+litLt
s78D5Vr7b7WnHZSxJSTqbxs3okzqgRs91z7jtQFh6acycv7Iijv1UhoJeZ+nmh9+
xi7AXBVky9fSVD5ce+rvqyLwsodNVs0WUTYnB1yvedO5gXBRfecHgc813WAne9j9
3/IHFuKJ5gJ0vmn9MqUerXW0gMqbpCkf1AwxqYicOMqobzsBPEqXorGvJvhIAY5r
Aodveplr9G1kV6JikIbrh6nocf6mbwem07ozjH6Ia7OmJca1kaaLKTEYBDDHS0Of
ZPle00OI1H2fpnDzlLPyJZ1g3AWlzQ6+ea1MwLMP6w/92/oXaziqJ0gg+iRDlAlD
u9nGuTY6C44wUvJcP2u4OsyxhBvwJG1l8TgNuvQ45XNCO71mKHDpRt+PvPMmvRrC
4lt0MaH52wCbY51jUFjB2qLSvkSo+n2BoonSCQ1ZbzUILYLXP+d7pkc+J8Kvg2Wy
4RYGNRk/QTNWnLR87R1L85yjvZ8aUR811+FbDcLs822tEwv6I08lDrhXWltIgMi+
Cnm7aq3o4+Me6oyu2xhky+JvyZWc/WDnI7+qWuCTIkK1wwvfnBQXPtw/StHtCRzs
xOaD88S4hLdtDk2wwuJsUfMVWwMu+nbFRzt9GFYf40PSHzuIchT9r/VJhtDxTg/U
9xKKewjbzEajf2q2V2DmRQTCcIW++cYfBk/mce8It/XDknUvczB8AR/+cgs9MkSU
qLxPUFihSa9KHmMqtdoGltlaMCNuo/3ZMKX7kvK9ON7cwr+FvH02gjdJsCrJcz1S
EFHgh9Pb+9pWT+bAOzoT8EAKIri6bLuswrMJcygAW2bsIZyfyuQrAR4yZ1OsFVan
33JOxwgqCHCjBEj+AhwGUGHWiBFa4UkpPTZg/YVupLwrNZDmaCi3gCNWarzA629Z
57vYoglkjUjrgCHVZER2FQsnglJK5Ya64Qf/B7w4S7KxxrD5dP3xFMkHPFO34fJ0
acSDlhjEv9sX5E3mfxMyTG/kj10cb9CGT5C7VPj/aXzFrre4wBhMpdQLeMAjxlbf
dpYXVr4FlqOBHLTU4XitovcFC8w812/N/HRlmzYE9XbtR6N6Yq4ntWvkPxR8RDbL
LlJ0emnc4EIQiu1/IXEDmIz7vvCg+o5CbY1I8JZ3vgrHiMCQrPqqdzHYYsHDziS0
4RqrGEjjN+JYt+H1sYYJ+e4cbOUfJKDdsN5rxS4wU+hgwnLg7TogjDP8nMtxy0wT
DHwBXwsm0bYVq10fUZ4hDfF8/YahDATQ7bOQN4mNA638u0VQbhU8+PwekV4j29Qk
aGEM8qx/hsRvShZC9nSMsJyVIAucfeCxGpXMc0uyykhbYkUsrr6C2oa4LdYYBTGe
SGnyiTSXhEVa3Udh+S+gA+9T7itWkP/KVDw7gRQ2zgWEhUYG1IFDPzC23ddhEwTD
Q01AMCUZOf9M18RfYGP33QhIWh19zPlISXxsibm20ss/lD+1jmOi4zGqKQAPF3BC
+zr4Sutq2ekLm6v3AK9FaQ/ZIShcAQZ4dJhWVrqYZQRf5mP+CXbC6xCb/DqmMtpC
N0JjBssgDEKFg/TFk8Dd70V6RovJdr/jgyDqWo4G/Y9eBt+ToY/VCeXK/Nq3CACw
oOfjMdtGIH3dSKGPNrq2K/GSqugENe3CDi5KFYpstUtuE1K5M+qu2qsGFLRANda4
PVKlHxinuxPqYZdoJa8/FaaC8znjkIssVza+B1x5PcwwJo9VEtfQLCA3lxPGzm8X
Db0NfGnFR4GAegm1mCDrTtb2k/i5AULklmeyyNcMB/0XgxD7L++34blB9Wgo7Gkb
Iu6YacTf4yDZU0tgFyTP0SbDMC2PlxoLqWIi1COJ/NK3LYtubcDYypmBZGFcy1tZ
G1qp850z43l3x+OLxTbvTwQODMmccRTC1bNS1c6NapBYuUVB8PfpSA2uMJW8Peae
QM0DIDbnq/ayflSAQo/ZSwUVIr14/fvxBibzB1fT/bzpX9/3jCzfYL8JniacmSKM
n6Yu5l3Boy0b6Gj05/o6AhrKJ1fLhHPA62U1HAJWuPLxArI29ywTMs5k30FDMJcJ
hDNL5xrjnxduuNVcVngiKN7+jP/7mLKXQMPo36Ghhk8OJBr8plH/bNHLPSMJBlzU
MAOnykdqS4bzDAUTuAGpCOYdsSD5SI7yCT2DEGX+KoA8QxLrwB3RhmctMq4RrXY4
XOqKbJ/mxpUOXWZ7R6QaaLJyJ6R2KYVYxQbMHZLdjetuyGx/HQi7JZzYDxUaSHXg
K/eavw6Tl9dOYDVDV5ib85g0jj0ScObJREcdU4aDhLOjGaaVHPtYy7uGhqcT//Tg
JnSWR22DPXONWIfgqSLE12nJZgUJlpT2z40mpd5UK3C4rdF+GQydl7W5+r5CGunJ
oXRs6NoCteK+TVWDap/XupuPTTXta4tvUvbcMunXvBM29cYNzkddWmRjAfoT2aF+
XeAQhHxtp0G61U2g5lvuMItizLQn7xFzINRBp5br1XZ0O6w7rnhTVl1at3Z28B9X
OeFTjHQgExWzTzuhN8Nxul5jCcU0nfg69y85ZjMAMf3/PekUSp8d87IZqHd9TCjj
pUdEYLVkSLmaqlqcRDO6FZLJNG4VDWNeO58ghCfuSBKuLhGBCi3I8HuKr2MGHKR0
mKsCM/TiVZPLN+ra6L3ibfAMd/PjONq7pw8rZHx+b23SI3tUwgZeFltEF7OmvoLc
DaRHI6+fP9X8gpE4InEMMRe2Dn1nXtNxJrZwdjbQj5lkJngBPusbPhSrUYOhj4GR
RZ7qBC75Nb2Qe/D7DHw1U/Bcbq/kZfrbQ+2WB4SX4QKb5P/mO7HO+D58dDiQhGTk
DLyFcogC4DpE+bzmUvarR8kE0IgXTGhFOw5HN5AZ9MAnKWpJiM8cE93C17+gZpe5
+96cTnc7PY1t0+LFCYO393E6LPZ8KYCZ7DXwqvQ+lKndRPGX7XvImPt8KVYVymwm
pCh4oCNO6xop8scba+3ss1SWNwZ95m4yH9Wy1xuN3Vt4zbu8F70s5TwjXaPFA2oY
lOBNTCwOCFPIEtI6KcShej8ejHv4TdHhlOBMH4xMSpwKuaP8+1JFFfOWtxrz0RYV
4ANiUNXQ3aQOt0YR6/Df0+hV6PyviciqvN2UVrEVU6kRHy1pZT1+xiXXUN37pbAC
Fq6e4SQrRgk757CK77T/MJUH1vAenhCDP5TZbnMLbThdXhrnJWFjZvJVPyq0c0fP
k5TIZodI+xajG/PIkN2Fej6gZG17UA3hZ9Btd2ttPlekK4wneR8n2HqCuhNU0VfA
WnSoWdeqjc2mEvxWSWumMHN6rMvMLT998n0C17EkiqFhy8qPqqYJM+Q1Dv4bplYt
CII3tSSf3XY5vcoHTAc5vxdBqBx3qhYt5DGCO3rlkcaws76k68X+ciJDtQYO4/IO
Z3CgII4L32+tcjzRGUNHeswP2UOuSUt/vY2VvysKisltl9qbtGoWJCv3K03AxZPz
7ZJ35tFTY9xk5SYTaE6eNZ+7tlG+6NJ98V/VdjsW3WMJ/CQmCQtujeWX006BkaSN
3xD1IdcE7EP3V76ddyUfq832Y5MgKgMmN+AALtwqO1uP5lzykr6HcWwiiTTno2ns
Tn+M1I1X6M5KYQ8dACMc/eIhmQJFDFzxHd6Ff2mEibMYAvDfFEbTMLrW0HqwyKIX
tPrH9QMOfwAEZAWpJvCSOAaRj/nSEMQDfX3v2aosI3JjwQrhzKpFrFQ/jeTJqtUx
QatxA1gfmXrqNG03eFBK4VcOLVvl9Ss8XPCydIPRaIAUvurIqYcrWXgkps8PlD19
ihwINPVqT6K6M7vFjFwHbWY1apXkN8/oUHE9c8yih8u6u16vuKZaB4m939ZfmXkO
sFVJfnUi3+HQEQAnhE2u/WXCnOtBD+R0U8ZdV0Sbdtjr3gnCASpypy20Kc/OUXdJ
XKrfrLd62snNUd9dZZEl7oy74Hfmtja2k9UkCARs7BhzfznR4GR3+0bmxKYDSJX8
DmCNUSGhspjUo2TtkcPwktgfeSN6P/j6wc9+RG3lqh+CjkY9b2q+rope+wp2iJ2z
vC3GsxKQB7Tnhw4pBP8UJrhWZNK+6DwcmrVznZMolx+44ARrVXgZLPGgYal46uZR
kAmkTrH/bGTUKBsrt8ofzj8qpdC8uxTnYC6TcTVyxni74TjDrdniobbL2VbOwSAN
AE+og/aV/oDCwL29rVwuJjUBsKw0F7MIfeEmU6Rop5Ob/jIvTIA3gYTDkvBJZjOt
T0VVY7butGrQukgH2u1R8ZWcN1BHObN8iu8UQIR+obOscY8AO6mH41+fNINqNF+W
dyXOly/y67Q2Sc8yPQbuoo5SSi0mUClY1XYXAariQr67/zLrB2rbFdTt6GpNNCpg
gYKNiQpygqDVRs4FZ+X9Jh9FI0c06AbihiyA9Fvi/TIZaeqajzNPQgZiPq4LLMtr
Ias/OUpFOMTbMsw40s6+sDUlHGFkmVF5rExptPL1782GB+Q0aSRPpjOtEkP01Z+2
PzVDKpBw8OdMLcHnJg/3u8+U/7RomLM6SXIzXcFE6UwegS+FtGfTfcEMv29b5Z2c
zA0R3YfxY22T5GZZR+1102RLUo0PDZnrQbSEsZoIxIg621FGZN+JAAEp+X9e+T4x
aLk+CSFoFuzrzqAY074fKzWBQkd9amGOPTk6AC7qo00a7vbo3BCQXaVp0WggTL6w
GgU4UtdxCouosClZulcV2oI/JpjrRNGUM49TmOcZ0TOZfdmtgr7TiJhj4OJB/gGH
BB7tgR19aQk/W/0J72AIVIFkqYK0v99FoHYKKmCJVhr/FqgaGFRj0mE3nlBEYM75
BfDcEO9YLFoXviWg38cPOKU+K5Byo8vHTs1YPi746byiZlBKWOknOk9eSWTgOcAC
8OUhat58+VQ1Auc+ye1yEW7B8JoA+4Sg3ZJEVSwjScS4WJHUHyPDKzEGawuc1l1e
yaP7DV/ICsKYwlVp5K+j1qh2/Wjd5Hpox+V2dVTb3BEfaB2Pq3M1YUw7F9WkKM5G
bCKBdtojCr4AyI/3NhcWbaeXS8bSW7wG2PfbQiraDHdrovj3SVaqyL3hMqT+2mk2
4qMpmhAvtFeID4nb9cs/8wY1TI/rQ9IxNOAa1JY9flOzQRLFAUxUTV4FPx1/X6vY
8ULcwqaxCFffEkhAG3zSF0Fd8QXy2Mk34QzrISpk+EA6V6C19a5YRX3Qoz+quuVr
2ypoAzDw6H+zN2Bue/O1K5xVhL2+ARoGXt513PXWgGNTCkct3RGhZrmUEhVjdAgw
Z4lDtCJNMCQ6sAd1ASfWbtL34MHgaDp4fUELuzTpTMzUJS2hu/nTUMhYMP60P+4O
7YJd/iEjgduOdI+z0R/ySJgnC+a0u611vPotTviGmykZMlg35uEWyJzPcUL4mfeq
eLoPlMm4ZbRPCBEXDkkrJlhF/CcMoqw3bcTyY+fxS8pxz0q3RAigDCjcnFNpaZkU
F7MOtwiPufM3dm2LwwkoBgIxLTQmzfL/w+bk/IQog13CxbQJZcLX3HTpPmAqrN4g
EdX3/OqYlBWVIc/MfsxRxpNzHHpBXmOYNnqnibP5mOn92rWPdhboxFJhacUqlNJC
3ZEAcBVB1v1RNP0z6dQCEREUwXZkbXEkRgKKGoa74kr2sA8y/SadtgXz/OpOb8OA
TEGH/TuKDNWoUFtt9fKSjKNVsG46HKJGs9Z7WnznOwTwHgh4PJYwjsfBIjRmJkCe
peImZd9DpdzSPJ+qJLjAQ1lDzs1uACI5lt2lYxfhR1QAS/WpNbW7bfK1yME82zZx
GGYh2H0MbSyNnO0YO8VAKU3T6JmJAWNsU227LdmtuTickX56/f7klFc6miIEbR9O
5IQtPXAAbS0ucICnSaNi1gc1eyHVno1Nc6XliNBh007sPHEMAmnjCzxRijFZZ+r6
XZZcBIDs00RhcoIvJGT1Pi9o/aWkUFQFRxvsVr6uo65CeyHfVq5PJU1Hz201+WCB
3JE4HYA2vKtJKSrQr//Z0JLrLr67t69ZqhhD8oCgS4icO7vN88yRNnnWeh7K8mPG
jPSds2S9ZXkc+0wSQvV5W8xGQMgD8xke2y74Q22Lxd8uyBtL+ICxyH2T5TINQ8Iw
tG78uEfJFGtidDbyuDeB/q929/r7MWsCDJUo/VhIwuBaf6/2pAtJqJdLB9BGNIw9
TDzNvR8zPMpabUZwH8VbtryIRT9T7idbKZj+jJlr4QxmkLBDCrKzpR61+t+4d5sO
OHcrli8fAriAWeBzTLrp780MCW2vqhCg2X5rbyfybdzyMiabjXViAAli0Mp0mU6c
nnEzDUI2//zAn8q8F9WTcmxym30jlkN6VoEqLTS3tgWNZBA5P8YDab2k7+zl7TmZ
v44etQTCMLkbLBASzkxQmk2AS8lDhteOwbg2fkkO1N/ipYUqSYGkcFXvpYdWsGLM
g7RtUzMYIRRmkCdbtG5cRXfjS74XtQseK2R9bYIsm5zkCXV3b6KY2MlryVLMA5QR
Bbg9NLlPVwGjMIBep2nuOaz+QQ7qzKG+jBrIrGBqrrR5sWZs1b2EXyzjO2Lx/NFg
NXCo5UOa9UKVKwjHUTQeM0BVGeW3EXLYG2nujZbauOtuRO5PccAZ2l3UDNZHOKf5
X1QFzECclq5bUYtfve5su4BGSQEyF3NDlC8x6ZcuhXBLXTOvBVpHQFMkMWQQ/Crb
8Lx70aDwGYH0I28hLIHotCN7+kX5RHbhEyAF+GjpWwZ3TgBG9czW/A/MBrsvHjMX
nrq3ILk473cS0nmucwTU35sd0ozQ60ZPKJKFya+9oYhc0KvVjsqIU0fx9KEJ6pIg
FiMfryTY76oCgusIycAx6/oxS4dzH1z/DNHQodPSyZDnzJflnm0KJ9yWk2tq67+9
KltT5aILd3GEINL+71yrTaFXAjgUhEDHwi6Fzz30XM2mXQkL64z2E4qC0C3iyoGE
fXAypm+NAJqQ6FAjUqjynKij9NN17zvSORyYxJOQulJBNlCVWCf8u24l5AZmngWY
9JVO/xCC7Ble1yu8hfJz56Fz+xBAPl4JEYbKJh0+tkMcjtDaACsIqEUNCsCsWj9h
Rn5XQPaQBRjj3jbv9Gz8F0WOS2T38A+YVmTfkL0ck0NZqGnWF3SGkw3s32HTLVWe
QyaD+zGCg0UFr/6EGB2nmcuQosqXdBP6vPxim9+0Jvr4o+SSQNvRODuWJqWOA0t+
uZwlJ8KN9if+zzRbE6t2FLW8Fg7qS1qMbcVPUkS6u5FmCgC1ySmkrU+kMl1lyGkX
J5Rus9THsBq7so50bAANJjEdrc6QCTm1Ao5ncqOUUQ6OLSayfqSnM9n3odoJYaF3
9GkJ3DXwNgp4GlpN4PNOpJ1aGufXL/HyhJZPq30W9NtEXS841fKhLW1uRj06YuJL
bbFLrSSii4FWFEggUE7K9Av1MtUKkNSyZOa6urI9wxZ9MVQOZ+3UyLKpG5N9ztG8
WSl1dw49bP2RrrLKv9zidvKlRh8RHboHn4hH2YXsV9Y5GfDoUp/Xksso4axM71az
5JNj3NU+uG2GbeXQmFFR58ad8UdBo0QGoOYYIJMaSnQJh6qLPTZOk+cYGqRv8hOr
Ugd3KIKdKxh9g62m5PkogRVLdsQMRHpkU7BdRq/WGBO6p6yO3m7OPhz7gJFITSTg
FjDGDPdvcAjlSdrZra3+9PMeQDmcx86YjRYNwxzQdcuragad+OmfwO3GNMF1Au+6
2xP0b1DeV9DoLNFGI6eGrvrUN2ppL+N+hDEhOp7XS0tEtJCEP9j6MgmtJmrmIgpL
L0hReHoRk/KX7znt9IgzhpOFGrx2eV9p2m+WEhx/kzY++H4Sw+2b0d3g4FOm7VPB
eg5abanMvwCO+nsCRpcSTQ46XzRrH0jUOAnB/uuaoOxdYzYLe3XDwJdJm5RWnnO7
R6NSEPynYkRykxZxsgPPN6+roatgViY5EJsxT6KLp561VXYwFrCyGncHNd+CKH/g
0T+QsUvGL8Ys9tmNCe2/mw+9IMvKWXTRSQSf6hPMCznRDcuaIyr6t7rFmI6C9WEN
AShReLG/dtcak5kpHxEPmAahK5J0Qal8Ol8B1y46c/M2PyEcoFHyblQzGaKyxhl0
WHZ+xV0YkKK+VN3QQv8EFCZWaSTGLxZHNKE3jTBgSbm7b+rBWTJ13Uefciiqpl6b
TTdrKfX5K/dfZQNBX0UFyyaqvGz7lL5F95bE5niVr/VCfc6K1LmE941f0VyRzZqx
2FoRxmuCxPYX0fT8FxOF1L0LYAV+WrtwBOgSVKTRTeDNMrtvnC5kRaeYW58sC1eU
cWSQLGp54/Uu+ddG/PCjIqwAbuPqKhrXYbQmEaUxsWcw1p5HdlM9FYXZ5SwHeteJ
J+VN3CnxRlaW8UBeW3gNV1iad6WJ3fKsd3s0mx/fvWNz7emsyKSUXrdafDEC56bu
PTXh2FT87ghArjknhX3Wh8lL+cCCzfj0Fjm51Ro87yVvsmy6t0I9rhBz+ILBWe6i
xplfrx3l45hK+1zD4tebZZUTF8snBUTBTTxfvIOGOaObA0PbPQhOPeHrifuUMLMq
tx8KZrsp7ZoLUA+t7Qg3CgbBoHQ7kh8mytVBvWz3Tx5NJbdsaMZDVMLIi5etzA+c
oaNwfEOMurvOImnB85+qjhdr6E3M0n/itLr7ENAupaBBY87XVymZIN420UtiXkUr
W7w32oPNxveCMNj6XnPhvDPewQ7WMpSV32wcjmJf2Q4hYu2388qq1QI/bkcwfDJH
mtuoUMXTU8VrOTEOW+tOJj2/alImT8SRKoLSItHtGzyz7O7YL93nu8kfI0FiyBXs
zXahMqrkc5MkdB4Y/6GXD6gnOHhyRjYV90XmukP/BxLcTCph4okYXrOLJJx9W0t1
aXFcevlKvDoRXqbfveQ2ua0tUPf6uDbY5971aKob8R0y2YnwvDZWypSht384dOfA
3TjApZTd5Ncl7FCcDK14X5ueZFNG9loW1znaXiDALDTp8GeOfWrTUZAhzvvE3m+E
jF0AuVwkH4vlLmuF3qAsS+Nk+d60ai55j7h1iKsknQmw/Wye7mjS7qAC11nSTJsf
NUg7YcmVPSEaVH90uJaxeGuaXJ1bDcCypGnUNZ3zFZ7EMnBoAq9gXF13eyEHSTPo
FXGvsSq2u68vYTo7VxrUBvDozqH/5W/3mdJ0ktb66gNnheLoIejWooYR/Pe2Eaft
zS1ZkNjeVu1ngZdwDXjkbAdpSkcEqEO2+6tpj+rkICpWFqWnEnlgmoM/UGvF0k/2
ibcIF/kp9mXLnF3HVjLHL9/6y+lPF0rCKB4B+REVEW00kq0udjYhuVgoCUD+0vJ3
aE8xbnKkYW2KygEcg5pBx58Wg9ynrHKr1uDGEeokaSMNlE2I1hQ6dp0wm9MS7YZH
eIim+0B9Srub3yMJxRWevTVQOQ6Lr6Qj2cpP+qghO5TdoFvKhpaGBuJ4ZjfAjBl1
mq5Qw7iD3e9D4nmKNhOSCfqGNNjx3IdaVdjQ2YGMgDMm5QrDDjmRMIJs8FxAzDuH
ULJpnxLI0PINzMdBtqMVrsbTri/4AN4SQuDLrZgsho+w56gNKyoXWFidSED1TXeL
OYQW5MAINJsJ/FDxKisYJDdCCxWm3vGV9O5zNgJw6n9lefG/JjFNC0izfhRaRc5S
OXB4f1vpcX3vWB1pyJOnm+kubSCFzywv2603vtCKw1tb1r/Jkq8x7X4Pj9EAtwp6
5v4dlQFlVUhH4N9q75FewTc8a+fhbJZnyaS8Mt7Iid2uATXcSR4Z7SGGl1JcIIFg
z1LkQbPny5VpobLeNeRQ6UQjGor657r8MU/Ga3UpyIgo7KGxX7nPI46H7U7zzLHB
oUc61+++TMGbatoBDRuJs+KmVOe9/m7FJGm0p9o82q8L3DITA0EPBdmU8xwib6T1
D2wzictDjbnA0VZbNFZLgdm4BfiZ34MGySu5OqjesYsjtJ2z1IXR9Yg7pMBDDHV8
iA6dMxHqh8pIo7HTrimvnHPvzqR/+VrQgozmIT7D6WtrnjuQZYqjpeNBws+ZfSzS
u/QcotkIAcNl6tsGnxPzNDxf5QuQ65MQDp17ZxusaYzr5POw2wVcjUWAqapPrHOD
antXw7gCyZmbt1CUSqcqheXWAIoLgTqr4obEADN/uz/+3v9CNdskTerN/8ALLVP8
7cF+3///lweNhYPxah8TPAMOJBdSavMWgsaLXZ0Lcda2jUQ6E1uPVckpDb/3vk4l
UaAHQLPBxjG+0VYHJtZ+7GJxhmlpEv9gZyX9Xb75eIWVNfa19Xo2NOiDv86FqkJT
sXBFGY/381LrpiK/LzytqMZGBK0NEQcqQfkYy6ZjDvPXRBz9uDqjVfDuONVyFh8V
FzgY0H6jgr+23UsLq1y3Xi8cwrEApjsVeJE+ed8QAGwA29jNMswm0/hGzRQp7tDX
AM+QvAVJoOa/E0Y2Wy7EZ/1mB6r+C8eSBRDmEfRQRt4jT/9gUmnXbaC8DBwnb50N
O1Ta6CqL3hHcCLPri5R/v6WShsRTBL2Q9QO9+CCCg3C/+NHabyODbaE780J/eQ84
9h2gB6aKRmBI832lDRnC6Qx0SuLAYE5b7tbhIQGQMe5wv74OUfy7zFFX6RYIi/Hb
L01PN48hVqijJvU655gi6u/KTnFh1TphRbhmdp/CVC645R3O+QDx2kxmw7KnL+V+
Rc0j60CIEkUh0xHYBnpJ+EAi+alQptPZuGIIHFBSmkkym656a49lPiU9Gd/g+ZoW
dnm66ZIZCAAim6Specm9nUQfJvdpxnZEG9efmCfgY9zFIJ6rbAcjYt8P024f5/XT
ZUSMOqEZzgEvw6l/aZPZ990snDxWQBYGXLkGQYum9e7P+KoldMYXBSE0XDo/jFyH
875MnwzzZH329uaCofMMO/3c3DWx/ePa0jPQbMfL8YVxkNo7GChIbTalL4JZPPnv
xVvdQxpVWNAIYjCl3eVSE8LICQHxSHqycwiEwZppDqmqHF9emwmcushxv03glKTr
uTdHeD6gxSpxh/DT5ROZ6G3tS6/PNkvvp5rG68NPNB8vBP11HkgC69UnyarRwYQC
sA8U768uWuktMLzKMwr7OcL551wA2RNn3r0uv9EVT/53izki5ku27ZCQxLY4u6C0
j5se5UnWOhefDKBNaqPT8pXpajMBS9q4wnm1FwXj3swA6q6+Q6348F/iz/0y1nT/
Kq0M5TF5qdQMNJcN1Pm6vsAZiJ9NpV1cjKYPpQc2Rim13rbZQW88ZiXh7wRhLtJL
rhtzAPxHv7xtUWmpZbwVKIdWBBNUqCxgQgNrpJ+/kyz/E9jj6S+jwV7jqIvtTlFM
6Pb27XKv070z+BSyowHv3hvDrJRoh5M3QI5KOH6DFb49eX9Hcu9omO/TRpjFg2Kc
XO3n1UngAPle6s/nu2S7yTGkMwSa/Pim0aV421IROS8SfAXz/bXTIFFq+Y/n6upJ
Mt0mJGMwdd7IyFQradL9vSHCw27Nm2nWvh5NBASqd/0Jvtll//eck4iP3hs+kcuS
KAegs6TnKv/QUO3HYpE/AXeMAgc7WoJ5zUdcEh9VRqyu6YnPKfwDE2Khjow4Kigu
pt6qKA4zY265oo+0D7mo+i55ahgPfGNS4qDDHW9sc7BW45+MNTtKsuz2nELGVtwm
p53wMSVH61IoXuIsC5c1SBdmMyigbyMvv6V6apBHHhoJxPzJFaYa1kUw2h5F4CKL
lyLp3kaA1gE/DUW+nOHE2S07PyAM8numceqwZe7gNj+Hxxy4Rl8uq08jsZrUYkUh
ljIgMb4QJ62lGJEYkQbCkzJ4HuwfANpnH3wvZ+XCpFM92WO3biN89xHaWaszNOAM
BoqOGRpjIeWiTb7CuPcvtByqYmOByJpTCywjIwDUc2uzl6yuX6B5tL+L+NerX/ps
RaMBPYWbH72GhdZuUGxSLL6AIUpNREuigzyQlf0fjV7ZzxQxG25X79Tqb8StuX6Z
hhUc9VtaBsVDM8/IxSzxqqQqp9jWvyH+vCadAHdRCb5tgMW2THer0+G+GvETdMPX
sjGkrSzLFz//+i3jJeieLKUsJQCQIyuUY5P7vLIF0yvB1ANNuLBHTDeviGQZn3vg
cRFLSQlSP2ETBQfjZoUie9F9f+/kh45k10XJK5KBG7dgy9BggGTFcB/eL8eu8IKF
8ugJtHF2awHQpx4ri4bcSe0ogPU3iCHNpyIeQQOcypQBNaQokzVivZ4QNeMG/eQq
fpjzj86JbyyzDgBq5HjYv3T4UwZRIfy3mz8BzslNAIP1s3Jq5hQmYP9qNILgOzJC
ZHPtO977AKMSIqbDO91eiY2fE7efh+ZMSRQYRh0iom0hOJmgPRoZxvQwVNNz8OEK
P33xMkPVBMJ65PLORB7zNg==
`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
aVQ8b8ut11/vtxke/Q9xBgFbsilGsdyDNRu27vvrPAtzFVKGgLBg9/c8rfJeEs/Z
NnlpKzXlN+agjotXXl5FOKf3HsLJlpZ7DHwwkcsJbrTBF2v/RBbBf0y7Nf7RDtMI
QoZtrOOn8rEvblJ0QU18tJhI+EBW0GtekScHTkejtx8OjhPeD0HP0LQ+8bHL7+OV
02rtisw/15gVBUN6hVMWU71JnsHl5dzPCGLYiMllDw4RpZ+jSyE9/tX+NTJJcOf8
Msfiojr8uw3y04MqScxCg67aybWg7yNQA8vSfCkRCnaj2VMLZD1keaB3dAiCdNxv
vVhiufeyJdjv1dgV7Rss6g==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 2384 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
MoEzKz/2XY6KyZIkrxDQmHIzJ9l3C/J6GJGAIg64eYDwhdhobw7dwsdZX6dvlL1F
iRMlDKksq6O4r81yzx+QZZbDa0coQCje/Z5XvgfrEHFRj7ed0tPsAKYykMfY6oDp
9FzKRaxrGUBvBQJ0cWj1lVlJ6ORH9EUh93yXP93+JeT48CV2ZJHEOfyGJoDU8ER6
IoUsEvUtQfeQhFS3STeXS0rN4cx/Ml0lLP9ciIr1fIjXTepFzEzREHHI906g8ZtA
s11Xa9F9pFkjVv/qxqQgS/um4l1sCvGwsoOfxNxiv1LaE9QBjijB4ZTkceqAogMQ
ub38kEFV7+4FrmXKI0pdEQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 4672 )
`pragma protect data_block
XtAGzG/1Tjn0ReVjLUCgp3v4aFgd1z5XBjEO/***************************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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
Iw+Au99/NA6ELwg8FZZ5zH7kw4StQJ9yxWCu6Sv2psWDQ+Ij8B/Mu0hDcK9nQrwM
XvH+muO7jPGDVFpVm8/KRVnlUvaCq5gGjgnAhGd/jARNpGT+85qmDjTJ3RKIB1Kl
uUOkp5ozBNHDhFcINcrIGVd4TygAjJQ/+IEQgq9sLRMvhIXSisNUoGl13d78NEAj
bMiemauRD9sy/A50FneZ9bVMNncKtb8PILpoTwwtkNFWadbx1NXEuDuXzGX8FDTE
JkPSrRCVpiW0NOxRV2lIp02buQxuwFj9n8FNj1kc0LRX5H9UW9jS+SSwSsdF+t70
9l37JikGdgsc0J3P/ZMXSA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 11552 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
BU0Hly6fvZsqWfwqik3Ikor/ukVPUjgTBG2d4JDrvhajN3j4iQpXh26E8cL7MFO2
H2H717nihJT6+oGkWX9HQoNIiNvZdfeSLv0pgFC3mAJ3vf6P/RcJeVn92WdZefHj
lmK1eggyIvjrV0CtDu6izbkvETxb+zHY/p8e6NvTLnSjdLli0/68NgJUI7LNxINa
NLIRgU2vEb4BgvD6DUJ97rF8fba257wRbEd/Lw9vbfupCzuIATQM4JkxwrdHmaWV
ny8CLbtozkwXP5PCTO8SIn6aAJ8JaU9CrX7xmGuIGPrPiYQqh3ZjFWUlyYdkg6+G
ScrIGpz/jUWwMn0ApbRSxQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 31328 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
NcCvWQE6LayGO92agRmFcWFZ3eGaJErgvIcLOvYSx6aZ+ndAQ0zPpe2hEwjP4Bf2
OzYIwFC9YM/uASOz5XAYkErtz4y+ZIMQNSZ6fsoOmUvbKgC3a6t+io+UJnEnB3Rp
nswazR0ySEoYG9w8Ip6uKj4Xegt0F3aT6cSBG49ue0m2GUgi1AVvx/k1jOUGjiW3
54xoncuy4hl1FLIB1NLHZ7Lz9lx6ndqfGfxejJqtmsE0BQvIGfqgp/erKAc2QmO6
fm2OBC2czc4xY5MjFvPOgSW65H6PtrrdpVEIJBoBOID0wftNhwrVHbDkLp+6EotW
Ah5KZe2qTV56YyMrXzJSZQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 6640 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
BsQl66uT41YlrYq0dr4bq+00LS73uTICIFizP8osCrPF9QzIbtU9LeCMyHmZLdGf
WChFf4K3j8SAsmcPD8PpSvqFC5zMgHlKcXdI/Kdj2Q/QHpv76H4BXUkatMhr42u0
FXWlNXqldmiQX6HlDqORHOyuFS7i800KM/9afqKIUMlpEVESx7pLCwC1segJLJCE
0YHZkev5CSN0Mb6ld+sv4gQEqEEodgskKEsjig7VJi+SYEq22pAhyAoBVyAR2mQD
TYd1Xqa7GOeNOHCvtfslXfndj5UMFdioSLYPHWIWt6mk9qe32EvwwaSKpIYzgu3M
wf0vG7QKeUKoUmqt87ItVQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 4960 )
`pragma protect data_block
LHHT4Yj/jmTHb14CRwzgpiTTzxrdHs575czKn5dngL4QxZsNjYqGm94TQE+JwyhC
lYq3J3bR7H5PT9yjEWRJcAZTjxE70mkcg/wfIQfK+nzlbvC/A4D/d9exz/JUlolv
pQT3ET5GOBDghb+**************************+4/no6aE9X786IEClAX6EaO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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
GqANfZ5B9AvfoykzIXo3n3GZosJwtyWbIxMrk6LbWyShAGWB3VLeZ+9t5qaD5DVg
U02UrSoGQT0TUkycTd8sOMzWg0OuhTs/4Jz9NDP5Hg79vZNtw/eNcWsFtEfZ2mKl
xYsnknzEMpOFsvEJPlgrr2q6ofI2QbuNR+TtMJpeVLui8wIXQ8m3eQsfEbbbzmk6
YIFYcmAH7iCrVb+y9hiie7s2FK99tMwE0VTBJgHRt2xDHbUBu8PJVqvRj6NN6ONm
FE6Wja4J2aFHWAuigi6JTNF9zKcBreQTwUihHpNxqEMHQR4NHVpa+WgyaMqceTdL
3y+PCDbLhjJHRg3jT3J9ow==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 6288 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
lBQKlqLhZl9xo+oDwC9wZsNgbAC+DlydD4A8m/Lit/D6sfbY22Qm7DnPpuHXRbZM
NC1KiUzsaelpemN0zUc57T97XxwXlwntklMbI4Er4FP0VFUT4VoyCiIWbTEwT3g7
KznjskmtZ8QaQXGDcxNqmT+bdQL3ZQihI/l/vZ9fKYeP5frh7QLzY82I40IUs9Q/
i8VQegXZjabl0MCN2lvEPlMiya2r4nB26zgeGyLVqMsPF4kXK4CJmTLJI6ihJVqw
30tvcS08q8KcEoCSfWYL+3ZOJX5XljFRyCr73Y+aJb/VEdYIthrVAlYhvSZTkx9O
3sw57wNeJQ9xnwHnxKjeUg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 2496 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
Q3VHyDJmSVS2xTDztxg4CsP6IANei2zY7nNMvaN+uNCRkyuQxjMcQJ+cV8sYMWdf
iWMIeji517QceiM52t01Z8aZjQCio45bqbNOSHb+Q9Knx7rWrJZQLmSoQHklXqPo
e+ZwhrAwzF1+I+x0hZAB6FMbnbTSk0Y1uV33pTN0Utg/cuwF2l0iaJky15/uoQGT
P1cGYVIiURL/e3D0rNnPgdNldIi8up0N2luCoOm+7X9u3Cq3WuGXgULTEjTNFsn0
97DNnt/xZyXECUy0BhCHPx8hEPu3XOmhUcT37yolPkaLwIbWzcy5h1biN3jlZWgt
aZUH8ji4dJmEqnwx7LooSQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 54720 )
`pragma protect data_block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*****************************+5yfx1qrhRD3cZqeUUalHLBiZFC0OIAksh3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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
eimOMfmfdAcQDJkxzYIiuDjzdgtgNMQYz5bM8PMPbB946BQdA1RpDXe54XLDCm1k
/kVrZhGsiH/Hdxs1KS0lrNZFBaWUEbzkS/p4YY1SlC078YlImPM+WP00E4jbSvgx
nax2bKS6LWIcNRe8lgm/zCTyliKX+/cSF1ToflJNsAsgPSHqxcQ2Mpt9xhgVkVGM
LOCIBZQ6fYpi8hW9HKa67UHcYwmbp+T9Qtv65M5Mpdkead1OC4+RycOWcniC2N9q
WefvLmqY7PU9Pmwrh0rFhiYpE16HS/mPrP1mqmTgXurzMs5KWU1/QeM6BeLRbyLe
fVYL6wJPfvFknSEp36hjpg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 9568 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
iL97+qrfhQLLSlssC/4KSdLlsiUoJOmK1WAH1iXHd+mVzg6RBW8IBq46SM93ehxq
Iu1+upeK/1No8IYHNdZDW6SzfSuJuKATtJ1LPR23tREFMjarFG4Xfq0qX8ubYMo5
li9lcFIvSDpptp/JqobVznqj9HUKO+RvAsvL1fkVt8HVQyMp2N+cpvfqQUr3ob0/
KUk96M77nBk3gwpSQ8Ym5mUqe16Fhz9PN9fyWhFXFcZQ2FnmAy1e72cmfG8xlpvy
RJ0MP3KNfIRAxck+kAgWpqVNMrlsa2K0rKIwU+Op+jpNWuW6Rbey6BA83QeEpR1t
KgwoTRJl13QSaRVyQkEChQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 26016 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
aUR+/RfvLjMaJOw4xaWcD7hrfDV27UA437aAzYAGDcanQLs3LQl6Yj871UAzddbn
juuj+g2bQ8HHX16ZpUiZSA4STer2NLlGYLOzB8vufxHX9veu5ywMZRiHPW1t3jWD
9p6Xfy/Tu6d+H+c5tew3n1YSivMy+EYQozydT2MmynTSiGvoUmCiJ7D2kakPec6s
CFEA9kpBSkleCA/0BPIzOIzLm0bPEnpbnAw/JmuZTTg/FcypfjTwXxVAV4DpUzH/
yCBjaFzSQMEEChZq6U4cv4GrCQPEqs5wpdd3fQRT3X/bsGAAa8Iu95174Kuao5Zq
jyeWKejxzC5uzQ0exlRukA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 28624 )
`pragma protect data_block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**************************/HUhKFtSJ/6YVqrGDMXSCP2Lb/fBfZqzbg+bgD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`pragma protect end_protected

//pragma protect end



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_csi2_rx) #(
    parameter tLPX_NS = 50,
    parameter tINIT_NS = 100000,
    parameter tCLK_TERM_EN_NS = 38,
    parameter tD_TERM_EN_NS = 35,
    parameter tHS_SETTLE_NS = 85,
    parameter tHS_PREPARE_ZERO_NS = 145,
    parameter NUM_DATA_LANE = 4,
    parameter HS_BYTECLK_MHZ = 187,
    parameter CLOCK_FREQ_MHZ = 100,
    parameter DPHY_CLOCK_MODE = "Continuous",  //"Continuous", "Discontinuous"
    parameter PIXEL_FIFO_DEPTH = 1024,
    parameter AREGISTER = 8,
    parameter ENABLE_USER_DESKEWCAL = 0,
    parameter ENABLE_VCX = 0,
    parameter FRAME_MODE = "GENERIC",    //1-ACCURATE, 0-GENERIC
    parameter ASYNC_STAGE = 2,
    parameter PACK_TYPE = 4'b1111
)(
    input logic           reset_n,
    input logic           clk,				//100Mhz
    input logic           reset_byte_HS_n,
    input logic           clk_byte_HS,
    input logic           reset_pixel_n,
    input logic           clk_pixel,
    // LVDS clock lane   
    input logic           Rx_LP_CLK_P, 
	input logic           Rx_LP_CLK_N,
    output logic          Rx_HS_enable_C, 
	output logic          LVDS_termen_C,
	
    // LVDS RX data lane
    input logic  [NUM_DATA_LANE-1:0]      Rx_LP_D_P, 
	input logic  [NUM_DATA_LANE-1:0]      Rx_LP_D_N,
    input logic  [7:0]                    Rx_HS_D_0,
    input logic  [7:0]                    Rx_HS_D_1,
    input logic  [7:0]                    Rx_HS_D_2,
    input logic  [7:0]                    Rx_HS_D_3,
    input logic  [7:0]                    Rx_HS_D_4,
    input logic  [7:0]                    Rx_HS_D_5,
    input logic  [7:0]                    Rx_HS_D_6,
    input logic  [7:0]                    Rx_HS_D_7,
    // control signal to LVDS IO
    output logic [NUM_DATA_LANE-1:0]      Rx_HS_enable_D, 
	output logic [NUM_DATA_LANE-1:0]      LVDS_termen_D,
	output logic [NUM_DATA_LANE-1:0]      fifo_rd_enable,                            
	input  logic [NUM_DATA_LANE-1:0]      fifo_rd_empty,
    output logic [NUM_DATA_LANE-1:0]      DLY_enable_D,
	output logic [NUM_DATA_LANE-1:0]      DLY_inc_D,
	input  logic [NUM_DATA_LANE-1:0]      u_dly_enable_D, //user control the IO delay
	input  logic [NUM_DATA_LANE-1:0]      u_dly_inc_D, //user control the IO delay

    //AXI4-Lite Interface
    input                 axi_clk,
    input                 axi_reset_n,
    input          [5:0]  axi_awaddr,//Write Address. byte address.
    input                 axi_awvalid,//Write address valid.
    output logic          axi_awready,//Write address ready.
    input          [31:0] axi_wdata,//Write data bus.
    input                 axi_wvalid,//Write valid.
    output logic          axi_wready,//Write ready.
                          
    output logic          axi_bvalid,//Write response valid.
    input                 axi_bready,//Response ready.      
    input          [5:0]  axi_araddr,//Read address. byte address.
    input                 axi_arvalid,//Read address valid.
    output logic          axi_arready,//Read address ready.
    output logic   [31:0] axi_rdata,//Read data.
    output logic          axi_rvalid,//Read valid.
    input                 axi_rready,//Read ready.
	
    output logic          hsync_vc0,
    output logic          hsync_vc1,
    output logic          hsync_vc2,
    output logic          hsync_vc3,
    output logic          vsync_vc0,
    output logic          vsync_vc1,
    output logic          vsync_vc2,
    output logic          vsync_vc3,
    
    output logic          hsync_vc4,
    output logic          hsync_vc5,
    output logic          hsync_vc6,
    output logic          hsync_vc7,
    output logic          hsync_vc8,
    output logic          hsync_vc9,
    output logic          hsync_vc10,
    output logic          hsync_vc11,
    output logic          hsync_vc12,
    output logic          hsync_vc13,
    output logic          hsync_vc14,
    output logic          hsync_vc15,
    output logic          vsync_vc4,
    output logic          vsync_vc5,
    output logic          vsync_vc6,
    output logic          vsync_vc7,
    output logic          vsync_vc8,
    output logic          vsync_vc9,
    output logic          vsync_vc10,
    output logic          vsync_vc11,
    output logic          vsync_vc12,
    output logic          vsync_vc13,
    output logic          vsync_vc14,
    output logic          vsync_vc15,
    
    output logic [1:0]    vc,
    output logic [1:0]    vcx,
    output logic [15:0]   word_count,
    output logic [15:0]   shortpkt_data_field,
    output logic [5:0]    datatype,
    output logic [3:0]    pixel_per_clk,
    output logic [63:0]   pixel_data,
    output logic          pixel_data_valid,
`ifdef MIPI_CSI2_RX_DEBUG
    input  logic [31:0]   mipi_debug_in,
    output logic [31:0]   mipi_debug_out,
`endif
`ifdef MIPI_CSI2_RX_PIXEL_SIDEBAND
    output logic [15:0]   pixel_line_num,
    output logic [15:0]   pixel_frame_num,
    output logic [5:0]    pixel_datatype,
    output logic [15:0]   pixel_wordcount,
    output logic [1:0]    pixel_vc,
    output logic [1:0]    pixel_vcx,
`endif
    output logic          irq
    
);

logic [7:0] RxDataHS_0, RxDataHS_1, RxDataHS_2, RxDataHS_3, RxDataHS_4, RxDataHS_5, RxDataHS_6, RxDataHS_7;
logic RxValidHS_0, RxValidHS_1, RxValidHS_2, RxValidHS_3, RxValidHS_4, RxValidHS_5, RxValidHS_6, RxValidHS_7;
// logic [NUM_DATA_LANE-1:0][7:0] RxDataHS;
logic [NUM_DATA_LANE-1:0] RxValidHS, RxSyncHS;
logic RxUlpsClkNot, RxUlpsActiveClkNot;
logic [NUM_DATA_LANE-1:0] RxErrEsc, RxErrControl, RxErrSotSyncHS;
logic [NUM_DATA_LANE-1:0] RxUlpsEsc, RxUlpsActiveNot, RxSkewCalHS, RxStopState; 

generate
if (NUM_DATA_LANE == 1) begin
// assign RxDataHS[0] = RxDataHS_0;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = 1'b0;
assign RxValidHS_2 = 1'b0;
assign RxValidHS_3 = 1'b0;
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end             
else if (NUM_DATA_LANE == 2) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = 1'b0;
assign RxValidHS_3 = 1'b0;
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end
else if (NUM_DATA_LANE == 4) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
// assign RxDataHS[2] = RxDataHS_2;
// assign RxDataHS[3] = RxDataHS_3;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = RxValidHS[2];
assign RxValidHS_3 = RxValidHS[3];
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end
else if (NUM_DATA_LANE == 8) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
// assign RxDataHS[2] = RxDataHS_2;
// assign RxDataHS[3] = RxDataHS_3;
// assign RxDataHS[4] = RxDataHS_4;
// assign RxDataHS[5] = RxDataHS_5;
// assign RxDataHS[6] = RxDataHS_6;
// assign RxDataHS[7] = RxDataHS_7;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = RxValidHS[2];
assign RxValidHS_3 = RxValidHS[3];
assign RxValidHS_4 = RxValidHS[4];
assign RxValidHS_5 = RxValidHS[5];
assign RxValidHS_6 = RxValidHS[6];
assign RxValidHS_7 = RxValidHS[7];
end                              
endgenerate

`IP_MODULE_NAME(efx_dphy_rx) #(
    .tLPX_NS              (tLPX_NS),
    .tCLK_TERM_EN_NS      (tCLK_TERM_EN_NS),
    .tD_TERM_EN_NS        (tD_TERM_EN_NS),
    .tHS_SETTLE_NS        (tHS_SETTLE_NS),
    .tHS_PREPARE_ZERO_NS  (tHS_PREPARE_ZERO_NS),
    .HS_BYTECLK_MHZ       (HS_BYTECLK_MHZ),
    .CLOCK_FREQ_MHZ       (CLOCK_FREQ_MHZ),
    .NUM_DATA_LANE        (NUM_DATA_LANE),
    .ENABLE_USER_DESKEWCAL(ENABLE_USER_DESKEWCAL),
    .DPHY_CLOCK_MODE      (DPHY_CLOCK_MODE)
) dphy_rx_inst (
    .reset_n              (reset_n),
    .clk                  (clk),				
    .reset_byte_HS_n      (reset_byte_HS_n),
    .clk_byte_HS          (clk_byte_HS),
    //To LVDS clock lane   
    .Rx_LP_CLK_P          (Rx_LP_CLK_P), 
	.Rx_LP_CLK_N          (Rx_LP_CLK_N),
    .Rx_HS_enable_C       (Rx_HS_enable_C), 
	.LVDS_termen_C        (LVDS_termen_C), 
	
	//ULPS clock
	.RxUlpsClkNot         (RxUlpsClkNot),
	.RxUlpsActiveClkNot   (RxUlpsActiveClkNot),
	
	//To LVDS data lane 0
	.Rx_LP_D_P            (Rx_LP_D_P     ),
	.Rx_LP_D_N            (Rx_LP_D_N     ),
	.Rx_HS_D_0            (Rx_HS_D_0     ),
	.Rx_HS_D_1            (Rx_HS_D_1     ),
	.Rx_HS_D_2            (Rx_HS_D_2     ),
	.Rx_HS_D_3            (Rx_HS_D_3     ),
	.Rx_HS_D_4            (Rx_HS_D_4     ),
	.Rx_HS_D_5            (Rx_HS_D_5     ),
	.Rx_HS_D_6            (Rx_HS_D_6     ),
	.Rx_HS_D_7            (Rx_HS_D_7     ),
	.Rx_HS_enable_D       (Rx_HS_enable_D),
	.LVDS_termen_D        (LVDS_termen_D ),
	.fifo_rd_enable       (fifo_rd_enable),
	.fifo_rd_empty        (fifo_rd_empty ),
	.DLY_enable_D         (DLY_enable_D  ),
	.DLY_inc_D            (DLY_inc_D     ),
	.u_dly_enable_D       (u_dly_enable_D),
	.u_dly_inc_D          (u_dly_inc_D),	                   
	//To CSI2 lane 0      
    .RxUlpsEsc            (RxUlpsEsc),
    .RxUlpsActiveNot      (RxUlpsActiveNot),
    .RxErrEsc             (RxErrEsc),
    .RxErrControl         (RxErrControl),
    .RxErrSotSyncHS       (RxErrSotSyncHS),
    .RxDataHS_0           (RxDataHS_0), 
    .RxDataHS_1           (RxDataHS_1),
    .RxDataHS_2           (RxDataHS_2), 
    .RxDataHS_3           (RxDataHS_3),
    .RxDataHS_4           (RxDataHS_4), 
    .RxDataHS_5           (RxDataHS_5),
    .RxDataHS_6           (RxDataHS_6), 
    .RxDataHS_7           (RxDataHS_7),
    .RxValidHS            (RxValidHS), 
    .RxActiveHS           (),
    .RxSyncHS             (RxSyncHS),
    .RxSkewCalHS          (RxSkewCalHS),
    .RxStopState          (RxStopState),
    //LPDT mode only supported in DSI
    .RxLPDTEsc            (),
    .RxValidEsc           (),
    .RxDataEsc_0          (),
    .RxDataEsc_1          (),
    .RxDataEsc_2          (),
    .RxDataEsc_3          (),
    .RxDataEsc_4          (),
    .RxDataEsc_5          (),
    .RxDataEsc_6          (),
    .RxDataEsc_7          ()
);

`IP_MODULE_NAME(efx_csi2_rx_top) #(
    .HS_DATA_WIDTH         (8),
    .tINIT_NS              (tINIT_NS),
    .CLOCK_FREQ_MHZ        (CLOCK_FREQ_MHZ),
    .NUM_DATA_LANE         (NUM_DATA_LANE),
    .PACK_TYPE             (PACK_TYPE),
    .AREGISTER             (AREGISTER),
    .ENABLE_VCX            (ENABLE_VCX),
    .FRAME_MODE            (FRAME_MODE),
    .ASYNC_STAGE            (ASYNC_STAGE),
    .PIXEL_FIFO_DEPTH      (PIXEL_FIFO_DEPTH)
) csi2_rx_top_inst (
    .reset_n              (reset_n),
    .clk                  (clk),				
    .reset_byte_HS_n      (reset_byte_HS_n),
    .clk_byte_HS          (clk_byte_HS),
    .reset_pixel_n        (reset_pixel_n),
    .clk_pixel            (clk_pixel),
    .axi_clk              (axi_clk),
    .axi_reset_n          (axi_reset_n),
    .axi_awaddr           (axi_awaddr),
    .axi_awvalid          (axi_awvalid),
    .axi_awready          (axi_awready),
    .axi_wdata            (axi_wdata),
    .axi_wvalid           (axi_wvalid),
    .axi_wready           (axi_wready),                        
    .axi_bvalid           (axi_bvalid),
    .axi_bready           (axi_bready),
    .axi_araddr           (axi_araddr),
    .axi_arvalid          (axi_arvalid),
    .axi_arready          (axi_arready),
    .axi_rdata            (axi_rdata),
    .axi_rvalid           (axi_rvalid),
    .axi_rready           (axi_rready),
    
	.RxUlpsClkNot         (RxUlpsClkNot),
	.RxUlpsActiveClkNot   (RxUlpsActiveClkNot),
    .RxErrEsc             (RxErrEsc),
    .RxClkEsc             ({NUM_DATA_LANE{1'b0}}),
    .RxErrControl         (RxErrControl),
    .RxErrSotSyncHS       (RxErrSotSyncHS),
    .RxUlpsEsc            (RxUlpsEsc),
    .RxUlpsActiveNot      (RxUlpsActiveNot),
    .RxSkewCalHS          (RxSkewCalHS),
    .RxStopState          (RxStopState),
    .RxSyncHS             (RxSyncHS),
    .RxDataHS0            (RxDataHS_0),
    .RxDataHS1            (RxDataHS_1),  
    .RxDataHS2            (RxDataHS_2),
    .RxDataHS3            (RxDataHS_3),
    .RxDataHS4            (RxDataHS_4),
    .RxDataHS5            (RxDataHS_5),
    .RxDataHS6            (RxDataHS_6),
    .RxDataHS7            (RxDataHS_7),
    .RxValidHS0           (RxValidHS_0),
    .RxValidHS1           (RxValidHS_1),
    .RxValidHS2           (RxValidHS_2),
    .RxValidHS3           (RxValidHS_3),
    .RxValidHS4           (RxValidHS_4),
    .RxValidHS5           (RxValidHS_5),
    .RxValidHS6           (RxValidHS_6),
    .RxValidHS7           (RxValidHS_7),
    
    .hsync_vc0            (hsync_vc0),
    .hsync_vc1            (hsync_vc1),
    .hsync_vc2            (hsync_vc2),
    .hsync_vc3            (hsync_vc3),
    .vsync_vc0            (vsync_vc0),
    .vsync_vc1            (vsync_vc1),
    .vsync_vc2            (vsync_vc2),
    .vsync_vc3            (vsync_vc3), 
                          
    .hsync_vc4            (hsync_vc4),
    .hsync_vc5            (hsync_vc5),
    .hsync_vc6            (hsync_vc6),
    .hsync_vc7            (hsync_vc7),
    .hsync_vc8            (hsync_vc8),
    .hsync_vc9            (hsync_vc9),
    .hsync_vc10           (hsync_vc10),
    .hsync_vc11           (hsync_vc11),
    .hsync_vc12           (hsync_vc12),
    .hsync_vc13           (hsync_vc13),
    .hsync_vc14           (hsync_vc14),
    .hsync_vc15           (hsync_vc15),
    .vsync_vc4            (vsync_vc4),
    .vsync_vc5            (vsync_vc5),
    .vsync_vc6            (vsync_vc6),
    .vsync_vc7            (vsync_vc7),
    .vsync_vc8            (vsync_vc8),
    .vsync_vc9            (vsync_vc9),
    .vsync_vc10           (vsync_vc10),
    .vsync_vc11           (vsync_vc11),
    .vsync_vc12           (vsync_vc12),
    .vsync_vc13           (vsync_vc13),
    .vsync_vc14           (vsync_vc14),
    .vsync_vc15           (vsync_vc15),
    .vc                   (vc),
    .vcx                  (vcx),
    .word_count           (word_count),
    .shortpkt_data_field  (shortpkt_data_field),
    .datatype             (datatype),  
    .pixel_per_clk        (pixel_per_clk),
    .pixel_data           (pixel_data), 
    .pixel_data_valid     (pixel_data_valid),
`ifdef MIPI_CSI2_RX_DEBUG
    .mipi_debug_in        (mipi_debug_in    ),
    .mipi_debug_out       (mipi_debug_out   ),
`endif 
`ifdef MIPI_CSI2_RX_PIXEL_SIDEBAND
    .pixel_line_num       (pixel_line_num   ),
    .pixel_frame_num      (pixel_frame_num  ),
    .pixel_datatype       (pixel_datatype   ),
    .pixel_wordcount      (pixel_wordcount  ),
    .pixel_vc             (pixel_vc         ),
    .pixel_vcx            (pixel_vcx        ),
`endif 
    .irq                  (irq)
);


endmodule


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
RY8sIOmbZVlolzL/hrWVGEaaerakbFrtxZ4v+WCcSrUhH3qYmIVl79H3gm053APA
ZqC9rnGbP+1nOn+z2n3sivkRz0t0C+uc/3tCJALSozHkZiiQ04OEfy0pTYfQg2KV
F4HYwdYkcMNhgQNPD+gQUaU9O9gKGG3pmbbYl+a/McKDh23MXdfSrHsB35X1r+d5
8yrRqMF7cqS6GLkRGaToJyMy51IvK+SnGXc1nzbSBZzoX8LkRjAhy6C9bKgQp5As
dhP8HgrTPy2qdMFR/62ZWb87UN4L1MTTECCNm/eUcldKtxqminRpA3+d2fsiU9Se
ID9JtoyQUJUc1YAeJZdgbg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 3568 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
BwhBA8ro8d2HeXuAyfZlTlmtWujN5qqv3SjccRgxhpDH3aDldBsjI5BsxivOKiND
p8J0jqg8kykQNux2pGnPfRkXRkUavsBNOoiLmMfzpkkHqNE6WGuT4IH5NIFPkTG/
2rrv7kx8X1c3IF63ZKJTiChqfQTfpkeNeSlDj7iFayU51BX/Ab5pxnrllRfiI9Bf
3Wop8LGQ/5phhUZUZfWVBQGIIpyXKrmPM4KrtGJljShoZwMRRckljd2l+VDIILAM
IS+JQPrUkpeSm8NQmY/lApl/E9+ocjQmp1lF3tavARnLmxYRE8mnPua74FUSkjkF
qFKD4NZfzBmNvoNfEn3mKQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 10032 )
`pragma protect data_block
8BmrolvJMnZhP1qsvcO7EBaSVmIqmXljjGU08QlWktoHHRhLALETkwoFaSSEi37m
nIkD+ilw76ESnbjLzywWd4DoB44AX/A9XbOT1nkTk7UY9a7GKd93jZKzIADlIJfB
cjx0Ase6FP9g8ChnE+1CCLNDGvbkny/*********************************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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
V1KCFszC7F7YUfhPO4fkQ9AhnDhgGEgS1ImBkcVCNwHbH8SVvSz686QlpqgNRBsw
0cS5dzRTGH6DQ3imZ1REgJHWoMZtMYqNXFwRk3KX6besKDD+8Q6MvFuJUIeg5iks
V0OLbUTxE4vLZW9T62VTQodkSELaPDFDYpFMpUAH42uLZ/tmy55DqhY7/S3FNKOA
Ed5w5xQTFgmyhmLNJwV0++m+GufI3WMFEQuaqbzZFbE1nUODHAf9ZRdndJVbnpbZ
tpKR4blNV2ybTAJ3Ng5+NNIbj8zC0PMF8aMk6AKFJg5IS4pjMimZyBM3S2qupj3d
xQWSGFStl80+k0v+NQLABg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 8800 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
QOt9c3pVil6Z4vbOZnW9WhmXTXwoGKfYRxR/FPzPOo81oYDBMlRDdojua8L7Y+9x
KNOczZNmORas9rIUwiZ4MW3aJVI5TxY4JjO/EpZYM4imYpIZJTZW61TaSxRxvEmq
eyXasPc4W4MOR4O7Q0+Eg/HkOLO32wu/5NE1VAMU1R+ge+6NWZi+1qWHOUzWmlav
BEKSAZCns/NKwLAdheCnhWPhspEZ0tzbg3vLSsjQKOozEnIn3eVAjFPd3pumzLdB
N0bwlbQRZSps2x5CjSXGbrmdrP2upC4NkjUDt7O4ruj+S8RUbs6SsBXWjYVpNMrd
75vVPVz/cV5XhlfwFC9LTw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 14000 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
h5GujbpQNZZptuI/owmnZfdkzGQTPL55SNIAcuEc6KrUaT2b9Mp05aKj9Vi8eY8H
k6UbMeyTcRUuNRHHfluL5f9Vy9HzEd+qhd8PasmFkSX2fvbCThWd4koOBcOQO3Um
FDm8AYciSyfPdaqRP6YqL2unale6sCIdBjJduk6XCMjKCDLwS6/araxbQrckrRrC
I7u6JA5kR0x0uO2h30mrJOktwQ7LL0Wwu6KJEr2eyQW4XDY3GAI760XJtH0d67XM
z02ydCnDBSIXI7qa5A1iojJMbLwCo/aYN+KWbaFTOF2emso4Yc1bJA0xBOpzD4Gk
cUun3O4z4SsVs29xQzDobA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 832 )
`pragma protect data_block
Kg732p7TPXvLckZ7NGOYzNDr2z3fkCNfetYQD1sLjybRSGUQu0gA/wWYLp8m+fT2
XNbyE+Z7hOTiOBTqcC2ebDDqLs3l+MBwPRLIy6kEAfr3gQAyuF83OjhiAwQJD4ic
8jJwvDOdhVaFmD1XfsFuBohZrdL2u6UmB9yO9kwJ/6qZ7aUKuyuaBA3aRqlIKM4a
BwHtbVeMxEE/BAvteslwDjrDyz292R65cZC7IdvNJDRoVtsNNwcBKsHwL07EVLig
RY0fkgihqNSLkQf4XTKm6gFy3qoJrSFiW8OacOUJND04MJf5urZHw7z/VE6HSYtn
MGoQOZesTIYCHNp5dHxwjBWajfZqkeeGhvSADLPAVrevcrxZxKMh/i+gTaJHsO0a
Lze9ZM/uBYzGpVelYI9WbZPAN2jeKdyWlRNC2y7qjgj+zXK452Wc5nvTbBFwru/+
l/eREy7L8Ug8CIlbwA14zPP2ELlk12Fpo+OyI14wkmvxdyJstvtkOE9lHkdTpRby
73AWn8uOrCBTbWr+sln9r1zeJlFUbgGaRuhH4BMJ7JLu3pD2gPneC/bHv1sXh71v
m6kkTv0qUE2bd5MmmULsy5AbU6vyHvcwBo/Hpe0DGxfvbwGgImh7lttmbPR1ugvw
cey0b308k6xjDk07dBhQkgVuC3RjD+8s58x/OC4v8hGP/bRl1R2wHD7e69uWUFPJ
vIuZ+06a1yOk7QJGfZttjpArwbo3aRoZRcTyaUR8ZnzkAcs/GZRw/x9VIcYZoe29
JSMJCQHpQQRb0Wkedn7eyxsFKOl+amTvDjlqjovoTc1mmiIYjbzNkXNVZC2Z14Q1
CGUNOWEjQjRNBlKRXYZckN/xzTpFLvRLIUm0gh/8ll3HiFEMUnP9gNvvWQ/mN1Oo
pqOrK3L02D4yQ4q061tsxl5/dFhBz0oI253EzwqdM+9uJ9Z+VLmno18Fqbn6Xi+o
rEV9ki0V0BwD+TkF984/prM8luQzXfIIIg+PRGbRpbaggSQe5IJIbfw2txJEAjI6
NIAHAGks2rL5kNS2ZMsCfVuzXCi2C30oJ5g6PGflPgeZjMFtR7+kqSJAcbByoPMY
T9mvFPbiyEQYBq3HVbu5uA==
`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
dizfEt0359DzVXtVfNKsL5qq1n+uSWDpVU3+95cQByDEwQe1y2nj+TATv2hlx9w3
DluXsqd1SLUq2WKg6+HPkbTo05dM1YWI/ghQOrrlKI+jph9ImbmfelRqgJt5uGNI
wnATpg6X2sxa/byTZxPLdVNkLFrPNRia1zqAvY4e7Zv3DtP7tKX7PHXTwVVy9ECW
f0ii+3mkSNAMf33WR6J7zhloQPIey8OF8WN9GNplwWnLguL7wPgs9leqH5b/s39g
IehYcJdvk2juJHglqWuBSGY9MF9PiXcrkgdhUJ+DqbL6vDhZBTTYcuNWvzGC7FI+
Jgz7Tkr1fYWjbDFnzMHJsg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 6336 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_top) # (
    parameter FAMILY             = "TRION",       // New Param
    parameter SYNC_CLK           = 0,
    parameter BYPASS_RESET_SYNC  = 0,             // New Param
    parameter SYNC_STAGE         = 2,             // New Param
    parameter MODE               = "STANDARD",
    parameter DEPTH              = 512,           // Reverted (Equivalent to WDATA_DEPTH) 
    parameter DATA_WIDTH         = 32,            // Reverted (Equivalent to WDATA_WIDTH)
    parameter PIPELINE_REG       = 1,             // Reverted (By default is ON)
    parameter OPTIONAL_FLAGS     = 1,             // Reverted
    parameter OUTPUT_REG         = 0,
    parameter PROGRAMMABLE_FULL  = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_FULL_ASSERT   = 27,
    parameter PROG_FULL_NEGATE   = 23,
    parameter PROGRAMMABLE_EMPTY = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_EMPTY_ASSERT  = 5,
    parameter PROG_EMPTY_NEGATE  = 7,
    parameter ALMOST_FLAG        = OPTIONAL_FLAGS,
    parameter HANDSHAKE_FLAG     = OPTIONAL_FLAGS,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter WADDR_WIDTH        = depth2width(DEPTH),
    parameter RDATA_WIDTH        = rdwidthcompute(ASYM_WIDTH_RATIO,DATA_WIDTH),
    parameter RD_DEPTH           = rddepthcompute(DEPTH,DATA_WIDTH,RDATA_WIDTH),
    parameter RADDR_WIDTH        = depth2width(RD_DEPTH),
    parameter ENDIANESS		      = 0,
    parameter OVERFLOW_PROTECT   = 1,
    parameter UNDERFLOW_PROTECT  = 1,
    parameter RAM_STYLE          = "block_ram"

)(
    input  wire                   a_rst_i,
    input  wire                   a_wr_rst_i,
    input  wire                   a_rd_rst_i,
    input  wire                   clk_i,
    input  wire                   wr_clk_i,
    input  wire                   rd_clk_i,
    input  wire                   wr_en_i,
    input  wire                   rd_en_i,
    input  wire [DATA_WIDTH-1:0]  wdata,
    output wire                   almost_full_o,
    output wire                   prog_full_o,
    output wire                   full_o,
    output wire                   overflow_o,
    output wire                   wr_ack_o,
    output wire [WADDR_WIDTH :0]  datacount_o,
    output wire [WADDR_WIDTH :0]  wr_datacount_o,
    output wire                   empty_o,
    output wire                   almost_empty_o,
    output wire                   prog_empty_o,
    output wire                   underflow_o,
    output wire                   rd_valid_o,
    output wire [RDATA_WIDTH-1:0] rdata,
    output wire [RADDR_WIDTH :0]  rd_datacount_o,
    output wire                   rst_busy
); 

localparam WR_DEPTH      = DEPTH;
localparam WDATA_WIDTH   = DATA_WIDTH;
localparam RAM_MUX_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? 32 :
                           (RDATA_WIDTH <= WDATA_WIDTH/16) ? 16 :
                           (RDATA_WIDTH <= WDATA_WIDTH/8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH/4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH/2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH)    ? 1  :
                           (RDATA_WIDTH <= WDATA_WIDTH*2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH*4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH*8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH*16) ? 16 : 32;           

wire                   wr_rst_int;
wire                   rd_rst_int;
wire                   wr_en_int;
wire                   rd_en_int;
wire [WADDR_WIDTH-1:0] waddr;
wire [RADDR_WIDTH-1:0] raddr;
wire                   wr_clk_int;
wire                   rd_clk_int;
wire [WADDR_WIDTH :0]  wr_datacount_int;
wire [RADDR_WIDTH :0]  rd_datacount_int;

generate
    if (ASYM_WIDTH_RATIO == 4) begin
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
            assign datacount_o    = wr_datacount_int;
            assign wr_datacount_o = {(WADDR_WIDTH+1){1'b0}};
            assign rd_datacount_o = {(RADDR_WIDTH+1){1'b0}};
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
            assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
            assign wr_datacount_o = wr_datacount_int;
            assign rd_datacount_o = rd_datacount_int;
        end
    end
    else begin
        assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
        assign wr_datacount_o = wr_datacount_int;
        assign rd_datacount_o = rd_datacount_int;
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
        end
    end    

    if (!SYNC_CLK) begin
        //(* async_reg = "true" *) reg [1:0] wr_rst;
        //(* async_reg = "true" *) reg [1:0] rd_rst;
        //
        //always @ (posedge wr_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        wr_rst <= 2'b11;
        //    else 
        //        wr_rst <= {wr_rst[0],1'b0};            
        //end
        //
        //always @ (posedge rd_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        rd_rst <= 2'b11;
        //    else 
        //        rd_rst <= {rd_rst[0],1'b0};            
        //end
        

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_wr_rst_i;
            assign rd_rst_int = a_rd_rst_i; 
            assign rst_busy   = 1'b0;
        end 
        else begin
            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_wr_rst (
               .clk   (wr_clk_int),
               .reset (a_rst_i),
               .d_o   (wr_rst_int)
            );

            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_rd_rst (
               .clk   (rd_clk_int),
               .reset (a_rst_i),
               .d_o   (rd_rst_int)
            );
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end

    end
    else begin
        //(* async_reg = "true" *) reg [1:0] a_rst;
        //
        //always @ (posedge clk_i or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        a_rst <= 2'b11;
        //    else 
        //        a_rst <= {a_rst[0],1'b0};            
        //end
        wire   a_rst;

        `IP_MODULE_NAME(efx_resetsync) #(
           .ACTIVE_LOW (0)
        ) efx_resetsync_a_rst (
           .clk   (clk_i),
           .reset (a_rst_i),
           .d_o   (a_rst)
        );

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_rst_i;
            assign rd_rst_int = a_rst_i;
            assign rst_busy   = 1'b0;
        end
        else begin
            assign wr_rst_int = a_rst;
            assign rd_rst_int = a_rst;   
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end 
endgenerate

`IP_MODULE_NAME(efx_fifo_ram) # (
    .FAMILY        (FAMILY),
    .WR_DEPTH      (WR_DEPTH),
    .RD_DEPTH      (RD_DEPTH),
    .WDATA_WIDTH   (WDATA_WIDTH),
    .RDATA_WIDTH   (RDATA_WIDTH),
    .WADDR_WIDTH   (WADDR_WIDTH),
    .RADDR_WIDTH   (RADDR_WIDTH),
    .OUTPUT_REG    (OUTPUT_REG),
    .RAM_MUX_RATIO (RAM_MUX_RATIO),
    .ENDIANESS	    (ENDIANESS),
    .RAM_STYLE     (RAM_STYLE)
) xefx_fifo_ram (
    .wdata (wdata), 
    .waddr (waddr),
    .raddr (raddr), 
    .we    (wr_en_int), 
    .re    (rd_en_int),
    .wclk  (wr_clk_int),
    .rclk  (rd_clk_int),
    .rdata (rdata)
);

`IP_MODULE_NAME(efx_fifo_ctl) # (
    .SYNC_CLK           (SYNC_CLK),
    .SYNC_STAGE         (SYNC_STAGE),
    .MODE               (MODE),
    .WR_DEPTH           (WR_DEPTH),
    .WADDR_WIDTH        (WADDR_WIDTH),
    .RADDR_WIDTH        (RADDR_WIDTH),
    .ASYM_WIDTH_RATIO   (ASYM_WIDTH_RATIO),
    .RAM_MUX_RATIO      (RAM_MUX_RATIO),
    .PIPELINE_REG       (PIPELINE_REG),
    .ALMOST_FLAG        (ALMOST_FLAG),
    .PROGRAMMABLE_FULL  (PROGRAMMABLE_FULL),
    .PROG_FULL_ASSERT   (PROG_FULL_ASSERT),
    .PROG_FULL_NEGATE   (PROG_FULL_NEGATE),
    .PROGRAMMABLE_EMPTY (PROGRAMMABLE_EMPTY),
    .PROG_EMPTY_ASSERT  (PROG_EMPTY_ASSERT),
    .PROG_EMPTY_NEGATE  (PROG_EMPTY_NEGATE),
    .OUTPUT_REG         (OUTPUT_REG),
    .HANDSHAKE_FLAG     (HANDSHAKE_FLAG),
    .OVERFLOW_PROTECT   (OVERFLOW_PROTECT),
    .UNDERFLOW_PROTECT  (UNDERFLOW_PROTECT)
) xefx_fifo_ctl (
    .wr_rst          (wr_rst_int),
    .rd_rst          (rd_rst_int),
    .wclk            (wr_clk_int),
    .rclk            (rd_clk_int),
    .we              (wr_en_i),
    .re              (rd_en_i),
    .wr_full         (full_o),
    .wr_ack          (wr_ack_o),
    .rd_empty        (empty_o),
    .wr_almost_full  (almost_full_o),
    .rd_almost_empty (almost_empty_o),
    .wr_prog_full    (prog_full_o),
    .rd_prog_empty   (prog_empty_o),
    .wr_en_int       (wr_en_int),
    .rd_en_int       (rd_en_int),
    .waddr           (waddr),
    .raddr           (raddr),
    .wr_datacount    (wr_datacount_int),
    .rd_datacount    (rd_datacount_int),
    .rd_vld          (rd_valid_o),
    .wr_overflow     (overflow_o),
    .rd_underflow    (underflow_o)
);

function integer depth2width;
input [31:0] depth;
begin : fnDepth2Width
    if (depth > 1) begin
        depth = depth - 1;
        for (depth2width=0; depth>0; depth2width = depth2width + 1)
            depth = depth>>1;
        end
    else
       depth2width = 0;
end
endfunction 

function integer width2depth;
input [31:0] width;
begin : fnWidth2Depth
    width2depth = width**2;
end
endfunction

function integer rdwidthcompute;
input [31:0] asym_option;
input [31:0] wr_width;
begin : RdWidthCompute
    rdwidthcompute = (asym_option==0)? wr_width/16 :
                     (asym_option==1)? wr_width/8  :
                     (asym_option==2)? wr_width/4  :
                     (asym_option==3)? wr_width/2  :
                     (asym_option==4)? wr_width/1  :
                     (asym_option==5)? wr_width*2  :
                     (asym_option==6)? wr_width*4  :
                     (asym_option==7)? wr_width*8  :
                     (asym_option==8)? wr_width*16 : wr_width/1;
end
endfunction

function integer rddepthcompute;
input [31:0] wr_depth;
input [31:0] wr_width;
input [31:0] rd_width;
begin : RdDepthCompute
    rddepthcompute = (wr_depth * wr_width) / rd_width;
end
endfunction

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ram) #(
    parameter FAMILY        = "TRION",
    parameter WR_DEPTH      = 512,
    parameter RD_DEPTH      = 512,
    parameter WDATA_WIDTH   = 8,
    parameter RDATA_WIDTH   = 8,
    parameter WADDR_WIDTH   = 9,
    parameter RADDR_WIDTH   = 9,
    parameter OUTPUT_REG    = 1,
    parameter RAM_MUX_RATIO = 4,
    parameter ENDIANESS 	 = 0, //0: Big endian (default)   1: Little endian 
    parameter RAM_STYLE     = "block_ram"
) (
    input  wire                     wclk,
    input  wire                     rclk,
    input  wire                     we,
    input  wire                     re,
    input  wire [(WDATA_WIDTH-1):0] wdata,
    input  wire [(WADDR_WIDTH-1):0] waddr,
    input  wire [(RADDR_WIDTH-1):0] raddr,
    output wire [(RDATA_WIDTH-1):0] rdata
);

localparam MEM_DEPTH         = (WR_DEPTH > RD_DEPTH) ? WR_DEPTH : RD_DEPTH;
localparam MEM_DATA_WIDTH    = (WDATA_WIDTH > RDATA_WIDTH) ? RDATA_WIDTH : WDATA_WIDTH;   
localparam LSB_WIDTH         = (WADDR_WIDTH > RADDR_WIDTH) ? (WADDR_WIDTH - RADDR_WIDTH) : (RADDR_WIDTH - WADDR_WIDTH);
localparam RDATA_WDATA_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? "ONE_THIRTYTWO" :
                               (RDATA_WIDTH <= WDATA_WIDTH/16) ? "ONE_SIXTEENTH" :
                               (RDATA_WIDTH <= WDATA_WIDTH/8)  ? "ONE_EIGHTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/4)  ? "ONE_FOURTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/2)  ? "ONE_HALF"      :
                               (RDATA_WIDTH <= WDATA_WIDTH)    ? "ONE"           :
                               (RDATA_WIDTH <= WDATA_WIDTH*2)  ? "TWO_TIMES"     :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "FOUR_TIMES"    :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "EIGHT_TIMES"   :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "SIXTEEN_TIMES" : "THIRTYTWO_TIMES";

(* syn_ramstyle = RAM_STYLE *) reg [MEM_DATA_WIDTH-1:0] ram[MEM_DEPTH-1:0];
reg [RDATA_WIDTH-1:0]    r_rdata_1P;
reg [RDATA_WIDTH-1:0]    r_rdata_2P;

wire                     re_int;

generate
    if (FAMILY == "TRION") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                end
                r_rdata_2P <= r_rdata_1P;
            end
        end
        
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            if (ENDIANESS == 0) begin 
            	integer i;
            	always @ (posedge wclk) begin
                	for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                    end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                	end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
        end
        
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  ||RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
            //integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end 
            end
            else begin //endianess == 1
            	always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end
            end
        end
        if (OUTPUT_REG) begin
            assign re_int = re;
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end
    end
    else begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                    r_rdata_2P <= r_rdata_1P;
                end
            end
        end 
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            //integer i;
            if (ENDIANESS == 0) begin
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
            
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
        end
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  || RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
        	//integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
            
            else begin //endianess ==1
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
        end
        // NIC
        //if (MODE == "STANDARD") begin     
        //    if (OUTPUT_REG) begin
        //        reg re_r;
        //        always @ (posedge rclk) begin
        //            re_r <= re;
        //        end
        //        assign re_int = re | re_r;        
        //        assign rdata  = r_rdata_2P;            
        //    end
        //    else begin
        //        assign re_int = re;
        //        assign rdata  = r_rdata_1P;
        //    end
        //end
        //else begin
        //    assign re_int = re;
        //    assign rdata  = r_rdata_1P;
        //end    
        if (OUTPUT_REG) begin
            reg re_r;
            always @ (posedge rclk) begin
                re_r <= re;
            end
            assign re_int = re | re_r;        
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end    
    end
endgenerate

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2021 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   gray2bin.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Gray to Binary Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_gray2bin)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] bin_o,
 // input
 input [WIDTH-1:0] gray_i);

//---------------------------------------------------------------------
// Recursive Module
// Description: reduction xor
generate
   if (WIDTH > 1) begin
      wire [1:0] bin_1;
      assign bin_1 = {gray_i[WIDTH-1], gray_i[WIDTH-1]^gray_i[WIDTH-2]};
      if (WIDTH == 2) begin
	 assign bin_o = bin_1;
      end
      else begin
	 assign bin_o[WIDTH-1] = bin_1[1];
	 `IP_MODULE_NAME(efx_fifo_gray2bin) #(.WIDTH(WIDTH-1)) u_gray2bin (.bin_o(bin_o[WIDTH-2:0]), .gray_i({bin_1[0], gray_i[WIDTH-3:0]}));
      end
   end
   else /* if (WIDTH == 1) */
     assign bin_o = gray_i;
endgenerate
   
endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   pipe_reg.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Parallel Pipelining Shift Register
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_datasync) #(
    parameter STAGE = 32,
    parameter WIDTH = 4
) (
 input  wire             clk_i,
 input  wire [WIDTH-1:0] d_i,
 output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] pipe_reg [STAGE-1:0];
integer i;

always @(posedge clk_i) begin
  for (i=STAGE-1; i>0; i = i - 1) begin
    pipe_reg[i] <= pipe_reg[i-1];
  end
  pipe_reg[0] <= d_i;
end
assign d_o = pipe_reg[STAGE-1];
   

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_resetsync) #(
    parameter ASYNC_STAGE = 2,
    parameter ACTIVE_LOW = 1
) (
    input  wire             clk,
    input  wire             reset,
    output wire             d_o
);


generate
   if (ACTIVE_LOW == 1) begin: active_low
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (1),
         .RST_VALUE  (0)
      ) efx_resetsync_active_low (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b1),
         .d_o     (d_o)
      );
   end
   else begin: active_high
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (0),
         .RST_VALUE  (1)
      ) efx_resetsync_active_high (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b0),
         .d_o     (d_o)
      );
   end
endgenerate

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ctl) # (
    parameter SYNC_CLK           = 1,
    parameter SYNC_STAGE         = 2,
    parameter MODE               = "STANDARD",
    parameter WR_DEPTH           = 512,
    parameter WADDR_WIDTH        = 9,
    parameter RADDR_WIDTH        = 9,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter RAM_MUX_RATIO      = 1,
    parameter PIPELINE_REG       = 1,
    parameter ALMOST_FLAG        = 1,
    parameter PROGRAMMABLE_FULL  = "NONE",
    parameter PROG_FULL_ASSERT   = 0,
    parameter PROG_FULL_NEGATE   = 0,
    parameter PROGRAMMABLE_EMPTY = "NONE",
    parameter PROG_EMPTY_ASSERT  = 0,
    parameter PROG_EMPTY_NEGATE  = 0,
    parameter OUTPUT_REG         = 0,
    parameter HANDSHAKE_FLAG     = 1, 
    parameter OVERFLOW_PROTECT   = 0,
    parameter UNDERFLOW_PROTECT  = 0
)(
    input   wire                   wr_rst,
    input   wire                   rd_rst,
    input   wire                   wclk,
    input   wire                   rclk,
    input   wire                   we,
    input   wire                   re,
    output  wire                   wr_full,
    output  reg                    wr_ack,
    output  wire                   wr_almost_full,
    output  wire                   rd_empty,
    output  wire                   rd_almost_empty,
    output  wire                   wr_prog_full,
    output  wire                   rd_prog_empty,
    output  wire                   wr_en_int,
    output  wire                   rd_en_int,
    output  wire [WADDR_WIDTH-1:0] waddr,
    output  wire [RADDR_WIDTH-1:0] raddr,
    output  wire [WADDR_WIDTH:0]   wr_datacount,
    output  wire [RADDR_WIDTH:0]   rd_datacount,
    output  wire                   rd_vld,
    output  reg                    wr_overflow,
    output  reg                    rd_underflow
);

reg  [WADDR_WIDTH:0] waddr_cntr;
reg  [WADDR_WIDTH:0] waddr_cntr_r;
reg  [RADDR_WIDTH:0] raddr_cntr;
reg                  rd_valid;

wire [WADDR_WIDTH:0] waddr_int;
wire [RADDR_WIDTH:0] raddr_int;
wire                 rd_empty_int;
wire [WADDR_WIDTH:0] wr_datacount_int;
wire [RADDR_WIDTH:0] rd_datacount_int;

assign waddr     = waddr_cntr[WADDR_WIDTH-1:0];
// NIC
wire [RADDR_WIDTH:0] ram_raddr;
assign raddr     = (MODE == "FWFT") ? ram_raddr[RADDR_WIDTH-1:0] : raddr_cntr[RADDR_WIDTH-1:0];
//assign raddr     = raddr_cntr[RADDR_WIDTH-1:0];
//assign wr_en_int = we & ~wr_full;
assign wr_en_int = OVERFLOW_PROTECT ? we & ~wr_full : we;

assign wr_datacount  = wr_datacount_int;
assign rd_datacount  = ASYM_WIDTH_RATIO == 4 && SYNC_CLK ? wr_datacount_int : rd_datacount_int;


generate
    if (MODE == "FWFT") begin    
        // NIC
        //assign rd_en_int     = (~rd_empty_int & rd_empty) | (re & ~rd_empty_int);
        //assign rd_empty      = rd_empty_fwft;

        assign rd_en_int     = 1'b1;
        //assign rd_empty      = rd_empty_int;
        
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        init_set <= 1'b1;
        //    end
        //    else if (~init_set & rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //    else if (~rd_empty_int) begin
        //        init_set <= 1'b0;            
        //    end
        //    else if (rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //end
        // NIC 
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        rd_empty_fwft <= 1'b1;
        //    end
        //    else if (rd_en_int) begin
        //        rd_empty_fwft <= 1'b0;            
        //    end
        //    else if (re) begin
        //        rd_empty_fwft <= 1'b1;            
        //    end
        //end          
        
        //if (FAMILY == "TRION") begin
        if (OUTPUT_REG) begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid <= 1'b0;
                end
                else begin
                    rd_valid <= ~rd_empty;
                end
            end
            assign rd_vld = rd_valid;                
        end    
        else begin
            assign rd_vld = ~rd_empty;                
        end
        
        assign rd_empty = rd_empty_int;
    end
    else begin
        assign rd_en_int     = UNDERFLOW_PROTECT ? re & ~rd_empty_int : re;
        assign rd_empty      = rd_empty_int;
        
        if (OUTPUT_REG) begin
            reg rd_valid_r;
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid_r <= 'h0;
                    rd_valid   <= 'h0;
                end
                else begin
                    {rd_valid,rd_valid_r} <= {rd_valid_r,rd_en_int};
                end
            end
            assign rd_vld = rd_valid;            
        end
        else begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid  <= 'h0;
                end
                else begin
                    rd_valid <= rd_en_int;
                end
            end
            assign rd_vld = rd_valid;
        end
    end
    
    if (ALMOST_FLAG) begin
        assign wr_almost_full   = wr_datacount >= WR_DEPTH-1;
        assign rd_almost_empty  = rd_datacount <= 'd1;
    end
    else begin
        assign wr_almost_full   = 1'b0;
        assign rd_almost_empty  = 1'b0;        
    end
    
    if (PROGRAMMABLE_FULL == "STATIC_SINGLE") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_datacount >= PROG_FULL_ASSERT;
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end  
    end
    else if (PROGRAMMABLE_FULL == "STATIC_DUAL") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_prog_full_int ? wr_datacount >= PROG_FULL_NEGATE : wr_datacount >= PROG_FULL_ASSERT;   

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end              
    end
    else begin
        assign wr_prog_full  = 1'b0; 
    end
        
    if (PROGRAMMABLE_EMPTY == "STATIC_SINGLE") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty  = rd_datacount <= PROG_EMPTY_ASSERT;
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end
    end
    else if (PROGRAMMABLE_EMPTY == "STATIC_DUAL") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty = rd_prog_empty_int ? (rd_datacount <= PROG_EMPTY_NEGATE) : (rd_datacount <= PROG_EMPTY_ASSERT);
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end  
    end
    else begin
        assign rd_prog_empty  = 1'b0; 
    end
    
    if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_ack <= 1'b0;
            end
            else begin
                // NIC
                //wr_ack <= wr_en_int & ~wr_overflow;
                wr_ack <= OVERFLOW_PROTECT ? wr_en_int & ~wr_overflow : wr_en_int;
            end
        end
    end

    if (OVERFLOW_PROTECT) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else if (we && wr_full) begin
                wr_overflow <= 1'b1;
            end
            else begin
                wr_overflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else begin
                wr_overflow <= we && wr_full ? 1'b1 : wr_overflow;
            end
        end
    end

    if (UNDERFLOW_PROTECT) begin     
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else if (re && rd_empty) begin
                rd_underflow <= 1'b1;
            end
            else begin
                rd_underflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else begin
                rd_underflow <= re && rd_empty ? 1'b1 : rd_underflow;
            end
        end
    end
    
    localparam RATIO_WIDTH = (RADDR_WIDTH >= WADDR_WIDTH)? RADDR_WIDTH - WADDR_WIDTH : WADDR_WIDTH - RADDR_WIDTH;
    
    if (ASYM_WIDTH_RATIO < 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:RATIO_WIDTH]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:RATIO_WIDTH];
        assign wr_datacount_int = waddr_cntr - (raddr_int/RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int*RAM_MUX_RATIO)-raddr_cntr;
    end
    // NIC
    else if (ASYM_WIDTH_RATIO == 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:0]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - raddr_int;
        assign rd_datacount_int = waddr_int - raddr_cntr;
    end
    else begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:RATIO_WIDTH] == raddr_int[RADDR_WIDTH-1:0]);
        // NIC
        //assign rd_empty_int     = (waddr_int- raddr_cntr*RAM_MUX_RATIO) < RAM_MUX_RATIO;
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:RATIO_WIDTH] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - (raddr_int*RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int/RAM_MUX_RATIO)-raddr_cntr;
    end
endgenerate

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr   <= 'h0;
    end
    else if (wr_en_int) begin
        waddr_cntr   <= waddr_cntr + 1'b1;
    end
end

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr_r  <= 'h0;
    end
    else begin
        waddr_cntr_r  <= waddr_cntr;
    end
end

always @ (posedge rclk or posedge rd_rst) begin
    if (rd_rst) begin
        raddr_cntr   <= 'h0;
    end
    // NIC
    //else if (rd_en_int) begin
    else begin
        //raddr_cntr   <= raddr_cntr + 1'b1;
        //raddr_cntr   <= raddr_cntr + (re & ~rd_empty_int);
        raddr_cntr   <= ram_raddr;
    end
end
// NIC
assign ram_raddr = raddr_cntr + (UNDERFLOW_PROTECT ? re & ~rd_empty_int : re);


generate
    if (SYNC_CLK) begin : sync_clk
       if (MODE == "FWFT") begin
          assign waddr_int = waddr_cntr_r;
          assign raddr_int        = raddr_cntr;
       end
       else begin
          assign waddr_int        = waddr_cntr;
          assign raddr_int        = raddr_cntr;
       end
    end
    else begin : async_clk
        reg  [RADDR_WIDTH:0] raddr_cntr_gry_r;
        reg  [WADDR_WIDTH:0] waddr_cntr_gry_r;
        
        wire [RADDR_WIDTH:0] raddr_cntr_gry;
        wire [RADDR_WIDTH:0] raddr_cntr_gry_sync;
        wire [RADDR_WIDTH:0] raddr_cntr_sync_g2b;
        wire [WADDR_WIDTH:0] waddr_cntr_gry;
        wire [WADDR_WIDTH:0] waddr_cntr_gry_sync;
        wire [WADDR_WIDTH:0] waddr_cntr_sync_g2b;

        if (PIPELINE_REG) begin
            reg  [RADDR_WIDTH:0] raddr_cntr_sync_g2b_r;    
            reg  [WADDR_WIDTH:0] waddr_cntr_sync_g2b_r;        
        
            assign waddr_int        = waddr_cntr_sync_g2b_r;
            assign raddr_int        = raddr_cntr_sync_g2b_r;
            
            always @ (posedge wclk or posedge wr_rst) begin
                if (wr_rst) begin
                    raddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    raddr_cntr_sync_g2b_r <= raddr_cntr_sync_g2b;
                end
            end
            
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    waddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    waddr_cntr_sync_g2b_r <= waddr_cntr_sync_g2b;
                end
            end
        end
        else begin
            assign waddr_int        = waddr_cntr_sync_g2b;
            assign raddr_int        = raddr_cntr_sync_g2b;        
        end
      
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                raddr_cntr_gry_r <= 'h0;
            end
            else begin
                raddr_cntr_gry_r <= raddr_cntr_gry;
            end
        end
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_bin2gray    (.bin_i(raddr_cntr), .gray_o(raddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (RADDR_WIDTH+1), .ACTIVE_LOW(0)) xrd2wr_addr_sync   (.clk(wclk), .reset_n(wr_rst), .d_i(raddr_cntr_gry_r), .d_o(raddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_gray2bin    (.gray_i(raddr_cntr_gry_sync), .bin_o(raddr_cntr_sync_g2b));
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                waddr_cntr_gry_r <= 'h0;
            end
            else begin
                waddr_cntr_gry_r <= waddr_cntr_gry;
            end
        end        
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_bin2gray  (.bin_i(waddr_cntr), .gray_o(waddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (WADDR_WIDTH+1), .ACTIVE_LOW(0)) wr2rd_addr_sync (.clk(rclk), .reset_n(rd_rst), .d_i(waddr_cntr_gry_r), .d_o(waddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_gray2bin  (.gray_i(waddr_cntr_gry_sync), .bin_o(waddr_cntr_sync_g2b));        

    end
endgenerate
endmodule


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   bin2gray.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Binary to Gray Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_bin2gray)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] gray_o,
 // input
 input [WIDTH-1:0] bin_i
 );

//---------------------------------------------------------------------
// Function :   bit_xor
// Description: reduction xor
function bit_xor (
  input [31:0] nex_bit,
  input [31:0] curr_bit,
  input [WIDTH-1:0] xor_in);
  begin : fn_bit_xor
    bit_xor = xor_in[nex_bit] ^ xor_in[curr_bit];
  end
endfunction

// Convert Binary to Gray, bit by bit
generate 
begin
  genvar bit_idx;
  for(bit_idx=0; bit_idx<WIDTH-1; bit_idx=bit_idx+1) begin : gBinBits
    assign gray_o[bit_idx] = bit_xor(bit_idx+1, bit_idx, bin_i);
  end
  assign   gray_o[WIDTH-1] = bin_i[WIDTH-1];
end
endgenerate

endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////

`undef IP_UUID
`undef IP_NAME_CONCAT
`undef IP_MODULE_NAME
