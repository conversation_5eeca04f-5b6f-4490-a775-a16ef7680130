
# Efinity Pinout Report
# Version: 2025.1.110.2.15
# Date: 2025-07-22 18:24

# Copyright (C) 2013 - 2025 Efinix Inc. All rights reserved.

# Device: Ti60F225
# Package status: final
# Project: Ti60_Demo
# Configuration mode: active (x4)

# NOTE: Similar information is available in Ti60_Demo.pinout.csv

+------------+---------------------------------------+--------------------+---------------+--------------+------------------+------+-------------+----------------------------------------------------------+
| Pin Number |              Signal Name              |      Pin Name      |   Direction   | I/O Standard | I/O Bank Voltage | Bank |  Pull Type  |                         Function                         |
+------------+---------------------------------------+--------------------+---------------+--------------+------------------+------+-------------+----------------------------------------------------------+
|     A1     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|     A2     |             lcd_tp_rst_o.N            |     GPIOL_N_18     |     Output    | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                    User IO/PLL_CLKIN                     |
|     A3     |               cmos_vsync              |      GPIOL_03      |     Input     | 3.3 V LVCMOS |      3.3 V       |  TL  |             |                         User IO                          |
|     A4     |               cmos_sclk               |      GPIOL_09      |     Output    | 3.3 V LVCMOS |      3.3 V       |  TL  |             |                         User IO                          |
|     A5     |              hdmi_txd2.P              |     GPIOT_P_03     |     Output    |              |      1.8 V       |  2A  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|     A6     |              hdmi_txd2.N              |     GPIOT_N_03     |     Output    |              |      1.8 V       |  2A  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|     A7     |              dsi_pwm_o.N              |     GPIOT_N_06     |     Output    | 1.8 V LVCMOS |      1.8 V       |  2A  |             |                         User IO                          |
|     A8     |               --UNUSED--              | GPIOT_N_07_CLK4_N  |               |              |      1.8 V       |  2A  |             |                    User IO/GCLK/RCLK                     |
|     A9     |               --UNUSED--              |     GPIOT_N_11     |               |              |      1.8 V       |  2B  |             |                         User IO                          |
|    A10     |              lvds_txd3.P              |     GPIOT_P_14     |     Output    |              |      1.8 V       |  2B  |             |                    User IO/MIPI_GCLK                     |
|    A11     |              hdmi_txd0.P              | GPIOT_P_17_PLLIN1  |     Output    |              |      1.8 V       |  2B  |             |                    User IO/PLL_CLKIN                     |
|    A12     |               cmos_sdat               |      GPIOR_13      | Bidirectional | 3.3 V LVCMOS |      3.3 V       |  TR  |             |                         User IO                          |
|    A13     |              cmos_data[7]             |      GPIOR_12      |     Input     | 3.3 V LVCMOS |      3.3 V       |  TR  |             |                         User IO                          |
|    A14     |              cmos_data[3]             |      GPIOR_19      |     Input     | 3.3 V LVCMOS |      3.3 V       |  TR  |             |                         User IO                          |
|    A15     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|     B1     |            lcd_tp_scl_io.P            |  GPIOL_P_17_EXTFB  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                    User IO/PLL_EXTFB                     |
|     B2     |            lcd_tp_int_io.P            | GPIOL_P_18_PLLIN0  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                    User IO/PLL_CLKIN                     |
|     B3     |               cmos_ctl2               |      GPIOL_04      |     Output    | 3.3 V LVCMOS |      3.3 V       |  TL  |             |                         User IO                          |
|     B4     |                --VCC--                |     VCCIO33_TL     |     Power     |              |      3.3 V       |  TL  |             |                       VCCIO Power                        |
|     B5     |              cmos_data[4]             |      GPIOL_10      |     Input     | 3.3 V LVCMOS |      3.3 V       |  TL  |             |                         User IO                          |
|     B6     |               lvds_txc.P              |     GPIOT_P_01     |     Output    |              |      1.8 V       |  2A  |             |                         User IO                          |
|     B7     |              csi_sda_io.P             |     GPIOT_P_06     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  2A  |             |                         User IO                          |
|     B8     |              cmos_pclk.P              | GPIOT_P_07_CLK4_P  |     Input     | 1.8 V LVCMOS |      1.8 V       |  2A  |             |                    User IO/GCLK/RCLK                     |
|     B9     |               --UNUSED--              |     GPIOT_P_11     |               |              |      1.8 V       |  2B  |             |                         User IO                          |
|    B10     |              lvds_txd3.N              |     GPIOT_N_14     |     Output    |              |      1.8 V       |  2B  |             |                    User IO/MIPI_GCLK                     |
|    B11     |              hdmi_txd0.N              |     GPIOT_N_17     |     Output    |              |      1.8 V       |  2B  |             |                    User IO/PLL_CLKIN                     |
|    B12     |               cmos_ctl1               |      GPIOR_15      |     Input     | 3.3 V LVCMOS |      3.3 V       |  TR  |             |                         User IO                          |
|    B13     |                --VCC--                |     VCCIO33_TR     |     Power     |              |      3.3 V       |  TR  |             |                       VCCIO Power                        |
|    B14     |              cmos_data[5]             |      GPIOR_18      |     Input     | 3.3 V LVCMOS |      3.3 V       |  TR  |             |                         User IO                          |
|    B15     |               --UNUSED--              |     REF_RES_3A     |     Input     |              |      1.2 V       |  3A  |             |                                                          |
|     C1     |              lcd_pwm_o.N              |     GPIOL_N_16     |     Output    | 1.8 V LVCMOS |      1.8 V       |  1B  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|     C2     |            lcd_tp_sda_io.N            |     GPIOL_N_17     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                    User IO/PLL_EXTFB                     |
|     C3     |              cmos_data[2]             |      GPIOL_06      |     Input     | 3.3 V LVCMOS |      3.3 V       |  TL  |             |                         User IO                          |
|     C4     |              cmos_data[6]             |      GPIOL_07      |     Input     | 3.3 V LVCMOS |      3.3 V       |  TL  |             |                         User IO                          |
|     C5     |                clk_24m                |  GPIOL_11_PLLIN2   |     Input     | 3.3 V LVCMOS |      3.3 V       |  TL  |             |                    User IO/PLL_CLKIN                     |
|     C6     |               lvds_txc.N              |     GPIOT_N_01     |     Output    |              |      1.8 V       |  2A  |             |                         User IO                          |
|     C7     |                --VCC--                |      VCCIO2A       |     Power     |              |      1.8 V       |  2A  |             |                       VCCIO Power                        |
|     C8     |             csi_ctl0_io.P             | GPIOT_P_10_CLK7_P  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  2B  |             |                    User IO/GCLK/RCLK                     |
|     C9     |             csi_ctl1_io.N             | GPIOT_N_10_CLK7_N  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  2B  |             |                    User IO/GCLK/RCLK                     |
|    C10     |              lvds_txd0.P              |     GPIOT_P_13     |     Output    |              |      1.8 V       |  2B  |             |                       User IO/VREF                       |
|    C11     |                --VCC--                |      VCCIO2B       |     Power     |              |      1.8 V       |  2B  |             |                       VCCIO Power                        |
|    C12     |               cmos_xclk               |      GPIOR_16      |     Output    | 3.3 V LVCMOS |      3.3 V       |  TR  |             |                         User IO                          |
|    C13     |               csi_rxc.P               |     GPIOR_P_17     |     Input     |              |      1.2 V       |  3A  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|    C14     |               csi_rxd1.P              | GPIOR_P_19_PLLIN0  |     Input     |              |      1.2 V       |  3A  |             |                    User IO/PLL_CLKIN                     |
|    C15     |               csi_rxd1.N              |     GPIOR_N_19     |     Input     |              |      1.2 V       |  3A  |             |                    User IO/PLL_CLKIN                     |
|     D1     |              lcd_blen_o.P             |     GPIOL_P_16     |     Output    | 1.8 V LVCMOS |      1.8 V       |  1B  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|     D2     |                --VCC--                |      VCCIO1B       |     Power     |              |      1.8 V       |  1B  |             |                       VCCIO Power                        |
|     D3     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|     D4     |                --VCC--                |       VCCAUX       |     Power     |              |      1.5 V       |  4B  |             |                                                          |
|     D5     |              hdmi_txd1.N              |     GPIOT_N_00     |     Output    |              |      1.8 V       |  2A  |             |                    User IO/PLL_CLKIN                     |
|     D6     |                --VCC--                |      VCCIO2A       |     Power     |              |      1.8 V       |  2A  |             |                       VCCIO Power                        |
|     D7     |              lvds_txd2.N              |     GPIOT_N_05     |     Output    |              |      1.8 V       |  2A  |             |                         User IO                          |
|     D8     |             dsi_resetn_o.N            | GPIOT_N_09_CLK6_N  |     Output    | 1.8 V LVCMOS |      1.8 V       |  2B  |             |          User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK           |
|     D9     |                --VCC--                |      VCCIO2B       |     Power     |              |      1.8 V       |  2B  |             |                       VCCIO Power                        |
|    D10     |              lvds_txd0.N              |     GPIOT_N_13     |     Output    |              |      1.8 V       |  2B  |             |                         User IO                          |
|    D11     |               --UNUSED--              |     REF_RES_2B     |     Input     |              |      1.8 V       |  2B  |             |                                                          |
|    D12     |               cmos_href               |      GPIOR_20      |     Input     | 3.3 V LVCMOS |      3.3 V       |  TR  |             |                         User IO                          |
|    D13     |               csi_rxc.N               |     GPIOR_N_17     |     Input     |              |      1.2 V       |  3A  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|    D14     |               csi_rxd0.N              |     GPIOR_N_18     |     Input     |              |      1.2 V       |  3A  |             |                         User IO                          |
|    D15     |               csi_rxd0.P              |     GPIOR_P_18     |     Input     |              |      1.2 V       |  3A  |             |                         User IO                          |
|     E1     |     --NSTATUS-- / lcd_r7_0_io[0].P    | GPIOL_P_15_NSTATUS | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                User IO/VREF/Configuration                |
|     E2     |     --TEST_N-- / lcd_r7_0_io[1].N     | GPIOL_N_15_TEST_N  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                  User IO/Configuration                   |
|     E3     |     --CBSEL1-- / lcd_g7_0_io[7].N     | GPIOL_N_13_CBSEL1  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                         User IO                          |
|     E4     |               --UNUSED--              |     REF_RES_1B     |     Input     |              |      1.8 V       |  1B  |             |                                                          |
|     E5     |                --VCC--                |      VCCA_TL       |     Power     |              |                  |      |             |                     0.95 V PLL Power                     |
|     E6     |              hdmi_txd1.P              | GPIOT_P_00_PLLIN1  |     Output    |              |      1.8 V       |  2A  |             |                  User IO/PLL_CLKIN/VREF                  |
|     E7     |              lvds_txd2.P              |     GPIOT_P_05     |     Output    |              |      1.8 V       |  2A  |             |                         User IO                          |
|     E8     |              csi_scl_io.P             | GPIOT_P_09_CLK6_P  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  2B  |             |          User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK           |
|     E9     |              lvds_txd1.N              |     GPIOT_N_12     |     Output    |              |      1.8 V       |  2B  |             |                         User IO                          |
|    E10     |              uart_tx_o.P              |  GPIOT_P_16_EXTFB  |     Output    | 1.8 V LVCMOS |      1.8 V       |  2B  |             |                    User IO/PLL_EXTFB                     |
|    E11     |                --VCC--                |       VCCAUX       |     Power     |              |      1.5 V       |  4B  |             |                                                          |
|    E12     |               csi_rxd2.N              |     GPIOR_N_15     |     Input     |              |      1.2 V       |  3A  |             |                         User IO                          |
|    E13     |                --VCC--                |        VCC         |     Power     |              |                  |      |             |                     0.95 V VCC Power                     |
|    E14     |               csi_rxd3.N              |     GPIOR_N_16     |     Input     |              |      1.2 V       |  3A  |             |                         User IO                          |
|    E15     |               csi_rxd3.P              |     GPIOR_P_16     |     Input     |              |      1.2 V       |  3A  |             |                         User IO                          |
|     F1     |            lcd_r7_0_io[2].P           |     GPIOL_P_14     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                         User IO                          |
|     F2     |            lcd_r7_0_io[3].N           |     GPIOL_N_14     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                         User IO                          |
|     F3     |     --CBSEL0-- / lcd_g7_0_io[6].P     | GPIOL_P_13_CBSEL0  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                         User IO                          |
|     F4     |                --VCC--                |      VCCIO1B       |     Power     |              |      1.8 V       |  1B  |             |                       VCCIO Power                        |
|     F5     |            lcd_g7_0_io[3].N           |     GPIOL_N_11     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                         User IO                          |
|     F6     |               --UNUSED--              |     REF_RES_2A     |     Input     |              |      1.8 V       |  2A  |             |                                                          |
|     F7     |               hdmi_txc.P              |     GPIOT_P_04     |     Output    |              |      1.8 V       |  2A  |             |                         User IO                          |
|     F8     |               hdmi_txc.N              |     GPIOT_N_04     |     Output    |              |      1.8 V       |  2A  |             |                         User IO                          |
|     F9     |              lvds_txd1.P              |     GPIOT_P_12     |     Output    |              |      1.8 V       |  2B  |             |                         User IO                          |
|    F10     |              uart_rx_i.N              |     GPIOT_N_16     |     Input     | 1.8 V LVCMOS |      1.8 V       |  2B  | weak pullup |                    User IO/PLL_EXTFB                     |
|    F11     |               dsi_txd2.N              |     GPIOR_N_14     |     Output    |              |      1.2 V       |  3A  |             |                         User IO                          |
|    F12     |               csi_rxd2.P              |     GPIOR_P_15     |     Input     |              |      1.2 V       |  3A  |             |                         User IO                          |
|    F13     |               dsi_txc.N               |     GPIOR_N_12     |     Output    |              |      1.2 V       |  3A  |             |                         User IO                          |
|    F14     |                --VCC--                |      VCCIO3A       |     Power     |              |      1.2 V       |  3A  |             |                       VCCIO Power                        |
|    F15     |               dsi_txd1.N              |     GPIOR_N_13     |     Output    |              |      1.2 V       |  3A  |             |                         User IO                          |
|     G1     |            lcd_r7_0_io[5].N           | GPIOL_N_09_CLK2_N  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |          User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK           |
|     G2     |            lcd_g7_0_io[5].N           | GPIOL_N_10_CLK3_N  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                    User IO/GCLK/RCLK                     |
|     G3     |            lcd_b7_0_io[0].P           |     GPIOL_P_12     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                         User IO                          |
|     G4     |            lcd_b7_0_io[1].N           |     GPIOL_N_12     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                         User IO                          |
|     G5     |            lcd_g7_0_io[2].P           |     GPIOL_P_11     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |                         User IO                          |
|     G6     |              --VCC/GND--              |      VQPS_GND      |     Power     |              |                  |      |             |                                                          |
|     G7     |                --VCC--                |        VCC         |     Power     |              |                  |      |             |                     0.95 V VCC Power                     |
|     G8     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|     G9     |                --VCC--                |        VCC         |     Power     |              |                  |      |             |                     0.95 V VCC Power                     |
|    G10     |                --VCC--                |      VCCA_TR       |     Power     |              |                  |      |             |                     0.95 V PLL Power                     |
|    G11     |               dsi_txd2.P              |     GPIOR_P_14     |     Output    |              |      1.2 V       |  3A  |             |                         User IO                          |
|    G12     |                --VCC--                |      VCCIO3A       |     Power     |              |      1.2 V       |  3A  |             |                       VCCIO Power                        |
|    G13     |               dsi_txc.P               |     GPIOR_P_12     |     Output    |              |      1.2 V       |  3A  |             |                         User IO                          |
|    G14     |               dsi_txd0.N              | GPIOR_N_11_CLK8_N  |     Output    |              |      1.2 V       |  3A  |             |                    User IO/GCLK/RCLK                     |
|    G15     |               dsi_txd1.P              |     GPIOR_P_13     |     Output    |              |      1.2 V       |  3A  |             |                       User IO/VREF                       |
|     H1     |            lcd_r7_0_io[4].P           | GPIOL_P_09_CLK2_P  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |          User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK           |
|     H2     |  --PCR_READBACK-- / lcd_g7_0_io[4].P  | GPIOL_P_10_CLK3_P  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1B  |             |             User IO/GCLK/RCLK/Configuration              |
|     H3     | --EXT_CONFIG_CLK-- / lcd_b7_0_io[7].N | GPIOL_N_08_CLK1_N  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |             User IO/GCLK/RCLK/Configuration              |
|     H4     |      --CDI3-- / lcd_b7_0_io[3].N      |  GPIOL_N_04_CDI3   | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                  User IO/Configuration                   |
|     H5     |      --CDI2-- / lcd_b7_0_io[2].P      |  GPIOL_P_04_CDI2   | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                User IO/VREF/Configuration                |
|     H6     |                --VCC--                |        VCC         |     Power     |              |                  |      |             |                     0.95 V VCC Power                     |
|     H7     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|     H8     |                --VCC--                |        VCC         |     Power     |              |                  |      |             |                     0.95 V VCC Power                     |
|     H9     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|    H10     |               addr[10].P              | GPIOR_P_08_CLK11_P |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                    User IO/GCLK/RCLK                     |
|    H11     |                reset.N                | GPIOR_N_08_CLK11_N |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                    User IO/GCLK/RCLK                     |
|    H12     |               dsi_txd3.N              | GPIOR_N_10_CLK9_N  |     Output    |              |      1.2 V       |  3A  |             |          User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK           |
|    H13     |               dsi_txd3.P              | GPIOR_P_10_CLK9_P  |     Output    |              |      1.2 V       |  3A  |             |          User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK           |
|    H14     |               dsi_txd0.P              | GPIOR_P_11_CLK8_P  |     Output    |              |      1.2 V       |  3A  |             |                    User IO/GCLK/RCLK                     |
|    H15     |                clk_n.N                | GPIOR_N_09_CLK10_N |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                    User IO/GCLK/RCLK                     |
|     J1     |            lcd_r7_0_io[7].N           | GPIOL_N_07_CLK0_N  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                    User IO/GCLK/RCLK                     |
|     J2     |            lcd_b7_0_io[6].P           | GPIOL_P_08_CLK1_P  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                    User IO/GCLK/RCLK                     |
|     J3     |            lcd_b7_0_io[5].N           |     GPIOL_N_05     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                         User IO                          |
|     J4     |                --VCC--                |      VCCIO1A       |     Power     |              |      1.8 V       |  1A  |             |                       VCCIO Power                        |
|     J5     |              --CRESET_N--             |      CRESET_N      |     Input     |              |      1.8 V       |  1A  |             |                      Configuration                       |
|     J6     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|     J7     |                --VCC--                |        VCC         |     Power     |              |                  |      |             |                     0.95 V VCC Power                     |
|     J8     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|     J9     |                --VCC--                |        VCC         |     Power     |              |                  |      |             |                     0.95 V VCC Power                     |
|    J10     |         --CDI27-- / addr[11].N        |  GPIOR_N_03_CDI27  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                         User IO                          |
|    J11     |                --VCC--                |      VCCIO3B       |     Power     |              |      1.5 V       |  3B  |             |                       VCCIO Power                        |
|    J12     |         --CDI29-- / addr[13].N        |  GPIOR_N_04_CDI29  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                         User IO                          |
|    J13     |         --CDI21-- / addr[7].N         |  GPIOR_N_06_CDI21  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                         User IO                          |
|    J14     |               addr[9].N               |     GPIOR_N_07     |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                         User IO                          |
|    J15     |                clk_p.P                | GPIOR_P_09_CLK10_P |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                    User IO/GCLK/RCLK                     |
|     K1     |            lcd_r7_0_io[6].P           | GPIOL_P_07_CLK0_P  | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                    User IO/GCLK/RCLK                     |
|     K2     |               lcd_vs_o.N              |     GPIOL_N_06     |     Output    | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                         User IO                          |
|     K3     |               lcd_hs_o.P              |     GPIOL_P_06     |     Output    | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                         User IO                          |
|     K4     |            lcd_b7_0_io[4].P           |     GPIOL_P_05     | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                         User IO                          |
|     K5     |               --UNUSED--              |     REF_RES_1A     |     Input     |              |      1.8 V       |  1A  |             |                                                          |
|     K6     |               addr[3].N               |     GPIOB_N_04     |     Output    |  1.5 V SSTL  |      1.5 V       |  4B  |             |                         User IO                          |
|     K7     |       --READBACK_ERR-- / verf0.P      | GPIOB_P_09_CLK13_P |     Input     |  1.5 V SSTL  |      1.5 V       |  4A  |             | User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK/VREF/Configuration |
|     K8     |         --CDI10-- / addr[5].P         |  GPIOB_P_11_CDI10  |     Output    |  1.5 V SSTL  |      1.5 V       |  4A  |             |                         User IO                          |
|     K9     |         --CDI11-- / addr[15].N        |  GPIOB_N_11_CDI11  |     Output    |  1.5 V SSTL  |      1.5 V       |  4A  |             |                         User IO                          |
|    K10     |         --CDI26-- / addr[6].P         |  GPIOR_P_03_CDI26  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                         User IO                          |
|    K11     |         --CDI22-- / addr[8].N         |  GPIOR_N_00_CDI22  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                    User IO/PLL_CLKIN                     |
|    K12     |         --CDI28-- / addr[2].P         |  GPIOR_P_04_CDI28  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                         User IO                          |
|    K13     |         --CDI20-- / addr[14].P        |  GPIOR_P_06_CDI20  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                         User IO                          |
|    K14     |                ba[2].P                |     GPIOR_P_07     |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                         User IO                          |
|    K15     |         --CDI31-- / addr[0].N         |  GPIOR_N_05_CDI31  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                         User IO                          |
|     L1     |      --CDI1-- / lcd_g7_0_io[1].N      |  GPIOL_N_03_CDI1   | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                  User IO/Configuration                   |
|     L2     |                --VCC--                |      VCCIO1A       |     Power     |              |      1.8 V       |  1A  |             |                       VCCIO Power                        |
|     L3     |               --CDONE--               |       CDONE        | Bidirectional |              |      1.8 V       |  1A  |             |                      Configuration                       |
|     L4     |                --VCC--                |       VCCAUX       |     Power     |              |      1.5 V       |  4B  |             |                                                          |
|     L5     |                --VCC--                |      VCCA_BL       |     Power     |              |                  |      |             |                     0.95 V PLL Power                     |
|     L6     |          --SSU_N-- / vref1.P          |  GPIOB_P_04_SSU_N  |     Input     |  1.5 V SSTL  |      1.5 V       |  4B  |             |                User IO/VREF/Configuration                |
|     L7     |           --CDI9-- / ba[0].N          |  GPIOB_N_06_CDI9   |     Output    |  1.5 V SSTL  |      1.5 V       |  4B  |             |                         User IO                          |
|     L8     |                 cke.N                 | GPIOB_N_09_CLK13_N |     Output    |  1.5 V SSTL  |      1.5 V       |  4A  |             |          User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK           |
|     L9     |                ba[1].N                | GPIOB_N_10_CLK12_N |     Output    |  1.5 V SSTL  |      1.5 V       |  4A  |             |                    User IO/GCLK/RCLK                     |
|    L10     |                --VCC--                |      VCCA_BR       |     Power     |              |                  |      |             |                     0.95 V PLL Power                     |
|    L11     |               addr[4].P               | GPIOR_P_00_PLLIN0  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                    User IO/PLL_CLKIN                     |
|    L12     |               addr[12].P              |  GPIOR_P_01_EXTFB  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                    User IO/PLL_EXTFB                     |
|    L13     |         --CDI23-- / addr[1].N         |  GPIOR_N_01_CDI23  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                    User IO/PLL_EXTFB                     |
|    L14     |                --VCC--                |      VCCIO3B       |     Power     |              |      1.5 V       |  3B  |             |                       VCCIO Power                        |
|    L15     |            --CDI30-- / we.P           |  GPIOR_P_05_CDI30  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |                       User IO/VREF                       |
|     M1     |      --CDI0-- / lcd_g7_0_io[0].P      |  GPIOL_P_03_CDI0   | Bidirectional | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                  User IO/Configuration                   |
|     M2     |         --CSO-- / lcd_pclk_o.N        |   GPIOL_N_02_CSO   |     Output    | 1.8 V LVCMOS |      1.8 V       |  1A  |             |        User IO/MIPI_GCLK/MIPI_RCLK/Configuration         |
|     M3     |                --VCC--                |        VCC         |     Power     |              |                  |      |             |                     0.95 V VCC Power                     |
|     M4     |                --TDO--                |        TDO         |     Output    |              |      3.3 V       |  BL  |             |                      Configuration                       |
|     M5     |               --UNUSED--              |     REF_RES_4B     |     Input     |              |      1.5 V       |  4B  |             |                                                          |
|     M6     |           --CDI7-- / dq[9].N          |  GPIOB_N_03_CDI7   | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|     M7     |            --CDI8-- / odt.P           |  GPIOB_P_06_CDI8   |     Output    |  1.5 V SSTL  |      1.5 V       |  4B  |             |                         User IO                          |
|     M8     |                  cs.N                 | GPIOB_N_08_CLK14_N |     Output    |  1.5 V SSTL  |      1.5 V       |  4B  |             |                    User IO/GCLK/RCLK                     |
|     M9     |                dm[0].P                | GPIOB_P_10_CLK12_P |     Output    |  1.5 V SSTL  |      1.5 V       |  4A  |             |                    User IO/GCLK/RCLK                     |
|    M10     |          --CDI15-- / dq[1].N          |  GPIOB_N_13_CDI15  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                         User IO                          |
|    M11     |                --VCC--                |       VCCAUX       |     Power     |              |      1.5 V       |  4B  |             |                                                          |
|    M12     |               --UNUSED--              |     REF_RES_3B     |     Input     |              |      1.5 V       |  3B  |             |                                                          |
|    M13     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|    M14     |           --CDI24-- / ras.P           |  GPIOR_P_02_CDI24  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|    M15     |           --CDI25-- / cas.N           |  GPIOR_N_02_CDI25  |     Output    |  1.5 V SSTL  |      1.5 V       |  3B  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|     N1     |         --CCK-- / spi_sck_o.N         |   GPIOL_N_01_CCK   |     Output    | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                  User IO/Configuration                   |
|     N2     |          --CSI-- / lcd_de_o.P         |   GPIOL_P_02_CSI   |     Output    | 1.8 V LVCMOS |      1.8 V       |  1A  |             |        User IO/MIPI_GCLK/MIPI_RCLK/Configuration         |
|     N3     |                --TMS--                |        TMS         |     Input     |              |      3.3 V       |  BL  |             |                      Configuration                       |
|     N4     |                --TCK--                |        TCK         |     Input     |              |      3.3 V       |  BL  |             |                      Configuration                       |
|     N5     |                --VCC--                |      VCCIO4B       |     Power     |              |      1.5 V       |  4B  |             |                       VCCIO Power                        |
|     N6     |          --CDI6-- / dq[13].P          |  GPIOB_P_03_CDI6   | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |               User IO/MIPI_GCLK/MIPI_RCLK                |
|     N7     |                --VCC--                |      VCCIO4B       |     Power     |              |      1.5 V       |  4B  |             |                       VCCIO Power                        |
|     N8     |                dq[11].P               | GPIOB_P_08_CLK14_P | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |                    User IO/GCLK/RCLK                     |
|     N9     |                --VCC--                |      VCCIO4A       |     Power     |              |      1.5 V       |  4A  |             |                       VCCIO Power                        |
|    N10     |          --CDI14-- / dq[5].P          |  GPIOB_P_13_CDI14  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                         User IO                          |
|    N11     |                --VCC--                |      VCCIO4A       |     Power     |              |      1.5 V       |  4A  |             |                       VCCIO Power                        |
|    N12     |               --UNUSED--              |     REF_RES_4A     |     Input     |              |      1.5 V       |  4A  |             |                                                          |
|    N13     |                led_o[4]               |      GPIOR_24      |     Output    | 3.3 V LVCMOS |      3.3 V       |  BR  |             |                         User IO                          |
|    N14     |                led_o[3]               |      GPIOR_21      |     Output    | 3.3 V LVCMOS |      3.3 V       |  BR  |             |                         User IO                          |
|    N15     |                --VCC--                |     VCCIO33_BR     |     Power     |              |      3.3 V       |  BR  |             |                       VCCIO Power                        |
|     P1     |        --SSL_N-- / spi_ssn_o.P        |  GPIOL_P_01_SSL_N  |     Output    | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                  User IO/Configuration                   |
|     P2     |               --UNUSED--              | GPIOL_P_00_PLLIN0  |               |              |      1.8 V       |  1A  |             |                    User IO/PLL_CLKIN                     |
|     P3     |                --TDI--                |        TDI         |     Input     |              |      3.3 V       |  BL  |             |                      Configuration                       |
|     P4     |                --VCC--                |     VCCIO33_BL     |     Power     |              |      3.3 V       |  BL  |             |                       VCCIO Power                        |
|     P5     |                dq[15].N               |     GPIOB_N_00     | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |                    User IO/PLL_CLKIN                     |
|     P6     |                dq[12].N               |     GPIOB_N_01     | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |                    User IO/PLL_EXTFB                     |
|     P7     |         --CDI5-- / dqs_n[1].N         |  GPIOB_N_02_CDI5   | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |                         User IO                          |
|     P8     |                dq[8].N                | GPIOB_N_07_CLK15_N | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |                    User IO/GCLK/RCLK                     |
|     P9     |          --CDI13-- / dq[3].N          |  GPIOB_N_12_CDI13  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                         User IO                          |
|    P10     |          --CDI17-- / dq[6].N          |  GPIOB_N_14_CDI17  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                    User IO/MIPI_GCLK                     |
|    P11     |                dq[7].P                | GPIOB_P_17_PLLIN1  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                    User IO/PLL_CLKIN                     |
|    P12     |                dq[4].N                |     GPIOB_N_17     | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                    User IO/PLL_CLKIN                     |
|    P13     |                led_o[5]               |      GPIOR_27      |     Output    | 3.3 V LVCMOS |      3.3 V       |  BR  |             |                         User IO                          |
|    P14     |                led_o[1]               |      GPIOR_22      |     Output    | 3.3 V LVCMOS |      3.3 V       |  BR  |             |                         User IO                          |
|    P15     |                led_o[0]               |      GPIOR_25      |     Output    | 3.3 V LVCMOS |      3.3 V       |  BR  |             |                         User IO                          |
|     R1     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
|     R2     |              cmos_ctl3.N              |     GPIOL_N_00     |     Output    | 1.8 V LVCMOS |      1.8 V       |  1A  |             |                    User IO/PLL_CLKIN                     |
|     R3     |              cmos_data[0]             |      GPIOL_01      |     Input     | 3.3 V LVCMOS |      3.3 V       |  BL  |             |                         User IO                          |
|     R4     |              cmos_data[1]             |      GPIOL_02      |     Input     | 3.3 V LVCMOS |      3.3 V       |  BL  |             |                         User IO                          |
|     R5     |                dq[14].P               | GPIOB_P_00_PLLIN1  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |                    User IO/PLL_CLKIN                     |
|     R6     |                dq[10].P               |  GPIOB_P_01_EXTFB  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |                    User IO/PLL_EXTFB                     |
|     R7     |          --CDI4-- / dqs[1].P          |  GPIOB_P_02_CDI4   | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4B  |             |                         User IO                          |
|     R8     |                dm[1].P                | GPIOB_P_07_CLK15_P |     Output    |  1.5 V SSTL  |      1.5 V       |  4B  |             |                    User IO/GCLK/RCLK                     |
|     R9     |          --CDI12-- / dq[0].P          |  GPIOB_P_12_CDI12  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                         User IO                          |
|    R10     |          --CDI16-- / dq[2].P          |  GPIOB_P_14_CDI16  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                    User IO/MIPI_GCLK                     |
|    R11     |         --CDI19-- / dqs_n[0].N        |  GPIOB_N_15_CDI19  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                         User IO                          |
|    R12     |          --CDI18-- / dqs[0].P         |  GPIOB_P_15_CDI18  | Bidirectional |  1.5 V SSTL  |      1.5 V       |  4A  |             |                         User IO                          |
|    R13     |                clk_25m                |  GPIOR_29_PLLIN2   |     Input     | 3.3 V LVCMOS |      3.3 V       |  BR  |             |                    User IO/PLL_CLKIN                     |
|    R14     |                led_o[2]               |      GPIOR_28      |     Output    | 3.3 V LVCMOS |      3.3 V       |  BR  |             |                         User IO                          |
|    R15     |                --GND--                |        GND         |     Power     |              |                  |      |             |                          Ground                          |
+------------+---------------------------------------+--------------------+---------------+--------------+------------------+------+-------------+----------------------------------------------------------+
