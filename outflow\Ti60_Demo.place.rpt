
Efinix FPGA Placement and Routing.
Version: 2025.********** 
Date: Tue Jul 22 18:24:40 2025

Copyright (C) 2013 - 2025 Efinix, Inc. All rights reserved.
 
Family: Titanium 
Device: Ti60F225
Top-level Entity Name: Ti60_Demo
Elapsed time for packing: 0 hours 0 minutes 0 seconds

---------- Resource Summary (begin) ----------
Inputs: 67 / 1703 (3.93%)
Outputs: 483 / 2267 (21.31%)
Global Clocks (GBUF): 15 / 32 (46.88%)
Regional Clocks (RBUF): 0 / 32 (0.00%)
	RBUF: Core: 0 / 16 (0.00%)
	RBUF: Periphery: 0 / 8 (0.00%)
	RBUF: Multi-Region: 0 / 8 (0.00%)
XLRs: 9880 / 60800 (16.25%)
	XLRs needed for Logic: 3043 / 60800 (5.00%)
	XLRs needed for Logic + FF: 1976 / 60800 (3.25%)
	XLRs needed for Adder: 891 / 60800 (1.47%)
	XLRs needed for Adder + FF: 176 / 60800 (0.29%)
	XLRs needed for FF: 3072 / 60800 (5.05%)
	XLRs needed for SRL8: 722 / 14720 (4.90%)
	XLRs needed for SRL8+FF: 0 / 14720 (0.00%)
	XLRs needed for Routing: 0 / 60800 (0.00%)
Memory Blocks: 69 / 256 (26.95%)
DSP Blocks: 1 / 160 (0.62%)
---------- Resource Summary (end) ----------


---------- DSP Packer Summary (begin) ----------

	DSP48 blocks required to legally pack design: 1
	DSP48 blocks recoverable by optimizing:
		-> DSP24/12 control signals & parameters: 0
	Best case scenario DSP count after optimizing: 1

---------- DSP Packer Summary (end) ----------


---------- IO Interface Summary (begin) ----------

+------------------------+--------------+
| Missing Interface Pins | Input/Output |
+------------------------+--------------+
|      cmos_data[0]      |    Input     |
|      cmos_data[1]      |    Input     |
|     lcd_g7_0_i[0]      |    Input     |
|     lcd_g7_0_i[1]      |    Input     |
|     lcd_b7_0_i[2]      |    Input     |
|     lcd_b7_0_i[3]      |    Input     |
|     lcd_b7_0_i[4]      |    Input     |
|     lcd_b7_0_i[5]      |    Input     |
|     lcd_r7_0_i[6]      |    Input     |
|     lcd_r7_0_i[7]      |    Input     |
|     lcd_b7_0_i[6]      |    Input     |
|     lcd_b7_0_i[7]      |    Input     |
|      clk_pixel_2x      |    Input     |
|     lcd_r7_0_i[4]      |    Input     |
|     lcd_r7_0_i[5]      |    Input     |
|     lcd_g7_0_i[4]      |    Input     |
|     lcd_g7_0_i[5]      |    Input     |
|     lcd_g7_0_i[2]      |    Input     |
|     lcd_g7_0_i[3]      |    Input     |
|     lcd_b7_0_i[0]      |    Input     |
|     lcd_b7_0_i[1]      |    Input     |
|     lcd_g7_0_i[6]      |    Input     |
|     lcd_g7_0_i[7]      |    Input     |
|     lcd_r7_0_i[2]      |    Input     |
|     lcd_r7_0_i[3]      |    Input     |
|     lcd_r7_0_i[0]      |    Input     |
|     lcd_r7_0_i[1]      |    Input     |
|      lcd_tp_scl_i      |    Input     |
|      lcd_tp_sda_i      |    Input     |
|      lcd_tp_int_i      |    Input     |
|      cmos_data[4]      |    Input     |
|      cmos_data[6]      |    Input     |
|      cmos_data[2]      |    Input     |
|       cmos_vsync       |    Input     |
|     i_dqs_n_lo[1]      |    Input     |
|     i_dqs_n_hi[1]      |    Input     |
|       csi_sda_i        |    Input     |
|      dsi_refclk_i      |    Input     |
|        clk_54m         |    Input     |
|       csi_scl_i        |    Input     |
|       csi_ctl0_i       |    Input     |
|       csi_ctl1_i       |    Input     |
|     i_dqs_n_lo[0]      |    Input     |
|     i_dqs_n_hi[0]      |    Input     |
|       uart_rx_i        |    Input     |
|        clk_25m         |    Input     |
|    dsi_txd3_lp_p_i     |    Input     |
|    dsi_txd3_lp_n_i     |    Input     |
|    dsi_txd0_lp_p_i     |    Input     |
|    dsi_txd0_lp_n_i     |    Input     |
|    dsi_txd1_lp_p_i     |    Input     |
|    dsi_txd1_lp_n_i     |    Input     |
|    dsi_txd2_lp_p_i     |    Input     |
|    dsi_txd2_lp_n_i     |    Input     |
|    csi_rxd2_hs_i[0]    |    Input     |
|    csi_rxd2_hs_i[1]    |    Input     |
|    csi_rxd2_hs_i[2]    |    Input     |
|    csi_rxd2_hs_i[3]    |    Input     |
|    csi_rxd2_hs_i[4]    |    Input     |
|    csi_rxd2_hs_i[5]    |    Input     |
|    csi_rxd2_hs_i[6]    |    Input     |
|    csi_rxd2_hs_i[7]    |    Input     |
|    csi_rxd2_lp_p_i     |    Input     |
|    csi_rxd2_lp_n_i     |    Input     |
|    csi_rxd3_hs_i[0]    |    Input     |
|    csi_rxd3_hs_i[1]    |    Input     |
|    csi_rxd3_hs_i[2]    |    Input     |
|    csi_rxd3_hs_i[3]    |    Input     |
|    csi_rxd3_hs_i[4]    |    Input     |
|    csi_rxd3_hs_i[5]    |    Input     |
|    csi_rxd3_hs_i[6]    |    Input     |
|    csi_rxd3_hs_i[7]    |    Input     |
|    csi_rxd3_lp_p_i     |    Input     |
|    csi_rxd3_lp_n_i     |    Input     |
|    csi_rxd1_hs_i[0]    |    Input     |
|    csi_rxd1_hs_i[1]    |    Input     |
|    csi_rxd1_hs_i[2]    |    Input     |
|    csi_rxd1_hs_i[3]    |    Input     |
|    csi_rxd1_hs_i[4]    |    Input     |
|    csi_rxd1_hs_i[5]    |    Input     |
|    csi_rxd1_hs_i[6]    |    Input     |
|    csi_rxd1_hs_i[7]    |    Input     |
|    csi_rxd1_lp_p_i     |    Input     |
|    csi_rxd1_lp_n_i     |    Input     |
|      cmos_data[7]      |    Input     |
|      cmos_sdat_IN      |    Input     |
|       cmos_ctl1        |    Input     |
|      cmos_data[5]      |    Input     |
|      cmos_data[3]      |    Input     |
|       cmos_href        |    Input     |
+------------------------+--------------+

----------- IO Interface Summary (end) ----------

