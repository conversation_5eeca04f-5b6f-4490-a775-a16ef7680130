{"args": ["-o", "dsi_tx", "--base_path", "E:\\fpga\\yilingsi\\A2020_demo\\Ti60F225_A2020_LVDS_Display_30FPS\\Ti60F225_AD2020_LVDS_Display_30FPS\\ip", "--vlnv", {"vendor": "efinixinc.com", "library": "mipi", "name": "efx_dsi_tx", "version": "5.14"}], "conf": {"tLPX_NS": "60", "tINIT_NS": "1000", "NUM_DATA_LANE": "4", "HS_BYTECLK_MHZ": "50", "CLOCK_FREQ_MHZ": "50", "DPHY_CLOCK_MODE": "\"Continuous\"", "PIXEL_FIFO_DEPTH": "4096", "tLP_EXIT_NS": "100", "tCLK_ZERO_NS": "280", "tCLK_TRAIL_NS": "100", "tCLK_PRE_NS": "10", "tCLK_POST_NS": "100", "tCLK_PREPARE_NS": "50", "tWAKEUP_NS": "1000", "tHS_ZERO_NS": "200", "tHS_TRAIL_NS": "65", "tHS_EXIT_NS": "120", "Pack_48": "1'b1", "Pack_64": "1'b0", "tHS_PREPARE_NS": "50", "BTA_TIMEOUT_NS": "1000", "tD_TERM_EN_NS": "35", "tHS_PREPARE_ZERO_NS": "145", "ENABLE_V_LPM_BTA": "1'b0", "PACKET_SEQUENCES": "1", "HS_CMD_WDATAFIFO_DEPTH": "512", "LP_CMD_WDATAFIFO_DEPTH": "512", "LP_CMD_RDATAFIFO_DEPTH": "512", "MAX_HRES": "1024", "ENABLE_BIDIR": "1'b1", "ENABLE_EOTP": "1'b0", "Pack_60": "1'b0", "Pack_36": "1'b0", "MIPI_DSI_TX_DEBUG": "1'b0"}, "output": {"external_source_source": ["dsi_tx\\dsi_tx_tmpl.sv", "dsi_tx\\dsi_tx_tmpl.vhd", "dsi_tx\\dsi_tx.sv", "dsi_tx\\dsi_tx_define.svh"], "external_testbench_modelsim": ["dsi_tx\\Testbench\\modelsim\\dsi_tx.sv"], "external_testbench_testbench": ["dsi_tx\\Testbench\\data_pack.v", "dsi_tx\\Testbench\\dual_clock_fifo.v", "dsi_tx\\Testbench\\panel_config.v", "dsi_tx\\Testbench\\shift_reg.v", "dsi_tx\\Testbench\\simple_dual_port_ram.v", "dsi_tx\\Testbench\\true_dual_port_ram.v", "dsi_tx\\Testbench\\vga_gen.v", "dsi_tx\\Testbench\\dsi_hs_cmd_tb.mem", "dsi_tx\\Testbench\\dsi_tb.mem", "dsi_tx\\Testbench\\TI60F225_MIPI_dsi_tb.v", "dsi_tx\\Testbench\\modelsim.do", "dsi_tx\\Testbench\\dsi_tx.sv", "dsi_tx\\Testbench\\dsi_tx_define.svh"], "external_example_example": ["dsi_tx\\Ti60F225_devkit\\data_pack.v", "dsi_tx\\Ti60F225_devkit\\dual_clock_fifo.v", "dsi_tx\\Ti60F225_devkit\\panel_config.v", "dsi_tx\\Ti60F225_devkit\\reset.v", "dsi_tx\\Ti60F225_devkit\\shift_reg.v", "dsi_tx\\Ti60F225_devkit\\simple_dual_port_ram.v", "dsi_tx\\Ti60F225_devkit\\TI60F225_MIPI_dsi.v", "dsi_tx\\Ti60F225_devkit\\true_dual_port_ram.v", "dsi_tx\\Ti60F225_devkit\\vga_gen.v", "dsi_tx\\Ti60F225_devkit\\Panel_1080p_reg.mem", "dsi_tx\\Ti60F225_devkit\\ti60f225_demo.sdc", "dsi_tx\\Ti60F225_devkit\\dsi_tx.sv", "dsi_tx\\Ti60F225_devkit\\dsi_tx_define.svh", "dsi_tx\\Ti60F225_devkit\\ti60f225_demo.peri.xml", "dsi_tx\\Ti60F225_devkit\\ti60f225_demo.xml"], "external_testbench_ncsim": ["dsi_tx\\Testbench\\ncsim\\dsi_tx.sv"], "external_testbench_synopsys": ["dsi_tx\\Testbench\\synopsys\\dsi_tx.sv"]}, "ooc_synthesis": {}, "sw_version": "2025.**********", "generated_date": "2025-10-06T09:29:10.671119+00:00"}