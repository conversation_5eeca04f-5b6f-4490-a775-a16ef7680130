`timescale  1ns/1ns
module  vga_pic
(
    input  wire                         vga_clk                    ,//输入工作时钟,频率25MHz
    input  wire                         sys_rst_n                  ,//输入复位信号,低电平有效
    input  wire        [  11:0]         pix_x                      ,//输入有效显示区域像素点X轴坐标
    input  wire        [  11:0]         pix_y                      ,//输入有效显示区域像素点Y轴坐标

    input                               data_en                    ,//align
    input              [   7:0]         data                       ,

    output reg         [  23:0]         pix_data,                    //输出像素点色彩信息
    output reg   data_enable,
    output reg   data_enable2
);

//********************************************************************//
//****************** Parameter and Internal Signal *******************//
//********************************************************************//
//parameter define
parameter   CHAR_W  =   'd320 ,   //字符宽度
            CHAR_H  =   'd64  ;   //字符高度
parameter   CHAR2_W  =   'd352 ,   //字符宽度
            CHAR2_H  =   'd64  ;   //字符高度
parameter   CHAR_B_H=   15 ,   //字符开始X轴坐标
            CHAR_B_V=   550 +30 ;   //字符开始Y轴坐标
// parameter   CHAR_B_H=   (1280-CHAR_W)/2 ,   //字符开始X轴坐标
//             CHAR_B_V=   (720-CHAR_H)/2 ;   //字符开始Y轴坐标
parameter   CHAR2_B_H=  15  ,   //字符开始X轴坐标
            CHAR2_B_V=  620 + 30;   //字符开始Y轴坐标
// parameter   CHAR2_B_H=  (1280-CHAR2_W)/2 +5  ,   //字符开始X轴坐标
//             CHAR2_B_V=  (720-CHAR_H)/2 +CHAR_H + 40;   //字符开始Y轴坐标
parameter   BLACK   =   24'h00_00_00,   //黑色
            WHITE   =   24'hFF_FF_FF,   //白色
            DIY_COLOR  =   24'hFF_FF_00,  //自定义颜色1
            DIY_COLOR2  =   24'h00_FF_FF;   //自定义颜色2

//wire  define
wire    [11:0]   char_x  ;   //字符显示X轴坐标
wire    [11:0]   char_y  ;   //字符显示Y轴坐标
wire    [11:0]   char2_x  ;   //字符显示X轴坐标
wire    [11:0]   char2_y  ;   //字符显示Y轴坐标

//reg   define
reg     [319:0] char    [63:0]  ;   //字符数据
reg     [351:0] char2    [63:0]  ;   //字符数据2

//********************************************************************//
//***************************** Main Code ****************************//
//********************************************************************//

//字符显示坐标
assign  char_x  =   (((pix_x >= CHAR_B_H) && (pix_x < (CHAR_B_H + CHAR_W)))
                    && ((pix_y >= CHAR_B_V) && (pix_y < (CHAR_B_V + CHAR_H))))
                    ? (pix_x - CHAR_B_H) : 12'hFFF;
assign  char_y  =   (((pix_x >= CHAR_B_H) && (pix_x < (CHAR_B_H + CHAR_W)))
                    && ((pix_y >= CHAR_B_V) && (pix_y < (CHAR_B_V + CHAR_H))))
                    ? (pix_y - CHAR_B_V) : 12'hFFF;

assign  char2_x  =   (((pix_x >= CHAR2_B_H) && (pix_x < (CHAR2_B_H + CHAR2_W)))
                    && ((pix_y >= CHAR2_B_V) && (pix_y < (CHAR2_B_V + CHAR2_H))))
                    ? (pix_x - CHAR2_B_H) : 12'hFFF;
assign  char2_y  =   (((pix_x >= CHAR2_B_H) && (pix_x < (CHAR2_B_H + CHAR2_W)))
                    && ((pix_y >= CHAR2_B_V) && (pix_y < (CHAR2_B_V + CHAR2_H))))
                    ? (pix_y - CHAR2_B_V) : 12'hFFF;

//char:字符数据
always@(posedge vga_clk)
    begin
        char[0]     <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[1]     <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[2]     <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[3]     <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[4]     <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[5]     <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[6]     <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[7]     <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[8]     <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[9]     <=  320'h00060000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[10]    <=  320'h000F00007FFE0000001FC000001F8000001FC000001F800000000000001FC0000000000000000000;
        char[11]    <=  320'h001F000007FFC000007FF000007FE000007FF000007FE00000000000007FF0000000000000000000;
        char[12]    <=  320'h001F000007C1F00001E0F80000F0F00001E0F80000F0F0000000000001E0F8000000000000000000;
        char[13]    <=  320'h001F00000780780003803C0001E0780003803C0001E078000000000003803C000000000000000000;
        char[14]    <=  320'h001F000007803C0007001E0001C03C0007001E0001C03C000000000007001E000000000000000000;
        char[15]    <=  320'h0037800007801E0007001E0003C01C0007001E0003C01C000000000007001E000000000000000000;
        char[16]    <=  320'h0033800007800E000E000F0007801E000E000F0007801E00000000000E000F000000000000000000;
        char[17]    <=  320'h0033800007800F000F000F0007800E000F000F0007800E00000000000F000F000000000000000000;
        char[18]    <=  320'h00238000078007000F000F0007000F000F000F0007000F00000000000F000F000000000000000000;
        char[19]    <=  320'h0063C000078007800F800F000F000F000F800F000F000F00000000000F800F000000000000000000;
        char[20]    <=  320'h0061C000078007800F800F000F000F000F800F000F000F00000000000F800F000000000000000000;
        char[21]    <=  320'h0061C000078007800F800F000F0007000F800F000F000700000000000F800F000000000000000000;
        char[22]    <=  320'h0041C000078003C007000F000E00078007000F000E0007800000000007000F000600000006000000;
        char[23]    <=  320'h00C1E000078003C000001E001E00078000001E001E0007800000000000001E007E3E0F807E3E0F80;
        char[24]    <=  320'h00C0E000078003C000001E001E00078000001E001E0007800000000000001E001E7F3FC01E7F3FC0;
        char[25]    <=  320'h00C0E000078003C000001C001E00078000001C001E0007800000000000001C001E8FE3C01E8FE3C0;
        char[26]    <=  320'h0080E000078003C000003C001E00078000003C001E0007800000000000003C001F07C1E01F07C1E0;
        char[27]    <=  320'h0180F000078003C0000078001E000780000078001E00078000000000000078001F07C1E01F07C1E0;
        char[28]    <=  320'h01807000078003C0000070001E000780000070001E00078000000000000070001E0781E01E0781E0;
        char[29]    <=  320'h01807000078003C00000E0001E0007800000E0001E000780000000000000E0001E0781E01E0781E0;
        char[30]    <=  320'h01007000078003C00001C0001E0007800001C0001E000780000000000001C0001E0781E01E0781E0;
        char[31]    <=  320'h03007800078003C0000380001E000780000380001E00078000000000000380001E0781E01E0781E0;
        char[32]    <=  320'h03FFF800078003C0000700001E000780000700001E00078000000000000700001E0781E01E0781E0;
        char[33]    <=  320'h03FFF800078003C0000E00001E000780000E00001E00078000000000000E00001E0781E01E0781E0;
        char[34]    <=  320'h02003800078003C0001C00001E000780001C00001E00078000000000001C00001E0781E01E0781E0;
        char[35]    <=  320'h0600380007800380001800000E000700001800000E00070000000000001800001E0781E01E0781E0;
        char[36]    <=  320'h06003C0007800780003000000F000F00003000000F000F0000000000003000001E0781E01E0781E0;
        char[37]    <=  320'h06003C0007800780006000000F000F00006000000F000F0000000000006000001E0781E01E0781E0;
        char[38]    <=  320'h04001C000780078000C000000F000F0000C000000F000F000000000000C000001E0781E01E0781E0;
        char[39]    <=  320'h0C001C00078007000180010007000E000180010007000E0000000000018001001E0781E01E0781E0;
        char[40]    <=  320'h0C001E0007800F000380030007800E000380030007800E0000000000038003001E0781E01E0781E0;
        char[41]    <=  320'h0C001E0007800E000700030007801E000700030007801E0000000000070003001E0781E01E0781E0;
        char[42]    <=  320'h0C000E0007801E000600030003C01C000600030003C01C0000000000060003001E0781E01E0781E0;
        char[43]    <=  320'h18000E0007803C000C00060001C03C000C00060001C03C00000000000C0006001E0781E01E0781E0;
        char[44]    <=  320'h18000F00078078001C000E0001E078001C000E0001E07800000000001C000E001E0781E01E0781E0;
        char[45]    <=  320'h18000F0007C3F0001FFFFE0000F0F0001FFFFE0000F0F000000000001FFFFE001E0781E01E0781E0;
        char[46]    <=  320'h3C000F8007FFC0001FFFFE00007FE0001FFFFE00007FE000000000001FFFFE001E0781E01E0781E0;
        char[47]    <=  320'hFF003FE07FFE00001FFFFE00001F80001FFFFE00001F8000000000001FFFFE007F9FE7F87F9FE7F8;
        char[48]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[49]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[50]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[51]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[52]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[53]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[54]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[55]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[56]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[57]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[58]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[59]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[60]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[61]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[62]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char[63]    <=  320'h00000000000000000000000000000000000000000000000000000000000000000000000000000000;
    end
    //char2:字符数据
always@(posedge vga_clk)
begin
        char2[0]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[1]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[2]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[3]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[4]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[5]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[6]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[7]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[8]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[9]     <=  352'h0000000000000000000000000000000000000000000000000000000000000000000FE0000000000000000000;
        char2[10]    <=  352'h00038000001FC000003FC000001F80000000000007FFFF80001FC000001F800000381800003F0000001F8000;
        char2[11]    <=  352'h00078000007FF00000FFF000007FE0000000000007FFFF80007FF000007FE00000700C0001FFC000007FE000;
        char2[12]    <=  352'h000F800001E0F80003E07C0000F0F0000000000007FFFF0001E0F80000F0F00000E002000383E00000F0F000;
        char2[13]    <=  352'h03FF800003803C0007801E0001E07800000000000780030003803C0001E0780001C001000700F00001E07800;
        char2[14]    <=  352'h03FF800007001E000F000F0001C03C00000000000600060007001E0001C03C00038001800600780001C03C00;
        char2[15]    <=  352'h001F800007001E000F000F0003C01C00000000000600060007001E0003C01C00070000800E00780003C01C00;
        char2[16]    <=  352'h000F80000E000F001E00078007801E00000000000C000C000E000F0007801E000701FCC00E003C0007801E00;
        char2[17]    <=  352'h000F80000F000F001E00078007800E00000000000C001C000F000F0007800E000E033CC00F003C0007800E00;
        char2[18]    <=  352'h000F80000F000F001E00078007000F0000000000080018000F000F0007000F000E061CC00F003C0007000F00;
        char2[19]    <=  352'h000F80000F800F001E0007800F000F0000000000000038000F800F000F000F001E061C6007003C000F000F00;
        char2[20]    <=  352'h000F80000F800F001E0007800F000F0000000000000030000F800F000F000F001E0C186000003C000F000F00;
        char2[21]    <=  352'h000F80000F800F001F0007800F00070000000000000070000F800F000F0007001C1C386000003C000F000700;
        char2[22]    <=  352'h000F800007000F000F800F000E000780000000000000600007000F000E0007801C1C3860000038000E000780;
        char2[23]    <=  352'h000F800000001E000FC00E001E0007801FF8FF800000E00000001E001E0007803C183860000078001E000780;
        char2[24]    <=  352'h000F800000001E0007E01E001E00078003E01C000000C00000001E001E0007803C3838600000F0001E000780;
        char2[25]    <=  352'h000F800000001C0003F83C001E00078001E018000001C00000001C001E0007803C3838600001E0001E000780;
        char2[26]    <=  352'h000F800000003C0001FE70001E00078001E018000001800000003C001E0007803C3838600003C0001E000780;
        char2[27]    <=  352'h000F800000007800007FC0001E00078000F0300000038000000078001E0007803C703060003F00001E000780;
        char2[28]    <=  352'h000F80000000700000FFE0001E0007800070600000038000000070001E0007803C703060003FC0001E000780;
        char2[29]    <=  352'h000F80000000E00001C7F0001E00078000786000000300000000E0001E0007803C7070600001F0001E000780;
        char2[30]    <=  352'h000F80000001C0000381F8001E000780003CC000000700000001C0001E0007803C707060000078001E000780;
        char2[31]    <=  352'h000F8000000380000700FC001E000780001C800000070000000380001E0007803C7070C000003C001E000780;
        char2[32]    <=  352'h000F8000000700000F003E001E000780001F8000000F0000000700001E0007803C7070C000001C001E000780;
        char2[33]    <=  352'h000F8000000E00001E001F001E000780000F0000000E0000000E00001E0007803C7070C000001E001E000780;
        char2[34]    <=  352'h000F8000001C00001E000F001E000780000F0000000E0000001C00001E0007803C70E18000000E001E000780;
        char2[35]    <=  352'h000F8000001800003C000F800E00070000078000001E0000001800000E0007001E70E18000000F000E000700;
        char2[36]    <=  352'h000F8000003000003C0007800F000F00000F8000001E0000003000000F000F001E39730000000F000F000F00;
        char2[37]    <=  352'h000F8000006000003C0007800F000F00000FC000001E0000006000000F000F001E3F3E0000000F000F000F00;
        char2[38]    <=  352'h000F800000C000003C0007800F000F000019C000001E000000C000000F000F000E1C3C000E000F000F000F00;
        char2[39]    <=  352'h000F8000018001003C00078007000E000030E000001E00000180010007000E000F0000601F000F0007000E00;
        char2[40]    <=  352'h000F8000038003003C00078007800E000030F000003E00000380030007800E000F0000401F000F0007800E00;
        char2[41]    <=  352'h000F8000070003001E00070007801E0000607000003E00000700030007801E00078000C01F001E0007801E00;
        char2[42]    <=  352'h000F8000060003001E000F0003C01C0000C07800003E00000600030003C01C00038001801E001E0003C01C00;
        char2[43]    <=  352'h000F80000C0006000F000E0001C03C0000C03800003E00000C00060001C03C0003C003000E003C0001C03C00;
        char2[44]    <=  352'h000F80001C000E0007801E0001E0780001801C00003E00001C000E0001E0780001E007000F00780001E07800;
        char2[45]    <=  352'h001FC0001FFFFE0003E0780000F0F00003801E00003E00001FFFFE0000F0F00000F81C000780F00000F0F000;
        char2[46]    <=  352'h03FFFE001FFFFE0001FFF000007FE00007801F00003E00001FFFFE00007FE000003FF80001FFE000007FE000;
        char2[47]    <=  352'h03FFFE001FFFFE00003FC000001F80003FE0FFC0001C00001FFFFE00001F8000000FE000007F0000001F8000;
        char2[48]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[49]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[50]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[51]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[52]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[53]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[54]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[55]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[56]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[57]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[58]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[59]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[60]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[61]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[62]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
        char2[63]    <=  352'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
   
end

//pix_data:输出像素点色彩信息,根据当前像素点坐标指定当前像素点颜色数据
always@(posedge vga_clk or negedge sys_rst_n)
    if(sys_rst_n == 1'b0)
        pix_data    <= BLACK;
    else    if((((pix_x >= (CHAR_B_H - 1'b1))
                && (pix_x < (CHAR_B_H + CHAR_W -1'b1)))
                && ((pix_y >= CHAR_B_V) && (pix_y < (CHAR_B_V + CHAR_H))))
                && (char[char_y][319 - char_x] == 1'b1))
        pix_data    <=  DIY_COLOR;
    else if ((((pix_x >= (CHAR2_B_H - 1'b1))
        && (pix_x < (CHAR2_B_H + CHAR2_W -1'b1)))
        && ((pix_y >= CHAR2_B_V) && (pix_y < (CHAR2_B_V + CHAR2_H))))
        && (char2[char2_y][351 - char2_x] == 1'b1)) begin
            pix_data    <=  DIY_COLOR2;
    end
    else
        pix_data    <=  BLACK;
    

        
        always@(posedge vga_clk or negedge sys_rst_n)
        begin
            if(!sys_rst_n)
                begin
                    data_enable   <=  'd0 ;
                end
            else if(((((pix_x >= (CHAR_B_H - 1'b1))
                && (pix_x < (CHAR_B_H + CHAR_W -1'b1)))
                && ((pix_y >= CHAR_B_V) && (pix_y < (CHAR_B_V + CHAR_H))))
                && (char[char_y][319 - char_x] == 1'b1)))
                begin
                    data_enable   <=  1 ;
                end
            else
                begin
                    data_enable   <=  'd0 ;
                end
        end
        always@(posedge vga_clk or negedge sys_rst_n)
        begin
            if(!sys_rst_n)
                begin
                    data_enable2   <=  'd0 ;
                end
            else if(((((pix_x >= (CHAR2_B_H - 1'b1))
                && (pix_x < (CHAR2_B_H + CHAR2_W -1'b1)))
                && ((pix_y >= CHAR2_B_V) && (pix_y < (CHAR2_B_V + CHAR2_H))))
                && (char2[char2_y][351 - char2_x] == 1'b1)) )
                begin
                    data_enable2   <=  1 ;
                end
            else
                begin
                    data_enable2   <=  'd0 ;
                end
        end



endmodule
// `timescale  1ns/1ns
// module  vga_pic
// (
//     input  wire                         vga_clk                    ,//输入工作时钟,频率25MHz
//     input  wire                         sys_rst_n                  ,//输入复位信号,低电平有效
//     input  wire        [  11:0]         pix_x                      ,//输入有效显示区域像素点X轴坐标
//     input  wire        [  11:0]         pix_y                      ,//输入有效显示区域像素点Y轴坐标


//     input                               data_en                    ,//align
//     input              [   7:0]         data                       ,

//     output reg         [  23:0]         pix_data,                    //输出像素点色彩信息
//     output reg   data_enable
// );

// //********************************************************************//
// //****************** Parameter and Internal Signal *******************//
// //********************************************************************//
// //parameter define
// parameter   CHAR_W  =   'd448 ,   //字符宽度
//             CHAR_H  =   'd64  ;   //字符高度
// parameter   CHAR2_W  =   'd384 ,   //字符宽度
//             CHAR2_H  =   'd48  ;   //字符高度
// parameter   CHAR_B_H=   (1280-CHAR_W)/2 ,   //字符开始X轴坐标
//             CHAR_B_V=   (720-CHAR_H)/2 ;   //字符开始Y轴坐标
// parameter   CHAR2_B_H=  (1280-CHAR2_W)/2 +5  ,   //字符开始X轴坐标
//             CHAR2_B_V=  (720-CHAR_H)/2 +CHAR_H + 40;   //字符开始Y轴坐标
// parameter   BLACK   =   24'h00_00_00,   //黑色
//             WHITE   =   24'hFF_FF_FF,   //白色
//             DIY_COLOR  =   24'hFF_FF_00,  //自定义颜色1
//             DIY_COLOR2  =   24'h00_FF_FF;   //自定义颜色2

// //wire  define
// wire    [11:0]   char_x  ;   //字符显示X轴坐标
// wire    [11:0]   char_y  ;   //字符显示Y轴坐标
// wire    [11:0]   char2_x  ;   //字符显示X轴坐标
// wire    [11:0]   char2_y  ;   //字符显示Y轴坐标

// //reg   define
// reg     [447:0] char    [63:0]  ;   //字符数据
// reg     [383:0] char2    [47:0]  ;   //字符数据2

// //********************************************************************//
// //***************************** Main Code ****************************//
// //********************************************************************//

// //字符显示坐标
// assign  char_x  =   (((pix_x >= CHAR_B_H) && (pix_x < (CHAR_B_H + CHAR_W)))
//                     && ((pix_y >= CHAR_B_V) && (pix_y < (CHAR_B_V + CHAR_H))))
//                     ? (pix_x - CHAR_B_H) : 12'hFFF;
// assign  char_y  =   (((pix_x >= CHAR_B_H) && (pix_x < (CHAR_B_H + CHAR_W)))
//                     && ((pix_y >= CHAR_B_V) && (pix_y < (CHAR_B_V + CHAR_H))))
//                     ? (pix_y - CHAR_B_V) : 12'hFFF;

// assign  char2_x  =   (((pix_x >= CHAR2_B_H) && (pix_x < (CHAR2_B_H + CHAR2_W)))
//                     && ((pix_y >= CHAR2_B_V) && (pix_y < (CHAR2_B_V + CHAR2_H))))
//                     ? (pix_x - CHAR2_B_H) : 12'hFFF;
// assign  char2_y  =   (((pix_x >= CHAR2_B_H) && (pix_x < (CHAR2_B_H + CHAR2_W)))
//                     && ((pix_y >= CHAR2_B_V) && (pix_y < (CHAR2_B_V + CHAR2_H))))
//                     ? (pix_y - CHAR2_B_V) : 12'hFFF;

// //char:字符数据
// always@(posedge vga_clk)
//     begin
//         char[0]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[1]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[2]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[3]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[4]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[5]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[6]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[7]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[8]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[9]     <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[10]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[11]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[12]    <=  448'h00000003E0000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000007006000000;
//         char[13]    <=  448'h00000003F0000000000007000000000000000001C0000000000000000000000000000000000000000000007800000000000000F80F800000;
//         char[14]    <=  448'h00000007F800000000000F800000000000000003E0000000000000000000000000000E07C0000000000000FC00000000000780F80F800000;
//         char[15]    <=  448'h00000007F800000000001F800000000001C00003F0000000000000000000000000000F07C0000000000000FC00000000000F80F80F800000;
//         char[16]    <=  448'h0000000FF000000000003F800000000003E00007F0000000000FFFFFFFE0000000001F07E0000000000000FC00000000000FC0F81F800000;
//         char[17]    <=  448'h0000001FE000000000003F800000000003F81E3FF80E0000001FFFFFFFF8000000001F87E0000000000000FC000000000007C0781F000000;
//         char[18]    <=  448'h0000001FF800000000007F000000000003FC3FFFFFFF0000003FFFFFFFFC000000001F87E0000000000000FC000000000003C0381E000000;
//         char[19]    <=  448'h0000003FFE0000000001FF00000E000001FE7FFFFFFF8000007FFFFFFFF8000000001F87C0000000000000FC000000000001C0183C000000;
//         char[20]    <=  448'h0F01C07FFF8000000003FFFFFFFF000000FF7F00000F8000007F80FC0000000000F01F87C0000000000000FC000000000040E000380F0000;
//         char[21]    <=  448'h1FFFE0FF7FE000000007FFFFFFFF0000000F78000E078000003800FC0000000000F01F87C0180000000000FC0000000001FFFFFFFFFF8000;
//         char[22]    <=  448'h3FFFF1FE1FF00000001FFFFFFFFF0000000070007F078000000000FC0000000001F01F87C03C0000003FFFFFFF80000001FFFFFFFFFFC000;
//         char[23]    <=  448'h3FFFF3FC0FFC0000007F803F000F0000380070FFFF078000000000FC0000000001F81F87C03E0000007FFFFFFFFE000003FFFFFFFFFFC000;
//         char[24]    <=  448'h1FC7E7F803FF800001FE003F000F00007E0071FFFE0700000000007C0000000000F81F87C07E0000007FFFFFFFFC000003F0000000078000;
//         char[25]    <=  448'h1F83FFF001FFE00007F8003F000F00007F8071E0000700000000007C0000000000FC1F87C07E000000FFC1FC03F0000003E0003000078000;
//         char[26]    <=  448'h1F83FFC000FFFF0003E0003F000700007FE031E0000300000000007C0000000000FC1F87C0FC0000004000FC0000000001E01FFFF8078000;
//         char[27]    <=  448'h1F83FFBFFC3FFE000000003F000F00003FF021FFFFC000000000007C00000000007E1F87C0FC0000000001FE0000000001C03FFFFC078000;
//         char[28]    <=  448'h1F07FE7FFE1FFE0000000C3F000F00000FF801FFFFF000000000007C00000000007E1F87C1F80000000001FF0000000001C07E01FE070000;
//         char[29]    <=  448'h1F87E0FFFF0FFC0000001E3F380E0000007801FFFFF000000000007C00000000003F1F87C3F00000000001F7C000000001C0F800FE070000;
//         char[30]    <=  448'h1F8FE0FFFF07F80000003E3F3E0E0000000001E07C0000000000007C00000000001F1F87C3E00000000003E7E00000000080E1F3FE000000;
//         char[31]    <=  448'h1FFFE0000001F00000007E3F1F000000000001E0780000000000007C00000000000F9F87C7C00000000003E3F0000000000000FFE0000000;
//         char[32]    <=  448'h1FFFC000000000000000FE3F0FC00000000001F0780000000000007C0000000000079F87CF000000000007E1F80000000000003F00000000;
//         char[33]    <=  448'h1FFFC3FF81F000000001FC3F07E0000000073FFFFFC00000000000FC000000000003DF87DE000000000007C0FE0000000000001F80000000;
//         char[34]    <=  448'h0FFFC7FFFFF800000003F83F03F00000001E7FFFFFFFFC00000000FC000000000001DF87D800000000000F807F0000000780001FFFE00000;
//         char[35]    <=  448'h070387FFFFF80000000FF03F01FC0000007EFFFFFFFFF800000000FC0000000000001F87C000000000003F803FC000001FFFFFFFFFFFFC00;
//         char[36]    <=  448'h000007FFFFF80000001FE03F00FE000000FCFF8000FFF800000000FC0000000000001F87C000000000007F001FF000001FFFFFFFFFFFFC00;
//         char[37]    <=  448'h000003F007F80000003FC03F007F800001FCF00E000FF00003FFFFFFFFE00E0000001F87C00000000001FE000FFC00003FFFFFFFFFFFFC00;
//         char[38]    <=  448'h000003F003F8000000FF003F007FE00003F8C03E1E00C0000FFFFFFFFFFFFC0003FFFFFFFFFF8000000FFC000FFF00003FF0000FC01FF800;
//         char[39]    <=  448'h000003F003F8000003FE003F003FF00007F0007E1F0000001FFFFFFFFFFFFC000FFFFFFFFFFFFE0001FFF80007FFF0003C00000FC003F000;
//         char[40]    <=  448'h000003F003F8000007F8003F001FFE000FE001FC1F8000003FFFFFFFFFFFF8001FFFFFFFFFFFFC003FFFF00003FFFC000000000FC0000000;
//         char[41]    <=  448'h000003F807F000001FE0183F000FFC000FC003F80FC000003FFF0001FFFFF8003FFFFFFFFFFFFC001FFFE00001FFFC000000001F80000000;
//         char[42]    <=  448'h000003FFFFF000003F801E7F0007FC000F800FF003E000003F80000007FFF0003FFF0007FFFFF8000FFF800000FFF8000000003F80000000;
//         char[43]    <=  448'h000003FFFFF000001C001FFF0007F80002001FC001F000003800000000FFE0003FC000000FFFF00007FF0000007FF000000700FF80000000;
//         char[44]    <=  448'h000003FFFFF0000000000FFF0001E00000007F0000F8000000000000001FC0003C00000000FFE00003F80000003FE0000001FFFF00000000;
//         char[45]    <=  448'h000003FFFFF00000000007FE0000000000007800001C0000000000000007800000000000001FC00000000000000FC0000000FFFE00000000;
//         char[46]    <=  448'h000001F003E00000000001FC00000000000000000004000000000000000000000000000000000000000000000007800000001FF800000000;
//         char[47]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000038000000000;
//         char[48]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[49]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[50]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[51]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[52]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[53]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[54]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[55]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[56]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[57]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[58]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[59]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[60]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[61]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[62]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//         char[63]    <=  448'h0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     end
//     //char2:字符数据
// always@(posedge vga_clk)
// begin
//     char2[0]     <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[1]     <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[2]     <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[3]     <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[4]     <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[5]     <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[6]     <=  384'h0000001C0E00000000000000000000000000000000000000000000000000000000000000000000000000080800000000;
//     char2[7]     <=  384'h0000001E1F00000000000000000000000000000000000000000000000000000000000000000000000000181800000000;
//     char2[8]     <=  384'h0000003C1E000000100000000000000000000000000000000000000000000000000000000000000000000C1C00000000;
//     char2[9]     <=  384'h0000003C3C0000003C00000000700C1800000060300200000300000000000000038000000000000000000C0C00000000;
//     char2[10]    <=  384'h000000783C0000003C00000000700E38000000703FFF000007FFFE00000000180380000000FFFFFF8000060E00000000;
//     char2[11]    <=  384'h00000078780003003E01800000700E38000000783FFF000007FFFE00000000F80380000000FFFFFFC000060600000000;
//     char2[12]    <=  384'h000000F0780003FFFFFFC00000F0FFFFC0000078380700000F607081000007F3FFFC000000FFFFFFC000030700000000;
//     char2[13]    <=  384'h000000F0F00007FFE0FFC0000CF0FFFFE00000783FFF0000006073FF80007FE7FFFFC00000F03C03C000030300000000;
//     char2[14]    <=  384'h000001E0F00007800001C0001FFE0E3800000FFE3FFF0000006073FF80007FC7C380C00000F03C03C000018180000000;
//     char2[15]    <=  384'h000001E1E00007003C01C0001FFE7FFF00000FFF38070000007FF20780003FC00380000000F03C03C00001C1C0000000;
//     char2[16]    <=  384'h000003C1C0000307FFE1C00000F07FFF80001FF8380F0000007FF00380001FC3FFFF000000FFFFFFC00000C0C0000000;
//     char2[17]    <=  384'h000003C3C000030FC1E1800000F87007000000783FFF000000607307800003C3FFFF800000FFFFFFC00000E0E0000000;
//     char2[18]    <=  384'h000007878000030E03E1800001FC7FFF000000783E1E000000607387000003C3E38F000000F03E07C000006060000000;
//     char2[19]    <=  384'h0000070780000000FF00000003FC7E3F0000007C00000000006071E7000003C3C387000000F03C03C000007070000000;
//     char2[20]    <=  384'h00000F0F000000007C00000007FE7007000001FEFFFFE000007FF0FE000003C3CFFF000000F03C03C000003030000000;
//     char2[21]    <=  384'h00000F0F000000003E0000000F737FFF00001FF9FFFFE000007FF07E000003C3FFFF000000F03C03C000003030000000;
//     char2[22]    <=  384'h0000070700000FFFFFFFFE001E703BFE00001FF010E000000060703E000003C1078F000000FFFFFFC000007070000000;
//     char2[23]    <=  384'h0000078780003FFFFFFFFC003E70018000001E7018E000000060703F000003C0E700000000FFFFFFC000006060000000;
//     char2[24]    <=  384'h0000038380003FFC1E7FFC007C73FFFFFE00007018FFC0000060F077800003C0FF00000001E03E03C00000E0E0000000;
//     char2[25]    <=  384'h000003C3C0003C001E01FC007873FFFFFE00007038F3C000007FF1E3E00003C1FF80000001E03C03C00000C0C0000000;
//     char2[26]    <=  384'h000001E1E00000001E0038000077C383FC0000707CE000001FFE77C1F00003C00FF0000003E03C03C00001C1C0000000;
//     char2[27]    <=  384'h000001E1E00000001E00000000700718780000F0FFE000003FF07F00FE0003C03FFE000007C03C03C000018180000000;
//     char2[28]    <=  384'h000000F0F00000003E00000000700F3C000000F1EFFC00003F007000FE0003C07C7FF0000FC03C03C000038380000000;
//     char2[29]    <=  384'h000000F0F00000007E00000000701E1F000041F3C1FFF800380070007C0003C7F81FFE001F803C03C000030300000000;
//     char2[30]    <=  384'h0000007878000073FC00000000707C0F80003FF7803FFE0000007000300003DFE00FFE007F003C03C000070700000000;
//     char2[31]    <=  384'h000000787800003FF80000000071F803C0003FEF800FFC0000007000000003800003FC003E003C03C000060600000000;
//     char2[32]    <=  384'h0000003C3C00000FF00000000061F000F0000F800001F80000007000000001000001F80008001C03C0000E0E00000000;
//     char2[33]    <=  384'h0000001E3C000000000000000000000000000000000060000000000000000000000000000000000000000C0C00000000;
//     char2[34]    <=  384'h0000001E1E000000000000000000000000000000000000000000000000000000000000000000000000001C1C00000000;
//     char2[35]    <=  384'h0000000F1E00000000000000000000000000000000000000000000000000000000000000000000000000181800000000;
//     char2[36]    <=  384'h0000000E0C00000000000000000000000000000000000000000000000000000000000000000000000000000800000000;
//     char2[37]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[38]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[39]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[40]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[41]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[42]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[43]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[44]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[45]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[46]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
//     char2[47]    <=  384'h000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000;
   
// end

// //pix_data:输出像素点色彩信息,根据当前像素点坐标指定当前像素点颜色数据
// always@(posedge vga_clk or negedge sys_rst_n)
//     if(sys_rst_n == 1'b0)
//         pix_data    <= BLACK;
//     else    if((((pix_x >= (CHAR_B_H - 1'b1))
//                 && (pix_x < (CHAR_B_H + CHAR_W -1'b1)))
//                 && ((pix_y >= CHAR_B_V) && (pix_y < (CHAR_B_V + CHAR_H))))
//                 && (char[char_y][447 - char_x] == 1'b1))
//         pix_data    <=  DIY_COLOR;
//     else if ((((pix_x >= (CHAR2_B_H - 1'b1))
//         && (pix_x < (CHAR2_B_H + CHAR2_W -1'b1)))
//         && ((pix_y >= CHAR2_B_V) && (pix_y < (CHAR2_B_V + CHAR2_H))))
//         && (char2[char2_y][383 - char2_x] == 1'b1)) begin
//             pix_data    <=  DIY_COLOR2;
//     end
//     else
//         pix_data    <=  BLACK;
    

        
//         always@(posedge vga_clk or negedge sys_rst_n)
//         begin
//             if(!sys_rst_n)
//                 begin
//                     data_enable   <=  'd0 ;
//                 end

//             else if(((((pix_x >= (CHAR_B_H - 1'b1))
//                 && (pix_x < (CHAR_B_H + CHAR_W -1'b1)))
//                 && ((pix_y >= CHAR_B_V) && (pix_y < (CHAR_B_V + CHAR_H))))
//                 && (char[char_y][447 - char_x] == 1'b1)))
//                 begin
//                     data_enable   <=  1 ;
//                 end
//             else
//                 begin
//                     data_enable   <=  'd0 ;
//                 end
//         end



// endmodule

 







