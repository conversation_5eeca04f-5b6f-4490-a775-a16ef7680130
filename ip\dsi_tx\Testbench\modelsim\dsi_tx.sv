// =============================================================================
// Generated by efx_ipmgr
// Version: 2025.**********
// IP Version: 5.14
// =============================================================================

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2025 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//                                                                       
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//                                                                       
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.                                                
//
////////////////////////////////////////////////////////////////////////////////

`define IP_UUID _a0a4cd9c75ba4eb19dc09753dd40f582
`define IP_NAME_CONCAT(a,b) a``b
`define IP_MODULE_NAME(name) `IP_NAME_CONCAT(name,`IP_UUID)
module dsi_tx
(
    input reset_n,
    input clk,
    input reset_byte_HS_n,
    input clk_byte_HS,
    input reset_pixel_n,
    input clk_pixel,
    output irq,
    input pixel_data_valid,
    input [15:0] haddr,
    input hsync,
    input vsync,
    input [63:0] pixel_data,
    input [5:0] datatype,
    input [1:0] vc,
    input TurnRequest_dbg,
    output TurnRequest_done,
    input axi_rready,
    output axi_rvalid,
    output [31:0] axi_rdata,
    output axi_arready,
    input axi_arvalid,
    input [6:0] axi_araddr,
    input axi_bready,
    output axi_bvalid,
    output axi_wready,
    input axi_wvalid,
    input [31:0] axi_wdata,
    output Tx_LP_CLK_P,
    output Tx_LP_CLK_P_OE,
    output Tx_LP_CLK_N,
    output Tx_LP_CLK_N_OE,
    output Tx_HS_enable_C,
    output [3:0] Tx_LP_D_P,
    output [7:0] Tx_HS_C,
    output [3:0] Tx_LP_D_P_OE,
    output [3:0] Tx_LP_D_N,
    output [3:0] Tx_LP_D_N_OE,
    output [7:0] Tx_HS_D_0,
    output [7:0] Tx_HS_D_1,
    output [7:0] Tx_HS_D_2,
    output [7:0] Tx_HS_D_3,
    output [3:0] Tx_HS_enable_D,
    input Rx_LP_D_P,
    input Rx_LP_D_N,
    output axi_awready,
    input axi_clk,
    input axi_reset_n,
    input [6:0] axi_awaddr,
    input axi_awvalid
);
`IP_MODULE_NAME(efx_dsi_tx)
#(
    .PACK_TYPE (1),
    .tLPX_NS (60),
    .tINIT_NS (1000),
    .NUM_DATA_LANE (4),
    .HS_BYTECLK_MHZ (50),
    .CLOCK_FREQ_MHZ (50),
    .DPHY_CLOCK_MODE ("Continuous"),
    .PIXEL_FIFO_DEPTH (4096),
    .tLP_EXIT_NS (100),
    .tCLK_ZERO_NS (280),
    .tCLK_TRAIL_NS (100),
    .tCLK_PRE_NS (10),
    .tCLK_POST_NS (100),
    .tCLK_PREPARE_NS (50),
    .tWAKEUP_NS (1000),
    .tHS_ZERO_NS (200),
    .tHS_TRAIL_NS (65),
    .tHS_EXIT_NS (120),
    .tHS_PREPARE_NS (50),
    .BTA_TIMEOUT_NS (1000),
    .tD_TERM_EN_NS (35),
    .tHS_PREPARE_ZERO_NS (145),
    .ENABLE_V_LPM_BTA (1'b0),
    .PACKET_SEQUENCES (1),
    .HS_CMD_WDATAFIFO_DEPTH (512),
    .LP_CMD_WDATAFIFO_DEPTH (512),
    .LP_CMD_RDATAFIFO_DEPTH (512),
    .MAX_HRES (1024),
    .ENABLE_BIDIR (1'b1),
    .ENABLE_EOTP (1'b0)
)
u_efx_dsi_tx
(
    .reset_n ( reset_n ),
    .clk ( clk ),
    .reset_byte_HS_n ( reset_byte_HS_n ),
    .clk_byte_HS ( clk_byte_HS ),
    .reset_pixel_n ( reset_pixel_n ),
    .clk_pixel ( clk_pixel ),
    .irq ( irq ),
    .pixel_data_valid ( pixel_data_valid ),
    .haddr ( haddr ),
    .hsync ( hsync ),
    .vsync ( vsync ),
    .pixel_data ( pixel_data ),
    .datatype ( datatype ),
    .vc ( vc ),
    .TurnRequest_dbg ( TurnRequest_dbg ),
    .TurnRequest_done ( TurnRequest_done ),
    .axi_rready ( axi_rready ),
    .axi_rvalid ( axi_rvalid ),
    .axi_rdata ( axi_rdata ),
    .axi_arready ( axi_arready ),
    .axi_arvalid ( axi_arvalid ),
    .axi_araddr ( axi_araddr ),
    .axi_bready ( axi_bready ),
    .axi_bvalid ( axi_bvalid ),
    .axi_wready ( axi_wready ),
    .axi_wvalid ( axi_wvalid ),
    .axi_wdata ( axi_wdata ),
    .Tx_LP_CLK_P ( Tx_LP_CLK_P ),
    .Tx_LP_CLK_P_OE ( Tx_LP_CLK_P_OE ),
    .Tx_LP_CLK_N ( Tx_LP_CLK_N ),
    .Tx_LP_CLK_N_OE ( Tx_LP_CLK_N_OE ),
    .Tx_HS_enable_C ( Tx_HS_enable_C ),
    .Tx_LP_D_P ( Tx_LP_D_P ),
    .Tx_HS_C ( Tx_HS_C ),
    .Tx_LP_D_P_OE ( Tx_LP_D_P_OE ),
    .Tx_LP_D_N ( Tx_LP_D_N ),
    .Tx_LP_D_N_OE ( Tx_LP_D_N_OE ),
    .Tx_HS_D_0 ( Tx_HS_D_0 ),
    .Tx_HS_D_1 ( Tx_HS_D_1 ),
    .Tx_HS_D_2 ( Tx_HS_D_2 ),
    .Tx_HS_D_3 ( Tx_HS_D_3 ),
    .Tx_HS_enable_D ( Tx_HS_enable_D ),
    .Rx_LP_D_P ( Rx_LP_D_P ),
    .Rx_LP_D_N ( Rx_LP_D_N ),
    .axi_awready ( axi_awready ),
    .axi_clk ( axi_clk ),
    .axi_reset_n ( axi_reset_n ),
    .axi_awaddr ( axi_awaddr ),
    .axi_awvalid ( axi_awvalid )
);
endmodule

////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   bin2gray.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Binary to Gray Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_bin2gray)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] gray_o,
 // input
 input [WIDTH-1:0] bin_i
 );

//---------------------------------------------------------------------
// Function :   bit_xor
// Description: reduction xor
function bit_xor (
  input [31:0] nex_bit,
  input [31:0] curr_bit,
  input [WIDTH-1:0] xor_in);
  begin : fn_bit_xor
    bit_xor = xor_in[nex_bit] ^ xor_in[curr_bit];
  end
endfunction

// Convert Binary to Gray, bit by bit
generate 
begin
  genvar bit_idx;
  for(bit_idx=0; bit_idx<WIDTH-1; bit_idx=bit_idx+1) begin : gBinBits
    assign gray_o[bit_idx] = bit_xor(bit_idx+1, bit_idx, bin_i);
  end
  assign   gray_o[WIDTH-1] = bin_i[WIDTH-1];
end
endgenerate

endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ctl) # (
    parameter SYNC_CLK           = 1,
    parameter SYNC_STAGE         = 2,
    parameter MODE               = "STANDARD",
    parameter WR_DEPTH           = 512,
    parameter WADDR_WIDTH        = 9,
    parameter RADDR_WIDTH        = 9,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter RAM_MUX_RATIO      = 1,
    parameter PIPELINE_REG       = 1,
    parameter ALMOST_FLAG        = 1,
    parameter PROGRAMMABLE_FULL  = "NONE",
    parameter PROG_FULL_ASSERT   = 0,
    parameter PROG_FULL_NEGATE   = 0,
    parameter PROGRAMMABLE_EMPTY = "NONE",
    parameter PROG_EMPTY_ASSERT  = 0,
    parameter PROG_EMPTY_NEGATE  = 0,
    parameter OUTPUT_REG         = 0,
    parameter HANDSHAKE_FLAG     = 1, 
    parameter OVERFLOW_PROTECT   = 0,
    parameter UNDERFLOW_PROTECT  = 0
)(
    input   wire                   wr_rst,
    input   wire                   rd_rst,
    input   wire                   wclk,
    input   wire                   rclk,
    input   wire                   we,
    input   wire                   re,
    output  wire                   wr_full,
    output  reg                    wr_ack,
    output  wire                   wr_almost_full,
    output  wire                   rd_empty,
    output  wire                   rd_almost_empty,
    output  wire                   wr_prog_full,
    output  wire                   rd_prog_empty,
    output  wire                   wr_en_int,
    output  wire                   rd_en_int,
    output  wire [WADDR_WIDTH-1:0] waddr,
    output  wire [RADDR_WIDTH-1:0] raddr,
    output  wire [WADDR_WIDTH:0]   wr_datacount,
    output  wire [RADDR_WIDTH:0]   rd_datacount,
    output  wire                   rd_vld,
    output  reg                    wr_overflow,
    output  reg                    rd_underflow
);

reg  [WADDR_WIDTH:0] waddr_cntr;
reg  [WADDR_WIDTH:0] waddr_cntr_r;
reg  [RADDR_WIDTH:0] raddr_cntr;
reg                  rd_valid;

wire [WADDR_WIDTH:0] waddr_int;
wire [RADDR_WIDTH:0] raddr_int;
wire                 rd_empty_int;
wire [WADDR_WIDTH:0] wr_datacount_int;
wire [RADDR_WIDTH:0] rd_datacount_int;

assign waddr     = waddr_cntr[WADDR_WIDTH-1:0];
// NIC
wire [RADDR_WIDTH:0] ram_raddr;
assign raddr     = (MODE == "FWFT") ? ram_raddr[RADDR_WIDTH-1:0] : raddr_cntr[RADDR_WIDTH-1:0];
//assign raddr     = raddr_cntr[RADDR_WIDTH-1:0];
//assign wr_en_int = we & ~wr_full;
assign wr_en_int = OVERFLOW_PROTECT ? we & ~wr_full : we;

assign wr_datacount  = wr_datacount_int;
assign rd_datacount  = ASYM_WIDTH_RATIO == 4 && SYNC_CLK ? wr_datacount_int : rd_datacount_int;


generate
    if (MODE == "FWFT") begin    
        // NIC
        //assign rd_en_int     = (~rd_empty_int & rd_empty) | (re & ~rd_empty_int);
        //assign rd_empty      = rd_empty_fwft;

        assign rd_en_int     = 1'b1;
        //assign rd_empty      = rd_empty_int;
        
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        init_set <= 1'b1;
        //    end
        //    else if (~init_set & rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //    else if (~rd_empty_int) begin
        //        init_set <= 1'b0;            
        //    end
        //    else if (rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //end
        // NIC 
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        rd_empty_fwft <= 1'b1;
        //    end
        //    else if (rd_en_int) begin
        //        rd_empty_fwft <= 1'b0;            
        //    end
        //    else if (re) begin
        //        rd_empty_fwft <= 1'b1;            
        //    end
        //end          
        
        //if (FAMILY == "TRION") begin
        if (OUTPUT_REG) begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid <= 1'b0;
                end
                else begin
                    rd_valid <= ~rd_empty;
                end
            end
            assign rd_vld = rd_valid;                
        end    
        else begin
            assign rd_vld = ~rd_empty;                
        end
        
        assign rd_empty = rd_empty_int;
    end
    else begin
        assign rd_en_int     = UNDERFLOW_PROTECT ? re & ~rd_empty_int : re;
        assign rd_empty      = rd_empty_int;
        
        if (OUTPUT_REG) begin
            reg rd_valid_r;
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid_r <= 'h0;
                    rd_valid   <= 'h0;
                end
                else begin
                    {rd_valid,rd_valid_r} <= {rd_valid_r,rd_en_int};
                end
            end
            assign rd_vld = rd_valid;            
        end
        else begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid  <= 'h0;
                end
                else begin
                    rd_valid <= rd_en_int;
                end
            end
            assign rd_vld = rd_valid;
        end
    end
    
    if (ALMOST_FLAG) begin
        assign wr_almost_full   = wr_datacount >= WR_DEPTH-1;
        assign rd_almost_empty  = rd_datacount <= 'd1;
    end
    else begin
        assign wr_almost_full   = 1'b0;
        assign rd_almost_empty  = 1'b0;        
    end
    
    if (PROGRAMMABLE_FULL == "STATIC_SINGLE") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_datacount >= PROG_FULL_ASSERT;
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end  
    end
    else if (PROGRAMMABLE_FULL == "STATIC_DUAL") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_prog_full_int ? wr_datacount >= PROG_FULL_NEGATE : wr_datacount >= PROG_FULL_ASSERT;   

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end              
    end
    else begin
        assign wr_prog_full  = 1'b0; 
    end
        
    if (PROGRAMMABLE_EMPTY == "STATIC_SINGLE") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty  = rd_datacount <= PROG_EMPTY_ASSERT;
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end
    end
    else if (PROGRAMMABLE_EMPTY == "STATIC_DUAL") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty = rd_prog_empty_int ? (rd_datacount <= PROG_EMPTY_NEGATE) : (rd_datacount <= PROG_EMPTY_ASSERT);
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end  
    end
    else begin
        assign rd_prog_empty  = 1'b0; 
    end
    
    if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_ack <= 1'b0;
            end
            else begin
                // NIC
                //wr_ack <= wr_en_int & ~wr_overflow;
                wr_ack <= OVERFLOW_PROTECT ? wr_en_int & ~wr_overflow : wr_en_int;
            end
        end
    end

    if (OVERFLOW_PROTECT) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else if (we && wr_full) begin
                wr_overflow <= 1'b1;
            end
            else begin
                wr_overflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else begin
                wr_overflow <= we && wr_full ? 1'b1 : wr_overflow;
            end
        end
    end

    if (UNDERFLOW_PROTECT) begin     
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else if (re && rd_empty) begin
                rd_underflow <= 1'b1;
            end
            else begin
                rd_underflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else begin
                rd_underflow <= re && rd_empty ? 1'b1 : rd_underflow;
            end
        end
    end
    
    localparam RATIO_WIDTH = (RADDR_WIDTH >= WADDR_WIDTH)? RADDR_WIDTH - WADDR_WIDTH : WADDR_WIDTH - RADDR_WIDTH;
    
    if (ASYM_WIDTH_RATIO < 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:RATIO_WIDTH]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:RATIO_WIDTH];
        assign wr_datacount_int = waddr_cntr - (raddr_int/RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int*RAM_MUX_RATIO)-raddr_cntr;
    end
    // NIC
    else if (ASYM_WIDTH_RATIO == 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:0]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - raddr_int;
        assign rd_datacount_int = waddr_int - raddr_cntr;
    end
    else begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:RATIO_WIDTH] == raddr_int[RADDR_WIDTH-1:0]);
        // NIC
        //assign rd_empty_int     = (waddr_int- raddr_cntr*RAM_MUX_RATIO) < RAM_MUX_RATIO;
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:RATIO_WIDTH] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - (raddr_int*RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int/RAM_MUX_RATIO)-raddr_cntr;
    end
endgenerate

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr   <= 'h0;
    end
    else if (wr_en_int) begin
        waddr_cntr   <= waddr_cntr + 1'b1;
    end
end

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr_r  <= 'h0;
    end
    else begin
        waddr_cntr_r  <= waddr_cntr;
    end
end

always @ (posedge rclk or posedge rd_rst) begin
    if (rd_rst) begin
        raddr_cntr   <= 'h0;
    end
    // NIC
    //else if (rd_en_int) begin
    else begin
        //raddr_cntr   <= raddr_cntr + 1'b1;
        //raddr_cntr   <= raddr_cntr + (re & ~rd_empty_int);
        raddr_cntr   <= ram_raddr;
    end
end
// NIC
assign ram_raddr = raddr_cntr + (UNDERFLOW_PROTECT ? re & ~rd_empty_int : re);


generate
    if (SYNC_CLK) begin : sync_clk
       if (MODE == "FWFT") begin
          assign waddr_int = waddr_cntr_r;
          assign raddr_int        = raddr_cntr;
       end
       else begin
          assign waddr_int        = waddr_cntr;
          assign raddr_int        = raddr_cntr;
       end
    end
    else begin : async_clk
        reg  [RADDR_WIDTH:0] raddr_cntr_gry_r;
        reg  [WADDR_WIDTH:0] waddr_cntr_gry_r;
        
        wire [RADDR_WIDTH:0] raddr_cntr_gry;
        wire [RADDR_WIDTH:0] raddr_cntr_gry_sync;
        wire [RADDR_WIDTH:0] raddr_cntr_sync_g2b;
        wire [WADDR_WIDTH:0] waddr_cntr_gry;
        wire [WADDR_WIDTH:0] waddr_cntr_gry_sync;
        wire [WADDR_WIDTH:0] waddr_cntr_sync_g2b;

        if (PIPELINE_REG) begin
            reg  [RADDR_WIDTH:0] raddr_cntr_sync_g2b_r;    
            reg  [WADDR_WIDTH:0] waddr_cntr_sync_g2b_r;        
        
            assign waddr_int        = waddr_cntr_sync_g2b_r;
            assign raddr_int        = raddr_cntr_sync_g2b_r;
            
            always @ (posedge wclk or posedge wr_rst) begin
                if (wr_rst) begin
                    raddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    raddr_cntr_sync_g2b_r <= raddr_cntr_sync_g2b;
                end
            end
            
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    waddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    waddr_cntr_sync_g2b_r <= waddr_cntr_sync_g2b;
                end
            end
        end
        else begin
            assign waddr_int        = waddr_cntr_sync_g2b;
            assign raddr_int        = raddr_cntr_sync_g2b;        
        end
      
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                raddr_cntr_gry_r <= 'h0;
            end
            else begin
                raddr_cntr_gry_r <= raddr_cntr_gry;
            end
        end
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_bin2gray    (.bin_i(raddr_cntr), .gray_o(raddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (RADDR_WIDTH+1), .ACTIVE_LOW(0)) xrd2wr_addr_sync   (.clk(wclk), .reset_n(wr_rst), .d_i(raddr_cntr_gry_r), .d_o(raddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_gray2bin    (.gray_i(raddr_cntr_gry_sync), .bin_o(raddr_cntr_sync_g2b));
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                waddr_cntr_gry_r <= 'h0;
            end
            else begin
                waddr_cntr_gry_r <= waddr_cntr_gry;
            end
        end        
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_bin2gray  (.bin_i(waddr_cntr), .gray_o(waddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (WADDR_WIDTH+1), .ACTIVE_LOW(0)) wr2rd_addr_sync (.clk(rclk), .reset_n(rd_rst), .d_i(waddr_cntr_gry_r), .d_o(waddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_gray2bin  (.gray_i(waddr_cntr_gry_sync), .bin_o(waddr_cntr_sync_g2b));        

    end
endgenerate
endmodule


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   pipe_reg.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Parallel Pipelining Shift Register
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_datasync) #(
    parameter STAGE = 32,
    parameter WIDTH = 4
) (
 input  wire             clk_i,
 input  wire [WIDTH-1:0] d_i,
 output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] pipe_reg [STAGE-1:0];
integer i;

always @(posedge clk_i) begin
  for (i=STAGE-1; i>0; i = i - 1) begin
    pipe_reg[i] <= pipe_reg[i-1];
  end
  pipe_reg[0] <= d_i;
end
assign d_o = pipe_reg[STAGE-1];
   

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_resetsync) #(
    parameter ASYNC_STAGE = 2,
    parameter ACTIVE_LOW = 1
) (
    input  wire             clk,
    input  wire             reset,
    output wire             d_o
);


generate
   if (ACTIVE_LOW == 1) begin: active_low
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (1),
         .RST_VALUE  (0)
      ) efx_resetsync_active_low (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b1),
         .d_o     (d_o)
      );
   end
   else begin: active_high
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (0),
         .RST_VALUE  (1)
      ) efx_resetsync_active_high (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b0),
         .d_o     (d_o)
      );
   end
endgenerate

endmodule



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_asyncreg) #(
    parameter ASYNC_STAGE = 2,
    parameter WIDTH = 4,
    parameter ACTIVE_LOW = 1, // 0 - Active high reset, 1 - Active low reset
    parameter RST_VALUE = 0 
) (
    input  wire             clk,
    input  wire             reset_n,
    input  wire [WIDTH-1:0] d_i,
    output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] async_reg[ASYNC_STAGE-1:0];

assign d_o = async_reg[ASYNC_STAGE-1];

genvar i;
generate
   if (ACTIVE_LOW == 1) begin: active_low
      if (RST_VALUE == 0) begin: low_reset_0
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: low_reset_1
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
   else begin: active_high
      if (RST_VALUE == 0) begin: high_reset_0
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: high_reset_1
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
endgenerate

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   gray2bin.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Gray to Binary Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_gray2bin)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] bin_o,
 // input
 input [WIDTH-1:0] gray_i);

//---------------------------------------------------------------------
// Recursive Module
// Description: reduction xor
generate
   if (WIDTH > 1) begin
      wire [1:0] bin_1;
      assign bin_1 = {gray_i[WIDTH-1], gray_i[WIDTH-1]^gray_i[WIDTH-2]};
      if (WIDTH == 2) begin
	 assign bin_o = bin_1;
      end
      else begin
	 assign bin_o[WIDTH-1] = bin_1[1];
	 `IP_MODULE_NAME(efx_fifo_gray2bin) #(.WIDTH(WIDTH-1)) u_gray2bin (.bin_o(bin_o[WIDTH-2:0]), .gray_i({bin_1[0], gray_i[WIDTH-3:0]}));
      end
   end
   else /* if (WIDTH == 1) */
     assign bin_o = gray_i;
endgenerate
   
endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ram) #(
    parameter FAMILY        = "TRION",
    parameter WR_DEPTH      = 512,
    parameter RD_DEPTH      = 512,
    parameter WDATA_WIDTH   = 8,
    parameter RDATA_WIDTH   = 8,
    parameter WADDR_WIDTH   = 9,
    parameter RADDR_WIDTH   = 9,
    parameter OUTPUT_REG    = 1,
    parameter RAM_MUX_RATIO = 4,
    parameter ENDIANESS 	 = 0, //0: Big endian (default)   1: Little endian 
    parameter RAM_STYLE     = "block_ram"
) (
    input  wire                     wclk,
    input  wire                     rclk,
    input  wire                     we,
    input  wire                     re,
    input  wire [(WDATA_WIDTH-1):0] wdata,
    input  wire [(WADDR_WIDTH-1):0] waddr,
    input  wire [(RADDR_WIDTH-1):0] raddr,
    output wire [(RDATA_WIDTH-1):0] rdata
);

localparam MEM_DEPTH         = (WR_DEPTH > RD_DEPTH) ? WR_DEPTH : RD_DEPTH;
localparam MEM_DATA_WIDTH    = (WDATA_WIDTH > RDATA_WIDTH) ? RDATA_WIDTH : WDATA_WIDTH;   
localparam LSB_WIDTH         = (WADDR_WIDTH > RADDR_WIDTH) ? (WADDR_WIDTH - RADDR_WIDTH) : (RADDR_WIDTH - WADDR_WIDTH);
localparam RDATA_WDATA_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? "ONE_THIRTYTWO" :
                               (RDATA_WIDTH <= WDATA_WIDTH/16) ? "ONE_SIXTEENTH" :
                               (RDATA_WIDTH <= WDATA_WIDTH/8)  ? "ONE_EIGHTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/4)  ? "ONE_FOURTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/2)  ? "ONE_HALF"      :
                               (RDATA_WIDTH <= WDATA_WIDTH)    ? "ONE"           :
                               (RDATA_WIDTH <= WDATA_WIDTH*2)  ? "TWO_TIMES"     :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "FOUR_TIMES"    :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "EIGHT_TIMES"   :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "SIXTEEN_TIMES" : "THIRTYTWO_TIMES";

(* syn_ramstyle = RAM_STYLE *) reg [MEM_DATA_WIDTH-1:0] ram[MEM_DEPTH-1:0];
reg [RDATA_WIDTH-1:0]    r_rdata_1P;
reg [RDATA_WIDTH-1:0]    r_rdata_2P;

wire                     re_int;

generate
    if (FAMILY == "TRION") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                end
                r_rdata_2P <= r_rdata_1P;
            end
        end
        
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            if (ENDIANESS == 0) begin 
            	integer i;
            	always @ (posedge wclk) begin
                	for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                    end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                	end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
        end
        
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  ||RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
            //integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end 
            end
            else begin //endianess == 1
            	always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end
            end
        end
        if (OUTPUT_REG) begin
            assign re_int = re;
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end
    end
    else begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                    r_rdata_2P <= r_rdata_1P;
                end
            end
        end 
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            //integer i;
            if (ENDIANESS == 0) begin
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
            
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
        end
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  || RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
        	//integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
            
            else begin //endianess ==1
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
        end
        // NIC
        //if (MODE == "STANDARD") begin     
        //    if (OUTPUT_REG) begin
        //        reg re_r;
        //        always @ (posedge rclk) begin
        //            re_r <= re;
        //        end
        //        assign re_int = re | re_r;        
        //        assign rdata  = r_rdata_2P;            
        //    end
        //    else begin
        //        assign re_int = re;
        //        assign rdata  = r_rdata_1P;
        //    end
        //end
        //else begin
        //    assign re_int = re;
        //    assign rdata  = r_rdata_1P;
        //end    
        if (OUTPUT_REG) begin
            reg re_r;
            always @ (posedge rclk) begin
                re_r <= re;
            end
            assign re_int = re | re_r;        
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end    
    end
endgenerate

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2021 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_top) # (
    parameter FAMILY             = "TRION",       // New Param
    parameter SYNC_CLK           = 0,
    parameter BYPASS_RESET_SYNC  = 0,             // New Param
    parameter SYNC_STAGE         = 2,             // New Param
    parameter MODE               = "STANDARD",
    parameter DEPTH              = 512,           // Reverted (Equivalent to WDATA_DEPTH) 
    parameter DATA_WIDTH         = 32,            // Reverted (Equivalent to WDATA_WIDTH)
    parameter PIPELINE_REG       = 1,             // Reverted (By default is ON)
    parameter OPTIONAL_FLAGS     = 1,             // Reverted
    parameter OUTPUT_REG         = 0,
    parameter PROGRAMMABLE_FULL  = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_FULL_ASSERT   = 27,
    parameter PROG_FULL_NEGATE   = 23,
    parameter PROGRAMMABLE_EMPTY = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_EMPTY_ASSERT  = 5,
    parameter PROG_EMPTY_NEGATE  = 7,
    parameter ALMOST_FLAG        = OPTIONAL_FLAGS,
    parameter HANDSHAKE_FLAG     = OPTIONAL_FLAGS,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter WADDR_WIDTH        = depth2width(DEPTH),
    parameter RDATA_WIDTH        = rdwidthcompute(ASYM_WIDTH_RATIO,DATA_WIDTH),
    parameter RD_DEPTH           = rddepthcompute(DEPTH,DATA_WIDTH,RDATA_WIDTH),
    parameter RADDR_WIDTH        = depth2width(RD_DEPTH),
    parameter ENDIANESS		      = 0,
    parameter OVERFLOW_PROTECT   = 1,
    parameter UNDERFLOW_PROTECT  = 1,
    parameter RAM_STYLE          = "block_ram"

)(
    input  wire                   a_rst_i,
    input  wire                   a_wr_rst_i,
    input  wire                   a_rd_rst_i,
    input  wire                   clk_i,
    input  wire                   wr_clk_i,
    input  wire                   rd_clk_i,
    input  wire                   wr_en_i,
    input  wire                   rd_en_i,
    input  wire [DATA_WIDTH-1:0]  wdata,
    output wire                   almost_full_o,
    output wire                   prog_full_o,
    output wire                   full_o,
    output wire                   overflow_o,
    output wire                   wr_ack_o,
    output wire [WADDR_WIDTH :0]  datacount_o,
    output wire [WADDR_WIDTH :0]  wr_datacount_o,
    output wire                   empty_o,
    output wire                   almost_empty_o,
    output wire                   prog_empty_o,
    output wire                   underflow_o,
    output wire                   rd_valid_o,
    output wire [RDATA_WIDTH-1:0] rdata,
    output wire [RADDR_WIDTH :0]  rd_datacount_o,
    output wire                   rst_busy
); 

localparam WR_DEPTH      = DEPTH;
localparam WDATA_WIDTH   = DATA_WIDTH;
localparam RAM_MUX_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? 32 :
                           (RDATA_WIDTH <= WDATA_WIDTH/16) ? 16 :
                           (RDATA_WIDTH <= WDATA_WIDTH/8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH/4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH/2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH)    ? 1  :
                           (RDATA_WIDTH <= WDATA_WIDTH*2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH*4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH*8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH*16) ? 16 : 32;           

wire                   wr_rst_int;
wire                   rd_rst_int;
wire                   wr_en_int;
wire                   rd_en_int;
wire [WADDR_WIDTH-1:0] waddr;
wire [RADDR_WIDTH-1:0] raddr;
wire                   wr_clk_int;
wire                   rd_clk_int;
wire [WADDR_WIDTH :0]  wr_datacount_int;
wire [RADDR_WIDTH :0]  rd_datacount_int;

generate
    if (ASYM_WIDTH_RATIO == 4) begin
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
            assign datacount_o    = wr_datacount_int;
            assign wr_datacount_o = {(WADDR_WIDTH+1){1'b0}};
            assign rd_datacount_o = {(RADDR_WIDTH+1){1'b0}};
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
            assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
            assign wr_datacount_o = wr_datacount_int;
            assign rd_datacount_o = rd_datacount_int;
        end
    end
    else begin
        assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
        assign wr_datacount_o = wr_datacount_int;
        assign rd_datacount_o = rd_datacount_int;
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
        end
    end    

    if (!SYNC_CLK) begin
        //(* async_reg = "true" *) reg [1:0] wr_rst;
        //(* async_reg = "true" *) reg [1:0] rd_rst;
        //
        //always @ (posedge wr_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        wr_rst <= 2'b11;
        //    else 
        //        wr_rst <= {wr_rst[0],1'b0};            
        //end
        //
        //always @ (posedge rd_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        rd_rst <= 2'b11;
        //    else 
        //        rd_rst <= {rd_rst[0],1'b0};            
        //end
        

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_wr_rst_i;
            assign rd_rst_int = a_rd_rst_i; 
            assign rst_busy   = 1'b0;
        end 
        else begin
            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_wr_rst (
               .clk   (wr_clk_int),
               .reset (a_rst_i),
               .d_o   (wr_rst_int)
            );

            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_rd_rst (
               .clk   (rd_clk_int),
               .reset (a_rst_i),
               .d_o   (rd_rst_int)
            );
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end

    end
    else begin
        //(* async_reg = "true" *) reg [1:0] a_rst;
        //
        //always @ (posedge clk_i or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        a_rst <= 2'b11;
        //    else 
        //        a_rst <= {a_rst[0],1'b0};            
        //end
        wire   a_rst;

        `IP_MODULE_NAME(efx_resetsync) #(
           .ACTIVE_LOW (0)
        ) efx_resetsync_a_rst (
           .clk   (clk_i),
           .reset (a_rst_i),
           .d_o   (a_rst)
        );

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_rst_i;
            assign rd_rst_int = a_rst_i;
            assign rst_busy   = 1'b0;
        end
        else begin
            assign wr_rst_int = a_rst;
            assign rd_rst_int = a_rst;   
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end 
endgenerate

`IP_MODULE_NAME(efx_fifo_ram) # (
    .FAMILY        (FAMILY),
    .WR_DEPTH      (WR_DEPTH),
    .RD_DEPTH      (RD_DEPTH),
    .WDATA_WIDTH   (WDATA_WIDTH),
    .RDATA_WIDTH   (RDATA_WIDTH),
    .WADDR_WIDTH   (WADDR_WIDTH),
    .RADDR_WIDTH   (RADDR_WIDTH),
    .OUTPUT_REG    (OUTPUT_REG),
    .RAM_MUX_RATIO (RAM_MUX_RATIO),
    .ENDIANESS	    (ENDIANESS),
    .RAM_STYLE     (RAM_STYLE)
) xefx_fifo_ram (
    .wdata (wdata), 
    .waddr (waddr),
    .raddr (raddr), 
    .we    (wr_en_int), 
    .re    (rd_en_int),
    .wclk  (wr_clk_int),
    .rclk  (rd_clk_int),
    .rdata (rdata)
);

`IP_MODULE_NAME(efx_fifo_ctl) # (
    .SYNC_CLK           (SYNC_CLK),
    .SYNC_STAGE         (SYNC_STAGE),
    .MODE               (MODE),
    .WR_DEPTH           (WR_DEPTH),
    .WADDR_WIDTH        (WADDR_WIDTH),
    .RADDR_WIDTH        (RADDR_WIDTH),
    .ASYM_WIDTH_RATIO   (ASYM_WIDTH_RATIO),
    .RAM_MUX_RATIO      (RAM_MUX_RATIO),
    .PIPELINE_REG       (PIPELINE_REG),
    .ALMOST_FLAG        (ALMOST_FLAG),
    .PROGRAMMABLE_FULL  (PROGRAMMABLE_FULL),
    .PROG_FULL_ASSERT   (PROG_FULL_ASSERT),
    .PROG_FULL_NEGATE   (PROG_FULL_NEGATE),
    .PROGRAMMABLE_EMPTY (PROGRAMMABLE_EMPTY),
    .PROG_EMPTY_ASSERT  (PROG_EMPTY_ASSERT),
    .PROG_EMPTY_NEGATE  (PROG_EMPTY_NEGATE),
    .OUTPUT_REG         (OUTPUT_REG),
    .HANDSHAKE_FLAG     (HANDSHAKE_FLAG),
    .OVERFLOW_PROTECT   (OVERFLOW_PROTECT),
    .UNDERFLOW_PROTECT  (UNDERFLOW_PROTECT)
) xefx_fifo_ctl (
    .wr_rst          (wr_rst_int),
    .rd_rst          (rd_rst_int),
    .wclk            (wr_clk_int),
    .rclk            (rd_clk_int),
    .we              (wr_en_i),
    .re              (rd_en_i),
    .wr_full         (full_o),
    .wr_ack          (wr_ack_o),
    .rd_empty        (empty_o),
    .wr_almost_full  (almost_full_o),
    .rd_almost_empty (almost_empty_o),
    .wr_prog_full    (prog_full_o),
    .rd_prog_empty   (prog_empty_o),
    .wr_en_int       (wr_en_int),
    .rd_en_int       (rd_en_int),
    .waddr           (waddr),
    .raddr           (raddr),
    .wr_datacount    (wr_datacount_int),
    .rd_datacount    (rd_datacount_int),
    .rd_vld          (rd_valid_o),
    .wr_overflow     (overflow_o),
    .rd_underflow    (underflow_o)
);

function integer depth2width;
input [31:0] depth;
begin : fnDepth2Width
    if (depth > 1) begin
        depth = depth - 1;
        for (depth2width=0; depth>0; depth2width = depth2width + 1)
            depth = depth>>1;
        end
    else
       depth2width = 0;
end
endfunction 

function integer width2depth;
input [31:0] width;
begin : fnWidth2Depth
    width2depth = width**2;
end
endfunction

function integer rdwidthcompute;
input [31:0] asym_option;
input [31:0] wr_width;
begin : RdWidthCompute
    rdwidthcompute = (asym_option==0)? wr_width/16 :
                     (asym_option==1)? wr_width/8  :
                     (asym_option==2)? wr_width/4  :
                     (asym_option==3)? wr_width/2  :
                     (asym_option==4)? wr_width/1  :
                     (asym_option==5)? wr_width*2  :
                     (asym_option==6)? wr_width*4  :
                     (asym_option==7)? wr_width*8  :
                     (asym_option==8)? wr_width*16 : wr_width/1;
end
endfunction

function integer rddepthcompute;
input [31:0] wr_depth;
input [31:0] wr_width;
input [31:0] rd_width;
begin : RdDepthCompute
    rddepthcompute = (wr_depth * wr_width) / rd_width;
end
endfunction

endmodule


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#CX;ej1Ao2e7@,a^JYO^[CBR+7W*}Z<v%[Msr?=@En'"o}WV}5E#Epx_N_@_;6-<W!6iRCo>
    aza<sk7_rPlwJ#*+[[o_n=['CW!Ul$HUl=U7BvVO#YE-7J]s3@;r#Z-'*Bze_p~sYs@]\2\OzE_W
    ^x}!A_BaJl5RETX>naCKlr-,B?>n5[m{~vyP%i^GT[sxTBaeX;o]?&!=nZR'UHIzAAe=jRyzYDWp
    Q$utZw{n#riK>*+Bl\5ID^ij8*QGWUA<p^loiD-=BWal]s}sZURK}Ip5pUL*jnJ~s^I}~GH/;$eQ
    ]Cu#,,1<UVeIUeQO=1R2$Vl[09WsoDRrw_4~z{[9IeIK7C1Yk<~CIC+[TD1rn'kx]*;*GK*=OH'2
    BIIuLn'rm-HCH*IA?Vn;_o3xGEis?,Q?}R#@Ui1\$6#Hz'+su}@,z7I$D!46r{;[xpz3^z?ozCGz
    ~CjecNB]7\1?-k#7$J!<e39m7J$1(uB;T9'}ZoOO^CBTvo-+;$\a$Wo\Wz2YrO%#<e{oFpA->uRp
    DiTjK_R+BD?m3G3uB?7I1XrK$4E*V2}~}OL^w2rvx7#x,$'rXAuu+}p<Tm?3_wvnY1uJX_#rlWJD
    @<{~<IZ7Uw?l^R5X-mO=v>]jQ$psxU{m{I'TROrM&V<{z~r1=wI3Q'k;1];{7\_ei,DU1'~lQ{Do
    @bH}+j$p{rOT<l2G<lJC-B][a^5rAUQ-[@R+R=O?Wa^$5Qgo-_]n-mXGBwr5>oz\?G*^nwZDYp,<
    ]n;lV6)EpiJ$d@v~7EJ}TIz}Z]\k;&DIJrQKGJX=D!fvRj'TzVr_v;CP<G3;ClO~kn5}t'-wA-=A
    =$2Z'_\]=GHjkuGDkAD#e)Bk_Ztkx?Gleu_oGG+'@7aQxKH{OIJEo-D^_B>#]3GuEkBGn2~L>j^u
    jH~l.x#jkO2WC+]_\W{npY]_*#rp<e2-*2\Xa7juG+{!T;Y,KK'5CbG.?,3,?B$v]#n1y8^+EH1>
    1Xr@<~WQK[:-'->+Hup!G<>isl=l?WRn-zk]X<##Q@#x[nG5EIu1xJK[+3IvGTWK1D#;nV5{EUQ&
    =>,Q(Vv?~iXU#757EvUYCMfJA7?7Gj<Ko+rC>5-x=R*C_l1CEQ?p*B;eWYkOv'+jATQ!xk@k'JYV
    2~W7;[WG$Q$'V^U_[<r0]?57hYMDjpAN)#_?JjRi;?}loc=nT]'OuYW^D_v@Z~?s>[,}_}zr^z9$
    ^][RI=KC]K;Ko_QklK@D#$!<pW,'-@}[Iv1TQuBKXop_a~>meQ'R$@r=n*^"Z]'$YmvJ,V?jPru,
    Txg[RW*2O;EXXB!Gl;pwYT?su'olXX\>ro1.Izv{E-3Of?Vl["QTWO7e!\>1-H^+o-x!wC,a-lYj
    >RvVCl<aVO*xmWsIsj{A3G[*uX{C3,raD>Fo2Il,RYes;O[=@o$}Iv=Vev^w*z_3AjulK@nus;Be
    ^GsV,-J_swn+Ux^#<$]UAAX;77l?p?Ur;CB$r\GRs}]<s3uDC2z12]3RX~^w5Alrz>jP3IvxPGxj
    w]W{QVRV=UO?-QZXJqskH}+*BECExe}[7#xOko'Fr>j[u-vWzx_,xs!+[>b=UBA:)1@uv+EV@QKA
    15I<*=BIu}H<DIMe^s+9_X\QjvmH}loG*iX@/C,'$7j#_5~Wy@n4mY\ORHO$P7e'3nV!r7J+YEaC
    OR(Kr<ol_T_Y5<x<\5lR<_*uT=Bp{<GTjA>BZ'[e\j\gCb@BA_CieDvXa7g'K]+)-E1[[iD#pz3_
    1TRjEIB,Z+!$^-mnJ*vpKjDrR}Z{2G}A\Q-rq<YXRK>YEz'w3p?RDonmGL]C$o$soDnxx$O5V>D1
    w]QulH9$G\Ct*@Aw%D@7jCW<=_Xe!DIAzl?psJRD]$jKz_#ED[m!o8neu?GkVUUw@I5zU-\pYo)"
    ?Urra>O=,\KT;5r=EHW2v?5Ke+}nOO2D\T~X$Yp$%Iqp5<#Un>KeAHm2YJxpT@jg0l]@7c>_p?@D
    :F/z~AvaDE\+wC={DiaVpm]3}WAenGW)@<7W=B?\vjDsTY!~o*ZKaaVYZC!-<ei$QiuAuV7v}-@T
    x8WI!;uETJ^Z>Y[pk![\V+ci,\1ZX+WQQnkQBiD?D[BZ}]vH\~@f:Ok}^@^3ZKwXJD<R3+[X~^]n
    {AE{Qvp-]Ko>?B/X=nIzskK~OITUw1wm$vDC{=3C"YH2O5W]-5}1sIZ'?HQQ'}151\U$1tOA!pD-
    !;^,>rWpn}D58VJK~?C3>WR++'n;mon$uB~WD3O~GY,uHqeX_JNk+[e\4EXXCY{^=p3p[<OaVER\
    eUQO*>-}pcBQnTA]5Qux<>1?DwQA@acV-lQ_C*{3.V1?pGJ1mIx,A+w$EFn_,rw]@u?I5xz_v#OK
    Q]lzrI$r\e=oD1-vow^amrK9_13!!wCC+wUpIVr3=<_{Yk'7Y}U>QDK}Z_!+xW5eIUmpzkpBlXmY
    OC2A^AUao<*k*=x]?=BI>E57aXU!YCAxp15>/uRK2<ou2=jO^I^o}l\RO1ZH-k_Ep^kWer]q}l;k
    PK]VI8xE>3lG\^xR#TwI5eZ+e^jwe[HnuvO1Ue@Er[SE~*BvRe-]O__$vzK\5vY'[!-;D{D3\x?@
    ]<v,OB$riJKCe$oL;Uze#xWBGGUH/sjE}}wEw'z,3#n!J]B2#pJZD_iRaqpRn?aAXeA,+2~>+V-s
    B$oeZVWv?QKaGI?l@>jaJ*;e{~DJoK?,EwG2<,<+wG!aHl5^UE0A^[lK^i5epu*RiIm@[Iok&Dwd
    z?jE5!npWC^'&>s{[{H=T/jOWu?Vp<IzZ<h_-**$VKD{p{z}ksiQuj1BG>#3I7oI]#E-'><@DUp,
    H3wGW3WL7-{[eUV1,;1GuaosoGTe6m>EaVGCk*j'2)MKU'?=sGJD7TT;nTAO{[_Vu}}1<n2?5CQr
    ;5=#On~ARTJ=sa#r-Q~C_laz^{Cx!{+rGQGe$?\"A]T!7O@U>nrK)dA+j$}71;@+m^a$r1K1IuaE
    o~]=~JOKHj'E?j;C1u='?7[Z!xI3=zDizXACzo$eoaT\HwLxCvAKHx;Nle#5sa1R!R~Ke7H@&ujE
    3WTXx,K$v];@nko=@hsRQXXHe;IUuBE?5En\\skYi!~{D]\ewa~$^{Pl3D<j\aeDew3l'j^7~no-
    OOH'E1j<V~zkEAK&RHr\CTpYrApB_+YU-vW~]m23/%=rH5"oD$unY^e9!rURp-1JxIe<-CJ<tNOU
    Im*$$@VJ\kB_mj5'Q3*}V\oT,5)~,E\ql[I{aYRic!Aeu*7\@ije^O5,[7"<jx~fAsDO#pAeoomu
    -[>I?xlG;D=-;HJYprC#&\<\$Vx\'V7;Y;UVsk_-zn[!{rm<C2EX1OHE~hCGl1d1~!{!G5K'}Ho9
    0njrE'kv2lKAjeZx5;Dr'7Hn@T-{za}m>-_^j>CK_++emriGi8ZsiDU=A{~LAUU$j,JKgkHAo]X5
    ZuY?x3V?K=$G]!sjZyi_-Bc|z_>?vUnr^5noAseG2CZ~p5-UWsn-v#]~xVJ[Bws{u]z#[m,_e>j=
    =k<X@R?lRZDa>DrserHvCH]57YXs'eD>X_1D^H!>cQYe]w5]7Av}3$sE,}{j#+w-~EUurbGY>71C
    ^;Yi{[ponAzG{EZY7IY*Wr{<'?3DR~ok,@HaJrPD}i}V]-\XR_]kO$I=;ajpj,2xTUB_v!e\+-7/
    rel2emGeRE$wCk~aApj_WowYc?'iak\Xx*W7\B?7+~T$xVz#C=AXxY{G11Z5-2$ls^r^GO3+'eQl
    v]=oTB$eB~{!1T<C~$B?UUXXrE=w;-*\ZF15RkPvU,Q1GrlWIB}]C\YjGxG1=EGV<lUznHIj\nkr
    u1>L]3ln~'n[y!r]k@ajOXj2VK-J$R^RJ[ZG=A]<lT\m@vK}JVnQuYO+odExlXBI{<}C@T=k}]Ax
    HIDEKm{*]{^R3T-CH'oXo3a^sCaVO[!D7H5ERAuHX=Zrm\feAm[IR\v^H_vVGQkx;BB1~OO_1eEb
    o]s3C@>u,nx#-jYri[;TOi-@CI]YOn+=\{ju?jCjQ>-C.-GjlnU<x1k1Y1XJ~QE?32{o16;=-Qar
    I77xZoKAV^3$J*^pB'[$#ksOW5e*HBGY!QUDe{>vB>Eiv}$=O7Qr#HYmV,iz7Em+sKCTVZR!Da+>
    7G75ms1D=W<EOBR+p$rE@IT_5Ru]@*}O,W!Ol5xJ^3xHI$3YX2C$jiCkm!eR7il7VIzCuvznpkRZ
    rQ>'w{jU[BA^}?B3{Y!zmvjYABDQIEmOEEliEIy+Q-{rA<WQizQBQ!T1VvY!YBTS}j1^OnI'$DY-
    qxDioR$]@-e71n{^wZYw_onJUyz3!OQKOIf_<HpzBUG_iv-,O{mqQR?~_}K\@VX=T1w?W+I!,iaj
    BwuT\>~ImC#xKC?\*?JjcoO^'TzZU@A!H{_pQ^JIn+wD5GuDnG,}QX5_zBko,!O,!sYw$7=*v0A1
    1R#-Y2@C-1}Ru~GIjvT{uw8I;$@YUW1pvp{Ih5anQ7=*GW5mKKAu3<>raa_\V~A[HaHW}-5$!:==
    J2^!2j=]pJxulu7<7K5V3*B[W+pi]I*WsC(WX=D@}vnBx~an5QG#[EpIYCjkpJ_/_Kz~$<1lD{G=
    *E;Q]vaKZav[)7Vm}mwr5AB{AC2HXGXeO<+jQcOH;n'nlxKBKxVwO55]OOs<x^>>WG!=2wr,+$Vj
    $Y~DYsx_$O^mms'pkTlVVK@z+<]2=KE?3#o5_?#R*KB=sCmUW\CAR,@\H2p^e5x>^\@p5K^Kv;bB
    JU^RUn}IHs@8D+1nb57lB$aQnenlx;11znDmUKw-'#Hx1j$,[nla{*D*,{_O[D{HA7r1!]unA<EA
    -KeiIA'^>T]3,:{CiU+R<,Q%EUA!{'?Xke2Ys7=]!=w[nG^IY'<,e$R<{T'~B>mQp7\]ZR@]B<_O
    }E}\O'DVMC2ApYK<~]@Y<<l1m,~7-BYvIb7<@~^on$2Ijr~{5X}rz?+ajU{z{pWhz2IAus2OvrYo
    Bv%95fxT@Yei
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#f<}+2*Cs?<7Bx\E-#,"QlR+7W*}Z-J}"[Msr?=3zG[i'e_}12E?e!'U7A<7pvX#Tr#xkW*j
    *UO%S#Ull!r#~$!RVN^,]Eemp[KwJUz^;}MJ'+H1vH!KUQ'FZQl-H_z7+Y5s_+lk[W3z{jkeJUQ[
    JX22'@@$=X$i^Tlp]eQ\)4{A@z1D5Jz>*Dm\#ukB^zcl{B~p?O=$H>?R5k1EvRB!zrU,-Zp[Z@Ao
    aj\$GIOg;v~lUrZaa[JwJAvBIDn5-'mY@}JA+Ba7k]*^BQ?YZlvj~HAuc]?loE,DnCJ\rb};@sk_
    !vHjD*'?}T-$T7=s+KE3w+0!1>zR>1uoJDz\~Y?$~IiK1Jo=,RBs5YsWO,p\R5EqasAoC!j{QRk~
    ExGi[#~!Yw,$XoqQ^aus~u'}_[u7-~^]pp}Bu@GH-W#CZBoR!'~R_B#[m1B<^=J_K|wHHIVQ*CZ{
    KvQiH!\[!e:KYZ+gDCwE|[J,'|!I@@2[vrBnDZ-X1O*<OW2*[jjG<!rAb^W@QO=oUrsUA#}7\"wp
    KZTC?+HDI]mOeD2'OJWaHB1~C+_EpriQx{jJ<ArO>BCEX^,*aA0f@=5XRdc,,55-$j{mB^TT-P%^
    R#;BWX{Z{BC7{{!vW@OreG7=I?op^{xzGzrh0+5D<vHuB(dj\<UO,>@^~DR7HB1(EwRvI2_aGKj*
    .l<Q,BI+R7~lew[CeIK!kL\K\Kci=a7jV277OA\@*-CO<\]G@VZZ<Tj*5QH]p_?lpKGpw[T/-aI;
    xYBW|tTBB7rWx[2n*BIJ!\ne1*CUw-jTW~%,Has}RlVojO*=KABOX+[kRIRA]Z'zpAT75r#VBe,e
    poagC!Dl+\/dzJ,~+$wB5[x^VjA5~UmWV;}];E7H\[r}x[}!*Alp*}o[Ozl+ElBK|UB<{G5<J#T-
    WV$;_XY\u^Ozxl{JO_D2Yk<KI?]],YGlJeQOK5HmTE*@Q]i5=y=o+pa^lxlOiR2xJY1mDO$Z^\*V
    -'XR#=N2OVAI33o]vD^+wW3'xKrd7s?>}wnOlkDvt'V#~VYnXl^kvIivK&OJ!e]xsj_D-[^~Zw-]
    sBaQa~K\D<v{_B{T6b7XDnIsrJ'+{u'V}2Rm@C2]ka!wa'-}rJ,nT<k$u]p}2==*,ECmO"M5sp^s
    ;xRG5$C^e?WaV{{DY5e]>{<Cb72$m~[]O,a}-rV+#!<E7Ya-Tu[mUAx[D^,+]-Q~$Irj[1Rl<<+R
    Do,<J$B'}c:f>Rr_}noTR/>n,TiwCUQ=2$?}uKHjVEpAzQA=o3)1ITGlVYo]<-JYe[l#jKX]KH#]
    tEG\Y<eG!LlwzHO*v;R11_v>n-^#XsIk|4s>{,S,2Z{r@A,V~-T,5$#_@jkYml{Z7A+~Q]~v*s<?
    <A$kQH[s!_x7Xo;Se<AVC~wo]Se#}IK53lw-G!M1[vW2V=}x0?eJ#!xs!0KpXDK1in#BKp}i\713
    aD'Z7jyP{=,-][aIlRJjaG#sZolk*<vI{[+U!ruJ<w-?Wsauy+v,TrB~A*5D'$X!^G{zCIVTR]l'
    Kc=<\Rk]32EumKtppX'1*@a]{(a'e=gEeXm0[Gn*Ii!*}_aJlX+r,$AuzTje^Rr'Q,U[{j,$Ws;v
    l~<,JGs#\!CurW<\i-2^c7,zp6*EYU1J*RrKw2*KG}B3]3vRZ@T7jvo]T5Ra*2!<~VkC+WUC^[[v
    s;*k{r+z'<r$ooViQu$D=^>55peoE+7RCJOsoa'[O<kGCv<v-rTRK1#1m;E^}wC[O_xmVG)QJ]=n
    -EA>1ur^!}I~jn3KL|*?o$7BHj+^!IQzJBCuD}_zY@w*~m$Q!jp-,nT<B31z?@JYEQ+<mp+GA~W[
    eZp-AW,[l{>[u#Q21T!OE>{>IEoA{\G<VI|;YTR0Y~GxsGRu}!<[w[jG[pWG\VJIlUHo7XQ<OI-W
    +vasg0"*@5XK<]B0!A,rkEU>&_\XYqE1{{:xvHK$k-EEBwJ-XO$Wp>[BGO>COB+~TD22RxuJ5Z;[
    ~l~^^pO<oo[r#=XeROXUEUxkH@<GO*$u\l3$TCD<\v@fmw^YI>CQmY[Z/{X!}i<XA[}@{A+3]CeH
    V|z:ee~l"l~KYrOimTYQX^TW'BAKA{H~Ji[E{l!nX?j#@oEA{oGJR1jQ2E#G'm[J*~1T~E@]C$C#
    @<>p{nAKe$[V<vkODJ7RT]*si!Nxr\?69WR+=2D;OBZDKFm[-R]TGr~5To^GDx<AZG0~rUuYeKJC
    {RJe_3R/@s\TI><5?rXK$;GKaTBa}eJ<;{\D<EK$sdc,OYrr18TR~[=B$k$BVB7};Ge]{BC^>Du}
    n'Es{~JDQ$E~XZHH7]e<~+(IWn!mo,A3V-weiKn7uHwAzruV-75B!J-<T{cIeRCi_wA'Cr+whKA5
    ~;Q6,Q]E$!-QRuX~Qe<ul,=O|xpz}o<-GO2r!*WH5RK][kHomvx,z@]T5[!GHyT^X<Y@mk'sm1<-
    Z2}kI]m}]*w-a@o-@Wnhx2@$z52aj{1I=QeiHx~^oCr*^sYUL7XoWU*HH=oY3;$+XfWwA~x~+u=}
    os];U7^xe-,#^!sIz<zaC}7BAxv}AWk]xos,2\CDKwgDzel9uOj5JsikoGlC2<npp^TZ-w$[*}oi
    r|/5]u2@_~5:5<z3j<V*-Ol^}V[U,sR@U[A;-E!j;5okan<@vA+?@EOazEa_"$xiAv]l+!lH[e]k
    pGCOA1>lYEHA^2v-}]AW}+-[1{Gm!Bjs^Qr;-eO+Y31-I'Uli3jrXOA+!s@-nj#lrOoe>VnZWV>n
    kgek3XU_Iraz3CyHYB}\U\pfy)Pq$,k$hBd[33zlw<-Y*$U|"sU$R%5CVOIm,v!\+GK}?AO@3Ozw
    X'Ka<@|U>jHDjWwQ@vo$waR75jn}R>\]XR+(5DRBl*leeW$1t@<J[_)_;7U$nKlu],x}xpiTH$e+
    YjG^KxTV;l^_X!$p_-~i<WTrHw*!]*@0Ydv+U#'VI^\E12RTU{Im*mVz=EYXQ\Z*w-?okKu-11__
    2W\KY,_aZpZVT!l?{Tze{QHU}jZaVH$A^k=7s-semJ~<T>f/zQYwY|AD{u$~IRrU3<BHQ_B{I^1G
    ,$Bw_a}X;s7E3XHn,$Q;x<]$k^^3<J}s7!e^WQWrEu?>2IE'2oVA}pZ}o2@BOva'u^?]R=:jXl^H
    sUT=G\=}xjorm!}2}{DV,n]XnC{xm!E1C[_GxEa,v;1UY73mEUY)u*YE<AH#r*A~rTuZ7m[?qkU]
    ,~ja+C_ZuKC]!XIXHk7j7}\7J1]J;1><>25*'I{JXo^w>*5'@$1CUjsnw}'+Jn|nE'j}q]u\wTC,
    CDp{A!Xxw,AC^U<[vy<>$RuYumlZp3cBI^\#,D}EO,K[B1p?N{{xpG-DeY5'7Ik--OEEXT>*C#Tz
    >dwXU3q3V_xijnI['*]H\~Aje-{t0J\-j=m7!$m_x3EQKZQ>_Bjaz|@^~;Bx[*xn5R=>;IHw^$z>
    YRUGHo^?GY5'{+'AAT$i\v.G*[KvEB{COl#?E2!LCo<jn&o,1Rg1nDe97IuRx#\2~G2o<O2_GZB?
    kB!uYBXw{Cr#^O?[Uepkj%^6/mwTV$X=T;T]5+}{oYEOvu<,OOrum2jzs/LvK2m=n+}'Ij<qHo,u
    7l}2-'ipY=rwA]W2[~=v8@*2Cmr+*FI+s$c}U]A3+AO@,HlE-w~uE;<p*[<tr7@^kl{_w[^@3eBA
    J1']5*KCIsx]YIb!RKCY#^xS!l35i>^^${$*k}VpyKrH]mEl1ZHH5kYnkYREvo'vH1Cm+@'zDln\
    Zfk51n}]u2]uzZcYwU*\T~xEwJsUe*'K7A\[QET@x~Q+Qlxvp]<7'{EInH'D{OsraZ1c^@\i\QYQ
    eOTC]oJ#pYoG{e=z,*JIL5KQpODnkvk}W+HA]Ka!,+pxI}I=o=p'5~xRW}*{{usaD*3Vr3XWEE@T
    k-piY!,~vCHs[~7a5x@\W1GVz7{HjzuWn=uu~C5!Hd7u\U~p'K]>KfkaT?jnXkcM,zmnH_W\K*,J
    s~H}HYY@U5,nYpB#/$|R<VuC3TRUl__Rjx[,v<#EsOWIwV]A_aGK$]n?e7DVuseX[+-]?Vj=avZ\
    ]73{En~sG>]V~AOPU]O3s!fK>amO7<{SB]~R{^sXQKB3=n,?+AGv?o>,aw@>mC$+aR$W,me2fZ5m
    nwUZTe$n$A,2Akxow72wI,m!U'}~v,C;jjK]r@oE3Cup+L9uI!xzn2v~*mBKBVEq#vHO--uozQp!
    sA33Z$<pTUEu5JV;;UH3_Yz'a5\w?B\r_,lxM7l{KS\Z*>e[w;E>3akU]ro\X5JGQR/p^J@IOWmc
    Kr[Mv+1s!7#si'jCEv=kwnm_,-OswRH21_I_.R_5oC;$Y?nZsY?;-w^}r5eeR\IiEB~ur]OUVy#A
    wnCiQX*HX,l!rZrC1]lkVeZC^~-Ve2#1Dp3QE5\$<m'll^azlx^^!V;l~XIvB^VA}G>^EaOTv;)=
    rRlDmUZS.\x]G2B3,0-Uelx$-lEXWG5}Dxr@z7G=$$AUWlkV{jXXvWr?C#}or+1Cn$!wW+^\u3j=
    ]$Cx=Ej[#kjp~~vOv$F3aep;Qm$T+GJ0Irmlrv7}EAIB,>z]mUY]J[DO5CaJQ7~'x!QTUQA$Q>;p
    *DVQ_u]T}:}QD@dG,ert]=D^I,I^l++});v^7mx=^jssV,7D]?hlzn=73IKC>2@$5@~2o]{Ip^5-
    _-EE*Zzg]-pReOX3nw[Wg''1whrV~Om^1+sr$YAax["1OjRTvm+^7%XY$J2_swDK;!_Ul3Vm;\7^
    Bsx!sQ=}$Ine[}<pOGP'jV1vlI]&Ok1+#+$EC=Im$$mn<TY+#U[DAUmIio@$YlwAwU{;,iH;YsDj
    VBXpRG~@{nOJ(Kjn!u5$}{jz]3YH=7>QT+>eYG;@A~5jHWv[JTDjW&ejDoC'mv~jZ#H^ieAA,,$!
    ^nQ>s[6\O?k[{[Jz,C#OQ{27{[~_*3lrorsJQp^3>ejV$eZD7Q@W$@m{s<Ge]<zU^KVV^2r$EOan
    Gvw3sv<7QW52*1K,#'uTAJa<]?#s5<z'o*^\;9<sn=la!*Mee!D^@27:CHD3R-Xk'B#=pz\Rv=-k
    -HuB3<2{zxWuAp_Z>XosavCH2eJQ2\Qj.eA5kwrViaz$YUe*wQpEAA,!CInW?]K;IE#y#TA_\{vx
    <TJ]$^V+&{.p\W1oHY#)2S+_Y^Eml{7ZO]_<G$5KI^*ZC;uvpU#Eu2O;H7eC3@wo+epT<IOnA9*$
    ^=B77~55p-2OH!,a!}Gv=Z&OWlT#H>K,Tze\sUaLfBw]G!R]^-I?{3_C*l_'*BA*?K>F,KD\oOVI
    jvm@!V2aO8TE]WD'>>a,=o'?$V#9vCkZ^C\[]{n#,GwJj#jva5G=EC=2l^Y}<=*Ak5-A3\7?*B@$
    9:"JR;aiY\]4Xn+K5n\X'QU{,v$U1Eu><p+;WOnZRxXIXG[~^-{I,TT!~1A1v?Z7VlevIGR^!xTn
    \25-eR5Akz;5-]v,/y2'>UB>aJXHC$e@;B1>r<'p3K7QABo~];.zk7WEq3ejC@pT@V?U'eu1!+$T
    ~KVX^iR3I"[$OpOApamCA#!zis_{'!5*zA_kUOeRR<'\dm<5nl{C<rwQ[5zmKIX7GOxnlje7-j+@
    1v,C,|;Aws?[lX'\'O*A=\H>IQlGXsmEp<px#Q+1oC"C+[,>oI\Q5vgWnUmo<BDVwGJ{DIo~IHZM
    8u$iWlpkz&=-~$Qr5>UHzeHUjByxk}?z->z"=}2{+Qru1EinYWGOUr?A<>UvC>os\~J+\&;UMQoi
    se+-!@U_mv<V<=yh]{aZWCsj15i<37xVxT{@]K_kXI@QJG+'Ge1_^i[2}1{7hBkY7Jz*k:Eo>evI
    23E5]]a}!x#IVCp}HBp!<OQ<mY,<+J!O+p]TIGDJX,eX^ZHsIs#X>;Q<+]:n+T?RIR_,-2G|*I73
    -L^kV_]e[!Xa+C3TTp5K@2+1iG;sjz:~*,{B-v_QxB3aYuEm1_RA]ZEv\G#,iTCUa;'+\12DiJEa
    V5\z9QC7<j<GW;_KX6BOaEa]{_]$IU'x@ur\[oo$Ys*'OI$zs!ARIXjB7XjsxDBZ1I\-^vy1'Hm)
    =CVx{e~X>eO+OOoUC<Yiw72unRACpmWeV$A7'$Ze5XQH5um,@D_w~=1i/'vz@*@V'eV3@TvvYB\V
    j%.y8iseQJ{HTYH1HYT3pB{m*DRiIK$QH_u+OR57wTxA7#{H~C?C?[#[7h\em+[,s*%snU[CZr<x
    TJYGi
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#eE;XOZ{a2=XJo}asID!o,>jW5#aXisaO[M\\#[QkJpI?32<1zYa}JKJzCiV!3vcen1*yeVf
    mT_#<|OZz%lwJ#*+[[o_n=['CW!Ul$HUl=U7BvVO#oskU2*3vH!'#Z-C_Gs_KH9,@Ce|)v++ot1x
    l@r?jHNj;Xo]g}ZJQVK!?N5CD-^sz50)^Rm*^a,mx*R^uoB?\='E-DGK>&ov3^Z',?nl@av^l}ZD
    Ym]{-X\!EHvA'^97$+n}m^A%}Vu[Y+RY!|KA^XQmRH$D-+<,G@fpz\$mo?pKYjr'zTuQ$i]Vmz@D
    i]\W&nj5!1x1CosorzxGDQzVs['[RzkTr.%i]V<uTK1-CYrS]'U#Ollre=2QBHmX$Kpuz[,H_$oA
    I7k@r^}k[~[no_o$W$oeRv!2lr<,NQuaQt?<p72EC{nO?CGWzlVW~e$+}UG;u<U-]Z]xk*q5zBUu
    jCzu_xZC[?;7#=YN+OW+[5?+o$m}_e]='1?5*kY'[O!pOT]u{w1Ai$W;;{-Z*\r~lQOpIapvYan[
    e\Xu37DWw<AZ5#+#$nMVQ{ZxZpWmY3*H-{IFVJ@o7*rVLij3j\_kwBV@a7+UlIO2WYnmQvX_~&A\
    W^@Xj<'pV3:J7l}B]i2o2Y~,ez^[Iwm(1,zi\7v\/Yg,BiC,roaNo}O,ACV$R$xE5Uw[L7^X\_@s
    ^}jJaD;-u3a{7VZV![JO+BekGz*Cu4'DQ[$?jvOI7sjln-GKV,lGxu?Dl!;C-m$&={{E]!Q$JnE{
    QOgm]7Zf#^xoB]#m\eGs'^BaGZ'?IDM_VQHpquTX\ri\,#,e#%LQaneWz1^E5K^jUTow5Yk5s{I7
    #Jxw]]?=*+EAqKBr<pxaunjkX_nOD0*jkaDd\WVOIE$nYr5~]o7@ou=Z1zYnRVQ}l!),UCDT5<\|
    L8\[$J>EERK'!IIlC3l{@QN,3X!^~n_]l\ex]n]7WC[qixRij]>ATEp>?Ow#@T[Y>Uax,pE*BU]o
    !}ippx;!IZnV]aBx_,X<DW\e<^uJBTY@KzI3Hw>sv@HWZ1p}}p?@=elH=xnR><<J7+ppe2O{73*z
    @Dov@zReY+\!n>YIGz+w+[Tsu-@~qyajmesY2CRI*o,'#5TD#2?[g+-@eClKU'3aoqtx1{?R<+A[
    a]ef1U3;\<'}G><1^'''YX7*=IxzXxKzk_3*xuT^7?}]^-5rR5=pr3'VI1wXs?mVa1-;@X!562pQ
    vMw\~<B>2T}x~Bq7Z<v}HYu1O_D~GQ3.c1T;~XX1~><Di^],]YxR3*5emOjUjO)~DooNJ{;<-l~]
    Cm21?A>+HH{UC2aOs@z-iHIZ,{<Wz*A3VOORv^o,:lupi-Gl'k]R}iRi7SoCY!'m!1,Y[lB]EBB+
    5!:UX1@BA$YrG]@EB}3Ve*3r_X>T5lzUXI{Dk_17k_GI;slW{=Y@\C253OW#Te7CII2pIu[mRBk-
    5opazZpAv~usVnrs*Q7\n~H<l$]v~VK2jG7O,u!eH-H'uV^Y3+r3o3'ck\>R*us$lr_zaQ1u'+;p
    5<]T2>E^i=Q#I25<Q^GaljzRIm$rEE,Qi1-1e^DAvom5Zz+}[zIJo;_VTO\3,wKE]?QWIpwxLKDD
    _<'J#'[?w=+rotY>XZ[Zo,oDzn=D+^Ux3G=,]$o_Y_WDsDcr7p~sB<Zz<X7fT<m!}2,TkR~\j1OA
    EStl^[3Y=TKV5<3p>KEC#[zQv~Eo![^C@WY7[R+T{I>y;EaY2lEKiYZ@#eI{xE='CJ1@G5]pxK]#
    uz=HAxI;zBe#11$?avE5ix[z}vHx+=U'GvZo,35v[z'uX{mzaIIx\7R$QkAH=jV^vKIH)\DEkA[?
    *Gl=XK+nou{5#m,l9rDXVrYKxEw+Hizz3~oz1f*uD!s!p'c;Bp}9:}[w1-=Irx7w22HQ5VI^V'B#
    owoC*$Hnu>'CzOnH1*iaYJ}IQEV;2VH5zsuYQIz{=B7QoX$2EsVm'5?TKr;[j13Ho^n$_aO7eVB'
    IA[,E+C7Yhz+QaC1D+Br@OWDE<V<5ZRT^?7J\<?YEkKn-TzCxTqb\{{\#T2>[)-}JrJw{aQxXBvZ
    }>ur;#]IY,?on[OumV@Cv<1[
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#*uD=%BBo5,~GA^R5DdwE1x*\[X@[mBB|'~IV|"[QeD,,C<eH3rB}}iy(__m,7*U?oJju7Ia
    R-{AC@X=pgaap<+1$'Q,AmOoXA31,['>Kj^iY\|_j-RJ+z[v5Xe;TGR_$Cpek][omYK$@=ekU~p<
    *n'[+Ar;'$UreU,g=!x7aYQ*,O{I)eYH}eWx,=]1sZwjDzj#]K_jTkBrDI+<s>5*3rk-jpp\e5X<
    }{$#2[}'oJz'*$Qjr}'><Om,;i5oV'_?pupETO-Ojo'=x!,\*GlWssCD>Ba2zlsk?r*_i2XTk_'i
    1#sD*DH=Ook3_]#vC^2$$}_jiOiu>!+*luC~<I?XCCmmX<O-1Os2x_J~BQm{B}{ua*5kTz^}7#,1
    Uq[l\OIA[nzW'kzsVWTa@5p\HWC=\5S!sB,{YEm~$A2Je@$h=m'^arpkI'5@Rn->V>7-p'i59[?C
    BN1iKw1u{Kx>=j?]I3m7?l_*upgiwVDP{Y;!]e'Xg<<;BKaaKo<2}Nve$$2-$\'[$\!YRu!H,lV1
    ET1<BB|1;Ol=5e_7k]D'!pGHr~^^jaIYYioY5!uq?1?_?C*ITQBV_[rUUEYu*#eX}4Q=V{yuns@V
    R;a7uB?C7e5Le<
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#iwI!^-!RerlCkoUsN-Ga]U}Rp^K{$\Hf&1+D[xi*?=?W2G12?\\BoI2[AP\p>V~Uz-QD3ml
    Q-MX<+2U{){Ck;G$k\s{ErsSKwJUz^;}MJ'+H1vHa0xzuR!{H2H_zo<{O^0"Q!{w$*,iY;=B|mTA
    [vZJ7DZA[]Jr=rv3<N2C@[3(*@K>t,se$]<[D;oB7&7Q]7x<UB=\AAe'v,~H*3$B@3KpDarm_{bJ
    Y7x=5HQl^zkGDo;RzA\T_YRzRin5=zIyK6}?G-eJ*5h=wY~*$ZJ&sz{r]l[i;jeV]2T#,V1<UIe]
    'r7BJ}'1C$oBqIRO2]WYA;o[By}J[Dxr}l|@5D7dZe+2@7wrxxrnKwr7esk_<l]r[,,zIuC#s$Xu
    pl\$exiozxx~jQzi'A}_,W^JlV]}Qo2pY?Cvcm{X]1xZ!0;{uIhnjuV=lU?~UZQHYrjSqwsz]p~+
    #B!\zy1#{vWIGn'4CTn5RZ-\6CWZX]NO^7QY-Y^G1$-skGlHrzU^~B*,>OVJ51sQuX~emAIXGxYz
    <j[B$I}]uzVu+{EV>)vUG!9dRCv^l;Bo75k2ReY?KvR[R_vA!r1xGYow\+'Qrov@*_Dxs]e#HDu2
    7va\=\<lU1ia''77bC#jopI<E'2Ya1puC_A>-sY{JU<Q{maC?ZIi]GR@V-CGs$,xn$B2T#r7;Bce
    TXD^^,*qVEpOBZ*G~eW!DVZu&O;,[R~sR21RY_UX$i]_YvAnsoH11-=]7opv]<5Hjr'R'OE5AU]'
    __R>u^u5$O},r\~a]w_IUo}<o@aB;i[bpw3CEpTH{j-#)B_$~pA-<UDxxUH1[Ip!r_pA?IU;2,,l
    -wrk;a'XXb$^B!Ue-sBuY5q{r]7-rZ[[;<@kUvR_^jG~EQQVoeQ,';_<E?aUa_lOAWzJnW}fAUI*
    Y+\>l#OlI*O[NE@2BWH7ATa{eiDkUKa-~w[Vur}+-+R]<c=!+nj'jH3D7[1>w{x4jK^jHUxT1H<a
    ^O'}\BD[ce5+r7@*[r<p2B1XB.,Di^|5j?5V>TkjG~~}U{~IQvj1K,AvBp}YoOo]~j<2E+oIPI-]
    [jm[,!D,rC'O5YW*}v>WB&QCuwF-7UB~paG2X[^FA7wTjHw<62z[p+tbmIOi3]HJkB*7T5]k}3-@
    ^JoU^E<R2ouW7AK!vXJ$8Krux*K7@xrY;HDXeCi@*{n*^M,G}utGn^lU+QGQW<Gp^meGG=GdH_N}
    mpn\jD>'w-]IE1YweJGB#rCl2wvN*KX}l-[##eXQrQr$erv<sUQQ:a}]pr4,\k{-H$3~+<uhl4U^
    x<[VT@.Eu-2GJsZh?_}pL;1z+6,n]z+^Qw<{{<czR=[V<A_[sH7=nmv2rJ{\z!Y@vB$1BaDMI~+#
    X^GmeYAWrX~lc*K1VJ**AkV+{-YH'DlVD-E=iyl{JVq]Qr1'2*=wQz~G3T?pnJ!$ilOoOk[B5x[k
    {O\sJB2O2Wu@je_Yiakr]J#[#O,]eAxTp>=?>Y7f^dv\z!]*r?X>mY}ngDVauIBQ#8zGX7*Wal?D
    [n\VYzjnGTyN]>J;|==pz:HHjTBiTX]rj~*&15vCTOwY{V_$z?-$=5V18_r=K*O>_C?V~@Bz-2o7
    _QU@3vA(h!\JX:n{K\_a3uHxiKEG!$%Lol<\w^;zrG*^$ep5#=nQij}@o@K}'Hxi']',2Cgo\AlY
    2[vN'J{mT5QmYZro?DkIo>Gl2Y>$M*DDpUV3!@Dk-WE5U[3D_ra-jHY*7SnUJ[pu=_@[u!v2@mV$
    X-)6-s'e~Uv-Qa=al7}ILKC~e$UUaUV1>yn11i-Gx#i-\luUGBa*iD]VkaI7@HLYjD5I}1[pV1~o
    u!D_7_JHG>2V?pprn1_};@B5R~,RVUop>\pI{*@p#W-v+3U1w@BdB_VVHCoxXr'zQQ<pH=YCrTrJ
    N-jj[bnrC1s<AZ_}KE!ji!*H]o/n<{3%a+-I+D{EvzG~e2HDIVrW,!{ZC7Q~WH-~HEi=~j_$'?HK
    wCL!{AXyiU;=W\u7MJ'Ei?w;J\uWlqY]p+M]a5vB2DsUN>w!rfjmo{BWZwv;o>.U<T7Jnrupj3wu
    A$=UGa2<_>VnVV7eek=-x^j_sux\$A]#aHHsx<G?.<H\{xv7EG~p=sT1sZYs-'@35Ie5rTzk[V\p
    5%$zim#lJ2V'#E<Yln;vr9VUXZBGKQZe'-\wJ5$BH-jQw}Y#@ut{a}Zr*zxNoBGOleBW;<]@SWLY
    -Iin>$+5pXUO7m[*R}?[Eu!-=T{3}?*/_snDYY-ojlAO~oY@VDW70wEI!]Z7;ravG+Dixc_s#kvX
    K'j?[;'pU@[YX~v?JJU'rvYYWV'Z<1x+12aUw5UQnIX{uwIeA-E+zBpXCz}^u]aX]#!ID@Rm~_eG
    '1N,#~r~a~?[A[\9%'1<xwn1u^m^D,E{R->vQQ=ieGBQ27}UB[?_*u1>V$2,eI[[EAoXW60,3^XK
    n_5}5^_:GYmKHRA=3[a2-YJzS_^Xu,naXHD$Tuv??U_,}conJ+Ld!YRa:$|{--U'7E}|InW?Y}*m
    lk~jvo^B$BUx[\k?oiaa3O7?,Xu2ioAD.i=$$[B$D[BZs3H\C>7wa+j!{s?>2erv[E5slaYG=$>H
    $YCQV5XjCE!,BX';vD@\li'J7R2p]|}vJ5<Blu~{5__Im?OeH}oxs?rj*J[+XK=J=-Ow~Y\o{~DV
    UK_2=)ARolP_iDTpW;2:CnlTU*wReOsEI!zmOGXrBU>}1QeiFDrmYkwH]A^CX7w5i3pvB",,JX1*
    U3Io+EH5ZY#$<Z&GAOxn_BHOa7-u\@2cIJo>IHZ]xp,^,}>V2Cp]aE-asx<*p~zmYpovvODTQ5T{
    B$='>wp~B7}s'X[EKXaZBXBezAn7V[7ZbC*k@]l<z5Nr!nOi=^'#TjT;'1lO5;GbYCkOI!wK7uIQ
    ']5T3luny*UHo1zTV.=5}kFj/klpzB\nvQPs>!AW{I{]xBjr'u7aVKEx-Tp_L#>uOmUI*iBs@R<e
    [U-olG+_\'A>[G@E+w^Ju];Bp@sr!25KC${Z?.'i,=l\*r)zmxp+xlQ$O;}=HV~2R@oQ_G*5G'@l
    #zG|[@,aEZ'\i>uvHU=Z,7B1nnuAX<;EuHzk=1enu[su~+-!A,5Dlzm5xV$^,BXe7ls=Y],^UY@n
    Bor[YvDakAjUp!5DDi^D?>\Cx$z5?o@sB~AOU{Qmnw\^p\>$pAHlG;In\COH*_apbFr$1B1n@rr6
    5J5-P&IKnpEe}J3^_,YzpaxHok\sXs_wZ^C{KX@_-B5KW]JX7wq(upoKwrz'T]p,_xnYROs}nYnU
    HHJ,'Y+[][;=_E1n7e#>C>xZzY\D[j2sl!QH-nn3Q}r}{+OX*Q$$2Il2KBe[DnUI*cCT+C;pwj;v
    pRMr[T@'=BXJ1<z}Ux+nw3Wj}JuHV5=IKx_T+*3Ue']ioIkUlH~r5+s;nV@E1]n2rkWr~7<G5llY
    ;D}4MmA>~*]j$#wAwr=T}O>x,5kozCpm+;Qw]cBl>[(Cs=_HOXB,5vCW_r-7DBTraw{Cv;<[],'u
    p<TX*[s717rETu]TGoAEnw$ooxunD<CAa}O>SzJ[W_,V?rn\nzV<'xH3lZ};XUQ,sG'Uuv5@}VEe
    HAvp!na+*1OclaT!iQ1Bx[_!wD2?DO>3<$O[so^rHBXXTs)(%jZ1_{As]Rk*[^v<OB[*a2SeE$;E
    U3oRx{Ow-aeV-l]e{_p.xnC}p~8Q+xrZOeI1zK]d_f>^v+;j{Gr[{}[aXpm-wETEn=z>!nF\+*H}
    5Y_r*YBZ1?O,'lj[{UJ'BJJiU{Ch@*?YDkpp7\Ij$ODHVp5$_K]BcsRQoZ,eU5!U}[5ir]!vn35j
    C
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#Krx*-IG5UY$}G#n2~pG#UVK;^w{]G~1}N&1+D[xKm[R*?;+17kCin1^_{$[Y[;O;*$=!*=d
    w}@o-lX2zCC[EfkQ'Be-e]iXa[zx^QI}VCX$E7$W=3NWGuElK3nva{U%{oQ#'Ol^9<5uE:L)1C=X
    $*+''C}rBl^mYCRjWUZ>J=i2IoI2sUQoiB'is\Jpte$_@^zzZaUpzCi1i\lGv(<Q2[zWWke1[}W=
    @}$lJuD*e'v1H*3jk!2_{=DmE@:3=+]Q]#?~=@o8*33;=r1<BmK_K<3^.6HA>TDt1Z;HJsVa~+!U
    =spwan;]G\A$iUrpoEDWsB'DH<sALICAD!TDBrk-*T5={Vr+Hmv7X.Y1<X/oiJ*eRn\!UVrG-Kuu
    p?2JE^[eer~lW3l%[x,j*kle1@~@K5[!gIkzHO\VZV>Qm_s5w?B_-e{m{=m*#I5#a1Q,B3''C_$i
    2,gGxW={sIIQQ5'*#lTgeCKBkj1a!{UwSL;xzxoA!RxEYTR?^@ae7XKro1pY^VQ!1R$3OV1@\E{^
    >QGZz+XXN3<<-CKoJ\}Ho(7Y}@|JoWIQ-5]<1#@$is*IUo<'+Ex=Iv{E2<$7vT;m'E7=/*v,Tj?,
    O7ERs*raZA-pl;HwDPY#*>p3]pluXoCo+Wxla3$<nU#7;KEOO}I+7n,Z$pH\*>J1WwR3xmCYWOsW
    W#xmZZqX=HI+QJ^fTBi!^orE1<2-Q@j-=1$uIEun9'}1$v~\2>ADGG7;@'l@Bwvu#!j;-!E_AxWm
    zbxu'Ea^2~~zW$nT_\y^TrCCX$Kp~T-H{Xjv_X*vp]u1H_lXn*@ro#e<CuADr~~Bxxe(AQ{Ju,}{
    Ix3B^$T@<epZ\<uTWv7uQQT!^IQB<z+=Ep,eHLe?x$;sWzCT~G2'\>u&RnO>+nel#>'=v1V?GvC!
    93Bj+OxkJRGD=IG3G7xKl'C*1$UeRfKo{K~+mv_jC**#5uVz}j-5=}~<U;!<T]Xz#jHTW!r}C_w=
    $u?Q{GH_;}0$Y},[Qw2mR#V?T=Qs@Cn\l_[TV2oxuK=12E*np\^T_,j8lvzllkYIvmBQM^pwjJ<T
    @oZ}7m{e]\,An)leG{},]'i1Tuersk<_=l}$+I?>G<kTau:Xj'u,#_5u*#vs2>##CAU}JDaCXZR{
    w2Uh/?]r3glG^wH^XJp+<{JzEnxeCwn\*IVT37D,<Yez[Xxiz=a=v?#}$2$Cv'XQk[$#ws3jDa*1
    k7n]R<Tx<lI?C;lwmVOTV[@zv-9Epv[=[e3C<Z^u}u@1zX+ee3Djhv2+vH*;p\lzuBn-r'@*WHVu
    \+UY=uOYi~TXR=}##OIA2Tx\}@EZ5V^,vC5_^*IJKr@=5H7HO=xXXS-5_Tp'=Klv;Wj,XG?RWE=$
    <m=~+p73RGo'^Ha$m]=p+V)WRWrmAQl/o]GJ\1pCqtpkp@[k~JQ@r^v~'EIJvuK_K-28qQ^sJIH{
    @AjACx,'J7n]pa'}'Z=nH/=?wj5Bv>%{$\}nlH>R=Jumej}}5xaoB#IvlH3<RE2]J{Xz-7XxD\kY
    mskXeBpj9ln=}a_k$0<s_JHEkCK5nC<]A,OQ@7A<C73pBvN2\w!rwpv(oaX[?+O[(w=ex1EU_|s}
    *$~<T#*uTB^[pA#a<pXza<![}R[5-pk]j@;D$A!R'Kv_GKjBaszUZE>'_Rr-;-%C]2[p+n^Ytie\
    \=r;w-nB[)?pka7Qp}eXs#xE!?lWY!nQBH3OwT?CxDI~112UOHzC;+%{Q-~@=xpAn{'zGB2!jjK#
    ,;?^-Z=/+HTAY5IDUAl]R<J!uB#?{{Z-Un,7D}3o>'l~rK<Y69=sTE<IY2cjapvwH_~M*}@eijpI
    oV+5\{l@l{~}>XX]Duxm\B~kIvJ;;O^l}}Uj2s\sp<aWjmAHHH3=oG}rje}^v3UD1a*'Y@ojh;w[
    B*k]UYHBXTw[ErOe73rU2\H@afx-KX'\'Ks$eO>r\$*5vanOosr*sUIil1bEKAYVG@C1H\I='UKi
    Yeuxl$Zr+lAmCHH7T-;w[D@qvijI]+YA?{}wR{ZX|Yx*H!ERmej1RQ[{+rTEXJHvT2en;DxxQTQG
    vr?2$sX=zRX'5)j1T,c]X]jHIX@j$;1G'E2H_H{8Bux_jw!Xk_!-seGv+1{KFT>!+-'Tn+V+jY9]
    !jX~5z5p5}[{=Cny=n+njm_n5'vTGwAe0pO3,]_++B\Y]=KW+EjZC+oIQ^wO[aD]i3SFQRv_H+AY
    Q>n#o+n2zXslx!2xosm!#$nRE#,1z+zV}E7$U[V+-T]^]]k25n[ErspQkp\a*-lwlAvk9vpaY>Ao
    -[<^vBY2p?rjVv2aO?]RXq9fZaQY,EUO-EjlpkOE97!;Aawv-53-_pOi;=m71CQI_j!]{R5WmN11
    @HK}K^1rBeW>@-sDJX~\zGz<Jz_~><OK{#A>j-sJs@rsvj]Q!{(Dk5J^s2v72pmy4_Qf~whjQ*U]
    r,7,m]!5#mrw><ro}Zl'C]^=!{zsp-m<{VozxaEXwGCk5<>lkvXE*[ED-+v=<zA@rUJ,I,T+XI\*
    Uo'!$iXFqw_#sB=Jwh,iTB}Yp-&EuD#VA5_\n$EA1KO<]}@?YinYKpAg7IIHE<'AK]#W!+-rov~Y
    Z';ws+GoX7QJksTm{7+B}G2u<Az3DU!kaw{7W-1#~$5'D}@l_z-T&Es2+u9+p1p<QpU.7~}ve'r#
    Q3!!lAp@M3aZZw$Ca[HU~kE\7!';!aOl,V-!ZiwJm7Zaen]_1zQRn@aCsMe>WXa5i\^U\anI7~Ye
    G?UHQorma~OemslGI}op<ktu[Dn2-w'bQHUGC}_x52OzHY1l#EIv.7!j-m7<He2~o3t{UrY7$>e^
    JoC)5Aw=I^Jr,zv!JCm#VA{en}_x\y7bw'#*3E$[o96p7=2Hnepc^^k7f0BG@n_E60e5EEKY?]
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#[G*lDZ;o{1;_,B']ZXz-oG~BrwD^3U@7"|.k9yDO",,57<l'C9UEq!B#wAG-@<'?2!$;JyA
    7#H#Ull!r#~$!RVN^,]Eemp[KwJUz^;}MJ'+H1vH50xzaR!{H2H_zo<{O^0[;ouXr}x}!-zTS,+Q
    \y>>WaC0]Jr=rv3<N2C@[3(*@K>t1_\eY7is2YI1#5iE3+!u!{=7$B*QO=Q>si\]O=@*2]THuD@O
    h?-'Eo=3Ec>=$]L~j\u[7I#9Jsr\VIXRO1DarWz7_o>;}JrrVz]'oGl;;5}nR\AkG~[*Ao,G=x*x
    NYImu~*=!RZz3$~pZezv_Pf6\Y2rUaA}CT9!Yi\!RIuQQev;=}i$za7msG#iopshn_k[PmBBO-1j
    >kav')73z,5Trn\@T,3DY[$VuX2R1vK<4}IW3!*uC&<EMAIL><\TJ^o{WCBs<@zE@!T\2Xj7T
    eTw7l\u'us\ln1{B3op^Y.UlOz[15x617vaLNJsWx2+{u$AHXVlA,Zw*$7wI773Gne\Dv[z-2lYG
    $*$oTpZ$+dI+<[F[um\i+Xp{I3!vgZU>UOxK2.PE7,Jz]E=R<,iH_7HRraC2$QH!EX-7>2s}D-9)
    Q_,IHGnW1U5Ut,*XugBzB@E*]eir#79w>W,RGiVg/(Orej<TYaZO-,xYkBvi^+Tor?:rF#r?*d3l
    A_HX}$$'xoC~Zm-vx[YBKo'HV<,3KT_!Q-!nK3XN7TY*ya+Kaj\3Z&D#AGUaoRY3Bj<VQ{=2ATav
    vrwO[<tH$wep+VAv2xKYv<X[UU7EzH$#wGI[DTVYO?-}Z5;1Gr<-Uo22v<*K+j7E5mxj+mOalmv}
    CKkQ?<;o@<E{[sW'~7{-_7lkT<?7,Ax=[ou;<}2Gm-ksj@ZREe],?HW_*G1sa$kuE$[NIoTz$~@Z
    {jpzvnY@V=l_$aoV5mIH,(ps>s$Hv5*iXxATT_/3-o[xpCT\zs-X7+WKV+[FEaK{!'+EusXo5R{2
    Nio1[rnH{~[BD~HZep;pIO,Z<n^Y37lE1\QCXAaB2l1Z}2[VCpv{,?o,Db@Q#s~8^{s#:*Ds!z3l
    p.vBj-_a=i~\V*2&*X,7kXrOE>Eo=T!<}D'Yx)oB=W1aJ+l;\$,[p?5cp^^rUO7~E{}$1;73oAT$
    |!1!sG3KIT$pD@<l7B>_W<s-mBVwZOaZ=I+\k[am7EY]7,$Rs-^eW.|Y@j}$Ye{UVIp;=+HMQjT[
    {{w2re<{OT++DGB^*}zm72Ru}T1a53(KDRm^KH]j$lQ+TrEtOV+DAEzup[E\o!JHPCJ{CT=mr\C*
    #&jW~<H$}w{>KkCej,eEmsnqGl-TowWD3l*^p${51AU[KI*]W*mrA'p1+a\pV~UHh,WJJ<>v=87;
    7Vo*?\i}1pV%?jk[_U,[CxJu>*_!xo{[R+>EZeR7lQju#]K{jOG#{+RG^o!T{_rR5^w~+OHbvwvV
    }kC^B!5Q]2AoOxI}71;~3sk=yIarHEGOZ;njGQ>,AX{XJ;'WoImwj27ja5XEsDY*\*Dw_o$?AsD<
    lY]Iv@E#IEn-32s3~ZIv]RZnwx@\u$>@IUR=lO+-;v)KG$TQ;WZ$3OVJpY$)oEm*o#UlaXA^-Y-!
    }C#1,RIwRw>pI{\k1_D7EjQn#_^iGOBon'r'Tr7Yh{5YC@jRwKAT{^-,Ea+u~}>ARyX]E!HG?kel
    >O~HpTDDa'3ABJ4ZQn#_xX?+or}Mk}]AN-X^Yz,pZ.@A+j[OC>BoDvgJs>CX=uD}pX{i7Z7Kx-IT
    \'o3{;QKB#kU>vp1.Q7~oe2UmQGUe:-o$*aeu<Y<>TJ[5{<'!}#CmXo5E,.z?IG,^?}xvOTMxvaj
    BR^s\vX7vn~ujzQ@}XD[D-=nujj^kliuCQ=?_BKexJpmooz1*<[;*-<J!YZkDHa~{z{5A_$!G51a
    sx27d[?KszET'zZYkcyn=BmC;JIpDZVAH@^W<@}K9o[j*DXo+#{AuGY\^'*~3A=>{BBAY)C_#nH\
    Y21#ww5DV'^K+Y1pQlo5~^rXxVV[olae#kQQ>!\Xe1|53CYUAu+l}ap7-1AmV>>F$?wu|xY{3$}n
    ^CJ,@H5;]2n]Zq7p_Wl{VJ)uC\;n]{l$1^;!_2>=oA1{7#~]e!B2'*wEnll,l=JE^uJ2BnEaG*3J
    onZ#nEkjw]Z^?p<C5]IQeJUY[Am'\YBn}oY$GZ3C<vaGnxre1{\T]15\TJ~YHr{]aaY6HYoC]{@W
    =~zsvuJ@1KZ!_mzHa$$lxsI{m5i'2+Z3Z&p<,rHz$^}@lZ2*C]XCK'Qv=uO{<?6\V]UTD)TwO'S5
    !{#D<enR_{?meU\nox+0zQe5/ljJ;ICW\_zOJz_jCi\@;RY$TCYmRCW!<GpK]r~TZ[Rl'U}[A&KA
    Q'!s<{eHOC@O<[.^n,YeUp2,]EJzM:4k>Ra*vB#noKZfDGm{xVAjnxXUoXa]E_=7-_iBk']}^o$^
    ua@=ow5EQRr*,p]-ql35vxIHoXzs!}HzJ\m~ExY*#[G~X?5BRQ+$<qHs]]7kYT{YD?wj2XrAWBI]
    <DE}CEv?CJmo_er?JBoR{7DvD#Y_W\>nnTW+@Ykz~{q@aCUda1\*HGaw7wp^!w-aEk>#d\^Aoz7H
    mD3O*072*2l*THo~]
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#e,oaiOR}!,7T<{+@L[e$-$DJlEw]vozN|.k9ys#P,,57<l'C&UY-<7c$ezKBAG;\<2Z,*ZO
    cX7+2U{){Ck;G$k\s{ErsSKwJUz^;}MJ'+H1vH50xzuR!{H2H_zo<{O^0[;ouXr}x}!Az!SmTA[v
    ZJ7DZA[]Jr=rv3<N2C@[3(*@W>bx_?=\<I}_'<uGrU=<=#A_Q{Rl5iVV~!=p\V?l?M}$sYn'[vXx
    uD-]-e^1_7'-E3Exkl5U}'~=!J|BC-Y-=D1QlW\C}zQ^u-+kV]r**A2D'+Rgp^B>Yp$nUEH<}C_2
    Z=p&W'^*HHTCFhM2T}^k=Vx[?=186Bm<Rm7#WQ2WC>j!#0v\EeX'EnW$11M[#C~]_z1xRZAR2(=#
    =j_Tn,(Tol~iD=KuB+EH=Q,w$J=cB$-3K-G_}5<jrevw=I]_pQBOv9jRxr7RO$QKpo|EPCQRUZsm
    ?<sTC[Es^{61WAV=#x*#He7rj%%r=G@@Hp=$1H1}_w?[5B@1,J_G~]XXr#=#vVonT!<i{12;&bbz
    rO-!T]BuD]ua7DmE7HZVtYKRvXX<~9'2Q]}nl5>5@plZ,RC-v^MIW.]X<B^M7<-;3$A#Aom>DHe!
    bCV-wX{!>9{Q{CZ=7AYJ+GPXQ+~C2+#I~K^2$1#Xx;CxeXsB<'^m]Ap+]w]V1k-s*YWd\5vY$kOp
    R2*#Ww>-HQm3vlR]+vulRGr<HoI^ajlpkY\[W=^U1w3Iel^o=?Q<yTIK2fmQl-I{7rqGZ5!>9Ck[
    #Ir#K'z?!!xRA.Iz=]cQnOuMCEQpjmw,Ic,sG]&mU*QClpE{Y[~^r~j'Gl~WD$~1;;H$GBmAHRij
    XWp7_2,{rXeexu7B>pw7oU,mOja}We]o~5wDDR*ffCvJe!C=1lW}1Vx1}rumn{7?I)~']=H$7OQ3
    a}vk,xIi-+\nXGV2eT?[Q3:!wvjEn12^YD'l#>X3[Yp{7@AXN,>x],e5X#$?$zep}geRoYe]1GG#
    Vn]qCHr5$3Q1V~-C@er773WV=m\@@5*W^@C~xRD+Iu*!o1%l3\pYm=J7XDwjGVz,\r7E5'ReIB1R
    UUr(LG+zGnYBTCZKHK=sn_VlYGK[7>=[J'ODEUUJC0I]*H'C\!v#A!#IKprGCWkHC~}kj[w1[=>A
    u_=O!V?pso+V5B^RJ*-=I^kEo}]j!]BA5G>1D@B2_J*n!E;rew?]i_}t;Diw*1]m5$I[pQ-Rl35w
    B'$'~sE1y^IXTv='D5vXW=l]~:V_z<lOT*lEJQ+Xe>$-3C?1,nr#-5E?,~<X}<D<2W15DD<$;G0B
    e?s)=_{2X'kavC>\RJ=-ImU+e+Jan5zp)^5,<Cae+\3H2,,}[wI1iB^KIRalI0o=kD'$Awi7wzJI
    r<H6#1;B=JE3m{>7)#_?CrV-^QJ,^U*J!DC^W5{mTGWQ=3DRXEGTpW{]2<Ye#DJZ!o(pHr;-x~!>
    D+jTaK['1ikVZ];;>*xj3\pe\HkAo=Ifnrk=JoQK8UeJ2!'nWpo#u,<G@xZI\UjO[>tI>Ewnp5r^
    T,aYl+Yt2a\HFDmEK(MDm@suIYx'{CaGV<r,pn7*%CW{C$_$T]w$1=H1IIw\HTYWxxAK^*WY1W=R
    xr>^*@A=TEQGnvB{{-V^XBIK[v3lZa,!!}V+Zpmxi+<D$UNL"'CY+7m3ZoEp[IpRx~a)NI<wrPhR
    ZQwWs;1?X^}=KJUekxe%jUUX-si7'v#O2=$IlluW;_R$uEDo">[,\U[iEh1^km{sE$\{DJe^8D^C
    XIU}^pR_KXrx}paZKZ$[mY/rQBxF2SuUYed-'UwxG,~eZZruYn5m}urR;v^_YsG^ZJ2H,lu*Av]j
    B\$ICp1G~1#Vn3Z-eYC]iO*RQ3\ju+@Rpw$wTYJ];Wn'EI3'#p1,R+CV3@?>rD^R?'#(%KrewOaC
    w{7Ks11-=7aZ{}@}x:YX}!sT!sz[CX5E{??n*z-<~+Anvs,z5psHsVvr>Y[D;l,#$Y'CV1_u2j,#
    $Dx-,~c8YG<G4UB-jkCKa9vQYY-l1QCac@zjlGnr7I,V.AnlDyg)'iD^OeG!-xxmJr3C1W1UH*k@
    =w<EpHKAKo\pe~;CZUj<k$-C,A}Dp}_@+nJXCkp7az$Q?RnWGQ=Vzk[xA5"GV{2i75<R#wlsKHX*
    e}KPxTGjFl>TJwUr*#SXO<O\+7-=KuuX-mw>Y*HYB$R{>p^{-Cr^!$xrH1Ib?+z'N:#TWI+eTloA
    aQall=V_{u.RkR[CJUxEn^\]-Br|m-J{-v#Gs**mNYk+16\E*weHlYr!Ae!xx+n55IO*JO)0lz$H
    n5,H0uYkn7rzGJHx+}^Z*v[iD\Z+1$a<{!Y3,3$meZ\i'YxVuH$s~@1>>lVKa>r^K7a[wlRX^G{\
    nU(3>X5OV753a~R@'#?w&~Si+jvvG+O,7I\KDus*@3,$Qw~_QQB$Xr_C#C\~GUoE<<^?<w&[JW;<
    lr^Q#x[Cusp!wn\Ic)O-K<tM+ar-H{D<lA'-E~riAGvp-Qmj]xRHV~@pRn^p3[saG_]o<+^I>e'j
    oJpD'nvJ<(DKJw,,,,}llRTp#,2x<7ap13=ow}L'[YRq?{mm=7^sEaDYEk_wgrYX@LeI_'7CU{]k
    wl>o[,K}e3+\@#j1a]Oev]]K@KwDI~<$nV-{*@\a3^{xu7'TZB{X{B$lK{@w}E2AEKI\~joz_aG]
    <1;8Z>[<io*$r1sTxJ-K9mTu2*A]~I_Gw-aT_#oD2-lXYXCs-L1!OC!^e2J73EYe[DaxZl=i~mTl
    _]]KrnAHJOgrzEZ-Cr{DrV]DIVozw}l9Y^$BUrR2{I7UkYrojC^V_+W[mRD2,evX9]_2sO[X!MP<
    HT2vI<QIZE,C#BRp\eDg[H+@AC[Orj@5(Y<UW#YT,@QC;moElIA1BmVe3Imerc7XG$*l[~BvC3es
    ITos3E,H}5g$5QoO*<jUX=~p^XaoUJwBA+<Lur1?H*nxGUs-+nsz:^y{p+CG<1e'x]wUR;C{[XCU
    Hj~wlu~wGjWP6;s-Ux?~e\kDp*1?nF-_J_!]21[V#{e7UG;}v'w13*rW}^_}p{Hp^{T_^eCp1]1O
    e'r{wlGD+p,_RB=RuUxTzYup+73'1k^^a@,;svC>\[m{>zqz+BmVa*ao,-aXQv=I;-llYGw{nxj=
    ,Z]>^VV?T^3jwno}Z}K+XZk|*?\~Qi7,!<7oZ{Z,lp~C#Qer;HC+Q-{,5^V+^]$GOBi~;A>BHomT
    II!X'KJXIWv-ji,[l=v*^K@$mVZu$EwIsQxEBYx}%DaVeX+x]uUKl$^]2PIu\G7n13,[~KU5,G!j
    QY7Y'2%XB!@n]>]uX}_\vZUTrDD}C3=I.R#lJZ}aa7*+YZV~wz5z{U<REeiH!n]7XJOB3xuaVf_H
    2z,5jBN~V3U+Hsv}Bax*~G,Baa5?s3uLZaU\uUn}3CiTGk>^v62eo\'DGex!<{T\]+asCOBIp_r#
    ]iUr^pOo_GN-xmK*_oUpi<{~r;Q_WBZ~H1+@Y$-vr1K@va?qX>sDlUD[qeCIQv?qIDTWXA!-}K!s
    c$-]>YzF.D^H]e*!YGZw'oT[1[Du5-O2vT}>DUe+Gpnx'+1k]}OGDkQ=G=rC<nA]AwY!oL'@J2LU
    11+O\\Objs='jXQI57kTmp\V_!>1nRpvkE+~[5Q1+*J@Vm-a~{HBsOD'Y$3p-e@XQZDC=EkVRppl
    V|;a-XIz3'<vKCR-H<RQZGYlT=!>>[DK2\\\w-z~KIWskv5!mT'z\j1x~>V7vnJD*IQUsk}OR{7s
    +#rTm[$iQ#_iAKViWrVUO3w\n7^j1DGi_,l1~!n5mE^a1m,Aj,3VZ\HEuV#VE{&|Zr?J*&?AuD$T
    uH#_^\J\@=;EB@&wz-e75DjqK+VG%RZ<]$U\oXBA#Ye-\3erIlj>nVu-o\~-\T]CxX*<*%5[Xup_
    VUIW+]aVu_2D*;,!OHU>\VpVVH>UI2,p=OrvuEo=+5ysU_aij*i&wV_$77U<IT,n>7W<3<XK-EVx
    AO=pYU7~J\3TUa_i5-T1ZBvDDYZTI<5v==I$x]pXI7l-W$p*NU\Qu>e+DDsHKX5@U=rAzosTCQJp
    Qs]^njioCv1_Zva2OC!JHKY]w~5XWCe-QIIxmB77G+O\3Q-[1~D>VA'#eG}$,:?A{]a_GelBjT3+
    Z2VEnBsCx*m]vH$UDJVEB2H=;a3U1I1vT1QDiD!'_on,HRj]^^J5\Wza_\^,?]}a,I{EuxEOis-a
    DWzQ+T+,ZB1HTKH$Y^<}GWZjU'wjwuG_,ZOVAW<$Bjux.3QIOdjpDalTj#'{xeUj\Yo}oGtz~@w'
    UjQ.pi2?x~;]GlWH3ECHD>7K_,!>~5KBRw<1BAjj3]ROEZ@G8jEI=fjBrOEX'QV>R'-[XTrAa]57
    o3KD_m/i\>J7IY\rR<YyDnoA71eD29_X^]-D+v_Z}$*HYn&c7*2[a'V=$='O0D{o*p4"\v{RGUzk
    \nT\UVi7HrJau\#o>z{nS}77p|OuK+@nV[Vi
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#9K1+A2_Q}-En\,sKExj_Q$DJlEw]v7;P|.k9y]V}a,mU*<lzCa+>u(FP+rI$<a3a6<7x;Q+
    OJ<|OZz%lwJ#*+[[o_n=['CW!Ul$HUl=U7BvVO#$skUZ]R3TJh_p?~E.'H~K1\Vm|8Bi,B'3Wop?
    ]H?CAV@]=mnxrnT[xVI5,ZT*;[keZkwsQ'_ri}_UA^S<5^$@G-=}>*sI5k}jB]$+wYK'#sH?{T]m
    oVQoV#zoHBH$r}@!5z1G)3z[OBn<JBu*mWTo5R$oO\x#B*]D2F*$HO,,?=^Y]D4aGnZ*G!K1_GZY
    -~;Rnn<I?Br?jrp{XU#/".hQwQA/&WCQ77B'i5[DYBB#xc*ZDE2r$J{,{xOA}_^e^oIZGWVu,rIZ
    x^O$n#BH-B+{2?*?xv]GQ$jCkkZ=XuG]x]oGCwzu1^@xKlsQxw@wYJ}F[}oTulR5vJ{Z&I><aBjZ
    5yRQn3nCG$1!CwvXu}=?m;-nA[YJEJp0jVxiGV,Gow_I'Ez7e#B2xeGV12{,7_BnTe}}'msWQuV{
    V5?ZjAV<(Z'J!D1nAB@HJ7(Gz~jy{wru2]AV->B5V3J*#_s'UU@U[RD}e;=-j_GWEDQ@ioD3Y\!-
    'Xjsr}wYT'vb/^>CmQuaTB<{#~U5~2C<[[jiZUe@!sCv[5!72+IkB;}l@\j5zU*?eE?+{5>p;0Dm
    B<^<1YmRB'HCZ=_K_DFZH{kxr\W;]eu]%l?+5VKAKz!<G,$k-\-a^li@3}3OEvW3~1]I!XQu#,aA
    oQ2CuY$^+Pl+7l[GXwOD\iUrOuRA7JD*QO{oK{D_e!tln{uTv3D!77>)*T!?kTVO@sG[37*-oZa{
    e^}XUeuC<5r1C37j^_e@7T-<GCxik=C!_RG=omOTC_C$}SY@7iYjr>(%!v_~!E!\^uV-$T-~R+{\
    nwej'#JkB,~-5xD~CHG;$GX5+*Rz0<D{['H~AB*BQUp[Je[wr1A3@l#T^=e]?=XlV!_@Q"qY//c=
    z3n!]#\l-<K)drV$o9Ho-Z#*w<ereeDi}kk5e~X]l,}2\ojDZWH'.WxDl?*e?V7pi(THjT#1,Yo\
    *DDwzGql3=woU77%oa_xs;<TVZVwT7uauUV{>1xu@G$UI-lCF^2vQZaXJ!7zXT<{#X^5a'_AYz^#
    3'?5kQeIl($m{ohBX4Z{~O#}3^#7;p*>p\fv[2vYprYjriCpDIa5J$*17aJ'1Q@O~np-wTzRR*VJ
    _^C=D+?7Zjjx)xz~mO^1']W{<kYB~=*?E#{jmGH=>BxQW|sAE]m-OG_+>vQr_ZQn~U9*D1uyuj<Y
    $#*_l+@n2oo{o9N@I,1aOR+$D]~07}!T,WjBTE,TE8QUAo3'zsZGxsl<<<I?{$I*7E|,[TY9ZlZx
    u*$O{>s2=!!DAT\Bf~\>v9zKCBH],XnrqJ'ks{nrZu*'!sYZuxRE2kj22I?nmT\@I\<{p\12-J-m
    ]}!1k\BGKj57>oO+K}=*Vn5=3*sW=6ORkuX=vCZnAv$W2]Rj3k,GJT>^k,ss[@5xXHk5,'UDJx'W
    }kIr5;lWnzvW-k'uno2eB7[h}vav=iAmiz7ICUCZkV$][sTTl2j;>{>3gwsR;\@@^jBB@^~v@eW<
    ,\nAxI>{]=@!RGAs!@73{s?V*[r27'#m#_{{!4!B_~BUwKjgQK[_^2X#H[X3:<>1[Di-Z,em1Zo-
    ;^?*+yY]J3~a$klM^{7Df,*1~I}\KDA\OoIRe_B#='OB-OKCw_vOxxw-'N1l!C72vwqz?&{l2CkI
    moiwBm*][H(K$}ii^2x=3n+lJQl]^{K7$rUD2uVIJ]eHT\V'QXC}n*A3E3'AaszrYsQ61G,u<Gsz
    zk{>pBjjAHH}~+rwI!'>l2]x_jdT]2[%1@_VLo<;'?sa}+aeZ'!Dj!'E->]V{.@H3n|mYs{j-!>*
    RrA>G>p,5+JJA~xPw7+_<{Q'j2CX_$2OQTD\H$>!zQCHOCI<D5ZG"&WQl!lEaX]z^;,^_jo$_CVD
    #DQ=ljzpxHFAnw+1_UUj7mE~ssRnv#\3Y{kIDi<7>WDBpI}Aw$,l_D+$eT<lXoz=*VV1AY^%lMal
    X{:IAO{{suo@5Tw,e+7RG33NfskAo;$}-l__J\bS\Elvr_\_
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#];G?z\Hk;BzIk-OQx5z3AC~BrwD^3h#N|.k9"p77w>$Us<\;<U+(Q?3{u]_nGTM)Q2WjJ]J
    ]Q-Cs~{BBY6+1$'Q,AmOoXA31,['>Kj^iY\|_j-=J+zr7'D]#$GOT$]G=k2}1e1['@1>iC1TVE$C
    Dx*{vKEA|;,~\Iw>-.mrQK'vvrS\HXa<$I[>{$2o<{O3LtKBG$n[Bl{a7>[pwmUHBi{<CEX94D}r
    Z^*\}eBzDSW'G1K5W$Q},r^p}$&*n\;rzW7wTVlAAl+{}AeA1kBCiTQ~aA\D\A{jYGkIZ-HGU+\3
    VI@ipEo>};\osUH[egDJx]}'Q<2,~$zQR^$c!QA$<]]pzvmD!5Al?Uelt[I+}uOCu4'~W=raxa}\
    Zx'3uU;olA)x#m*TxZJJO<pQ'#}VV?B^;O&1W[!^#_rYC[DYz+#b3=1YmTZV/2>s=[;^+61G#a#w
    uwVI7?5~Y3!+urOOA,~smRsu^s'lz@:r;EEn$T!,BEr3}}xfCB*JTaY1dDA>2>*mvJr^*l'#mlm3
    r3sJI<Xep$e2xb\;^?}nYUv-dlIB{@<>JiOJ[mz#'qK*b3TV^Z^Akwa[XJCi^GzK^EuW^C/HloOd
    '7w[j}HRjv7DDKQEV5zkXO$ppOZE|Sz)Hx[I$'su=],~HAa@ID{Im_^7D~3xJ]t5ZzD!I>=vms}9
    vW_iN,gGz>a6*RACX[Cl[XZk>=l}8ok}mP{=Z!5?d:#R;I3EZ1*!1!{IR2u<AJ_,J]*UElc=_{}7
    KKA50Z&pwX3o>3[R\~eqP?pkJZYbXB2BtWr}ll5k-Ilw}j?*{jY[!Br7[<zv]rTHe$K7wB_*C<^w
    e\ZCa,Y<Z~U;-z}\u_;}U},{{p]EadkQA=$/?rGv3CkUZsG?Yam11nI]Vr@{nQ1Au{1aao,'pQCj
    Qa=iuA=>/$?AIrR7o+YXu3{37xAxO08#D1]B+-m6d.xIi$<$D-C^A]B_]!r!HE~YrKxDk<!5@-=^
    x!~^77w_(,~Y]<o1m\@Ii|kUJ!zn~l{]\X~R]VlmoD7]EXQ@sjZOza]-vuCB#aK-l^ie2-<}<O~$
    v-uBEKs=kVve5+O{maN,;XoNd!B^@73WQ=@@E7~-Vpp\[E*rG#Xs*[}p(>_7,7BX~+YYr-$jKQO}
    7Bzs!nTBVr=<Q01CoZ^1jBB;^'vnjVlvAWh1T\WPQp>WvR[l#B,@p~a_^OYA-E13*=_~X]KA!leQ
    CpG7o1)7UCD'uBe?<a2GK]koX+rlu'Ez,Be73]7l}]?Rz=n=RXU=(1_Zw*GV@CJ=@1Hv+l3uXBIu
    HN=<Q\$p[^1k3{'E]p>5D@YZ}Y'7>\o~2-A=em)Bp1={+u;eK@,[>KBVs$KKX+*Ep<,7Um\b_V*I
    -=iTT1u76@]aO_Xw;$nm5DnejxxK]Cs7}?5kJoreWIk2KC22whC<[T/*?-Y#-YruxBnVQ$X1HBrQ
    C;WZe[~o'ak<,ZWvn{U~1YXW^TAfE->WUlw},E?LIDO@!o\Wz~e@A]~wUjKOOhvp*O\D*@Hal{Uv
    ]@UoEBe2~}vaxHQI*-;OA2DD#Waa-O.wYXB7sjYQ3j>Rx$jL?elun'#G1~7n$iTYeoEwi${Gioz?
    YH=[-$e#a]nWT\}QY~_CA}JA\x\e<Dk+xT!jzW5*?=luD1GA8,JvIoZDB=oo?'<~x7TKl_k'7D+!
    ?guER~;qJno[Y?!7qEAe]cY-<W~I}-z-}~lBpw03[eIW>7o3nj_|t&r+XT]"DdsZG[\DYGVXpZm+
    Ru1qu*u?5nsrKHAwO1O1g1TuJWO\'0va=Y~[\Jw{[R)AoDA{$TzEn'O|e[1v,WWH}r-}I<@RBC^D
    G<XmBkXXsuKZn7rwK}\31ODjo#_5wT^vW-;w1}=CWr=pe5H!$-;kC,VU*Hul*u=V>B[so73,J_WY
    G7eol<WCWwAuWQ,#.UX-;o3D\<onOE{aE1,2],GJRI_^a^iJ1;T$jI*C]^,IUKUT#GoHJiGQ$l3H
    RTsRnX-an[T5G*A+VMW+aAoK*?W15D^,Ce\-nA>QwuXBjAwrIJj!RB@8OZ{GeB-H'ewWW*e*K=;R
    Y~\kp"}ZpIr#A?x>IZsOo'3Ge]?w!kl_D\~$1IveRoXX>C(?sO^;+p+ccC[j7ql#X}}TjmYKvOGA
    EK7EYUHT[;D1OQ'HOT*~}uqp3^s^r1B>lB^JpB7w}E\dnTK>k+XeUjE#<jEoEmC17['kAe~XE#E@
    (yU>^TO$pW(x.}5n-GTJXp$r5r,3->aCeZx{<c#>^^CRw?Hhe-OVF-nx{ri*1@O-^xHE~2x;R3Q{
    $xa{@I~wE;7nJ#w}[\zz}9zr{}U[RaR2Pl?K[LH^WKQ^oA
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
module `IP_MODULE_NAME(efx_dphy_bidir_rx) #(
    parameter tLPX_NS = 50,
    parameter tCLK_TERM_EN_NS = 38,
    parameter tD_TERM_EN_NS = 35,
    parameter tHS_SETTLE_NS = 85,
    parameter tHS_PREPARE_ZERO_NS = 145,
    parameter HS_BYTECLK_MHZ = 187,
    parameter CLOCK_FREQ_MHZ = 100,
    parameter NUM_DATA_LANE = 4,
    parameter ENABLE_USER_DESKEWCAL = 0,
    parameter DPHY_CLOCK_MODE = "Continuous",
    parameter ENABLE_BIDIR = 1,
    parameter tLP_EXIT_NS = 100,
    parameter BTA_TIMEOUT_NS = 100000,
    parameter tHS_PREPARE_NS = 40,
    parameter tWAKEUP_NS = 1000,
    parameter tHS_EXIT_NS = 100,
    parameter tHS_ZERO_NS = 105,
    parameter tHS_TRAIL_NS = 60,
    parameter RXSTOPSTATE_L2H_DLY = 0
)(
    input logic        reset_n,
    input logic        clk,
    input logic        reset_byte_HS_n,
    input logic        clk_byte_HS,
    input logic        Rx_LP_CLK_P,
    input logic        Rx_LP_CLK_N,
    output logic       Rx_HS_enable_C,
    output logic       LVDS_termen_C,
    output logic       RxUlpsClkNot,  
    output logic       RxUlpsActiveClkNot,
    input  logic [NUM_DATA_LANE-1:0]      Rx_LP_D_P,
    input  logic [NUM_DATA_LANE-1:0]      Rx_LP_D_N,
    input logic  [7:0]                    Rx_HS_D_0,
    input logic  [7:0]                    Rx_HS_D_1,
    input logic  [7:0]                    Rx_HS_D_2,
    input logic  [7:0]                    Rx_HS_D_3,
    input logic  [7:0]                    Rx_HS_D_4,
    input logic  [7:0]                    Rx_HS_D_5,
    input logic  [7:0]                    Rx_HS_D_6,
    input logic  [7:0]                    Rx_HS_D_7,
    output logic [NUM_DATA_LANE-1:0]      Rx_HS_enable_D,
    output logic [NUM_DATA_LANE-1:0]      LVDS_termen_D,
    output logic [NUM_DATA_LANE-1:0]      fifo_rd_enable,
    input  logic [NUM_DATA_LANE-1:0]      fifo_rd_empty,
    output logic [NUM_DATA_LANE-1:0]      DLY_enable_D,
    output logic [NUM_DATA_LANE-1:0]      DLY_inc_D,
    input  logic [NUM_DATA_LANE-1:0]      u_dly_enable_D, 
    input  logic [NUM_DATA_LANE-1:0]      u_dly_inc_D, 
    output logic [NUM_DATA_LANE-1:0]      RxUlpsEsc,
    output logic [NUM_DATA_LANE-1:0]      RxUlpsActiveNot,
    output logic [NUM_DATA_LANE-1:0]      RxLPDTEsc,
    output logic [NUM_DATA_LANE-1:0]      RxValidEsc,
	output logic [7:0]                    RxDataEsc_0,
	output logic [7:0]                    RxDataEsc_1,
	output logic [7:0]                    RxDataEsc_2,
	output logic [7:0]                    RxDataEsc_3,
	output logic [7:0]                    RxDataEsc_4,
	output logic [7:0]                    RxDataEsc_5,
	output logic [7:0]                    RxDataEsc_6,
	output logic [7:0]                    RxDataEsc_7,
    output logic [3:0]                    RxTriggerEsc,
    output logic [NUM_DATA_LANE-1:0]      RxErrEsc,
    output logic [NUM_DATA_LANE-1:0]      RxErrControl,
    output logic [NUM_DATA_LANE-1:0]      RxErrSotSyncHS,
	output logic [7:0]                    RxDataHS_0,
	output logic [7:0]                    RxDataHS_1,
	output logic [7:0]                    RxDataHS_2,
	output logic [7:0]                    RxDataHS_3,
	output logic [7:0]                    RxDataHS_4,
	output logic [7:0]                    RxDataHS_5,
	output logic [7:0]                    RxDataHS_6,
	output logic [7:0]                    RxDataHS_7,
    output logic [NUM_DATA_LANE-1:0]      RxValidHS,
    output logic [NUM_DATA_LANE-1:0]      RxActiveHS,
    output logic [NUM_DATA_LANE-1:0]      RxSyncHS,
    output logic [NUM_DATA_LANE-1:0]      RxSkewCalHS,
    output logic [NUM_DATA_LANE-1:0]      RxStopState,
    output logic       Tx_LP_D_P,
    output logic       Tx_LP_D_P_OE,
    output logic       Tx_LP_D_N,
    output logic       Tx_LP_D_N_OE,
    input  logic       TxRequestEsc,
    input  logic [3:0] TxTriggerEsc, 
    input  logic       TxUlpsEsc,
    input  logic       TxUlpsExit,
    input  logic       TxLpdtEsc,
    input  logic [7:0] TxDataEsc,
    input  logic       TxValidEsc,
    output logic       TxReadyEsc,
    output logic       TxStopState,
    output logic       TxUlpsActiveNot,
    input  logic       TurnRequest,
    output logic       TurnRequest_done,
    output logic       turnaround_timeout
);
genvar i;
//pragma protect
//pragma protect begin
`protected

    MTI!#uE-}II_Z<sJ*U1JrD;DnGT3x5}D~T5Z?};-5*Dkrr3\XvXzm+*o$zXnJp?*Q-r'uQx![bwo
    }E&w$C^[;W{uekJuQ2Qe.Q*X!rUTee-Vz\_iZX'Y[3,s{As=zofe]k^}\WBrG}5Ta{*!Qv2f!<7u
    a'U$zmV_-rI?Z<@ro>V]u=rl^nJs]3Yx?OkoI@553nEm>sX#w72@zj+s|k\mXv?-nAn2[hO3xU2O
    ;ur!\EZ=}^T$o2BWCWl\#[l~rx:l_Ox-H*>V:m=Da1uv!!11lkC!A->Ym<pr~'sw;=m,sIU21q}J
    =OeKQ7zGX$_Jn3I@vT=53]1r@OQGeX$m;KvmBv1UDDReEG$pV2D5inJYQ}H7}2kBK?sTXQWl#[^O
    !+mEvm-^AUxe!^ms]lmCJ3u-VI0UDip[>>p3=3AEOwRujiGRr~~=mxv|t?='kVzB2z+V?]+zTlle
    kC<=Rgw}#jYn]~#E7T2p}u^#!*N.2{H!iUmw2]wvjI3{3osT]DpuC{='ieajX$aV~n'oYWw{wUUO
    ]z_C&2_[Jr;eDr\$-lx^3=XJJY7r]ziJT*]ZX>XuCQe~elE>Y6z@3nnUT1QBz@05A<vlk!_n]Y*e
    R;#i1;?mOaU:G7Q<[Qm\ZO[]e*+C:m7=XKp_sEI-kUAAQWa5{k+URUOn[Unn1BJUn{}[*C513~rE
    ~Az@{WH'#eH5G;v{$.u$2UY~UB0*>HV!+]RB@WjUa5,^H@^w,xo\}+2@v;#Xp>CKBToxZ!'!+]$p
    ?U'e*!ldhsB~l^Rxj6'>n<l^$lBKYGvzB1+5\zW]~E>Ia7:keUTBl=VOzY7_O<+{&pH\33sEGT=K
    v!hG3VCv~]=>+x^$+JQUrT$YK1<'nnIqV-'Dv'KY,2E+:CC,}Z7AAVkDG,7Br;O^5-A<oe{+~?_x
    Xs$[><OV^jjZDpi>Y7nnrRrpIrDXV1*GReelX$s^'i'p<+=jVqG.\1T*[>BG}nAe=uoO7nR{fCiW
    r5w2s>>=1'l$R*FGzE7\THGK57]Cpes-ROHql#smL\p\?.e3AC*Z[un\-Bn]v_-Tmsi1\=,]n1l\
    7~^z#+elz[KswJz7>7iRaCRYXXOwv-S7kE3eO,uzs'-[2CYrDi2{v#**3UQH]jHXTB2s\i\l2*{|
    D}<#?D*Or{-V&,!ej%X'#TCz3?.K1HOU}#,?_7r;5X,]}!}R!s1I@@Z^K2;=]z~CzG$RxX!{,kIg
    vs$l,T1\T}-,^;!=mX>#wa\RU<<?*GmH]D]+1W3~^<7eBJY;<nz^#$'}V]XI^7#kARx5'*pimGVw
    vw_=vRm^rUD]_~QJ6iA1]-Te[!'0{R>Z;[2UDIA^#r\@}1YkO*[jvkx7E~~s<G@EqO3Y]eo}ixEV
    CxS!Qs~/QX\w5=?n3zQx^<{[Bm\IC-~}E{>{zv<OplV#:S~Vi~B_$1;r=3mBi}n+jii=a$3nVGja
    {?rHrBzk33-'^D}2{V2v?<4e\X~6\WHABRG3DUBo'[iJ3UYua,EVB@>[Ye'G&IAW?'23{C*VB*^{
    [B^W2]kRQ,Gr'~U3C>+2!GXQ@x_=^}O+[4yD"9K}[5kO,'Xl>J?^mw#j$,yY@e@M\{xQ~VD5"^-*
    #o'Bu[z=slGo!XX_!]YvE7#-Zo7=Dn=WBKjk~O*K;EzjuGv[25mO[,p;Gl5Oa<]AzZQ-x,-QVNY;
    nEo@\>9er#EH1+s[wjO*Ze,UeoQ3pX5Z-,o*}vJOBIoN[3'VxTDHv<(TTQOf_XR=,en'>U*O7ru@
    JrpJL!nREHC2QJ}Z$uVHH$2@rEwA7,EplzlmkC;!lnHOUXI\aYFX$Q!D$2<#OH^&*ez_UpvGGzom
    Gr3sA*Eo^lY,;r]GBR}Wo^R{=-lZC_1[G+3!OuVz@7zBr'6le<jp;p$\GzT=o7_K=~Oqs%z}jlGv
    GQ[X<#PlzXk1{Ks_?V2fP,@wZK>j7^+r?a$ooy<skw4E17KR\\Zzm7@uzB>>*-GQuG^|Q<!^oOQ~
    ]}wu3UmV>[I[[#3{'HvlBKwj'^I<F;,u]<_#Yu\X@IsG<nO'{H*m1nEYi{>7ECOr!VI5w9u_nzJO
    1Wh7iYO-]{n\Pq(EG7oIB#G:pZaEC,?ZxHATzoYsG>'D=[D_Bkp;5[\V[(^{-#[Vs=@5[m^l!5([
    f*n>7a7sZjG+{VZXEmBJ}u9whW$<@$$OwrsTY'nXK'WH>O=R_-zswl]'$[n^zIu;Y$-=o?{EYZX=
    -epZ-E<YWW,i[-*#e7f~wGrg>xx~F"Vu[!CUu^[3Z>eZ}'%}F3Y^l)o~J}Ct(pKHY&l%l*,kjmZ+
    ;vY7TjXpO~BisiAuH>Y<R#,#w->wYZ,j}eu_-^m*ax}^Ei<]pTJO]=R#elk@k'z~:?-75!jv>+a+
    keG@Y?DJkx#v\NI@O+n{l}e<_mt^p3TCkQp'^uK*oZRBABoGaUAH_=aej@>[33@3Uw~CWmuwUpo\
    ;Cv1_=Q$~uDmHRY;H!wA_3,Biw!DV5!7zl^,EDG}>+G}R>kCX_@KpW=ErYJko!vp{~=EQkZ\$V,&
    ^K2eG\av(D=okw'Q=k*K>-l-;!7C@>E<3Nj[}K^Q37K,za+^*\3'3@*I=!9U>w-rA1K^[jxHe!RE
    Dks>r-ZM=HD{ZX\TOU^H_YqaT-xfn=CEKj1^3>*^Gw>_(UCalECvw3Te,_#^]=llvz}o1}~!ErAC
    2BAO^H6{s+rAri@a,kYQUDi{VAQ*DG'I;C<aj*33EjWZsBk,jw2+<$,};Wxw-{>I2Vm-pYY\TB<e
    RpUeoUkW{<eP{jIUX{};2>UX1WYXxUT[~Br]oKVv\IX'JsuD\snv\T!m[++VZ7-?h~V!=ue{?3Tj
    Ev2KzOX[;/O1T_Q,sAxi]{uT5EP}!YaT-$i2oZ-#BiXr!wpl^-@NBg^V2\bC;C$CuHw'o{+pN][p
    12=[JZ^#pCl;K}DApEQiJJLHw\\wBD=W<n~k-~3#TxX7}DXiVK]*AZ3I1a7>e~axeY*v]pzHaKK#
    5{z*[+zz_[nORQ,M?YD^X}BJ,*?KV[[zTE^p<[a!NvszpOZ=_4?aB3=HzmG3BVIDUzW&~5,jhoO{
    rHelHZokU]jlz;G=+p7XI>suQI55AAVS'}O2Voa][?1[kaO?#t_\=7f*iK,xJA~$^#TTvZYF7=i;
    5n>'}V5{EO*Xk*x@<''HmaTp_2J'IpxuV3V<IT+XrD+<a[E}a*p'D^iRC+AKc7EXJQpQ^7}$Hx3J
    *[#Rx_>O+1T\RLi^};@G?UUXeWzRCiK^UECKOr;I>_*,=z}ZTCEW3zd|}2A56<+7XVvA@9)!n=x]
    TD[l27~![u$qxvHXk+~r.}7kp=W-k=}n2[Ql5B@peA[z3IZ-'<[DO=?~XYEo#WzQl#Q=ps-Ye8TC
    VDIswoGOG-lW^W,]@x_H>[lD1Ua$j'?'oWNnXUKW}zC;E$is=Xo9-p,5v[u?,U$BURQ5ev?{+DUn
    Ib#j;_pka*l$C$p{<BWz*5pXbGvZYRaQp1!,\TaH=*n~~k5@XSEoBt$\!'*<!YnXK[Ge3?Y[]_Dp
    aGTG*xB$As]u\r#C<D-nj$']l@zYj]j<,}5w1RUs''_-21~OzuQ\I?$Y\lLl,2Q1*9!j[~Wa[iEO
    Tw=$;HPj=asO@~kzCwn(\$rK^9DuKrT5<{RV[DnjOJ2}Ralzj@1J!z^Jz#B~YGN_FOi3[,\aIXA[
    ~[!~V~BWR:s+lX,R!#]e*YYz'2>7}3iGnOsv#GnV5E+HnlUY5}g5.a>{I@o[llC!!P:uV{r?peC6
    $a-YiAT\l<D}Yaw<SxAA=D1$COXA]|W]iWlXpiCs^V+sT3C^DJXUpJx*#ZU}uJ?U+^41+liK7p{c
    9i1*X122n~O5spWD7+,maV@C_e1sw<l{xaDjU\KmR&VH3<B!]OA7XJOB~p5K7AI)Ys(vWHORi,[^
    H[lO\U[xJG{GTD?t
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
module `IP_MODULE_NAME(efx_dphy_bidir_tx) #(
    parameter tLPX_NS = 50,
    parameter tLP_EXIT_NS = 100,
    parameter BTA_TIMEOUT_NS = 100000,
    parameter tD_TERM_EN_NS = 35, 
    parameter tHS_PREPARE_ZERO_NS = 145, 
    parameter tCLK_ZERO_NS = 262,
    parameter tCLK_TRAIL_NS = 60,
    parameter tCLK_PRE_NS = 10,
    parameter tCLK_POST_NS = 60,
    parameter tCLK_PREPARE_NS = 38,
    parameter tHS_PREPARE_NS = 40,
    parameter tWAKEUP_NS = 1000,
    parameter tHS_EXIT_NS = 100,
    parameter tHS_ZERO_NS = 105,
    parameter tHS_TRAIL_NS = 60,
    parameter HS_BYTECLK_MHZ = 187,
    parameter CLOCK_FREQ_MHZ = 100,
    parameter NUM_DATA_LANE = 4,
    parameter ENABLE_BIDIR = 1,
    parameter DPHY_CLOCK_MODE = "Continuous"
)(
    input  logic       clk,
    input  logic       reset_n,
    input  logic       clk_byte_HS, 
	input  logic       reset_byte_HS_n,
	output logic       Tx_LP_CLK_P,
	output logic       Tx_LP_CLK_P_OE,
	output logic       Tx_LP_CLK_N,
	output logic       Tx_LP_CLK_N_OE,
	output logic       Tx_HS_enable_C,
	input  logic       TxRequestHSc,
	output logic [7:0] Tx_HS_C,
    output  logic      TxReadyHSc,
	input  logic       TxUlpsClk,   
	input  logic       TxUlpsExitClk,   
	output logic       TxUlpsActiveClkNot,
	output logic       TxStopStateC,
    output logic [NUM_DATA_LANE-1:0]      Tx_LP_D_P,
    output logic [NUM_DATA_LANE-1:0]      Tx_LP_D_P_OE,
    output logic [NUM_DATA_LANE-1:0]      Tx_LP_D_N,
    output logic [NUM_DATA_LANE-1:0]      Tx_LP_D_N_OE,
	output logic [7:0]                    Tx_HS_D_0,
	output logic [7:0]                    Tx_HS_D_1,
	output logic [7:0]                    Tx_HS_D_2,
	output logic [7:0]                    Tx_HS_D_3,
	output logic [7:0]                    Tx_HS_D_4,
	output logic [7:0]                    Tx_HS_D_5,
	output logic [7:0]                    Tx_HS_D_6,
	output logic [7:0]                    Tx_HS_D_7,
    output logic [NUM_DATA_LANE-1:0]      Tx_HS_enable_D,
    input  logic                          Rx_LP_D_P,
    input  logic                          Rx_LP_D_N,
    input  logic [NUM_DATA_LANE-1:0]      TxRequestHS,
	input  logic [7:0]                    TxDataHS_0,
	input  logic [7:0]                    TxDataHS_1,
	input  logic [7:0]                    TxDataHS_2,
	input  logic [7:0]                    TxDataHS_3,
	input  logic [7:0]                    TxDataHS_4,
	input  logic [7:0]                    TxDataHS_5,
	input  logic [7:0]                    TxDataHS_6,
	input  logic [7:0]                    TxDataHS_7,
    output logic [NUM_DATA_LANE-1:0]      TxReadyHS,
    input  logic [NUM_DATA_LANE-1:0]      TxSkewCalHS,
    input  logic [NUM_DATA_LANE-1:0]      TxRequestEsc, 
    input  logic [3:0]                    TxTriggerEsc, 
    output logic [NUM_DATA_LANE-1:0]      TxStopStateD,
    input  logic [NUM_DATA_LANE-1:0]      TxUlpsExit,   
    output logic [NUM_DATA_LANE-1:0]      TxUlpsActiveNot,
    input  logic [NUM_DATA_LANE-1:0]      TxUlpsEsc,   
    input  logic [NUM_DATA_LANE-1:0]      TxLpdtEsc,
    input  logic [NUM_DATA_LANE-1:0]      TxValidEsc,
	input  logic [7:0]                    TxDataEsc_0,
	input  logic [7:0]                    TxDataEsc_1,
	input  logic [7:0]                    TxDataEsc_2,
	input  logic [7:0]                    TxDataEsc_3,
	input  logic [7:0]                    TxDataEsc_4,
	input  logic [7:0]                    TxDataEsc_5,
	input  logic [7:0]                    TxDataEsc_6,
	input  logic [7:0]                    TxDataEsc_7,
    output logic [NUM_DATA_LANE-1:0]      TxReadyEsc,
    input  logic       TurnRequest,
    output logic       TurnRequest_done,
    output logic       turnaround_timeout,
    output logic       RxUlpsEsc,
    output logic       RxUlpsActiveNot,
    output logic       RxLPDTEsc,
    output logic [7:0] RxDataEsc,
    output logic       RxValidEsc,
    output logic [3:0] RxTriggerEsc,
    output logic       RxStopState,
    output logic       ErrEsc,
    output logic       ErrControl
);
genvar i;
//pragma protect
//pragma protect begin
`protected

    MTI!#i*jKlAuT;ARDfp1?+ep1Eor-rPPBeGY73[>N|*/\b^IIv=7Gz:'B-jf$u~3c-TW[[K3Q=?Z
    ;u$U@7Q-Xj@;$O7^is']XpmpzD!]U?>Yrb!}+ov>wxG#d'IG7,mp,E}\j;1[U{Q-G5D*#VpU;Ns{
    r{ZwBI'-'!}e-e;l1!7C#$n'X_'[}^Omp}r}A;lBKYYIr@\w*5uRo-KDmzEAOQz^eJyy@sT[x!s5
    ZQYo+xA<Hsx[Yj~k$\1\j}-QWjRpnjaQSi=;7QlTezE-5-,R-xZCUH+Q+J{p\VzNEe\I%kU$o^2B
    UERe}?jzAz?\mYu;T3Hm7QY2}+I,':lvH;i>ll}TBA=>{#5aI$T15*5<@r{e{~57j#v\?DB&Dv@o
    =n_-o@m~Gzr}OvI1\-}lviIir"E;=_$uT!oC+75kvB1,l]m<XH2+sRD1!\u<O3^={=$'k_M,$'AX
    \B1sG@a@e-x'<'<+6QU>lN9'ekA'RY-wG_jRC2,<,CW+},zY>I@a1BV![i2_TlQ!>bT${2EbkHzp
    }~mI^nB5f<E{GQYXVp5Bi[V$IN<r#A[rHw]<+zpKE__;CxzIE+\a!I)WT\KZEf/g5J9O@3ZvosjR
    Cva(uo1@omV7XG,ZVrC@7r<_7}QOGe^#KUU-;_Y'>D@2*Coi'GQz1pA}_|{r,t[ll_1HJvv^-5EZ
    @unUrwo3a1^u[OI,'Wq9>YIo[e_E9"N[,X?jwoJ,>w^_ZK'AD{!v'ix;\mGRl>D@U<Tp<I^g*2Ta
    aoH=+'u@165W-ZmSk7k!Ws2A$-'^Z1DCl}5CTlV~Omro>BxiI+A2V+5E7--Z,a]<@Rk7ral<?+DR
    oaQTFT7]IZ7*{$_5v_#'7YwD-]xZ2j][!YWm=xj2*hknRE.2${_YZ*<Q?=vW_T-HQTj_n[Cek>I[
    TaXqrsua'~*ZE~{]5eRD+wpx+7w}}]v3]+CBx:T1Q2oj$WDvl\$w<vG!YlzQIBoCr?Wrp?>Tws#-
    }$Q!GX-]IxxRZC7uH@H[aD#-!TI{_<aDQ<xB}+a'Kn~CKC3Y2;XlnsCWB]-C!5>=_<$CUEk5Il=z
    Kwj*Q7vIe?KXskjNQi[}'e${I7-aEw+_$?$*ezx'vV{1^Oo=S1u']CJ@2*B{w:7[\oa7[,z~a}3]
    oQR^lQz2+3xAjKO]$D2nIsiR[Cu|KaEI]]l<8azv_$-<{R#@1h,HAH]]ZJWCSuAITHH7keuloi$I
    #oeu~^G^G;C5s6iDFx=D,*AZ{ol;]EE1K*p+3QnYAW{O5^7>jR}Aj_Dn\YGe?KTvji'{xJ<[]H{]
    l$}z1;n;OA]2v|$X_?MDK}z[5?xBJWwhB~ZQ_1+w2RCu+\TCl+X?<TY]x*tuQ[mQl7z7@!}oQ[]C
    =,Q>=QkQZ!*v;Y\s=]YhDvG\DCvo}*\-n5$2v!==C>pWE/@xvTk_B-vY*5aIX^}a_*:$~\u1^OOW
    }=HVT+X+E;=H*Ypj>Gw2pwB_=o[V1i1d<*o2EQkCMO;a'1x<uZV\~;Bl?lKDaO]lV<^C-D?EHbs?
    1R6u{=C=WeRqw1;Y[;ZaYV[x=Z,nCT;p3n-]DsouJ]-~:'#$\#e@{7@<z-QJA|ZE,2eC$GnCuQ$?
    $R@l\m2vC7x{jrYl!a[AOmeTG+_om]Ri+^V[;VvK}jIO;l5j>YW1#;EWe2aBv=k*<O,@R^"p^ZV1
    C}H<]]QQOR;_#os~1JHDbNE,*mYmla)E<pa{j5pzOn7]~<Ov\,-bc'z+Qpu'Bul]![C{2sv'*OkW
    ;p[w6sH{D0$VvEZ<j?G5;VDToGpHWje;@@jEnX7C^@Twx''~V*zE!K&#[$j{[pv,l=J<=Wr@{+{Y
    22?B_Vz#D_roYn@oAVV~$++Xan<W_X{XnOH5Hv,OOu#5[--g@E^>*JKO=smHETZKpzC$,>[p,sDR
    vwe<,A1!>^C]{Hp7\E#H,>+kE#~;}\+?sn~2IsvO_zke<x5_@[$@1aJCG!=1I;COzj[GGu7kqVGG
    aV@RAE*Q*}',p{Bwkxgl\'!'aY@A7evP5v?!w{[1%[Ju\@Yi'vm2@@[O~_R>75#mI~zK\dU$H?ZA
    IDB!v-s[!Ga7Bu@{}jJ^#;Bs=Dk>m{[Z2+{}@3n]?@+_#lYwzG}u^x={',Cks],pVw![u^NTG$#j
    Y{]^o1~[<-$z-]#wn'Gvrr}}'2@$Ek_T>Ca1{8"[V{}}n;jZHQIVvKR1R$o?T+viTrBQZs5UQQ@O
    1_pia3-z*X'}j6.+5DD~Em~nYzX|~$I$9k}VWE=OotRu<-VOGX$Ao5H[^#5$\{lhlvRD:8D?E=$!
    ARq*_,H+pm57eX{HjGAz$eH1CB}Boja!,=$ew[W?{D'jT}lAe*pk*DD@QAUD_w^V+Az^!J#\{TIa
    ]_GJ_oUB\Xw^Tp}4~n<'+G]Z@\koPWjVn~Iwx^Z>w9oa6{n5k_$szhoi2;_0r=]^,UAnR3=,NVk]
    ,Pk[{{<Tn]_Iull]'~e-J1u{{wDGY]]?,wvvBU'=*j{xrvJE2#cvap_;[H<]AKA'UVEJ8/*7j5ju
    bWR[@5=~Iu[l2wYzpwj,p*I{IHr;Gm5Y3,s>@D{~2pI+pQUOVw^GrxZWC>O'mWY\5jYBZyaQaezu
    l3'ZwC5lm+s?s!B-JEhG{e,=uYl]X5ZD{V@5-vkrv$jo!E;!RG{VR!zUr\Q!nW\_2{^O*B1eG^_h
    vF;7<7$2o[Ly>YvoP3{\2RH<CKT5!4m5l_F(>TN=?XwD<XAlUUWo3o=B}kU}W{~vk'rJ*_JC_B3x
    ArAZBOu5vl~fRUIYx_jGOvYs#l]G,z$-#7B?fGI2Qo!Q?C=}OH}<XW\wCD?wz5er!\!>W,=n5#<A
    Q7HRGOH-Gpn<?\Z2jj7B\O]=G[EOQ#>,wA\T\E3-!$oar'nV1'nB$\Cw~Bp1_n]OBB1mlszl7QGo
    }>7}pEY#X3}CGq^A$CAXDH\xTH#l_BaaZ}?,[Jl@xT#<B{,TOZ?]x*QkYT0'R5G=a1j_<[ala,U|
    la}@fCoV-;=H]>_Dv=5BKI^w=5+a'>QK=-*5?^XUAR~sT}Auo8>pYDO"[_5Ji[kWv>eIRo#<j1_B
    \,^}[_UTJ5pu?z{Qp'\^tZB$#lH;Rl;V+{[!Bi\w'*xxnCkr]ZRX'Tx]_[u~uQx^V_vHTJz}A(<o
    {xIu$\m>l@^V15^$<U]R7>=s'-k1<-j>o]?neei=$x[~WEnnEV![?H^1_-BB#r2<OB}}Ku#jV5@w
    o7'I7{bwC\12,Vm%s+R{v-v3HUEV!zAsKa\*+'Guc5[eX<\*a~>2KBJ$O{5u1Cw<=#}i=(xD-;P\
    HVol~_@or~QYwnW}V7~Cu1x}$?s;\A]s32*!$C-$OGR*T!rNei<$!_xA>X;ezk{B>Vu{=7w$\K!k
    9Oe7QD_QWY$#kYn2D_2_ll*{!#]?-EY3,9=!VGju}ehB1CH|tG75EDe'KzJA-zBRYwl1~IuHrl'1
    $Y$Jvo-*7I_;o$m5'z<zWdp{]5uIeJ\]~{2['-nI-'>Q]kO$o3_H=w$eCR<{W#O;7]aY#UpW<{ip
    OWue\?ns+Hk7QQsO2KIY3pczG'Ai{}O-[AkR[eYm]$$Us-Grw_WkGRv++Y]K5xVO{D$k$QWkDx}5
    R?VrioIMs~R1X9X]JGv<av\vCK<l^W-+!>&RmV;e}_BOG;kep]A0',Ce>j^J>nITB+-Eew!oI^$B
    I_aYA$>D-QO]@j&Bxn1HnIvGwl=<'zC+}<Q3O7uEB~GIk@j@e^HrQj;o3@+WRJTza<[\?E7R{'pl
    7vv=ER^tixT_J[xw$;U?XT*ew773(P2E}OG;*1{=1Z[si2EG[m'-,zmC#j)goY[1Ev*;rJzO#<mm
    J5'p}p;]P$uSx3o]'*'5ZU++!Y%Emx}1Em*zi
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#D}Ea$>>B|<,J}#nU5moDvU}Rp^K{$\*y&1+D["^YQ1>'C5em3}#\}ix?<ourCp==Wa#1p*!
    YBZLv--s~{BBY6+1$'Q,AmOoXA31,['>Kj^-1V-]-]_ipHj'ms1+B>w[[sB;&}ZEW]G$w{,ACHa~
    [_!v}SOxfF.GmunCJ-;Wo_\0o=zsn1TW-\Ipo^2-9RoJu<=w@ZY@nk7iYQu3Cu$?uq6<<C<-Qk<U
    aZT1DXp*@}l~wls\!nKe#zeI?'n~17<Ex?Kg?Y$=]9AT[wYm3!C631a]3}WT6[kGA^R=!G<R$L*$
    T^dsG]Z?-U^75c['C2!a~,HX<Esw>B#7vWa5H'Ox#Oc^T,'$2+<DA~prVxJe#oe:?j~2T8(W>W?7
    O13l;D$+wWA;7DTxCV?e1$}'TKYj5oa1!lOm,7n6y*m<#*ue\os<aYg!.C[>eK-Uem\BTb};oYEw
    Cl"B{<llnBo#nz[vur<-[On]!$#KoXY~Az?+,G<QkQT2+X3![\uDvR~9g^nWJ2\*__?.hrIj_/-j
    5^!>mRlz[QK5;aVKWw),Ck[P*Jue}nHD[}lO~nx?F[=<Q14vOBw'?RU{HUp[B3E7elp+^+$a,u?L
    =C_W\1^+NQ?5C1HYIJ$@m(<pAGHpip%&\\[xBm^>CaO-?+T{C_$_n9[VjjD{sAoWYu|bSl*$H!zY
    Hlf-D,H7$<or1\v'XE]T9[Ds>OV=OU}1@<z23;X1o"$n$!>Q-nnH@D\\aD,A5]lEG+{T^p&G=;k^
    eUeQn,T525p@_e<\kDWupk{Sj;]>IrH+qRY_X$}V\1s$iJe{'G{lHwlUJIvJTjU\u[D{}c,mVT!R
    err!KJXBqXH}U.DAO;{aADeV5Rvp@[iwz}.z,'T}~@W]iW#5r+*a5AGT]J7R2zQ2*R]-D3?eI*vC
    57I,^W@'Jj2;s$s"=nAXx|BlTIEx1#[1A[.Q[C*l{x1CH+p"3ETZQRY5JX+evX,7;Ao,};RioYx!
    <Em#CA-w%U,DVm^z#Eu@X.Y$R!S'szseVGiVW1ap!Rk>=1_v=#Zj~;}EAVz=}k1Rj2+8mVJvKT]}
    y@Al+gI7-,Rz2_z3XYBu=3,KaOYjYm:d',[Y6TAR!i[p^=@mlr{>>H{e;zau@,3Ts+sJ-=la#~Cx
    jl1o2s_xJXo'lQj-l^;Al~}xK[T,]73R{PGBpCHUD7D-,ArCx'aHO-JHAH}OVTxR'mS8e\je_W>R
    ~TIO%Ks5TsGnWlH}dwIm]r3@mj$]O[|IE1jWo^;IT+Xpk}i1:whc=n=#_wYik5-=K<nm({eKOTY2
    Q5'[A,GkJv>\H*o~7B,[EI6vJu>ww$+OT'A[VX;\>wAu_zOI@BD8}f?DZEh~sw1}p<+5Br?VA}$I
    ]51lA*1jE^vn>E;2*R~EDJ73x*X7X<TC;^J1[<,=A\~a,~pE3z>mDVAY*!wxa@3Eu[uv<w3\e,u<
    7U_\maWg=Q*2[@jiG3<J_3H=)xRWBex_{0,nZz-HWB\s-Qv<G,*GDjz#Ra^@j'{s};^+GXQ,=+*p
    @UXQwj'BQvQeDJTIvA5#+*:bnw$nXEYoH-<$p6pn<$WoGZmD$Jf!U+G@1E~5~5TRiCZLWA+\G$TJ
    BjR\C[^Vl+ev{AB7GmEnc^,poVmNkXW=?wEBl'zk+C<!JrYXk5l'7r\o2'm^\vu\R*jp2o^Z1Gu+
    =~p$-oE,J\iDE'anE@]1$@rwI*Be*uV;Rk![1\{Gvnu+!n\\$G-j^{<#2X!ZUB;\COpj*C@^1?7Q
    KI,3:T9{a'#I*,}72a[WoX#li_muB'Wn\I^!oK2!wXGC{\zUsQ;Wx=Ckj5n%@avifAl=GO}H~wV_
    {MD3K'k=jKw-vO\]OxR=#;[q=HI>j!wTr#T^;++~;C7V]-E<uI?u!_}^mp<wz})z#3Dz[xX{HzBr
    +>Oorr1({+r~eE-O=J^>a7>}BvwQ)sVZQ5[7mhD~+V$Urj\R?;+YQu5~=5aYJpH+z#UEJrGO\;66
    kI!p!la$+]AU5'_=)<s_~I-E~pAaY>n,#&-j=\EaaRD?lD}2XokHR2KoCZD7izV]^iw7l~$o+=VJ
    ;pG1C[Z{{'IxTUfZp+>7sA[I_VITn_R?X^HbJ=O}$,!pIO~Gep*Xwj_H>x$o=5]m*Z'Z!_rBZr~-
    h5TD;QeUwKaY]avGrNW++@YIi<wR;7@>,a?wnoI]3^{5A,+[@]KV3RuUwj7A3O-w7D<]<3+RY~<s
    sXu12>o72_l~pv7]}2WGB-O]X@4[*e_JIw@;wH[JGa+@lHXaXQ$j#RRBr-R|E,QE{^nQ-T3!KC@W
    :V^HJBZQ'O1SCD#ev@ACEk<oA'\5,$e5$E>Ks}eXl!-s,Yz#TImj_op\BUwWgo0[EOz:jJGz@p+_
    moTDOHDUYJ>HC^+?+an;[Z=mCOi<IUsBK]sUj)?^wGVpIHKpx!17J~TrQpGnj{p?~\D+5am-Tm<^
    o#*zkoY|H7ouW+m@{oim=#$m,p~=rvp-a\s,=illZn<'FIEoTfi=w?BY75{,QX-jvIHED'a]X?Z^
    @Ks!7}M^p7Ea[qBlzZ}zApKw*,3UA7-E5Ja5A?T{xX_GmeTCO<j<[["*a>]{e_$"}WXWz*<JH_eX
    'Y<?$k{X={VTb{|eD13G"XY=+x=CuSPJAnGB>ZQ&mUaA$@j+~}u$43>[wE]-2uo3RG3r}rzRzmR,
    Tn5a^h7@nD3>^RIB~78_aaE#'ZHDHZaJVG>+ano,2ImW_<?TsKxn{+CnGrAC#R,CG{{BJ->2w;X'
    A,w+5GRes{GlG3#QrpURnT?ZHJ+^~1o><<nJv#V%NJ\n?p3uE.#O[3?e_om5!~Ov7x'G1'$A3!_o
    jCvKVWHAR#@zEH7w,^gXn3,Q]r$E(E1=*KC}CoZAn']u}a7n~}pE],X@T<TDQrZ}+Ts-3y@G+D:,
    JYn,=GUnv3=7]DOiXQ@Wa*E3=$2P}KD@,QCO[-3,_!o!G@3H/Lw[>$LY;A>JQoUv@^~u\VH^i[GT
    DuEaI+sCUB$@vlv[jJ2,w1o@lO!R3UY7PgPDCnpIx7
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
module `IP_MODULE_NAME(efx_dphy_tx) #(
    parameter tLPX_NS = 50,
    parameter tLP_EXIT_NS = 100,
    parameter tCLK_ZERO_NS = 262,
    parameter tCLK_TRAIL_NS = 60,
    parameter tCLK_PRE_NS = 10,
    parameter tCLK_POST_NS = 60,
    parameter tCLK_PREPARE_NS = 38,
    parameter tHS_PREPARE_NS = 40,
    parameter tWAKEUP_NS = 1000,
    parameter tHS_EXIT_NS = 100,
    parameter tHS_ZERO_NS = 105,
    parameter tHS_TRAIL_NS = 60,
    parameter HS_BYTECLK_MHZ = 187,
    parameter CLOCK_FREQ_MHZ = 100,
    parameter NUM_DATA_LANE = 4,
    parameter DPHY_CLOCK_MODE = "Continuous"
)(
    input  logic       clk,
    input  logic       reset_n,
    input  logic       clk_byte_HS, 
	input  logic       reset_byte_HS_n,
	output logic       Tx_LP_CLK_P,
	output logic       Tx_LP_CLK_P_OE,
	output logic       Tx_LP_CLK_N,
	output logic       Tx_LP_CLK_N_OE,
	output logic       Tx_HS_enable_C,
	input  logic       TxRequestHSc,
	output logic [7:0] Tx_HS_C,
    output logic       TxReadyHSc,
	input  logic       TxUlpsClk,   
	input  logic       TxUlpsExitClk,   
	output logic       TxUlpsActiveClkNot,
	output logic       TxStopStateC,
    output logic [NUM_DATA_LANE-1:0]      Tx_LP_D_P,
    output logic [NUM_DATA_LANE-1:0]      Tx_LP_D_P_OE,
	output logic [NUM_DATA_LANE-1:0]      Tx_LP_D_N,
    output logic [NUM_DATA_LANE-1:0]      Tx_LP_D_N_OE,
	output logic [7:0]                    Tx_HS_D_0,
	output logic [7:0]                    Tx_HS_D_1,
	output logic [7:0]                    Tx_HS_D_2,
	output logic [7:0]                    Tx_HS_D_3,
	output logic [7:0]                    Tx_HS_D_4,
	output logic [7:0]                    Tx_HS_D_5,
	output logic [7:0]                    Tx_HS_D_6,
	output logic [7:0]                    Tx_HS_D_7,
	output logic [NUM_DATA_LANE-1:0]      Tx_HS_enable_D,
    input  logic [NUM_DATA_LANE-1:0]      TxRequestHS,
	input  logic [7:0]                    TxDataHS_0,
	input  logic [7:0]                    TxDataHS_1,
	input  logic [7:0]                    TxDataHS_2,
	input  logic [7:0]                    TxDataHS_3,
	input  logic [7:0]                    TxDataHS_4,
	input  logic [7:0]                    TxDataHS_5,
	input  logic [7:0]                    TxDataHS_6,
	input  logic [7:0]                    TxDataHS_7,
	output logic [NUM_DATA_LANE-1:0]      TxReadyHS,
	input  logic [NUM_DATA_LANE-1:0]      TxSkewCalHS,
	input  logic [NUM_DATA_LANE-1:0]      TxRequestEsc, 
    output logic [NUM_DATA_LANE-1:0]      TxStopStateD,
	input  logic [NUM_DATA_LANE-1:0]      TxUlpsExit,   
    output logic [NUM_DATA_LANE-1:0]      TxUlpsActiveNot,
	input  logic [NUM_DATA_LANE-1:0]      TxUlpsEsc,   
	input  logic [NUM_DATA_LANE-1:0]      TxLpdtEsc,
	input  logic [NUM_DATA_LANE-1:0]      TxValidEsc,
	input  logic [7:0]                    TxDataEsc_0,
	input  logic [7:0]                    TxDataEsc_1,
	input  logic [7:0]                    TxDataEsc_2,
	input  logic [7:0]                    TxDataEsc_3,
	input  logic [7:0]                    TxDataEsc_4,
	input  logic [7:0]                    TxDataEsc_5,
	input  logic [7:0]                    TxDataEsc_6,
	input  logic [7:0]                    TxDataEsc_7,
	output logic [NUM_DATA_LANE-1:0]      TxReadyEsc
);
//pragma protect
//pragma protect begin
`protected

    MTI!#n$kuAnBl%:tf'CY21A-a6Ko77PBeHs7@*m@Y#]GwW[5oXkixUuKlK}aTw<oOlkB-sm^<;\'
    *W;J$a@7Q-H,p+3*QIiE5]jOI#1YwTUQrj17K2zB-K;lTXj#$2A'jVo~sV*oTA[xPY-!C+{X!]CM
    @Q^<ArU=lv!0<s{m@][BLnD+Iao~[NEbY>;x@o\i+<CZ,;G3|W<Bv~nx!U=AO=rK1m[jE5QwX)a^
    3IxxTaZR[QY+IPlzVUTr^=El+~2YEv/^-UC<-upu<3Z5$b0Z{{l37ekPsxmD~<-p,rnTQQ[!1AB@
    7kJ?vF1n[^lB7OI>{X'~,[R!@K7;OA-r\vuGXCX}[3lu+^z\@Jom^lnj*sONjVI*^s]*~'$eI{;>
    $+;rnA[r'l<zCNmD7mNAC#;AtlVnvY3$2m[j^0I_l_aHB?=Ou-E<}KBIT+-s27*C+Ak<{E!OO]{=
    5xx=p[l+JeA\r2)6s\QeKe@waxnT9pR?URx-<>nn,w_TjM$VpeAYYXD<y+Unm1TXrX*u=kHm7=!z
    vnv5C}i]_7Du\hJ5{B,vk~n>{XW[5H{V'a@5^XRTu[2EusvA03Gj$$\<?b-QA{o@W$3IrQ%[HR$\
    >O*ZvX]R,naA7K^a<aJI>Z5?rw!5+Q+OHJEc'ZJ^ZR7i>D5}UT5$RCG#.NK>naB_D,rzEU_,U;VZ
    QZ|L7{A$$?>xSC2A~piOwsJ^J=T[7IzXx~TV>~DGY,Ow5jD}*O~'X\lA>mDW!5kozQJQ~B-*p8_D
    2CbQeTwvaoI:gLB#Ual8Rn3}cl]A}ETT+d*JI@e;\xplRsVwo3>wm;(G3[E*kBVRs,Z@VI@[?J$Y
    m@z,DJ^I71wzT+G-E,@7*{rIVODRDOv}C+#-]T;#Ya@uBAp~x#Y.j~}w@jWep<+{ZwHlAl2'wxA'
    Hs+{\ezx]?pE_5DoXsH{*G[RYKsiV*[poe|l@->I1=Xn7'2R_VIV@B,mozDcEZH_oVT]HI=uPOlu
    s|y'XmC[K+!'=ra1i=*VOE'ClQ@z}'Q(7_Klr^<RZe'I*lJ{$1;>kX-]p>a]CR3!y9!{szfuTEC?
    +e],#J\^-@[7{ABt/$ala*uXE9rR{3E}wYaT{^3nBlH's'OR$s[n[YLHa3?]]nUvA2#-lYn<=Hlr
    ,$xiUe{pzA{#D,mOvBQ2+{5v,${1iRWuCD<QnZ7w>B\-j^R^m2-]1l}U$z?\ZT2X7o[^i<ABC-#B
    |n^A1GR@]1mj]IHZCD[2a:kCH--\GWQI7;$#ol5uZ[ejZD_jWW7+x=_a5AfYI3OB>QI^Hz_DTpew
    }T-n15}in!ku}7o^GT1l+H@}Z]^m1tJ$@>D_i>As]lqi+=~UCaw[x}Gn5,uw>$#[_nU{ron/j]D!
    'Xem=7C'\>lr=Yo2G{=a-wz~BWW<\lp1S^HKT{^ea;Vl20wqaH*C#1;lvXT;kDTo~l*msIviY*Gv
    ]zz^?'o}#za^'!<rHB<*BN?*R!pD!}_=R!:$YIDC{<GUv,-?xCxP@Q\;ivGlCaYro7!DEA1mCnxv
    WR+]SUATk'@O\KHTr~[5*Rl^3wECZ>w$Qnz\eG?;eA+XZ=#oO=[V^!pR'>>Dz+n_ZTo-*]r\PY@~
    2mrIOkGGXBJj\5]<Z<6GJ^2DPo$*!!YjVEe!sxROxZ=W};=A3s{Oeu<;$PB<+1vYn{>1[2Dk'~={
    $XFRw>~xOvuWO1TK+7IBqI=XG^X==ZBj2#7K2_GcW7J@p=1+-rz#/9VCB,RpDExHH-K$RXr,U+xz
    VxWnCajQW+?-xK$ZJo!a,#Kz'~Izrrusp\;+W5diXoa<RG>lDox=H,,Y@~BUauV?HJ<HOi-TTD][
    {_>_JmCH{lIQGiAQmJznHHUaYkRh57j@ds*JuJ9|s[plHH}7qVoZ\-<{>e+2G/RxW^-^X$AEpZuY
    A@I5?H97f{Ek@{QUaN'B}m^3_-W<sn9T5R]r@!Y-*}}GujVR{lB\oiZ'Yxj8ZQ<TEAj?avvH#Y-Q
    v333Gk7UK<Qz&'^=2VE]r"BXo!h}il'kw3@w]A;n,T-\s]w'j^sx@<I"lA']e]irqK.v}WpJB2?g
    OD3@/zmY]oe{@<lI$>,![=IQWA{G$JXEmY*<!51[5Tv,5[t@=]w}A_Z=@U2/6x@TuR2B>Ii5oA<X
    Gs>x]\e?WTOO=WR@RZ_^]Z$<wiDQTtx;$UlJ{_6j<pR};*321Ez!a~>Y=\=x'HXRk,RUV*,pZYIW
    xW!~}jI^;v$LW+Im^W][E#C@WTDm1m\IX$[]e^$p5k\p\Ur{%sH\iIk>V9j$e-o7ReOsW'CEJzDa
    mxmzQe+O]*YYYmM+rI;kj}u^+rKD3GjFGpK<GIl$=1'a}jvr#aa=GR7x=w-7<*[[jiWAusW@?EX>
    EQZ[,mU^oE>?ji
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#,s[zTC?Q+a,risTRs={TpzC,1-<T!e~,[yRGO[_!Q]O3*l=vs'{5uD?Gt6]GCT~aRHCuCOa
    $Vm$uMGZ,%lwJ#*+[[o_n=['CW!Ul$HUlIH-BAI_#QE{CHv3I}3r#I7ZzpfaIG@B!~B'HARV<+pR
    -G?l#C*|%}<AB}prKB5BX-e!=l^I#iD[+};ZHBB#}6,><uoOE5s;,rO,[5+o@Q$[#~:V?][I"ksa
    '<\3HY@I#Z7[wC$X-Dkl{&3[#r-T=@+7#D*UH_;a*@|lR,uBTU~IQB+}T;m3HeiH,{H${Te}RW7r
    jViroI^{ajoXEDjY=ETO3'Q*lOJCEZO|~{WuU'=evQ;QyA<vV&'ICQqRR7;&puHaIYs@<DXQz+uT
    o_-r?r*jQip2#l,@lr!~ro@e1'3m2[3$^!W~Gu[I7u_?8ko[}@e;Qj~p2G~OmV[AK=p[2i7;K^}I
    jGATU=[=jCbIa1TmX5sHz5kqel';cA=;#1'=j!R-G.~l~A-l72HQ1srv[k$RlHEm$2^ZWpv@aXE.
    =(e4~>A^(lQ2I&lC5kl-7$55?~''a@/YlvpLR"lTRug&r2G1,CW2\vX}p!epQHRnpw1!H11!T$OG
    6T}rs=_l=v7r;xTx$\oI^:o5z;71<u${owAIk~\TpY+je5njCuRA1~Cwj@\Y?R3GjDvT\$Eo,r;^
    '_qHIUxQXD'^X{epvV+Jv7#zz_C[REjI=C!~V$wfR^TkK.8k,zUjvWBOZ=l.<7<lG3mOzl2^x#7T
    T^uv{w!aE[H*O},]p#Rmp3j$sj<\rIQAe]'n:+lwUIs{2|(*-uo11{DkUB#1o@?G7C'an=#mT}$q
    j1B_5'\Ta7K#'2{UH'A<Xz!-/0p}nK='Dx@$}l$]w5E5+>$je{ansQNFEz~~aCR1^8f*Zr2z27E&
    En{;I@wuYHpT}u[RawnHQV$@wOQHV^&m5U!@[Ie,~*?wB3}yr+;7U>H$]vQ3iDGsj<ts*ReRJ,X#
    _'p}BW}_$_,rGrI}Q]^k5eEu^XT-z2nl?QeW$,^X}ys;>{h,O1=31$T;QzrH7W#8RrO~-D*=ixn1
    *2m7\Qo-1-EHH}xnj<nDi+CA1@<GuGlo1={>suIQ,\^;7-Qp@nfA7olp'JVyI>'I)V\Rzavx!YXx
    Vp]rJ3jvBIs'^Ioza;&BQJVR2-e*;Z?X**C}o3K%i$rU!r_>N5@$,77';ka2Q/ipBiw$V]Ur5[i<
    nZ^i;oHqeRD{O3_K|xuVkIY<KjzEop5C<sEZJ^^JkmQm2z2YY#HzmY[l1U[i}_@j2@w'!]Xmkx]-
    v1Izl3BQHQpr+O(ri]U!UAU'sl{w'=o5l>!IC_VsHJKlI1!m+_-f}Dlje<7OG^UvqsBR=r@5!x?G
    #Y}'7nsl=&@';mvBK^G^vK{^QIx~TsWwlWC2*W]r?JrsT!~$De2[i_e'!{_]aHi7HXUQKvBg2^m+
    <le}ksAoq'\]>oo-T{'7oxkosQ=_>D}W_$xRoZ>>>,+Ga*QWDpz[#R{[*qYJ=v*]~UqW[Wo^}tD\
    @B]$v<o\J[2wDoCYr,zkJYr1@wU$?~r#sAWU3zqJ7OD5A+T']~ZZ=QBbWas;a_n@:e5EABC$=8MU
    vs]cokK<qtvaUoUj[OIIA\}r<Caj'z{welYD]OuT>m}}v{Wsn@]=2-'lJY^WAJ&A<1Dv+U+He=ml
    $>aA'\#sG#?'DTVI<zJr*Q~R'XIF[*BBC+masW<}J1U+NO$BKB<Epa^_l*EJ#]Qk#dsYJnBm\J-\
    *wV7{U2IT}o2mun=51=+wm*[^#,BV'!^QTGJ>!z@}BBQ*RUOv_R2O7<pOpQAXUEHR_>l+wiH+*Qo
    r-F[|CKYmoOQ~Q1KA}n\?a$j>w{'2I~s>3TH=^UYUmD+$gWHJl]\D>w'vk]{<RD[3pw{zl\KGXHT
    ]_okUn?}Q]EfLoxp^$7R~,g\pee=w]YUeBkc''3En=,2,zVi<55X/oWzBnD>+[aZV.1*VZ[Ekviz
    Qi_JRa>RkZD1{_D\RUBj1pQjHY[1AZ@e<Rmp@?|opkBEn}3A\vL^RKaV^RR2jKVJ5\}]Up'}~Tel
    7[C=!7^VjkInDQ=B]3#-X+7}a-7Y^a!,c?r[x~z$xFT1o\vJW?|*O31$]kk0}U31}E<n2[3kr$x\
    F/m,1u2Y3n+>mTTVZ;mDO1bHzJ<dpl;mhB#^mAXu\H<GkeaRX$kD,s7e'\sl7k1}RBCj#mD2?IHJ
    ~a]lTN1wlW-o'A$I_Emr@#a9?>]De.GDz{izv^slY2z{DrW5@[,Uo5On*T@1mpVgGHm<~$7',kDl
    *=U\Cw-Z\1<s2lH]'O]?xz1BL{HZl=#AU['jUA1~u3^wk2a@mpwEjn^_lED?oJR,[!Cpa'D!AD,5
    2}YIr=+w,n>[KR@e*3oH;'DKsDelwxa>7zkmB*-zDv2VQK*U;4YCrAo0eB5KY}Ta[v25&7Zj?jkl
    51]1muA=<7U*[[$G<z;j>TwlXejW3=H[-njo$7G~ko2T\*k*!a*~~&y0ZRu$WO1Qz~=TEJW_O^A'
    5D=H$-li*kv?e{Ya-T*~1.'*x2K\rYYjZ@u[?eG!~o7WnJ@$=QBz}s+1%MRxAkHlDk3TYp:,poT~
    wsxRCIKXC7=J$OwowZ-R=1IwU[H,$X'H^ZJ7A,]G:1CTrhaUw52j*[e;X1=!se7__]uVvl!AEG2{
    xiWE#1C]{=?a1{UC~He=jj)I@D_y$HWZ0tR{H{C!D3p]xpQE5${C=*{eM<XRX]lQ~zp>_5IE-MvJ
    5*VV2Rw->E2QKGVK\]%@]p+^'7$wE^K7'3'pEk22E\{!<BCB.AUBnN{5^~>QaE0=Y2I'puUB$pun
    pKr#<!K_azVqnUQIoemUA]O3\wMk[rV-}Ym=sj{Xjs~C~[Y7A+K\$aE;Evx$GeBs#Jp_{J@I5e\;
    ^1rQ'\Oo2peaxeR]QX^$HUHXp!z^jaK2QmBI;Q>p*Q3<wC}_l+usZKU+jH]mjR]'sE\gxJ{T!^+\
    t(DipOX}-CODi<lY*@nYGCOt#[i=*\i'#+VW4E$}3ml]R6'Q!@AB12Y5~B72<^5n<'f1ETU~nJuz
    ~OiDeIW{[^nE1JuwjGzBD\OG=^1AteKep.6RXo^C'?RwD^}mojTNj!^@]YYpfdETVkgDx5;un1?B
    uv$2s7Yr2Iu(^'jpQ>5;BTwDPXU[*l]WBm7~A5XQW@EAB5BQKD^kBssjo<s!__u<B\7$exZKvNv]
    mme5ju,$;[j21E1x21lDw_2T+]0=r!vj*>[$3e_x[BRCa3wGZj<x35D=r^UD$lRCp5##Yo]#-JkE
    3JRcXO$nv2+2^AKuoD{GOnW*1u<@peXA.jB>Dr?<D3oVp-sZWWhOk='#oiWd,Q@2pH!Qw+D>+[,l
    ,+Ov3GnulVuInjnQ&TH'Wp-OKB@xwv@RQ7'Ov~V*sg'H\{M@[UEH_1u~7=a]#1DKzz2BOva]~=$'
    OV;B!>~r<;*kTZ2!YWeokrswC#-[g+^v^;_uB@HXa,\*$^^BYTAa!]-<w+rvar7#'-831+lY1^_K
    O'VeX$xD-[3c^jB{C>]1=mGZZjKYH]AV<A!w-7Ik86WjOEpG}3)A,W]!L]oz2O]3We]]7/)Rx-?:
    zX{pa,~!njRnz-G1AO_=T'TrD7{;\$DR^^U{zp<,~TCB?}*p7eIp1}=Yn>'v-Hoo>aas$=YZV=BZ
    %0%Y$}i\nGWD=ZBl[Tu7aror>W3'+GCI-eD=v{+KT7j!n&-}$3pR?pureC<[J=Q+AlO'jT1:r\HW
    1Em1bQ;KswEYkx']uI;^~BJ$vY\DpI$[7'#CE<=pii$}o[mv@[+'u1J\n68IxKIAH1,AH7UkAxma
    Es+l*]UA+'RnpoQPO$pk7a'sqWoO^vZ_ODDx1E2pTRI1*@}D7|^{@U^;7z,DKvJC}BEQW1W*~Q*l
    Emw-UpLKp%G]z@*_6]?BuE<o2]v,7r1uXUT\EBuIAx^UmBCio[=!Y|V['H*\w**sZs2GAY3r,\vY
    ^ZHUAE[JZ_DW>*;YeDD*B,Ue{e\]?EOjWT_J=<aQ,$VnA$b~GlRT<W{jn'iRils#j~amI,xpLs[e
    J*5,'+<IK$]$w',VJX[oi9j0?GG}sD]zn-uU5!{{$'\*'0Y"y*Z3O1}DVJB,p*,$$Va*DjAji]-'
    {*E-rl@=C&vku]3'+IbE+aTmUu~p[#mOBVaA9P'_unQDXatvGsQAUA7V5zX5~v<k{Zx?]sr{[nYC
    oGarrrKY<EpJs>7K-Zj$1I_tnAp+#UG5le@{ZXEl-l<Ojra5>{1#ju5~j*Zo,HV}9_<\CO5m$N[$
    QD7~=^{\j!;tuT'o,x2j#X-JL'sRuLtrxG]BUJ<#<DKJOWQ]!${r;ZsS~BC3Y!W\]msx;psskE$#
    YYnOLEKGZ\<$\_w3We>'=EiH{DYZwPW7le\gzilrVBR]\AVrHY?z;*e=2^zwLs[s$[7XTlK~]5*<
    ^*E;OSBs!\%b{}V*1A;\oHLw'Aw@DwRl'u^Y%u]Qx1a@lPF\Dm'^Z$K7I3GQTGz{,JoRTGo572A2
    5\$eD\'}Ze\oUOmIWnemG'HYX~u<T+'e_vQ$UXA~G<*]SD!K*DE'GM3>B3*v<X?}]H;B=K'*?1O=
    -;)Y?51{B#?-pKm\I+v5=Q5x'u-j3wXnBrEz#Bj>=CaV~Zv!5IuWeKGv#U>jYU1;^{3{+*Jm+2Y\
    Kp}\>QVUr'{]$wKDejU<a_!lRuj*aVr\TxsbD1sG3UAHCj+WDOZ]oK1n8Ik<R('GjCDIzDXTz5Ts
    Z_HlvwGQR2ko5\u1e><x,Tp?;jUX{aiXW#!1nj*e;3%3<_{|ar,Q7}V!DPt~{]jefG{2+#QzpH_2
    UaeZ{&{GGI?{!wU1]n]EoKpG1]E!$2{\mR?[>-$Y]?[A@;z;mEJwZrr,R>[]wT!Y#QgcK=TxDro$
    br?<J6;,+RB!HCPzYW1v$w$2Ok+snn2e?Cw5$iu3Go![#>3?'I!~E*,v\$1YHz3_A{E2[]2l}nIB
    *V>Y^?#OQ_1WO=Ule;l~*-~Gxk72jrjpW,!V$^DOC>1EzDw=7]-o'<rnO[QYoK5rz_[KB#+FZ5!+
    Q#v[kOxXTXs\Xz?wQKsl3j^K{Hr'x@R2^3B\@>@HEI>*,@Rk_T,=W}YkN;rV7\=(:7=hm'*5vi,!
    lGX*C*<Texs@']X_],v^"s^*}=RVR&{C3{jR!G$j\eoHJ\k}~Dj*IrYeaW.=rCA7]_E[<E^-[$A_
    s]2Yn{]-B~7XOT_>AB',>elj>A$7Zr*7HVrIoJm7G=K:[251z}\oea;?,WK$izJEV,@v=Br?JEsV
    V1<Q<T'Z,'@@iUxGJz}[I>ZZsv22o!mR[++,I$Ba_JozB7'ODEs$DjG-![1Wh1mzwDGjj+5H^Xs!
    K?ATZw5ss>v!Jw}v7tLBsR5jkEJsVU_^I^pH{nmjp7lXBY-j4:ru+aie1m[U]WsDul]UC3u[TnBT
    2'7CH^:TGR>?nweBmz{Q'~jxj]KX5CH5iDxsr-7'@DYC5xAqq>w$v}nXCz?;nJl[C_\1>a7Ik}=;
    w_XJ273$+{AoI2js$b^Il2TRao1\z#]KXat*n-!jpwO_mUm1O?1sA@}d3}gpD\]UC7Dx^'VUD]#q
    njnrBB?^4/D]#\UN4JaU[ezv'5*l_cvK^WS~D;!18]M=OjE,uW2V?5p2*u)io-E=k=#bm7@Zoj~>
    {^YI)v5IG5~QX,GK|Q1w*|[*#\,a'^$;e,b8lBOGi^eJ(jY?rARjGP}R@j*#TnmE-;A}vj#D#'Vn
    *D+XGrvVAs/A$Qm}2>>1;]R'BeEC@~_-[ABQ[\E7p,\JBXar@*vkp'rI$5Ar_CJ{-n5ZsOZtQ1up
    CJ3#YKaw<ar{'JXpahrp+U-]+\1imnoWm^sEAw?_E}BJ=eXemz?pA]C1e{D;}Z6_H_$VUCQ#'jEk
    a\Y,Uvo_I$<xl3O8OA{!NI;mlGm3l5rGp*V7B$7XGQkQ$*5T\K\*s7upJUpn+*5V{~}w@memAQ3z
    Hg!LX}-z,_aYXxQvIsxR25$rmIV@}n0u7!~GHY!09OkXBW[*#sj@1=rZs$Dn3Yj>_IOHHrCs{*Ep
    ++=Y\oo*j)QDzaH$}BG;nsXjOVb}Rz>,aQ^1CAvD?W=~^ZrC1uXp[1]WQ,JFlIiZEwu3}7C=2xl2
    ,KIr@G^UE{2~xk<px;Zr}E'U+1x];DB^!GHlr'-wn'3=yT^B5_7VG@XApYZ<Z2AH_IW!=oQ<Z>Ua
    21|UrWQKjHj?l>!73wHwBj~I>1-B*s_G-]xE7~@H+'?o*k#!O{~<7}[*-<+TYABnv+<Y#T\V#Jr#
    $alO7<,V'KX)mj[wRzo^?_U$Q}#u,V[;m7w=;<\@zl;A$;v,WI_K#V!^Tv!K@o!*B\$U$k+-*~n\
    CK{1rMVw=RDHCj=bq;{Z~nr]re!]Iw*7u=!{2$zTl#'[el#JEG[#KW9^Gu2Y3-Q*?TG&-GqAO!Qf
    ;xJ11W1$_slmC=2KD@Wej*\k4mRa<OiCQ92o]e/a<lw%eoZRZRCsx@7Y~I#C,w'ETeT;8[CVGtqK
    B^_GeRC7@-]G&snjZI!j;~U;r.~w^$yx[zU'oA1W\?B\X+?v*vD,vU1:ZB;XI}{sQKo1'uT3|=*5
    2IU<Y*^+u^km\wla7zrTOK'5EHEH_7KEkenZX<5!ZjE-H9f&V1]a}}RYsqzGAvp5ixlemYe3-a;l
    <BX(>-^*\Q$v3U]J;x;ayBXHj0]WT^SEJ'#_wz>1!;_ZXBT]wxk-aw-t#UI-EYe}<<m7rjTjxa=a
    4)F]Y;CE;V=2n7]uo+jiV<Dw_[-I#-GkIe3\,pu1m1kOWC>[*H<<QJG*'J7+ImIa1DAJY;k721lV
    zoOklsx$SY04eQm^lCrQB}J3e]~E#E3<1k;Ou[7!/yB!<pq,7[nA>*3s-X,r3QxDERIt@$'u^\\K
    IOje5zwTBQ+*IgxD+H<-2Q,';*BAGG{Q$w*BK>Ea'?]Z{uh}]>sBa*X\YJVjDUuQ~n-<Dky@}'3O
    $VvvT5DH=;XC'lKwHV^L!=?2RX>7gWs*A=/7Eo]%8w1jYoX>#$WC3!t+YH<QZ@B]Ij,YuAv2A{;V
    juZ*<HsvDKDCk_]JTnKgi]E;AY\GL*OmZw]$]3vwZ-1~YVR?eBO2-$]{}21>2raeuJ5Tj{sp\--A
    ].B_~,lOK;}WVu~G_JreWDB*~X=uK5rB${6<7-z~IEsB\A$dIVwJz,pJz__zRJO~M5}#<,UHl'Va
    1-lX]kej+[]%/_\!'Dlu~MpT<TQmojGuYUliZrB^pTGI7x]vo]@]]+1sl_2{~_^}-X]>!\p=$}Y+
    2TKjK@4,]->\A@,I?-zF~XsTA,A@jC[K_!1CA5[uXz{[(vO{YTCKIwTZ5aT17UjO2xEQaO3vYp,,
    #s@Z7+XJTze#enzk2_3YBbWj2]fOQ<uO*3OG@]AGKR2KE,**[rr\iv^ee@*=5p[E!1>luT'xSdVV
    Wx65Y,xUUZT+a}^Y#[>VXsBw]2uDB,JyOJ]CalD]Q,w{)]-Rim5R_J^O'aUn}JY+$D{2#BJ$j/O\
    pWJ7{R+Ir_TUmwIibpvH1V>E#wU]};j--{5JE*OeI\]Hx<*7HgB>Z$POI_GA*3XvOp!~o#@n\i${
    w{z[XzkEDGB#Y\KU$uW!TT?G.XoAuF'$#$7xi_N_!-?'Z^pN0QTaED\wW
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#jHo}_xzGn',BvzuB;Q=BI_C,1-<T!e'u[yRGO[_J['}3OlDuWzY>}o,k\;rW==j}n,-x7*i
    '=v>7-H#Ull!r#~$!RVN^,]Eemp[KwJUz^;};<Vnzx#XX{\V}jaY@vpp7_CH[,AlYnOiY]il%>5R
    nK5#a(oma+Bria2jpo<5]nCK+=lhDHn1!so\uoT?VwbRCBm$cKCO}c2n;uUn!#+vU7eUAuW],["K
    DK[$5uOo~H=R0#lA;u{B1W[<_#al#AY+A[Za;A]xz7;n<an^OnHj{ntDZYuEv]VQ3x*!IVnCwB_B
    ?+C$XGv=O{BC'~]Q]-Qvb}?Q@~'JjszskX5B[QlD@\x}WXE+7o~}A{RB~]o@X.1J]x1e5j:k,;Cx
    5m@3--Zfo7OeNH*K3UG{e,>]=!X^z6>v<uG=RJtsBi}BQAD.[J;C=j[OWGv?'mDR-p3sUU'w1CUw
    IiC=v?OnH[ks5KZ[a1IX+I?s37ReW+WAd'Y2WeOia2<5E'V'kyl2UE*2zi]<JZkU!<![{\_xaYR3
    {Eu<,mKw$@zlj;WpY^<X5T5T{zoZUnE2j{g*Qjr1W]Yex5+k}!rj;YzQ'RVe[7idd]nArqTehe-A
    [\C2!W<>?o>D7,A;}kS]J3lkXApEG\+B[?wPDUxs\nxl#13v\<x*I!v^{TvEP*]!{CXn-K=u!6]>
    ,{7B<VAIKCzsvr_1+ZRp#xmveBAj2Oz-GWjFRQGRJEZRpwmA0*AZ$Fl)jo{3eVUO{>**=YDZ>rn~
    Y7TXjHQw#r77m<xQn7?]'H>o}AsKOW'EAleo^!BDWx}@[}q5aeB\i2-.O$E*XUB@DAWVYe1{Rm!-
    6YiKGmlnu8+'+H^e<ou]]!|paX2/]@IwaA]a7,#sU*K7*?n;7aYp1wEG]v{pu=,Bm$<~B@}?Do[l
    oiEEj@s~N!,DIKXVKUEprjx*1W$;pCdK1]2=}{Y=*^p+C\la]#Jb[U2[%Dov#3G[#kspKrQs][z3
    soCOR]=VzY}X;kR5QGO,15Rm-j!Wx3$Zs)^X3T\xu+L".{v=^[e-<L,_u<asRHCoX@m+RjRa5Bd$
    }I^as\R/-X[W1;*5lu@e\w5DCIpHH{^}DO3UmA1<I;n,B3G7]kjK'Gf?}n'UsA,aO3ssB*TXX[e.
    3e!liQ3@1!_E^pVJeQOZ~Ez1]gPN>j5uZp#u5$I~>lV]~r;X_3>];,GXrx~1>1+=$nn}T5*E}e>W
    iYr@Bl1BL.u=_'}lCUQ]5T.?C@=[WYsG+B_ctw\vKx"W1T1}H@Y$,~C4|<O\[^Zes7i8hx=\p@]5
    n2->*M]pJOpzuJlrpYr7RKBQnR.]nJr:k_oCKv5jXw>[=$J\eZuZ0efTnB@ZwU2rw[ZD<1KO-Ara
    oB;2E>sZ<a2a}arFoYXk'lV_Ne7rO[j3*{X1JEO3EGl}w]rI{pKI!d<B:L{'+rpppJKSIHo{a$_k
    =2{w]KAXZ=m'C_Bou$D*~^CDij@Yr5H{,V^zr+OOoPBl=r[lkZzCeiV*H\j=RlU7\D*JzujQ@A,u
    {]?|ZxUURea#}s-;j,<7@DU<3X<p_75u_AVarp#;ks{\RBDHO?@Qzwlx'J-W-TOoO;Q_>pX~x?ZY
    R3ZmSlz+pV*~rwHX{L'!H@kDHJBi+<_!\v^\,;D@uBUUuHHDiEule*<Y<BEpx\&>V-<Qe*]YRE7\
    AJ2=}UA'Y@lmTCBY\iDCwuHzua=kABB<xA=oZsiOj*O>R2+A'WsKw'ev^<pv'jY,i2ar}']l=Clc
    0B\OXKXviixCoy<el^}ZZsnaYzx'W13}KD3}5Yi>m5aYQjHTBWD7r-%T1p2IVjrn'jZ:){[I7s?l
    ]i+vIGG1_'_1iOK]#%=+DKR+I{6vQ}=l5Y<lZ=ppJ\C_2Wme\rGY,M^vZ#V1H_ken!]sKDlv25,H
    \HJ1s]BE>TB{pe]n_zTDa1fDa]p/0!B{Hf^{_]d1RoTLpm$u/pXT@'?_OO1j*A=23]WZzc-,EmsC
    -HHVT@m*Kxv#p2][5\qC=aATneO-V@QuXo#;}>3F5@*;DIA<-'V]_[wJ4-nKl(~}\Bn^Ozx@u$_u
    ]V^UHW!DRU=-oIR@]+,?p[QWlUu1\xxZ<^w5vWB4w>!=,kUk@YEYD>Da'YKGZEwW3s=v1#aJBKw;
    J7pw@HaQzu!Ws7k=mX~-2G[G}W{ax]DG}AVip_\+NLlYSo<2s}A;ZjR[2<5j\HzwZE~ws*z{-C,z
    X#CC!_cmRJHj}M+Up<,O!=I7T7\AQ=<<lG2sAY,#o[jI#u^==?v}kYznO^0iO2e{_YX7>weN#1>=
    zE]CgI'jW9(o;YsO^mT*_J<CJlO"__.rAAk=E?B>^R{3C{nYm>a}os]a1D=np;VpKVV*2<^WG;[_
    z~O}Hp^*>1~Dk@~DEC+TGe*oV!KqfE#{$SF\b']2lv'WY5A_E5,WI81AJw?7a1j^=\_@C1Ew12C)
    |n7$~r}[DVUjH{AIW}r7RV5,-*<>jjKCC?ArTO}C{gXEiJ=kUHflvEJ7p<kEBmp7eV'7w_Iz'3<X
    o-Eyps]apmz5'#QZQ'?,5YQcnUZ[75J{Bs[xkE3V-QUkOe[j'/R}Z'+v\7tnq;_Es~1;^E~K{:KE
    }'$W~IN{HQKFjZr35AH3YO<#C2I1KOkBqQU<3=EHW^rDp@*5I_\w$A^nYzWjHNkY_BO\3~^vA23+
    QoTGD!Y2[}UVu_X.Nsn\o>*EYBBXR2YiHNro!V1w]1\]ZYH7>'OBumHjLYkeHY>XQI-3rQGw]=,5
    loQ@WF;7$ACgE*Kolm\Qj^I#B5jrLX'z{-[vralQ?IEu1#I+GCn~Z15\Z3BYmj|lWnTI!>^J5j2O
    o}!@_sv?YC]+T<#DuCjI@~+j~Qp>x+3'>~!}K!*K}ZJnT>X7HTD~^iOyK{Tn$n_3'*3KlwY@kEj^
    $B[e$_-'@]=~'iziXGV<G#7]_$O=2vR*u+3[uYz[$T5O1?T+!7!_sr!Q0$?j$G'UY7R[wm]RrTIT
    vxJ'pN-ajE[kzYRwpQK=^Qnv@,NT^[}*2Xkm5w@Rz@[wnj<VAJ@i,uIk^7_lvRK<'O]"Q^B<Y3;~
    7sa[d$j+lAO{<T7onx~u,2&D'rDEXzZ=<w<CZjBlY!D[,HHsK1JIk_VU[!lzo$j^Qmn3\D1tR3-+
    Iu}rK>$7!+J}=\A*ln@wJrx-1smJ*<_-*ImXA[_,UsRnC!jBV3^!$eX{}BHT~+-sB25Wr!QB}l>~
    +\3D*apW]OXn,BC$H5Zl?O\\*;nlL@1j*IkDR@wx=;a]O3Q?;-_-,Y?]2E#rV{1ErD{A{DaT;GtG
    V\wbC[l='2nY;RkX0'GTKvJo@$u3\QYQk7Dza*Pr_~+cj8}RO?TV_{7_uVQj3]^{jxB7C+-\^WYJ
    Y@7p~-Xa77GrG!_}uj,1iBU,VRBBx=I!'G!ID'i,+-_ZA?x}>K/[BUA<H1T]xp[uCrV$3BO,Y,C7
    ]vcU73o>z^'MUr(r;u3Q,m_+r#rpm@7#Rw,#GZjBGknl=xIY2=Vf1H5!zx@o$a^?l2'?s#QohO<*
    A[<r$&gWT;7*1k'o\#U]|!ID[FX-XTjslTB[$G]^*@$>^k=okVi*V]EZW~#7Q@AAukp*?H+\VYN^
    Y=[->nG;,Ozp@O*{HzEG'C3{v1xh8np+Te*#$25R>uXWA_}{\&!RA~j7R^1pom]Vwme^TTI$s*w-
    ^\'UpkK8ZE]{YB}!p}piKoj!orW{2Uvrs<]#=\=3QUEjY!slXD_=fR+WXowX3=x{JWo~TW}eks*1
    =Vi^?Oa~$+}DH,V=ohj2[rasI,~w!YUIE~#R!U,loYx<pi*}+7Ezn@HRZ7DBi2$nFS[DeQ@UXnmn
    @lUB]Hx-A-}k+a/OO;EEQ\e9CljuGnQ$p|x\${il3<\j73\le3sTT2pkC#Izx1"zoT~zBTW@CREY
    j+QL.j22HOJm!kQe3jx@IO=i}Temw1u$XzWCnzv}G,T7<XwQAvZ',*YiYpW}n&e=11}-D5r@V^?a
    1~$,i[o@-;r=5l$]RQ1e_=Ya_Bz]<pxHIu|W-TzQ~1_$5ZQ$OEz5^p?w[vWQ\!enYioT}]mk<U~i
    1X@xG@ETrARrJA>>[D^]X,G,7iu[5TmWT+D5uT~zIu}ulD@TaA$e<@TCTWV^*JE{7EoXY*uxUH72
    Bko;lol,=G3j-1[sK2D=IaJkrm<'>7z2O*Y]weGx!B;sE~QZ<7,baH,z?YY<QYPk5,,liAD8L]=H
    ^<,k+*X=@V-R?KV2l^TQ29{*>Hsjs^8+p?!rR\=2<UJlwJ1-p_Wvo!H[]QW_WW{VVV\&4m}pi]Hu
    C$Q!?vJZvBJjjQH\7G1}re_5QL.x}Cll]Y'kQpej~H<1Q~K+<2A*^7{?AXp%~5J@pZIl[X$+I_oK
    emKk\$xw(#Az~vlHVeWa!olV^p+=JX}iQol@-~BE3W7}>DuYCrW7#9hJAR;sTpW;}VRT1^7irY+~
    RDH1@D++t$/^v7KgC>Va]ZUATjIaI$]*\2_Qp:O,,oEsim[1J;t4i}r>\a_V!_YnSO1l54>D}7GB
    )=(<aajDF)[HDu5jDl_snJ*7#32,vC>OlGy\UDXHjYADm$^=,1JEjlB_$E?7j,~!Ip'h@$1DBT=Y
    O+Qw>eKnd+VjaD]ZYQvvVe~JRWY7pe,k#Yx[$3GJ2ZETBG2Qv?B^$bpo1l*-ZRkpDBT^umKp=xTB
    ~*3'a=IUAH#7aRUOpYwSEEn[M{zm<$BBv[\mmTD7rJe-uVHz,*C^+.\x3Dx^G_O<W!O\*@JQnO5'
    z\BG',mQH@v+R[QnD_*{YY:x^*kADX?$iHCVxiT3wp!caxi{aLUU;jX_k}x?x-^WD{A>}ER^\?G<
    =jz;X>-7\Uj<aRZa]k;7?2ds_Y^k_i~-5a<}+$enjZOWA;]nO-jl<UuR'SH[_1ru'uHEWJ<rKKC;
    WuhWnT7ip1ChK$ja-o$5C^[>\s=ZE*Hujrpj2H,Xlz_na^>A#CC*A$s\$-2=$5>H'G+oyQ[xxr\T
    kVE!WI?D;*ZmQ1]@;{lm=x=\e=<!5uvuRj}5@x]-aQB7+?v'KlYoW8[ilBK_CRq{,oCzGHTBe~YZ
    $j@=#[Hnl*#&@+j#ZIAX,#{pKjORR]}}*'13QlOT]m$v2\n*hxO5j/YW=v55e@ZqguA\Ul;>@!15
    l!Qw#>BH1isw#BnV2ujTvji>^*E2D=rXX}kO@Ko*nJwpB+*nJZX=!{E?!nvH?uGUBc#$\,vU+!C2
    \R2{vair==WlQIflX]Z[v#T[D$QN~zpOhy2UjC*{U_Bpo5FrJV?Qkw^u=HJlXr}rUH2mD7',H~\l
    QBsOsp7\[mr'VZYZo#r'*@Z>$5=YzTp6JE<wQY^QEnmWr]k?Z^Y,3oEiQ\<GRizGp5[J'majB2-5
    neE>9'-~]ATrlBk,}WwZUJ-CKPrD<>'O1WV_}>XsYzt3_1r3EO'OWl~}^s?l'nsDrxVknEr.jo<z
    =<3m}~*=3HBrlHKnJ[k2#T;TBOR{-wx=w$rTI$C,BK]KTlo$BwD3-j#k{,21i=w]z~Z,$7wAJGO]
    m7JvCiGQB1oIn{1@]<U-Gsl{57T]'~Q$HCGH+}{~Ds[l[QE;kU\m-1[u<C-eS-{U{e{U3S1UJDrU
    7!qN\!vON=}_\eWD~+],HV_#j$_m!?n<EGG*okwjsB[]+Zp^a\MHY>EPcplo1<rUl]-Xrv_+W(Hl
    'US1paDjxp,C$Qup$-=-GEm!+UnR#7_I!j@v?xr<XwRIOTY2v+'\{aQGs7?VJ]+0z]WRVQ1ZB?~@
    8V3~$J5,p$]oE6_DxW<QWJ6,*VsW^#KT,}UeQC\)NUY<Je!IuxnTGf*u}xrY]$pz-$rzJ<O#7l?-
    I7'>j#1''$xY5eVWa5jaw<$ZKzo8x7nx<rOTzwR[:A72-:*piE!7k2<sD]RJo'A.<o*i@^W>OIa]
    QA1TGg}!5sHBp#4^lpl5[i7QaA-Qo3A_B{[p{7ile;umAYQ{_\CB1w7JDs;XXp-eYHEi*;G+IYGw
    Y<DqvU~B#YkYV-us9mTeRoVr;x?P1ICn[3s^;w{->H-?c6{lYA0G(c5U+Y?=pzp,lz[J$_Zsezn>
    7'e_JlRsllp-vGKIKHH$_rP7A@<IQ!\mB\n*3V3H'[HVWj=@G!+{T;aoK_aX1,l$*>ZE<A?Y7{2^
    'RQRw~u@I+zK>$AkEl5EzuppHvrO55Xoi2Er@n_]A]uzk-KY{KlBim,upEI5O>z7a-u{x;u-GIii
    p^ByypJ<Hk7@7#GBmoiI{,}]?;Q1C,3mn{EB?}BTQo,}Y@\OW!}T+p]+<fA*\X4Y[YJNvR=m~TnY
    7{@TMVYJ__'{,?C@2[l*W^IY{=g{ER}Vl3wc!*IU7!jrz*~[BRJABR]uJr+kA\;'i$=iUU}~*Y,#
    E-^mY*Zl{AJmDj1ZlG!YJxjDvpXzv?\ZN3RY?Dl,Rv{C[2O=WZ*{o/V\#zzY>+'WD>zCTlskRE{V
    l!}+G,,~poa\TrY]W?XU@<5HD*5=HlUz?^[lKOD{H7~=B\E$ra+^H^17']5@2j\3E[*zTpJ]zx)?
    zjkj}Bml}*pwrnZ55vos#>R,w!-YURzV7;Z$2xrIC$xQkADMKn{Gj7Cr)v?-$oUoG,1QRwRYBEwI
    +QkxB8r<>Eszo'pY}zDYI<H\R>OjW1DVsWQ}]~55JH?&'Zask_nmlnK{*ol3~=CZ7]run.v"C>DT
    z6[XjZs11$!5u72X*2^ZjKl~UzAQ+'E=kRvvl<#VBz7[vkflempn7nRaBliB,U{pEw;-5#TG%uBv
    u>\?,3],uDIms-NwUuelVBvGD=<T7@Xj[jH;o537zVsU(*r[eIiB_Y{n@p1p_olzx>^TC^W<]J_\
    _*Az[Rpo@>A+oXnCj-Rr*ZQ!15WnWorjO,i*]p3=W3saBK8j,jQTBr^|#7T\KC\*%'7*QN~nvJ'*
    7rN=_{~Br{V#D5m}lipH<ce<n]m_K]io{;{$VTh9D!\3G=ZA[Wnm3p5GrIK]xQ_\\#>GtZ\#lB$k
    GZ52Ep2Z[<ExJ1s5$s*Oo,lsxTE=_^z[lvTBGm>w]71TTe}wxIXulfK[EoYaY*QaI$^E$lwC7?__
    I;5av*7@G]0,#p33'\a'I55AVKa3oC!8zp>~2H}#!RDz7{zC<]+zI5wx>zOoAz+'C3pVa_<r[e<!
    5ll@![2~2=mv?^Jzz]z>HU-E3j!^\YuT{OW~?j5C-5YZAo3?}o#3D#{;<,[;Hw+a~pzV!lO3(BGE
    @2>p+g<<OHln'!i$--;z?QP7$Gv(IJEe]5<,->@Er[n#2piBjKs!3euAL=1,24Zj_sHr]*k^YU5}
    7m]_],@C'_jGioJ^}zE5E^>xVa}{Y7G?ZojRJkkp$HzzowLw+reCe#-BK*a_Uu@[d5;n_wI>lR3l
    ,z'?7?-rkge=J[JYn>p@+Dow~sv#pxp1l-X=Z$H.x-<'LlY'x,KCBpYY]W}#e5\3<5g_;DQwz!uu
    s,zYs!UAOQ!;+^,Cu-CrOwmO*_@g?5}xLXspxT-Y2!}Hv$_kVoK1p]+1Bv-;,rT^+Z12_mo*URXw
    ]p[n<DkZBCz<7=-Z?me-z~okuoY*n~Yl,*7nRh#aol[nmEgtM-<-~VJ@T,Z7${T>lpi*\[v,*:se
    JzKCn_rmoC_^*7JTvOv:LVJv3pQ{sez;-'*U{]{>eUU_rAH-{]XO]Nps]T]fG$QTNOsOTR>\l>Oo
    #RJ'E=I;Wh{}[ZC-[QO;e?rO<]}OwQUUG1|Uz{\IY\@YC,u{Hp'@AE\^7*nx#ll$iI_lC?Ge<Y[,
    ZRBx~m-wnE?sK=-{RW[Ev<zw<*;yY1~v=^m]WV3sC{A,Yu'Y@'x;^\Zv>Er_QVTx&K>n#%/I?o1E
    HUOAv$EEiwJQwEYEA7axjRYaEv[$A5<!=VGI'O3@VGuQ[J3><viCDA~rsQ'R~Gkn^Hzj5-D\W}T5
    T<U#rUnX'mpkVDI{_O<TswJ7Bv_>17~AIu{GEBkkRQQ-O1T^,G3,\lI)-]Y!~vQkw{HuVX<5zT]L
    Y=73r!DsYTmu]}'Z=l_UGi*,<oYOrH\+lBQlY1.zTWE!<~_J''};Ow-]YQW2O?[<^@u=pZwZwJpm
    a;EcQVGA]pV$G'$]a[B+j~m7r'i>CGia@A7##$rx&A5H+Z}~7Hpz5'_H$_f!757=^O#-j5x{a$__
    UTKO,iW^Os@-jm5<'5~Q\vo^1{-Llb7B[I{+l1+}fk[ZIcYmrp7Io3r7Umv2H\eDI=C~ruzus,2*
    mw>-X2Y5T]}@<DaRaY1Cz@oJt"-\j1:@\Ek]#<UaXI;@oXKIo~2xp{2e^usx,Im3R-#wOE@Hl,~$
    @m[5z{YZIkzl_m,7[pK'Upov_@u#I#Wo<l?e1a*-I!D;Y>H8wBROG#IA5jA?e1jXk}Wp,@]D=kKK
    -5CEr7p2Qi@Ts?1Ta]nn^5H*>E^w]5'O>Up$@-z[H}rOvfcn$Ku}3x7W>InyRk,2J]lEB~V2E1}W
    C@B!l>~rpnvaL,ez{rk;e!]Eo'D@$*+7kzK3oZU[Iw77U\U*oVTUKDV{^,5-@^eG5zY',j1>~hp3
    IJWri1wY_$XExX$;5@Kp_RG72lO5wOzWD$15~jp;OuKBJpY7J@nwHvV<uC|HDDCh^?1~K11lk7sJ
    Vj!}aXrvX^RZpZD1r<nzV^HUGnBZ^;*5a+^IYVZ]1]ln]j2~@,w!$WxGO7$GzRaVJ}pV>raYV3R3
    +7olb$mJ}>aB7uQ${QiAOua^})*}=CQW3=?{<e~_oEQk[+<\AJr&li^JpW<,5^V7zuA$oviEQswC
    I;*TiB,XCIj5!H]T;^zQsWRx?jlm$7E2'}XH~CV#'~BQ?\^~VZjkxJ7{I]/r[evjuux;aV{x&\EQ
    ~[Cv}II^?*\2muw@<IAYaYt\G={,_;R12~s,l*B'uW<'W~?m]vGj'HU[=lZ27Zof5nEr$Oz-|ian
    T[S2Izj1_xH--au#oe;Ve]mLp\JU^+T+DNZ^sI3exrD*2zgQ><{KL1m[*xuUG[>m@G,xe?$TWU7~
    ~eKUDABj>m+ol^aH1B?@DGHVIaOERVip<}Q;zn,-U~D?jDD3j#}RKIv,[Bn<J{}-e7A*G;1TGpA~
    pc=xs,Y7H<7s[;Yru2DC?5H<-'"\;<70K<<I-H,VC?3>nA;mG;wvgQ<Q?!enperT\'WVA*]=!+51
    ?@Y7rMnqr>~-M5B3^-R\neWnT}?5!h*h~GDkrK2us12[4-,loxleTRX=oZ[TOf<aGi>D2YRkrUSw
    z+5e*,D^i7#QJ~J\U\+vZn7-E\3DpH]'Dz!O1jT,l+?a*n=a[o>m>z32H+Z|Q?VUp^-];1JE&CKT
    ~Msf,~$^q<AX[X'Y]7o]Talu2g6QlarR{!@}3E2#Bsi?r[Y[avpergI2~K'#VoD*x]$C@]w[@pe1
    j<],3'sa<KIv@~p@=#aEe2O*r]+^-Y;$-~K-$W5_QEq|$GJpn+z_+R!w3lGiKBr]0EwrX8'jAsv>
    nAx=<+G=jRQ=5K;1!!*Gn5]!A'[7\i6mpw}j3=77o#'QoiTG=\n~XIG
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#-xoO;spx,'DlUpne1II,5#@Q[{}E~-sF"=~Yihw^7[oVW}<V'-kn!['3A<[*+,^_x+ODWmz
    QWO4X[+2U{){Ck;G$k\s{ErsSKwJUz^;};<Vnzx#XX{\V}jaY@vpp7_CH[,AlYnOiY]iV%>5RnK5
    #a(oma+Briarvpo[_uXcE_\@7~Dn5Ymr^l2pixjB"RCBm$cKCO}c2n;uUn!#+vU7eUAuW],["KDK
    [$5uOo~H=R0#lA;u{B1W[<_#al#AY+A[Za;A]xz7;n<an^OTH1{yNDZYuEv]VQ3x*!lVnCX[\B3n
    BI}s^R__#5'3]Q]-Qvb}?Q@~'JjszskX5B[QlD@\x}WXE+7o~}A{RB~]o@X.1J]x1e5j:k,;Cx5m
    @3--Zfo7OeNH*K3UG{e,>]=!X^z6>v<uG=RJtsBi}BQAD.[J;C=j[OWGv?'mDR-p3sUU'w1CUwIi
    C=v?OnH[ks5KZ[a1IX+I?s37ReW+WAJOmxC={X<=aui]aE.2=BB_7Yp,p+Yv^RaEB]<1<VjB^^*H
    oZ>R+JRg#1Q?Na7*;-DaIxuH*:T$>*p2$JKzK_ol^1r?XJZYUE;{n2${Zn};KZ,m3[[wv2/aw+BV
    n*;Q_IujtV?}1ZvI2}WTK*UG~Kx'GpU=WDrCka+zBe']TIWj@zOJCmYrj4C3TsKrZp6rwO2oOTeo
    ;=<AQD[^p=2ZE7k7>mU/E{xCAjKUgmYro',o~={7kGUnas>Q}p-'eBT=e{oTn3}~v.rQ$xHH*?OC
    kAR5inEHHzp^[,[CB>BK;'vOJK5,l*FSVuJoSd{8_=evBF<HZV=xwY:zK=E_l2G[1AISCEk@[C&O
    +,^vWW{'j[!Wvi]%-+@l:R<j@ED_;6D!m<'r~sCo+{x~><n>$;7u1}R]-35Y=p2NopO<5~po[B?*
    |B=Vz,nDk:E/#Vx_e,$R:B?oz~Uoes#~=YOBpK[ee3w<\?};e=Q2{MZw{CTvKegx;Qw'x7RK^uUn
    YZQLw\5KsH~H{sA5xwu$iTr[:3z*_R-KW%>5M;h_#GakjC7i7i-%s,ko6.&z\A^[un-~TV~G@D_r
    +Q\t-={<$\H*)IiD$.|9jj&xXj@GpGU2=l~mvmT',suVV$,=@_m3>!v\vwA$EDR^ZYiRV\uP|eOs
    +{[pW*j2m[#l?[$^[ew'nCs*?]ljkDnJ-*<V>ZQko/wr=rf~G7@5='XiY5r{^>QUG~EG_l1*EvG<
    R]*B7[7?<=H>nCR=pu?RJ+T55KJIwQK]Q!3GezI}kj#}!}7l\wQV?n-FlCIp>s,{7XV}\={pck5I
    s>G!eT=<{jZEwpeGC8.MIDGT^>^$~v>A2'GBDOln2E\!<BxR=3l^^31@FqzE'K(T]jeOIZX7sVO}
    z<,c)JX<Z;]DEM=TEmeG>rSHw'pE;{Cr^r[E=p{C[eO#,C-a'Bx2v!u\msknO!Y=@HYsc=2-CT\w
    !\~r?1sJDHXU_a-5R(PI3}+2Q5HAY#W*}r{I5e\Q}#.-_vv,O5aB#=?*l@Zs^XBarj_:m5K'7x^#
    $a[+/euJk-^\?)_KsVn]TwOn>#v=7VN3R}!!Ew#va<{5v{<'33^aYeD=/Q*DEeaHA-$<>}WQQG>{
    _:rxIx{+O+W{neTQ3^:pnXBuekV7}=-<7Zk@>ZOBkTOHUzk5*oT?+T~v;soR~Jm2jUwAV3o*K1{y
    VZ;I+=*T+Bx}ZOWpZ6>e{v\$@ookeC,H-zp{@zvKYpa5T+;R{Q$zVKa=K]WD?m#U7nJRXo?aC@vX
    's>[}Yf7lEa$$<R,x[DG~$2ls[@=JHGV_s{T]O^}x+#DyP5ZH{rwVu[$Z<A7Qu3aTTwBrn<s;^;p
    <$DYZ[F2Vp@Kp]oJ1r{X(gN_5lzavzC{>EI[]p1'1}oG}#BYaZ1r3[~E<DiITOCI+VTYHEX}Rm*V
    +p,\FI]~m}E,T0<^V$*51JOG3>wR<@1=GoH'z]Cv#7T^\<>VE5EU;=VUEJaUwB@+j;Vm$YE-[@K*
    E~!CaA<aB>$]}7oR3Z^7YuF7+^iaEQwB?pJYCWV~p=_BA~OQ,-#?nBxelVB6V$Dj@<z'eZj,c^<R
    ^rWeKdQm~aj1XA]!}JYXTruTRz*C=$gw{Z@X]J71?Q7,3BQHn{2!+o?@CQ$\7UH!vI#VH-#|Y?JQ
    ?V'uzWA+27e2r}T{m,El2<Cp{VRDa>-_oe^57!\oXa^~7+2Kaa1KG$3{%YYX[5Y~'3aX<2|x*#E~
    U7<7P4R'O=U5lGWOWkl2e5A_/$Y,s_Q7pNdYm\^9TA3\<B1CL{vO?Q;~vI^dvQ~@7wJIHD%R$j]+
    D\i<EWsc5{lY^pp]Oj=zQHnu_My}&kVsiW*viAG+$r--_2[5p/w7$,C[WR][UormzuID@^^Ndl[]
    Cn{*iwO^@YoTa,#Jwx5REvlpu5zHUYJHmCmDCzA,@+>nz"M=~j[xO7>&z@W,N'A\2{UYU^nZ+cYC
    ws$kV]}wj@y<aAWBj#x[rZZB-w^Z'Zo?a-^~5+-aj;ofs}k##>mmIZe_KQvxk1Ip'o{#;C[n$jXB
    HnV]-R=\{.*v2Q\GjUk]AmR!oAGEsYl<xv~Bl?5oT@OxlG'+BlYioOpEvTUsE>G>loRz~v3$R}rT
    T$DCZsH[#^mVe<<AIJO#G^.]XemN{EoH6BW3_eT[^-+[_sesw~\QK~DooVY?A1uAwo#2]knJnK1D
    1<rBv?wp1wp^'};j_+w3xY)G[xuv?lss!jArDoxq*_GlI1Y'$zGu_o?A&Q5~K,im?,WJCn\JIOQU
    \,+{DiEi=jZQ7x!Ir^*3*$W\[To]iV>2[(}p<Br}72]]r7,s--h"Y}s=JER{']pHE4+-{JJD-WB@
    TwKvK!=>mGQkz!l<-TYUTk2lI@6;={e3l+xY1EB,;-{9z53#xXQ\D2r_K1kar3-+6}+mlGinKj5,
    Vr^CjG<,{n^EK@5+m~=Bs*Ru[AXnjKCJ*6r7x3-Ul#elX$$ZH#h3X]w,eaW^U>Ep-_jI3[2l?Y;a
    [v?Y^n7\I-5ACCO?O<Zr-!}?x<5*GQWqvTKXR,e^Ipe}z=e}1^Y@wA352oKz.*Gm!aEO3[CQ~?'J
    3N]{~D@H3}>^YCB2Ymp~Ql>'kZQkj>;5wQ+wVYd}OvGVzH{>V,rvu!Z^8ZvA1K1@Dr{1;z#eKl$@
    {1[;HT=er}Z-s2_5n2e$k3swYmCCw@Dj<{Q?s+[XUj2>aYJsXk]#5h#OEjaY\i>jHOdB'>wXVY<Y
    OZz\}YH=X5Ws!*@}<1sK{-B[xUZl$=^uj,'^,<!1#R'C5+mVBji2[!wXETVj#oW9+IuX@EY@:EeT
    =la71@nKxlCWXYke\K7*,HQmsVkU=+*~7q^Z^$uHj_xz\Yokjx7aO!2^G'Q<7<yR,}un}Dr5n<p:
    %HQDvkem!pUXBe-G!jQ5[^~$u@r{5wYE@)_Cr>YH$W[;,BHax~e#ErxEHsrpspz?3C4An!#$j}?3
    'W<,-\-XE{T^DJYj2pGF&JDsCWRT~!57GlkODGo-+I-<'f5aX<E?Xmv>w<lYiY1pQw1TEWHInX_7
    T$-RQ2=$'jHx]5_[{'r#WjV@wUq;R$k_YIi'.\,#}IJ7rz}]CmY!]WaRv\K'aq$zaOTG]Ue_H]y_
    2AV$u^*g"RRw]$u,+Z+52Uxmvo1,{I*v^O_>ROivw-CrBXRljj]*rG"jX-*?>pRUElrUH<~/*q7B
    ??#\$[^Wl{]nvl']R>sKIm\x^GNM^@Giiojr$iQpf|FVV?CevC$Y?BwgAOW[GJaOl-\}5T;r#GOp
    ~UB1\^k25<7o*U{>1EIDHx7o>-AJs\ZE1T~Qx1,U!j#~epZ]_~7CF'eu1:#Qau-*Qo^RA#QVXOHA
    $ZR~joW<ewYCa\YBuuOj7Qe^3#1ARlkaTJKRv}$[TwTO}{@U>B@xv3vjwmC{->x#1ByO[13G*7]R
    vOHo_^lZ$@7goT\j}Q@>@Gw5WnV7.j;\ay#1##x\}C/^z,Uo#Xe-r?R3De2;emB/kvOGGI<K\jr3
    _2VHn8TlAek^jG5=9*H!wq@skjmBaTDm[lO2^m4UskWXnE}+7v#kxaC${\#>j3oJqyYj<sLD\=YB
    ;z!!,in?TU7;Ew+,RuTe>737kQ_Q1Kj*lU*e=V@Nln'z&QECR!*5w@Yme@,j!-xBwmAEeBJ3EAXn
    *:kBYG['kvDj1eVOYJQ-QQyv>Be,pO^spK{Jo^\,we2ZXTU&G=r]CaZ@WY@HC*>j^[a1n_m['kE!
    I!YD]uAJ}lxCDoawF-CGoli2o\~!orERw<Ea\pw}!QvD[5=Hsoe2xUDA{CXe$IA3EOu=uYXri!_p
    uF9W]v@~'*i]{D-m>p*;T,#lBx>eznx$kKDCX1nsZ*@}vO*}aAUX1YVwYQ!ZV,=Ds7<7~O+?_5wT
    q>RYagO$?3(}z\v3vV=oB5Z31ZzT[YvZXo5JHTpD3,!G3_+4#C-Aa^K'Iw![k-G1aCC*Y$*Hl#B*
    YyTjXVHAxV[^i~_UBja<,+I_8}I@Z^Q>Em>OV4\e~@C=j^*>@H#*xkuv[iD$}="oW@sUaZa55IRy
    [#l*g=2s?)Y[H'/Q[UX_Z<nlJo<kliQ'z!^Q>Q{uDua}uAoI,sAJvKI.A<TnmU!2+OREL0DuR\3X
    JrwX[j3EX<rk1_lHDpW>3$;w~Wfh[saX\J-@-YE'/;IkU8AX$l,[+Wr>$op2$a*e[_r[-+$#>Y?*
    ^iK,5@V3EG!\aQXz#Qo@w{,p5=-w>Hi_;2@B-ppK;CnvIeOlX=5rl51a~HT]aJ=B<JRG*?Q1$lyY
    j]#7u!+p1T3UE*nA-]uEe$\[-Cm|WxXkxr?BG1o*_{ajjl{urG5Ulv!I/KXm23su[D'~zDlFE,B}
    #TQe\Y7_i-w~5{$VPG>=Vs>x1x[w_l]Zp_E$^YDT3\XDTXBj>O~!Hf?Xrin88$r{DRCXB!5Q*^*E
    ,AEpnm_,D)Z_I'@>^*mTEVQpm*[>I\u}uzBTGs{I^\KVzw(3,+_Q'Vl-s'>]2z~KG7G~{*x^srl$
    ?{vr(wezk\vO1j;<yB+QWnExpmUa39L73+27@~3^=oiDx~Aes!eRwAlE{o<IT,}9fEA17msiC'O@
    D|t!zREO,EZvvIVBkIl"K{ps2jmT#A'JArDe]Ju<k>azz]Q->7,;CT,Tj{loz;VmBJ[k-n{Jm_#v
    uC,@.j$,<>OWJ,xx5m[AjD1x\$;Q5>Q{Owx?rH<p<jBwsgUO@C]EYYCan#V>D@G*}DN3LQ2WZs[[
    snsXZk5ZZ:BXp]S\m^k<[j-@TlZW=KzWQTj,z2]MU7Q+ECW*?BWk8Yqr|RO}u!nAViQxU<>I^#wA
    37Hol/{U@T!V@Y-7J>GDJmRw1i>1-]Gej[5Bl{lvp{YJI*7$;@$Vr{$xRwiI^n:GCiXeZsz[Z!uO
    w']v$)e={*si~H=#2O~Ej1e{pYK[BZ~pO\_{,vBKB')j~;e*V<Tz[>ll{w7]xC-~7z[^K_r*O^]Z
    7X7in^~R\'w*~WzK7z>|!REKK7{u!}CC{VsQjZD3roa^X>B+$=nEWG~<fOn^-<CTk@pi-_G#Z1@+
    aI~>BT[rn,/zs{_Y'U{0\YV~Kj*DrCsw1,?},<5ioUmBBRQ\KVZwOv*$hY^RD*C~*|,xpIT[?AlE
    #jEp2Xyn\D{]eU[=nGo>=+,s-p{_KjUAG^!aH3!5vluOlJ~W<!A['YZ_EeW_m,Ut5$VBG5XV^;nI
    ^Lp2_s#a*YRl<C#GCsTQ{B=BusOxY$ArG;*YO_5nalmw3nW}DzlY>kknpBmTC~1-eO4XlmD-C!{A
    j2V6~^GXi-]X[I{<@[VZ17KTTj1kQJO-*H^Z.AoUYs6wa-sIksIPb?B<@RdK{{rJ,V?,+[-RUsWA
    5<vziTV$EA$)T\D\w+TRx~KRW*<nk5Tekwj@B+;@oi]]e3'E\\Zuo$1BVixnxTo,co$X5.[=\z#$
    _}<EP}^O^Mo,QW#B,ve}sx$D!QpHVA&'RjG)u$QU,\DiaQp,[R-r]OJ}_*}Zta-l*]Xp!WVVuTY$
    u,j-arOG'C<au}$YWCB},Y[jr\dBw~jGo}'4JQYp_5EuBGs]<VZ!]A[Uf;-X,u,,IIGmr?D~G^G#
    DXIDm'^GG1IB<FX$?~=-DW@n{XiX_nOr7>v35s-zxl?T+Gk<O<jX$K-*V;zAGX''=I3{X=$uuC]\
    ?rv;R\5j@Cx=\Y/~E7=2V^$]_xC@U']EwXHQ<5s][QCW-3aH=V*Op_v31ka@G#VfpW1=C{}eu{vC
    R=I+woX_z!7rv,G-[{3H1H~Y\A+W=-!u3'^^l<-7z2X@jV{EizV[I?$XjkV~#pD^EO,OGzv3[p$r
    **@{W^k=rRauQi\BX'XQ6I5Y287@nez3jO(Y_#sCXZjCl,~wI_rxWG74i,H+:0j+DJ:}sRs"3$B^
    ]7;!rQonk5lw#Bz-}[mO=UQ{E^OVKr$]>HI1~^@APRBZBvrvOH^7B,p'=3XJJz,_kxV*xG[p7cCA
    rDIB}J2Ts>ti'wO!xn[pe!>2>!junZwv#oR<\!Z:O5*#~5,Hx32{dMCUI}JLDUp@s^>TQ?K78evX
    z>R+{$Hj}ARO]JRH=w|vHn7*xE1;7IpQ}^wmH^vWI-1IB*D2lzuvK<*Vok_@'Bw{7lOIAmj'T';O
    -3]3R'Hy;1;=HHp1@1VB@7lJ^:wBCewD\3=k@H6,+vaVK=n_eT#p2E-e%w{OJz5}5ol*eU\,!^zT
    ~,$X\D}VOVl+<<<~@ZGrJDsU;ODwu_Tj;_rok1v[TKrjw'1=nksk'-{wDwOpCYpwx?_2?RO7G,J1
    l>5$#mpA{Yjmj!';AOX{W2lOn3V]!#-GZ,vIK#1Iut7QDvnI$'\V5{,T=,3-Aw9U(lz#w@zH!Zx~
    r-R{aj@~HTO7rkX_2C?o#I3aBQulK\lJ$QsK=ynAl1DDvW]K!nwYkpo#-[uzlw@HGUbRU;Xi{ZeJ
    nX[$Wre7Z-XjX5u\Hr!,Iz#sU]TVJ2-2CjH[w\]Va}[]5uZ#<zxOHm[ECu]MKoY@a7[vQmY-+<J<
    W^D59VUV7O23!(*aV#1{<VJI#k11!JxZa2UEUKWl77'wG''B,]U-3X]*1<e{~'k}R[B)13Z~~OkW
    J}]3#{QDl27x%^HQ7-e*m7lp=$R,GD>z]pVoD!waJUo\T$QrW^}11P5+I!_kjaIQTuAwei$uG5$G
    {T~a$az>~e,+1@l~zTDj<e=5z+GWGuj,~YVY\;A\Kv',vm>CK_nx$~];KBkoV39dJxwnZsi=2+=J
    Ar;@oOa[GGGZBKeVI!w@#^mRW_s\:Besi$^3*srw=$ZeU|xDx*3-vj3'3*QCv^3v*I.e+5k\K;<)
    },DK$kQkBD1WH15\7,\QA^1Ye'QntDm57\uEv*UnTB_G~EoG<B~<ReO!+@eu\CK{ZH]5x!R--5Z1
    ]l#[Q$;HuCjCo5<YYB-B3v<^'{ja~'=K}EOz'm1w\nz+;Xn.r5@<<o$AUB}BO}Re>s\A:+T,u@1^
    =,57CXo+]*=v{#VI[FOE;5aR3[penauDYxko5_$-I7k{IIW,DCoo-2<w\'u-{H-5Q#C.2E_Wkn>]
    zEZCWp-$kXu?E+3;B5WpGzo2zCYGe!^!=Dm1TjQ-7JvHpEK*6UE7ZnITG)~Cpuv^x5UrBr8EAO$c
    5C{mC[oj?nHH81p}*\1G'?nQK21k2%A-!5lsR77$W,'{nHyVoXsn-I#^pn='WseBr@IXIIl$Tuj*
    Z^xlU23,YXJp#Ukw,1uQ+VGi}-Rd+>>]a7Rw5#~C;B+Wo$w]^;@[bA__vlX{_BXeZHBB1GTIveTK
    T0]\kkKI}wj({$?-aDe#XYj{'ZD*!,J]u=mUTB\]wXTBLX7@U<jx3^pa2Z{pp1vU$Ww2[f-wZa=H
    I!Z11eU+5z?{sCD5e[Mx-vI{+JvZ<o5eZ'EuXC$-Uw'K\vJ1*j@O7jo@=~CQrRezAHX>[$Yw|sEa
    X:2$@$'rr7D*UHT\G36#e1@n_[uQVi^2+Q^rCe=','CCa,JM-*;?y)@{jup,?!OpOoT*H!>XKp]Q
    -QilsmXnTwaaCnnOTDKTQ{r@C;RZYYIReJ~p5Y^7kC$m=i*@*aBi;l.juDz|*X-sBE=lao]?x^,a
    !rv2Qw=H$@CViY~E&xDexpA3KgD'\m1G7W*V$VVevIXD?O%z_[TC1]TzTppHVr_j^ADv+nB6R>U^
    ]B@x#{\\<\IQY,$W+T@V}Bw+;<TK,T$uipHJBj_rzRlYmUjkZsuoK7I!,\s$w[Z\_2I!~$5JI_l;
    +7'E?C>7T72}$7[E{R3[qSKvro,s\$neX;YrH5}*li\X=}=X}J7m7@oU3o^{U'~EuDZIkevUansC
    ERXYKee],YD;O??*3^}wm-2-KJe,7u?-Q,jZv~fUv]*?*\ExG-X+a=!{'}jIxwuU>CAGjT<vK7p1
    CmX,g,~w#?Url'kxIB;~zas^[Ij?v-]p^wpQZ%p#Q\eJ2]j~J-w7wRwn;kpJ-QYTjn@pHaB$A$s,
    nR@HWvu]U\sR[@;R$JD_eAx}CxZ1lkJ>7HMpuzHWX7Xr_kpV.5R1oQ$?uT]GBhLBj2T?UAGkV!u=
    T3rA,?_/x!A-[sW]r1;<@^CGvr-w_9,=Z{]]K[xXn!{Ax*su$<o5vv7B_2!7TGJ$T'rkEK4Y@oO=
    1!B}3YsTa@ut]pE2@s{G!_!l$X[#'V;oBn!YV5x}|gO7,xxjAX?VvajK^=^2D5QmCHxp{YR>ErET
    zQQ?$j!pwu><Txr[@>,}jjAafWzIXKpGE#1#^$O>D+[w~\mon=WsKjU^2@nu'Q_Rz7p~r?{uT3[_
    i2'nvAOWxCeBiYU5Yzns=?T{{(}H7~R_K_T>W$%ar]5vVLv,Cua-m~WQKj*HBk]<$wjm<A}>1_\#
    aCW7+^v#vGdru+GRJ3^![Z$uG!X,}$#WCl-Y5~}QEA-XQ*J%:pRv+5@JDeVHvE*3acADAZ[woQ=;
    ,U$h_\;C*G!$Uz[T!,RjWUQuNO[]+\mY29uEx??]vO25[JJAGeT{zY2nYw$vA#r*kGT]OKO=}p[}
    ;x'I~-;$O^dpDvW25eH3ew~ekY2erGj]V<up2uEm'=YJ,u,:#=5~R=J7'jHYm'V,\+H,vmHmmYU^
    k}#3$Jo7<AAC#]Xmu]m~<BsK$K@;KnIe1#[!}W$u(3r7<s55i*iR]j#*Y_2rJ\{X!pkzuKe~DL?e
    Z=A}7V\pmo#_Cx,-v$|vm7BClpV*k\uTB}V\2+{5*7ppC1sO;Z}Hea3xZB]2-R[UB?!R~+]E;@eV
    5j#x\3!>I-};Ik#zl[mKwr_Cu7Xvspzpz-!wa=?#,pw?zveXC57'\o7*ArK;VRVvu_2}0D+JrCm]
    jI{me]Zl1~5ruruB!;QjV-{C+fi-^;WH!]*kOKGnX74LNY_uQ{*U*ZpCGtG<,'j>Vvs7D@5=,DkG
    +=0G]s5_-[XB5pujOKnGD*Is-juOyAp7~k5m2IVjHVi\S(^bswp!,X7;juW=UU!*{7uz3D7B'"'a
    \^$}~{JX\I6'J,@2,e+k>'!eO7jX}Yo\rOAFeizR"JH=reGr;,1}j2B'V{[Q2,ke7vU>#flCBe=^
    Vw_eu#QZTJ7D\VOUX]]nY'5@=\!om[1mYkEwUQHs?kBBu3VV_GsaY{*;X,]am5?l~u]~W1gzz~{[
    umYrHxTBl7spWz_G@}^+B1Exu^xxGVTZQ{x*{r#Qa!AXp\wV_jkC9+ssn<*>KM~'a<Zok<1Ck'RW
    W<lAnI_G>X^pU>A*^'ciAC]O51>x${UJ=H5fl1H;JIO^w]ZVVH$&*!@;;GrXE]*]lK@DU1$E^_3>
    j<1}f[Oj~v'Hr_wDvC,Hv8CR722B~IR7?O>9xT[a#7'5D^#Brm+;UAO*?5Hz$1K7?53#OwalR_rG
    /YT-Ghd=O*T*],T3Y0iRBZ;_uowpY~-wp[HR?Ub$#[vs9T7]=^xB^!a$V3\e<{e~soaIk===iQTl
    ~Vsna=~zOCzT+,G_K+j_i^>U,=wnWsKZo$VUHdV2C]q,RH*}JYY>-^_Z_*-~-+'IT3-Z,{,H'O>U
    x>~>o5^(~Y?~gAos{VHI]"}"k>TJQPi6{rCB9JoI<,;Tl]xX5>5-B<l^;HzHnQwIZR,nUCB_jkTT
    Kg$iwKQvIVpkmR_JGpQ2_{_]oj_~wez7-2?j,Ck-$p^KYBYi-Z,C5X/k1[~e#Ym]=n{NjXX^.],]
    w1+YJ2V!@6OH2Uxs7!EYo2_Oi_Y1xTIZK\]\B5<jCwgXOIIR2Rx6mYO1%i{~l?j5{lzHV7ZYv2,K
    O_RG{+>]Br;C?J5[ozu=AaX-o<lk]*TAQce!['laQ7-<XRswT2aIw*B]#wQ,@=V;U=~^*K^Bj$|O
    ~{#O?p^rww5!l\Yj}Yr,]X^G1C5!nO-EXoj@XoQ$Gz$x#$kAI[m*[A@IB~'C<Z;\|ueHR}\Ts?O<
    {)eK$V/zXrWg7>>HBV,{B{aWp^^\CDi,4,;[Q5?$!:_H+1ojzlsuj\%RG>$OkvlAj5n7'\Rqz?,$
    K5OEZ<Yn1T5]\;'u[+nOSj5Oi^2*UCg@Ik#t@DU2^2p+-o,+pw,G@U{aeD[KJpKuepUa^<<>Y;V@
    ;Cp2lXKxCu$<v>vZCW~#M&$z+_5D=v.vHK]Yp{]z'kjpj^=BXG{^;ROaTx~]j=Q\z!x7#1Xn<T^-
    v}m@w'-CeC]N_3si)MEQ[lFeo3UrI|hD=lv2XlG
`endprotected
//pragma protect end


`timescale 1 ns / 1 ns
//pragma protect
//pragma protect begin
`protected

    MTI!#omJI#HDWjTamow3{P\:0u+W#KY}p?1h"=~YihR]<=oZW*TVV-=T>k(FPV\I$[CsQy,V^#?T
    ;#<|OZz%lwJ#*+[[o_n=['CW!Ul$HUlIH-BAI_#QE{U2*CB}!r#EoDmp!Y3#i=oaV0S1jV#CZQiA
    5J];Q~!>5AQQHoHZjY'_55l\[ei,*5DH^!TBiuE^vQ;~OJH'Z<\2sm[C'>*dT'w^d.-5,ipYU$9-
    <eZzCuz7HUa=i;['>X_JB>Hl5s5,B3uk5#[C}+;Ia->Y2*QWRxp%xn\J-v7u@7_T1@!pEApuZa]E
    <jZ{Cz{r1U3[r1G!c?_DT~s+1L$'$pkv#-}L${2+KnCi~+lB~BO^3a~sAEYEl3jWS{BD]QUajB>_
    s#oZ?P4$YR[$U<[N;T[]ETAp=G=TlnsmOi17KEf{7m^'DzJkq%<$OZ\?>5y13]=oelro7zR^+Ies
    *>],BYQN\,AeZ*R[?_1WXG#2noA7a_!RSB"x<Xer$lD8Z>A{nD;KCEZ;@wB}B#rKx&AYu1>o#-Ks
    -;vBVGG_;+#pJUWQAxDI[2OZ@apj=Kj[T\-hV.*Im_[!Q7BjQ*'n3^]~J[\2sWlXD@'<mZR[1uz@
    oTORsnv1w+\o<@[TjQD-Do2zm}d@5TV>-mk^5BW\zx-u+2E,5k~1XlObgCCH{ip{m<z1$nY\k5<C
    kgjsU{#+@wUO?DyvACU*7p5[3RiBm_T?s>$}JeR!AWr^1xR+RBUB!WwL^x~='1\-dQ<e5we]ieh#
    n<;B~*1!'?O'RU?I7\Xa>\I_15jwv>zH5+Bvl,nN,o;^ZoD5_<aVeu2u5!};x60l/un*QILs@H[Y
    vuV'=IB:1-$G6&'eQ{n_#BRU-WV1!Z'ZX\"\U3_g!*DpOp*}Y~3?jE$uH5m<A^_T;B!am$zzRD#B
    7Da>$+oR{<ou]a!R|.;II}C}n,B<<H,k3>TxVs'7#JHaoBkI^U_>w#xp?uqs?CW\-{Z]a'?R#CkC
    Y<3yb;1*^+rYI?SA5$!j$ZRQ=-59mH2zIEZeNA1_jrlriV#$W/TxBXliGVl'w1UlOGxCXzQC#{xD
    i20_&27i=z?<kQ!I#UVQ~e2w#RVBDYN@{V~K>G<k,JUk7'J1X3W#\77T>(WxkE;{lE[A>YPxlZ+~
    }iTE^V<so]s:{'kC]#+'HNk1l%EOV$apE[XXA<Aa_~MaTn\1_1}qog\7?2V+\zGB>WE^m]4_3Y\m
    ]#xee2<@GWZ1_~1K+A-pn>Jz#>{>BZDdIH,1UDKC4=I3H7Vv{Hs\2Y~7igl~Z!_JCiGA{$W{m~}i
    -xTeujR,Kox[2Gn}<#m]U5@R^v9-__JeUz_s>TrDroTuemW;1UWDDk{&^]Da\K@OL#s_IOZD~znA
    k$-,R%FHAul]B!z'mW+wj37WR_;~rJa>];RW,7xJ*#Y@Q=I_v]vkXuAC>net1!VD7x}!'U-;R2oR
    _Ue]s-IJ#s#*@w-~e}Kw5NO?+n%dmI+]*r7mGmo=OyKOpB\a\[Z1Ie+B13Zv>^D{rRi7J?[o>EYw
    7x#1$n5[D'-,?~_zX@7?}wuYvH=D*>SL@5TxW+XOC{mo011Xs8pVwu={x,Uwz]J'R3BW;lQ~TYQG
    jDHe~G5Rmpi\aRrYjmR7B*QVix5oU_e7m*~eCAZV[]Yj[A[F25H7nU{]ERQanv*wADR]1p!XU\}U
    oX{~UY5Z,<n#K-V=e{j*pZW{YV{CR~*GYla*'G}Q?r{\~j<QHps=;=Uw/0@<>JlPZsQDD!BWR#!1
    ?-==j[[BB#>IZAOzQenQ:W<!psi]YE^TDz>1J]G>uXDW~li+RGB2A_?uJf=[5Wi=3~{AeXKQ;u#$
    WY>B+o3Il,5$=}'KjQ7AVa|d?RT$,E7m+EB]B[z~b^zI+k_YIxmQX_v@U*p<-JIYnY;^mYR^u*>!
    BInQ={<mp+{C[m_Zu{=snv5U{=j]u@>2CevQjWGVXe~uAs,eV>_,v*HIpuRXH?-{o~H\xvm~Ui5]
    '+rk3jm-2Bnj2:v~2+WAzll-B+N;vkBpiC#iHAxOUmm-$'uL2+_Ox7o@ADGs*$wGe>Y2pjo$\=^Q
    ljA-{I1;oW{2$[[kH-OwV;p\+O$v~-JYao'']sGr,1X}T5$zQ$<1Wa22!xlJ'[?k7~G5*Y]+_5~_
    Q!n7l>5G*nW@i}e2JXT!!=VT<xY3~C33)B'3}eU+}zwI}rT*+\&Y^]HKj5nmUxsZ5IaE+Un/+sRz
    aVn!?V<1B5n*EvUOA==]ir#H@GX#*w+U3$]]3j*lBmwvlvnUrC-~h^a>Ds~uUO7}K]}X#_VRQQCK
    \[eO{8+$#*^^Zna=v~$ViUu$3=kTpuHa]_QC@=JAHuY1!>z1?3>xE]2YUExiC{T_Y^b}Ka=DC^VO
    -7[@'}7<^1J<Q!~g<-*kx!C1r=H]i*{TvWnv{p2@Y@Q#T'o2#^*_o;QA+wlpkTvC;<R@o<>GR]uV
    ]~ZRmrsIp3[\jK_Ie]5Jk<-5dWe*a^[liR-~DwlQDHn+]@jzv]\C^vpRTCOlE]pHjDZF_mBWrr~$
    QC'^lp?GD*;<Ae?~RT*!'Y,C{v$s<D=x#v+Ws*eCw1JGwOQHo~rrQ0u,>KUU6jRxk[KXw'u]#,i7
    Ipj@_vDYT/U,$poOs'BlK-$[aoH'5+Q>OElsT5\$[u;pB7wr2\=~{R_~QoEH>uzB5~r7,!]TKJr+
    JW@L+nK#E*^7[p3zHsCD_KZwZ7,]=XTapn{G1C~kwxw{h5*\sBhT]Q113;^sAe}1C~3T=<DBZw!!
    Y$l'UovCD=*n^Gwme?V}ZlYm<XXvO]osCZ^o_2D83*kB_GJscroX]WCuDv$1Rswo{E7DJ?75J,+;
    {gHA$1_uoQC}}xH\#$xW^nxK5O->mkV\_ewS'V<krZDHp<@>]$An<XQuYR?^Z$i>5B+HqAX_z=?Y
    2Zs@2<s?*+1_m2eajYC2I7lTDR[pT~]noBj#CV>U<G$J!;1-m]K=wD5_Q2ev[bRGR+'B<R=!Jv}7
    i],a;^psDKTEJwIK11:pX,=eR~<p>+=uTnRsnI}2p*ivx2UT5ZlAQuUa{IJAs3IlrQ1AL]Ja-Wxm
    }\VsoUei^#UR#rO@G-,w+([BYix$v+2$KHo\Dp@s}zTp71k,@?oAo-XOlH(J,iHsvVWGInOH_;n2
    wDR[J<]{zBuz{Q7a^lQomK+;]k^^!<@}K^CQG-nS3+R;#}G@Gk+{jnx1<HYvPz7>^YAzD@Uw$D!_
    *q,@W~lx=-IwIvmGiReuKIQ;I^,,ipBu3{wY#Y@1[i)BOmT{Y>Iz[U3CJ3WmQso55>1aY?w\nO,<
    ,r!)Io5;_pjDUCwE\Xp_%3js3:;Ellw(C#lsKnwTW-VH1r=zRi+$uHu[m<T@-VBC',DBD!RY*;\G
    z]uag$AAIes7T;GEEwsYYc73TzO^<Wip^e}!D}u}zE}\OAe${ZlTwJaG^@elwpQ$\7B]3rvWYimp
    Kul5A@6'}CWOrzB@5m[uGAs@\zuj<or<{Dl}=~<+5VGP3}Zr[eDvV_;m@,V7QHSCYkv1X3X^=mDi
    Y^1JwJ+n_VR7$x=CK-vDYz~8iDT1Z,@jf$rIIi${~]H3+=W[zz1\i[u_TJ+Ru,!_jI#^}7CVeew{
    De8v\-53B73X[>VUIe]DjVTe~p!>Y*u^k+?,{Q*x+G^_]ADZ+D^;<siU}Oaq5D&^dvu*,^rnuHxX
    m=@C7}2GI$awT1n-zL?{UOB*>_3>eX-s_uvx'wz$jVw7Dx*5^3O!~YaVuRZ]-jD#m11{RA~B+pNl
    \i1ml7R[kRkw{a+O,}[X*3URGE@o\K}I[~Jy~lT3rZEJUAziKXe7LY_e}7@lX,m7W{R_@IU};j>*
    Ga_@H1oZ{0lw^BGOW=}]YlEZ!lj\H-7}aYeG*\n7*=iz$i$G2[NH7H_F6[J'KOsK'
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#JOZ>~xO\TQI2[O4;'OX3\-7_<72}_5Z7;[_Bj"Yq#s'V}@3ApZ]n=u[?G3m_^*s?RWs$7va
    D-]A-c>YT}aap<+1$'Q,AmOoXA31,['>Kj^-1V-]-]_7BzI'mv'XIm@]<URl;pOB;Oo~>AVri-[+
    +<[XEY9YCK,!=Aj\j>{$=]AqY9*Oe_5skTB'Y@XTIm5IWWiOi@*IpuOBJ^}HY,1uZJQB$}Y']?=+
    p+YdRl^3=W2Dx$3#I_l3RpCT1ealTR#Ea1ZjCLeRno$R5]RkuCE>EU]u^C=3$aI_@~j+TDkaUxk*
    <{iUs#;D@[{VW~Rvvp1x]Q+Xlo!5euo=!CIZDEHR*]O]_wX{<]\IunCu7JH-SB=-uvID2;H@DaXA
    ,^7w^JU$$7@!w3X{?^155\ul'<UIl[nwIW<IzO[<X:*o>VOGQ]Y5Z1k1ID2lv?vOKeV!{CZ]z[9O
    'e~HUvj'GXvUs{Y?z>DrvKBB[?ZRZueQGp{C3=}Enwsu&}DvGiVB}*j1#IA1s\5U\!$B,Ie#ups~
    ,A+DJrvQj'WJ]szIv_s,E1).s0l*WG|WVU!IZA5+<-7KIAWb^o'uXU=7ll??D,eGZ}CmZrixajYn
    ,K>!Tw-!p2C7Q}A\[,V25Q6omA>\]*mZOO2;7E?*GBa/<'vlY'{mKsmV?},*3R;l[CO1@s{+^ux@
    a1G?!RRE$5WV'V;#eIO_Dv73Og,!mQ,~l!)s!sT1Gm!6YT7}^-7!sx]kf.#'Y[_Y[xT_s'USb'u;
    ujVs7C}>HODH5*$;m?_D72[A_\pe}__,[HC^}L)-R}T>+<=xATWv}B'lYW{Lh2wHKkVr}'i~QU'X
    1jj!$Xt!*jODa,>J-A~2w-KgXAK1YrX[}2[Q$ClU{]]$r;1''*~<o5?V~O^$v'V,5KI]Y*2$XEvA
    .HVr-laKwd#vQ7TRT5Z$\o*{,2p+IuK,AVjp#u=ko3Xnn+5l_v\5+Y1~'Txe15*JTs*kVCt*B<x]
    i*e<5YZV,Dm_or[*?vomVO]6/^Vv?t?lZ^=Je*nDXZ\^$B_e^7K[woEsix%,a[nIaD]toX7-BAZe
    3TW-\F$WKDN6wDu<mH'pi,eaZQkH'1i@{x=jp@A\r^znJa,<Rn_=N|71$WyI'l\[#sO=~*$$\pCI
    ~+,IxR-!s,TDJ5KjXe_1}TO6xxG[KsBlwXH'f_[r}mXYv7*r-VlAEj<z3)Aa2$%D#+;<[B^oiC3}
    G\il2Kx]F[;{@nRVC*V_wX-@RrB=oTU3j0B=]H^\muGWZ&4r17ii[ZIlnUo}}k[[{'nO{B\
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#n>w?u<C\"s5;Ar[v1XV^XC4_<72}_Zn7;[_Bji2$YTuQQW*Txv@a-w!INp!}TyKX1pxmCl|
    mU_-<'k$_rPlwJ#*+[[o_n=['CW!Ul$HUlIH-BAI_#QE{U2]G'Jhlu_Zc[QCO?QkGD#Q[SYVY7K\
    [T6BA**7IE<'EGTjB'R@GQY6J1H_">1oUD!s<5sC5IeJ}7_s>WvvanI?^2Tr}_!^17DZ{H5J;?w]
    1xRko)}5V2iok,D[-u!IlT^HeWe@U[aXP[G$ko2x-{n5\h^ruwCaT'C<no'xXT}Gn;#+xX[Hr5'X
    IYa-RA/<rwA~$R,czi{3{p^@^O[+QnRD2oGEz_$z;DVcEk{^Vm@-]]uZqCv#*w=CsBaTR=uTOWY[
    ,"O,?3+r,#Ov+_lmx@KrRw^@H+_KVE[s@Rgv*[}B?_CDjn+$xv-m+{G=!AG+vHD{B<pH,unE\uHL
    #>JvVJC[>_H!Dx=Uv'{@sZ@QBQl7U}lz27,uVA@EBp+pvEp_sGxs+*^]!x3B!E{e"75C;HeaGpek
    z-G3BLazrK=]=J?<\koeHx7H;BL}<osr[*Xk]>ZJVGk7<^vIY$:2Yv-7[^1N.I]nDws3]1xxsVCo
    ,?}DpQoWRZs^=;_JZxs3z$\!7Cx[}uAe!fr\kkfKv"U]X*-T'uE1<kGvjwN<EkO:X]oOlnW;UEmI
    4^{*IH=e*R5avlETU,+!u+so[pZo]'Kn_uhMF6rTj[T,*#E>WTvxQ,+$oQzU3v-{{BrBAj!x27.=
    GGrx'!^}HoZ<BWKB?.["CG@2}{-C?s]lH}[IivYn:p+Ae-EBWH'WRG<K*i7mQir-r\5{[O'p@rm{
    _x\1+X1>j)c^_A<ODi2v}U*\-uO8E~W<6j]],f.)5W<alDAYo[H?DBB^C}!*CV;@jeW{vuz2*XmD
    A_i,&QorY'@QD>{E#d\T[@GkvK&+7TBz^a_uY=YL72xXC3EG>E$3^V+!=AzY{-{aSj-TB,U{_%C2
    >#C'Au'_;'o@~$R5-~{OT]mI~?K[TE7aB#O7[@JOXT/nn{DK95v,T-+=OeW@eYk!C;OX^2DOe,i$
    Rle$$vEsn-ax$s_Bm^AwWno^j*,I'=son\OXkHz[o)5zW!QDo3Ruo>oj,~x=V]x~2J3[VoKO5_Zz
    ^]CKw}mX+3g_i2w^5zi;x=Wg*~J_\l^Y'7XkH\Y1}WI?O'#3El[j__!vlDIIYQTaxmY~l!I3>I5x
    *QUEO%$Y]2*?7YV}~R_]o#suAWEomrIYvY&5X!EII>$k5Hl*C,n7;enx-e=12Hu^A!*cuTGi_K<1
    '^-\Iw~I]x,{2UI<\v{>lU=]'vZsBz{{'K<}mz]pCr\vr~!IGK}'}7zuk'AYw]2j*I3!+7~n+Y};
    A+'U=;$'luVAp[TZGiY2Vlo*+\>nnEi$]i]G+A+2@[B~pKzYI<eA@E$i(xI;=YB}j~Y7$XY[@#{Q
    R{7lTD@R'jKKD$D7H~jpTZ-[EIA<WV@]^,,\]Q,sVB$ZK'J-Ukpv^p;-AvEl2(BCp1a1$',\GHke
    C5=#L~UJBGep2<s1Ej\Y}"#a{-lJnjUU2obn=oI1nreav,RpJw'73j]YBIX2T]~jW=vW=Jn-EOl;
    zGsA5o7=A2IW7\p[mQ{KY'>&<U_lrG27*rDo$p&sUTAQ3C{$DYuEs2]7ow!uw~BKvi!#,TJ{$jn]
    TR[*?J5_wB[\iY;OT^*KA\DwV_<*!J-Zp~JY#a1N0i$Zr=zCnJ-wrXT\{57U@]V*WkwWxz1\I_i+
    pN<-KCC75XBX}Tmr?l]i]ar?;TrE{exOQ-~^_Z_Dx7urwa^~D_*82QX'eK!El'Hn.vaRD=}K<YOm
    Z[[k;HQ{@QJ_];H5~co9-zaZ<AB&TeEl}l@u\iVH%~rIl3'5@=?m1L^=2~CJ[-mzHKJ{o@nE]<TO
    E{P,7mvT]-!3UD7sp+sO*l1CKp<ExUo,@lE=jw?P5^XGk*~3;z{1]r\]{lVVw*lTBCmavJ'X[!=a
    )vOi;:KvA#glEJ{${A=o<X[YmmJre5O;[MX{'[pm[}1E~#&
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#!U~131U^3OjsUx=~V7alR\-7_<72}_3?7;[_Bj"D1uJ,Q5[TvWT5-+[&izA{V=aziRB^B2W
    X;]T]V-ns~{BBY6+1$'Q,AmOoXA31,['>Kj^-1V-]-]_7BzI'mv'XIm@]<X]kO\GnAVo>AIzr#=B
    EVWBJO+aa-T1TmmD_+r>TpVTO-Z'^xj-{[mF,TB_EGv<7eTeww5Vv]Ir;5knjj~Irl#IH-Rl:H5C
    J7Ior7azGm[Q>aQH#IwJ=,Q5?;}i'xw{nn=ml3]AjGGGj{>7[BKH_3}vOIl3^1JDJ.^z;>$5>Gv~
    nO2ET;xlXjp!ljC'OjTjwX;>zYF}DGQgHwHWiO-R$Gk5ork\Rj.^oxR(e:-X!I^+Wov?+@$I*$1r
    -VQwY]aVB3;=kA1a2BnC!]mr[!:I>oKHHZ__+@^1ripeupRICTJa1E^Rpwe{*?#RvOAW<mTex;*A
    x!U^n}VIXRo={rXY^u@-L1XRAKnW5EeZUYu}sPBAKm7oopwq@sa,k,wo\2$zvw7:Tp+3Ge>G]Axz
    5}QRLaz'uwQk~B[D{%_ewnxAmOA^KHLU,zGUH}mQ2-p+_*J73XG'wu,D]{GX\x@0'{1OrsZJ-+}O
    y3[HZ;nAkKC~;%7CQDBqe~-na=;se9|^tp'-?]^s>iEBvJn{+rC\#.3rn#h}H$X'D'ugN"}l+oGn
    VYompI:&D?T]aTx="opZ-lKmQskT;:7=E;q*][JUEY*U5UlBE$C,+!TM\K!TAE!{vE@zOp7O<}<$
    CnXZJ>!{}Gr{,TK;CGz?^vXj7Q,Qs7VlVeU?>a,\Pl?x<Toj\'=!>os~?O$V3$-*\j=!^A1jO[iG
    ^ekul_r#!;r@7faBDio@RJFbvsG=rO?~l_*X%}7G,oYaDwCYi}QOjqZpYrhB@HQN$2z\-(w<<oG}
    ='nzz\GjYw*uppo~JpaQ->V5n[1e\K$37i|GY{]{aKo<$i}P:TH}7{sB2]lH]q)#}p'=YrV7x}?n
    }=Z^[AYTG@]!EZ'fj+a*IDx5?e2Uw}]}"=V53V}7;J&sm!W3^7CqE+j-[JrG,kBAHsQ~y*^wp.X[
    r!%-VTrkN*?K>ZE3u$M_2-X+nj\9=mZrjp=m$EAIXD3$Q]J]3QkK_<'m:fq]ZY{q:nR~~OTJEu,E
    ,Ynv~l{xiEYREApKxv#CTD*un7[7ToIX5v4x1eRe][~C}#r)@HVpsvlWHI@oWAsnB$H$*onmD?$u
    $#}e8x-W@q~[Tou[Ak}OCk+jnrsD=QyeomjXXQoxWlZX$=W|)uH[Ai.{<\+5E,H!YZC"wE{KpT\\
    -a>752\v'=_73oUk@,7'bD[m*]<Qn<xI@WsAU}Ep}rzQEsE1E_J~7Mr?}m1-ZW{C{D~oz7XB1VYl
    nB%6kB5mmnx{#UxnQv2r1Do,pi]{}G{2tmvIJ[uJnH>l!,8F^aE;=51saOmzBAz]UI'_x@o]QaHu
    3n]QGA;pNm\XBoQpm=;5A<+GJYiToRaYXjUK+fA>YzzD*-NKaaxh@5RC>zVvOI_s-rm'2GBD5e?+
    u[lG*5-,$a}GOA!'ouH=y1!olBrk?I~}we[{+$X,Z~HH7z<AR;]'~E\a@pV-zCknD_5GXG*eGkO2
    ]^B-ZVD]UT$Ga}]j-HX1u(sG{5@YV5F}X[KCaj-<E~,k>B*e!G?$Y151W}5tHV{;mYY*kD<G2Ev~
    dY}7'[xY+pi-$5l=*+[CGeKXzzwvU{wvE;pK>CX}IG;OjyCsH>nXT=1[n?k<lkiD?uU$]nyWHz,M
    _aD[=uRKe>xOQCYR3{ea|eYx3<l#Ah3n>#bP>,BGs,!V\,G}mEu-pZo@l#2]U7!Br_]zoj5IGr~*
    !}eumvnsWee=;Do][k5~]*oA:*o_=rKjZ:uVCI),wj>\ls+?]}jZx5YO.,aC{5?7ZK+K~kV7T]p@
    7,-+[WR'v]U~suVl>/|_=!UE}umwzV;GA$'e?Qo0Ker5?*3YnQu@[w@2zzH*VJ\j^Wmnv$X;&2+Q
    CJ,#WDnB1[!\JOxH*,zsph69nslBJHXV%DY<AlH1jT_;]BZZBA[jUk('_K*'J$+7Z;V?TIHv$\C8
    RJVz<QBsluv]R\>s'~2a{QU@v}1o6JXHGz,[]^+1aTCEGKUmGKe^rJx<r#$v<+RETjkJ--<v\>l~
    ',x[*CA*Z[Qk$@5nIr!v\E{$$+'\n3(@Bu{oqRA@$Cm\}+]k3oao#H,O+z}sYr7-7b=7i^BA-k,A
    wslmzKfl_A+IZ$D^CJ-anr2,'k7s,j#*jj^a=i$ECJ]vUwIjU\kllel1w,l{5BZmYv~Vunr7{$G-
    &;UTT@<,?/,kV1=3V,~{uljOSp@E\]@W$Oq:YzYiO;sI|k,=IA[Ze]o=J[}7~eOW9<w2ag7HxDq,
    m=YK9\OxJ.@_*[y$1E\dgk&3Gz^XI?a_'kHUli@e%#a-,|^1\+zrE{*BYps,OoZjj_kXO37sWZem
    V5=!B!/,TYVVUU{{lUX#]oA7=w2T^EJv^7Tye;WI]>*x]xxuH}3eVTwCa$V@<QJunC11DxW1sH==
    7wA[G$p[|P3&H]V<I_2D7PgrJ\n^<r*
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#lGTrD,Z27LOZYi5J+v;Tp$h_<72}_R+7;[_Bj"ex]J,Q5[TvWT5-+[&i,A{Vo>e[,[?1X2Y
    /<RA@@D=pgaap<+1$'Q,AmOoXA31,['>Kj^-1V-]-]_7BzI'mv'XIm@]<+pYO\GnAVo>AIzr#=BE
    VWBJOVa<-uY7~?E;2rCT^VTO-Z'^xj-{[mF,TB_EGv<7eTeww5V}\Q$;R?_[FAR!*_zU^Wp?W)~l
    ZUVsHXvkU{~AI7B]?{K(:lJZz3vOB/G_<=*$Em\#vVEA,TDRrZwTEIj=R,2}BixxJOx+Oo}az$+r
    3,}O;e73Y3W^aHVwHo2XB_^mYR=>mRx@j_mE[5]RwAr;mKaRpsE@}2'[reE'!n>n}$^-rO7^aVRa
    DC'[Q)yG[n_wT}WfkQwV\H7ZQQk]vaI#In{\K-,\KGtWzK'u\Ho$wJ+l@;H1m~[R7QiUIln)^JuQ
    lmRI$uXJapV#_CwjroOYv],~v>^i$n$T'~OV*_i@lzjBUoe'^?3T/*V{vy#z<UQk<IH-sjyJ<[V+
    >;m\IGQ+wvuK=?}enUV-r1+u1C^*e]kC5V$]+J]{l@]#QUGr2$2UTvm'-Z]+,*>QGBnT5\ounm~A
    =@DJ$[5?v=35k>HXrB@>+XGIavx,u'r-}sOkr!+Y3!I0$IuvAO2~RsD,XD'\tG]]zAV[2XImvr;X
    $nQ+sHx[$}@{mBCVZ?zOIUD+YrtBGk+fwv1[\r#+R+@^]{j{$Ax+AI=7lE<ebkU-]iV2lAv-nu*#
    \Js^K#p$HvRE{*Tu@=o{I?U>C/Cp,JpQJWHpn=DownxsIjvnGmv{+pxg3D^}QB_15u'EO+u[Kr$G
    v#C=IRW'aXeQYElV_WaCRz#<=o-B->,ZYRZE'K;#WOj@XjC$K>q7~uTl'YZa|JUT?kOGka<C!1nI
    uVD<!\{1ZnDn7k']jW'WjsoDk&w$v,Bmnk|@}\n^j3!rKWQN1rUE-5e;[3p;Vl7Z6[}z,Hsw,rJ_
    ?-o;3}x~W,Wj=xHK*}Ro1a+UsTY!7"$vOkY>[,L>QD[3a1jKU>BRjz5FIs3j$j^is?p{=A<W!][K
    @,G[<z2DIc}ew\\C1*&6>Y+YG*EsJoVx>}<W]z_@D+YEIeT@Gu**YQYE,HXKaI{oGLnp?EaAl--O
    J,G$OdGon{wD>]^KlX]A<v+e-#l3Z?wVUuQw}}zzIea\-xIAT$}wYrnpr~g?v=-C2_z^$V5AzmYC
    '5*4<QYmX[mureT$WUQEkSf**HR8[A2B1kYVoG~>OjjO[HG>=v*1YKA@*Q$uNJt4_H,p[v\V7+~}
    {}p)'G7T^8Kw]l8.@wXx{'*!\p~~AOQ2^HV;a7nlne!zvnz5-}ZQ<rpxGXnJneoOI"*I7u7!_Ytv
    5YoQ)X$\or2HXGj+#uwTj[!B37lXVE~pKw$o+,!ljozsR[\IHR&_>,G^@}p@a@xk[6?{+!El1#^a
    {_UYQ25;+[O1oD=^_URx_r{lmEOG}OXs+@=-^uz;r1B{Y3B@VR*Xp+Y4rs>;Qrn^aws@?$Q$RB3>
    r_\uBuzlDRpEXs<o21j^dQX{RZa!XpH>Zv[VmDmRKBZ15nwa[3nE2^}A5or\i'D?^QB*X,XW2Trs
    3qNsH+x?C7EfveYT#$^]H{w-_#[uI?IV1'73j!=WUHZX_+}CM}urwv?K^QXA$'[Dr'#^AM5<Iie#
    3D%TlT-xpUj'wOW{ojJ<_Ku3+jU,v^T{>BsG-RT(k$lDO,;RoC~$C<UWne*m7ZAl4{e!^7,]KG=i
    WURv,=5<~\,kR[vzwZw7izx;Yw]A3#aXZX}$;Bke1'kamax*<7V#xj~wvE3rXIK$BHTEY[+1}C'}
    m?Da<VH{1Y5-U5$7z*>{#xu[+AzKaosEkxZZmITEE]TAI'{mT>^apTs1GU_xJ17ToUw\#OD^Jow{
    eS7aD$Ekmj\m1a>Hx$^U}@n_B*i\w},wRwyXrB$j+{^8s3jx5DG$lUQ{|=wo,vZpv$w~m_Cp']@e
    1BERYQ><u_gBeH;VkXClkKm;>QCm>[B7W$#A\Y<ZIEoeTXO@-5lpX+;7$=Zp7AA]_sx[l<}YA2;R
    #]v!vmxW+E?,]<]'}7o-v=>@-U]O52v,zVD~<vn@v-#xEB}eJJ5v7JRBlr?BHBvnT!<)G'il1R=?
    *\\sie[a#<}Wk_eIp=$T>GsK@zkCzW13--)sUE3BAH$U-$VMr5YKM;7-T{U>^OT<#e5=uB121T<H
    @G!xe3HD'RVoXm=,oE~@DzAE,=~Q,>Q!u2_*syfP5'j2'xx*x+1oV}G?4YT]\FImsk^1e1e%Sj-U
    xlHZjYs>-Da^D*rACp~K_7s@Joj2x|7C+k[:2r{2*r*Be*eJ^?m-DB>2~U+OFwB'DJRpx2E@K's7
    m$CT+>_o,CW]l&]r7R]{}E}$-}7JKl'BoU=ZJKeu2zm]EO;V1;D}5lOoupImEeC3Gev\TexspALj
    <ICE[u2=oJBzK<OP5KW~h2xnwXwo$Y\QHGvkYz#nCvieRml\'v}u<GCXxmtn,nj%,YnV7-T2L%@C
    5ArlH5GU_X^E-ure#oUoX^wa<n+j2eoH]ozKwVU,O{Yp[Oz-1Q-Y{Yi_oO1-XVI,Ikr@ETTjGY;z
    -m?jWr=IGXlrekZ]W!CK_i+puR[Qvkrjexyr/aD]_-->xuVU,I\#n<*A^zMG"wpvnrKTA*@vX{'+
    \Y2[]+_2Ie#\U_A[^VN^Q>_}j-$xG>R\-KAz?R[ua<7j+U<Ei;lQ;7WB'I<r[O]:Iz@Uk[1a'Yux
    plK~|in\Kex-OAU0;+3-ua]QbxxQ+epI$-+Y-$Y?DC_m!$Hw#BUBxf]mK3m'b7R^pW{XWO$YXxJJ
    >O11oJn>@UsmaeHZ>C[pm<1+#7@<Ife~_'@l='^@+BZ^J+U<Aw(QmK2{o-j5o[JEkHa+[jIp#@GD
    )B;3,\lxa-{r;7WwrB}]}UQ@WZTQ]An~@,T>vC_ss>I^@v$*DU1x\{-Q\'=KzU'OGV=j\OK[!nB^
    1hs@KC"LeH^@W,lp1'DBZUJD,JUsxVT5r@Bw:Z{Ux3*kp_R?1I#T^55WGiR}'T*ArGe]{+'i+T>7
    ACez]f5?jvp!},=uU11U~-B8'V}+pQap:_Tll:vUOez?[O5O~Owz\Dmx~rCvpo*I=z1?$o[i^QZ\
    ajvKB+oBT]M{BBj=^nYlDOKB1tb>]T3?j=_WUxDeKpJ]nV#D1>T1@Iu.:V,+',EB<>sz$ZI<G)Ok
    2XV3WXz2R?!X~JY[ez2<R~ZT}i5nnWe>TKjU7D*ATanYBY/{Ev2{X1{pg[\j\deaX5RCnHI?-I';
    WTm-T31;}mIrlj'\o+VoTpfCo?@*_;7Wrz!Q;5#rx$AEwTalRs#bY<1[0$mKZ+DnOOz-;lsKjlnQ
    ^@[I3[<TlTRi_3Bv_!$_@#OlT=-ou+5oQirJTrZ27OW{C'XE-jCeV$][mFM\G@;<*K~r#@o(kr5z
    IQo[$H\?HrlIfG#l\Q5*2^xxZCT[5$JE}eoXG3*lJOV=#q#aQVuv~m'sUOE$ku$aC5'*XrVw@^51
    IKrw37N,{J;AD.s?\\-={#HaA$gUw]$KY1@I&_X,I{IJrDv7r,jmRXEBawr31Q-lDpBGnxb2XX+W
    pDDOK=D*;}_VUWrCKD5IU~QY5aOklJ=ACj{mTK^}D~>'lKp+os@aBEz1j37OoIGB5DGnwI+)_+)V
    2*]Ww-]8^<+;8=TU5EjY=RC1xIz5>[uAaoV*Ws*?1HX<~$xBr}*k]ri=!+ookUB<10*wV-Q}-rjl
    i[oLkx'uZ1@XvK5_[Uw'e-z[0^3up}.*CA1se'YvrO'6a{YYi{CGJOER$[G3|yaRD!wvo@(\iWDu
    AI@Ga]Thq*aE]Yr[x+DA^*K{GI-BaTA3z-R{nDm}~'=*_H51W+j~vC>o\3_=-Zj$l;V?]L_$i~j'
    }@twU~E!XX7ke~nG]nVl+E1lGs7bT=vDnVl]CunlK}mu*!K}=F}r}oB,$;#az~ZS8+e-v$+Xs
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#eXXY~=>KIok#D\mApX+]iwY}j,jxCVez}m<j<riaCKK2_[x\Y$>$@C[-CJAaajR<+5#[jZ~
    ^{]nKNxLzGC[EfkQ'Be-e]iXa[zx^QI}VCX$sD72[jNW'iE71HH@GwU(-\w'Q\Tp~^\a/QGY_=#+
    _lv?-F1$YGx*o*p;{_CU^Zs(+l@A,*aDw7?^YuDE;<Q?oaA'Y5;}xa7Z^zx=TYrJ=,{[n]\neia'
    y>>vjelw7vRT\X'#X+zU~_5GIT7-pAI!*(GBJe!<C#Az@]{Ru!Q+O\WXD#51?B*$W[v+u7I\<7wY
    }<}/'*XU~C}J4^Ap1mBoD>jH@puCw72CQ"VY$Z$T!JR1$jLTD}Tu1_]!nHT+-Y[Ke_Q}>n}vW[RD
    CJH--aQu}#s~OKvi>@HM7on5wD}2sro\InV-%s{$kJ[WH!_s<Tov;>E|NeTY#o9EVE=}WU\Vj7}N
    Q$<-|Y,!$o>7QI;rmI;3>3D<>Gx<_/V'keR3u-E5>-kjA^$5^pu5x,[\@^$k+r$Dz@uHI<orw>vU
    G0k_sEGQXkYK\Q='zkA5+{#EWOsDu;pBE?~IZ@p1V[^U\]EG>HZsB5oJ[{a{2BuEu,sunZAa}@;R
    'j~E<s77'KjrDQ;B*E?+z+X+n{\6QjUOo}K\v7nQ>A!;]VQBR,\@As!rYJ>m3<lYi,{B2$\$p+>j
    T<$R/B{'E{D?*xRW@zneO>>D^1KT>jv?@PJj#z}AUHBkvD7,XR[jnveq3\+3Q3mp'QC>v_j#2lRU
    e_1vR\7i|Y}{mOX;BY$*Dv!=$%app3jl3B<ID@Bp+R,*2~;A=V%;1Xvl]K[MZns~G;x;m>$1@os{
    Ao}O,EUuW-Ce>xOp=a$IUIC+3v{YGkr[mCKas]1K!VxO^xoHI2W<{Ex}iY^I2*{IY'u5*Dx*f4:\
    vxAp<oWDvRYvCO3~<1[^i]Op]j]J-rHE@R\t*\1lKe,=*a\1,E!K{r?2ipTnT7kuTB0xGkYEA5@F
    '\(r3[[K-z*G{aDlWHs$W-+I7~,O~UXpmjR<w]O3vVun,zn$euC?R{XV+HuX<$]K[2-mDTkvQk1r
    \R]k{1;nsrT~e7l21k,&+Re[B;_7$xxx^T\uCW[r"IWHVBi_mHe@+ewW]Xzx!fUII-Oj+*@EU$ok
    2ZQK<Eq-=VW2YQr*I'Z6Y7;;G#-Y=VD*]{xYVeUQ={{ATonB^k3{Y'D{,5Ii5'*>fE$Vm*VnYo}2
    vn]m<G\v!a-5T6luE,#OsX<1w*<V5^Ek{V}TK;ZeAYYjUxDV#aC_I<s^?nit5i<[RSQwK33Q+B\1
    =^oK{;EI,R_K['CW\@-+3's~l]<-$=~sJ2;ha7!'(VE+^$3*5}oJRzropXjG^IZJ+eJwrx<,+*AY
    YIGYu5kW!3\V3!7{ZE#2o+\^ZQ[1JFBO=imGdZTz@@{jz<xjjJjX!Gs;uACm*tA$H3\>wYaa![jD
    }u=U]Y;CT_H$,*fUxKUm7uvW}5O2G-,aDnH*C^msvrvm5'=!RO}*n@e:h@>[{VYA\7}m;!l2r7eK
    5B!KOWDQ3,o[#Ha~_u'n3,H3@x7~rI@R$x]J=[QHuIQB<.|7^[Y};\>zuABd5x5vO7V*YxnGkj7w
    a<[jIDV@?xUxqOQ>5moZ=BE$1Ow$o-BiVI}#=@]EIQC$]5<5o~_<m&ok~TB^-sB?]vD]7^|yVvGA
    &Z{Up_2~EYKrp<rIU~'E*j,21dR2[TDiX3eE[R{*Y<RuW?/9[*ur)}X${xOu?)@Ip7wh2<>+5_1Q
    y[AYH+A_*l[u$B*Enw-x[KHjl,][D<EW!D}UO=AE#Rx#3kO=uDi'!$nT-o?7vKT^GO{=oE<ECTpV
    #;w*us[akDAJ#E{$2(~e!~kT{sB~XYv3,+\Tp'^1Yo7i=Dq"(EGXsRAxQRB!Eg;1*p6Qe\,65W*C
    lCxe~AO1CQnx,vRQvY<$!z]YJY;a[W+K<O*TlK2Q7}sai11uxlJKv#mu7Bos*-OQez2KoRQz+x{~
    w'n<WoZ]C}V^zY_2iTs<1iC7i-<uW_A~%U*^;['5s.OEROxG;2!RO!r-l$n>AJYum2<^mrBEOwQY
    sZ_1]pz={oCc~\jDir=kV}svVKxiKE=*$}DU%fn<{*$EY1s7JT+s,ia*ivSe',Y$'*#$Qp@oaCv=
    O*_^C+keoIGe!vRpn[<,!{o7!HW\;~]<YTGXHKsU<p{W\s<{r>=gN2vi7'*BA{UE5mj51upY<Q}~
    \D)};RnLIA7~@B<,Iv#3.n1jla1{\4KaJj2p]]I?QQUlEuBW{2Y\1rYTB__{7!F+ajlIGER2Twp#
    Vx3HzoRXrziG^5n]'$Ap2-Zt7s]!WU_[kTlW|g6Wv3$::DjkR@aXV2o;G1pAZ}#e3msRB\ZT;jl~
    ^-s'nV#!xWr7Wwx=wn*QVOAvHzOvIE=p;~RTIT^wJ7UHR1ZXD>$N@1?-'nAm]*<;pGGO}E{vQCWR
    2C2BGY_E$+!2\3[zo>epQ*=TB=HIJ$nn^+j3e[{2=3*Yo}QnQ[w]5W;adals?+vKlY<Z2xTT@K{+
    l?YQ^KTu@BAC,ol+xEAxYivl@/1X<T;1#+\vK@52]R}{J;-YQB>Eke$Yk$KHV'$kECT\ma<Q*YDo
    2GZ57EBiA${[1=$^iTROO~~<G2XpVIAo7#po!,>H>'T*KalYwl-AlnpZf(YO$z<Qm=G7521zK>!x
    #u~$wXsHC^WI-H;_jw\C2kQ?Ee?8IA'I=C_],?=3|I{DxO]VRkxmT#,v#XLAAIXoa\u=CTEi+T>'
    lH#(CIK'O]W}vO1UV_1[Ao?IYuHa5>j,pYw}G@nJ}2WutGksj_nKU,>UTn1}xk*nXIi*R{>enyd3
    UJXHw+C(zG*]e]-*DGVuVr]1S,=zu%;wKI=J1K^XVm:v'<B[Y=jIHorI*}<B=\EoJ5#UEOQQ<Z}X
    OaHlvm!<Bx!D7-\,u'2D#XU5ampGuA=72C>BrKUEC-\uErIXnE@Pja-2uaGp&j;{<U85]Ca-x**3
    j{;'K;$J7}[hrr3opYa_Q<ITzuo{'u};]V{KCE@WVC#;_\<ID7Z2!*wUZUvz'$o77}=oE>a\9RJj
    *r\}x#>K]@a~U5VAKkB\RAzTKCT,ComjVDXaW5^zK_$H''C'-YCTH'O_ixe+nEi1@7*jBqQ_KKJ>
    QH#V[TV+n[1Jo75#m=U'JeKp?1B2r['J<3\5wj-nD_#5+H0uz7i@O,W!oEoB=e'ea3k,T$lm-DXD
    UJRJs+l7[JH;xmo$;~CUH=#-Yo?w_=GE_QIIV=DCaZlQ@O^}#oDTwReX'j'K]m}L#TJrd[jj21zi
    Rl@1svmV+eQ?2N!5W5uzlvApGeaUvo^,w'_,Vm$}{r<}zDsi[rIG!<#e>7DsW[x+77]*?Ipz}lz+
    X+|$;nCBis{*nWmre1X~{'K1==#CU~A^'^pznKvDC?s1^0xQEzPKliC>RIsv;Kj*CsIG+si>\+lK
    QnG_m$s3o!_nsD$@-x;Z(9oinz'7u5\<O2X,k+ZTIRiw.~>v+lO=spKYwr<{<[Re<lHQ]DmmwCnr
    !?ei;>*>mC{[ukR{3O\R1QnI1BBkpm7<Ip^O#Y,CZG$aR,2w~}xpX5B]?BsHI3*KH;-;*^J{\I#v
    ]}Qk!]z'o7a$U$sD[a1[EO^Ws$i3GmE-~$QK}K[#o^C5vfl!Kj71Z\x@{=1~V1]\O'Ga>-B'jUeC
    px);_}eIV'l?-VH]_+X}KK@QeOYk{<^1^}3Caz$K<p;ijXOKHKjBsZeRe,<Gl<5x=swi+GIGRKpe
    Kw!?'D'AEssTwXk%?DrKa]'-$i+pR$Qr8'?JC|>wD'o?3GA<j*T=>JS1#!!X-'ZZ9)krZw?$vU.C
    v3a{Bv;\tuopK\:TXYH!s5_YY2mzZQu-+lrgXw<D;I1WseDC]n7Zjelrtv-!rE{1}>BC@$pCW-n^
    =x}?B{IW']k}~x+]O}<+BA}r3u}GOrs=!a$vHRC1sDKEl_u[z2e~V'2[iA]z<7ZIiSs~77spiJz3
    _oUT2GxsJwZ*p[_zC;rV#e)$#@Q^<~@|BC;jQ${27'OvVlY>pJ_1{GXw(p'#'@jRpE,Ws,Uv},pV
    Dsm!X\@}!_CUjR?~'h}iXD[sa@easWld#$?j#<pTGTsO\=+-lQin[R[_Czx,?$5,EpepnnQ2s$wG
    V=-a=!e'4w=olB2JOHn{YYBT5gVRnQaE3]%jI5;^BG{E=*J|"r+ERIkO~z~A2oD+Gm5i3zA>AoQ7
    EC{>l\tze~k'[$TN,?j-'{a<zoXzLr!BOH-xO3av_j+3<c%rB,*CEI\+Cj}m$,$!vXrx5I^T]5-j
    UTzG2aswCmkGJueR{OnVR^ujWRl+EAn$2<n3-Epi$I#$]}GCPx@Z{rG2A*R+k_EJop#H'R-^TO~j
    w'={UV3'_q0GC>~=+;};joXaU-55V-Az|*Ba\#IxU$aBQ3Dku$+v7C$2@T5VZ$B,RWD?x<sJu!_3
    D9)=O\$^^xGw5p~V{-YOB!ap5\;L5QCQf[@_R**X#e_o{VoOK2_GwE'$n,AoBR\}]IUn{*uo~IWV
    =H'HWo2Gewq,r{QIWGC6?Y}^GS$D^El\v#Y@eD*@3@wx$USe5K~_<IueKIKwXllm1v>k}{K5]@W2
    XxJ*pl@pGoHw[[E^x1aZ*>j,6y1R_I'{+JplzoLE${o1kej2T>O-HnDzd-xYGoTn?8HxKr5!u*Iw
    !BC\Yn7WHE=xl7Eb$sej+'@Zj\uvvza}vIXBD>Wp'&Tr5Te5l']OK}K^rO1res_R${!5i{V@+vP;
    DXHn1>Ym7[$u>\@?E\vTTQZP~<v-#5T5{GpvCWpCEj+[iAvnAnXV-5-3:kDH<WQvYaU*i2>]!'#,
    ejHn'2A@ux1D-Ez@1QHa\CTwE'^Qi~$,OC{pR1xB$B7oQK<Us<]\K>>l7#\!}uXY+rVZQj<z@#X3
    IiDwn>zVY!r_ClvOzJIVitUDvWqll*x[;IK2Q*I>\aC?pVa^\vR*;7jVKW\d;GJ;_?*j7zZaB!{3
    1>xRUHjO,\Q-U$~1zC-D><n!7~lK1Yj[YEIpvwXv>O1jF{xTZoCO2_awOFmGAw*2[kQ\j'zAxz3'
    r25U@x[$oQ[!EiZ=eaCA+;\e1}4u-vi=?>W:<BArE=j#esnW_H}R'V\GjrQOY?@H''pj'RBswVr>
    LO?sJ1]!-}YiT{<JUTEr3vuElejxXZT\'^u2^O>zV.+Xlsi9tujxDj>-#-wmR3jV=rQGTwC"~Vs,
    iE>5{a@QY<CCx#I1]VYn7;rius}D^$Xz0}w_XJO+=*ne$pjv=vBx3BX+?ZnI;<_]~l5[jll<>xQk
    {b;>HTVA[R_{u</]v#K3'=C2*[Z~OT[eOU<RlHvIvR]GmE+_p^3>[Bz|DE=}e0_=A-|^]1+-n-av
    ~TjX_p<^JI?3R}Gtz<>GqG++1@HEC64?x}J$H@'+THUA5[=<H@A,}\{yx#^Q~e2BxgIOZ$T>5+29
    o+=v'_I=^vn^sw'meR=oxH3=uVo2j#E?i{^p3_=pCWRVI{rv#]D3As_-*RY+DEmpz>[^)kIZ<uD!
    sBW_Q*i{^Rj^oisIa-Qp+w=3*PxYCxC=A2+Y.>q0UBC-e?]a1VGKG]kW-*s?,*<VsJso,@C+kX]!
    -n{W_eYG#D[mG~ss|AjWk]!UvI!=2GU_,ReH'|jD'X/tE_j<K1*CEvCWSbTVU><+]rvYQx%7jOAk
    w\uR}ssKYlosmI7B~K['~]TBC{sY\[mB2HzY<T3ZjOu{xYGO'-afpOv>V#;Va]~jAAm_:d;RX{.x
    GK~ccDu^U1viv5#^OeTa]5KT}ri^XRX-H\\m!R{_ilE~2W_E'7iq=#a$H^uB.1UB~v3mXsinI/nQ
    =@AR<{RQi~W5yo?uV!{X-XU_B#92>DkTD+sM_Ka?Vt[C~~rADk<[U2xB*[+,*=%*,$uz^+\xi}[p
    nmVJ^+vn+2s"}J{2}-x<<ruv7'XZm>Ij{Yi^nU2<dn<GB>w{Gp'$Tv#CC2oQDZ^[Xr@xYjpp}p3G
    G+<*'RxwQIlHKl\2'jkQQDI_2kAsBH$DiJQ{v5@upx?A,ZY2,vYO22wzU}~!Wi$seeT;^pHI^{XA
    UcUQ#5upAB@]Rv*)%^ps,~XG*X5ao13;{z+sHj5Vw?O~~|J<lZ?Iw]QTZ\_-OG\n5_jz<pT\*a<A
    ,O5CpT~\B[mGK+V:'C2eRI,7p+O1@VA{3C-o)POe=3GKO@;7G7\5XKWalK]EY*YB\uE,[#V9~]5X
    w|+I\+3wR$+,v,HEI{Tv*x#}?$#>\]3<\aGo$ZXa~'xf,}-]]EA1K=$CwQl{_\\nKrG\,_[J3IIR
    I3OEHjX$GCZKO+gQ\X~70U'+zBU,5QCr3,AzV-a_7}Z,2?z=vpiln9rza[\v-kn5$\?<5xHR$zsl
    ACe\\'<>WmU*+~C;>o>=zW++~=]7pV^?~a7lr-7T]i4u_5CJ}XJa>3{u1JG\XZ*|_{$m[!IGd|<q
    e2>DS-V_@Eeze$kB~O^oAG?7vJG{j7?j58RA3#fBluWwQwEWa>k!]{~tQDVp__izYXo$4TX5U#7l
    <pE*2b1GV'Cj}JMYK_3]\Xe5lV1RW=j9Rw]wbJ,YvkR?=Ql$kXV1U^~x}UYV\YA[@ao\TU1WvE[U
    UX]>1&fH-Onirkw>HuwaIVmN1p[*_eZwG"|O+TOwO_V~_HAi&+DAHH1K7"=y,=';EGvZD?pe[?<p
    ^WxY=\Hp#zn?msa$IK]w|VrW#j.=4{ao*}_HxnX_U*{{nxO^[vPzx@Ko3X{]p,m'j3QG=]7GB!p^
    WEXNQAEV[jC]Vs\_zQk{O1Zw-eG{tBH]$@[+m_5\ieAnzBln=eRso^V\mY^+v-eEUKGH11TOWpTl
    ;,Lx{+\%l7s$9$VCJ~*YAI;^K-IV2vOQ-S{]zl}CW5C;'eWXV[W^-AZDCr~]}->ro?CnE*/S-YU5
    'nIDCDw'_t9T$~lo7e$_m!\i[}DrDAE\m;#l]AE+]]rW'n>+O?ZRW+YAX1ob{EZ#RsX1sY<2vu2Z
    5JoQIe!VOIl?]K^n|_;Eix<ZEz<
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#E@3G^eB~q,Yj+7K@<6='{}E3sxO+CeXEk$3]8deUY2_[n*Y$ajiCEl=sk!YH<$Kw=Cx@UG{
    $veludOZz%lwJ#*+[[o_n=['CW!Ul$HUlIH-BAI_#QE{UmBC#X;'#Z$JVs2o;#i=laL=mYV~E@aX
    wA3U-H~'^iXQ#n@\}GHmExxz,?,R1J@RjH7(\@R~n=pU}m^E@B{r+UD\O?E~<He$97#G?*Oz]Jzw
    p\kGuisT7~'~EIs3pi*QKiVrKKw+V_E;2rp<~Wp=BD|{z;YP;To3v*avwAQZN]raZHaoXHoIadH<
    $#}r,+Wv~eT*UYHoJ]Ye\~v2p,p},Q-B\!hvn*n^HvZ<owr={zlE;EDh@{uC~>{VkpA5'nA$oKvz
    }z$RIkE;o@Ou2pQ-7-<[OBxw[6};WVCI5spI5{s~s]@}j-3}-I<<5eWV?].55AZaj_!Q'XrnHor0
    eTWkf?}#AxIpIe*!Ol2rT1pY}5V,$'?A[$}2e>j[D!7#TslEZ<Gm2r+Z{A5{RRC[QZj$plW$]HHo
    Go;VU~$b,x+oU}>7B'I=\5Jz;$aVo5lBN9o+p<KxG7l@lGf"uB^$,V?apU1lOCp$w_B<YC<UV=n>
    ?]KAw8rDk}j|lsHGCxYo~I^#[;sx!pBjY7vr!+[wOHH2l@R;\JOJ=CBW0B;*'^O?>I/oWD3]+}u7
    AsK@l7eNK9B>{YR_l*Q_D1znD!B{=efcICH*BjH5<wBQR;{I?x_-R~IGxi$OQk_G'!m~.V!v7L>r
    uJ:js@2'ZQ{xeERC--a\Bi*Zl_T!aCTQav+52Q1{xTC[p2U+*X~{YQTZa9DnJE/M@w>R-_WJjq-D
    {BY?en%;p$Yj[]lyX'7O5w]k@RG*1unp.sW$,[U[eGO,{<D>3d}T1<IE2GW1ove;p?{\O22e\5x?
    Z#UVO>%?-}v^!\mkrm3wsn~{l@$y'1uolXBi,o\Z5a,>l+=$_CX+^]ZAJIn'o4u^{CTaAjj1^1KE
    iI-}G*u=zrx{+uCr\Va7Y*RK77@G>OAArZ[17TBim!'7$@m*IrEk!@^[#uiCl#_l2BqIdQrURC'x
    3wo<'Bz5$\CI<>wu[^mEw[{EeO=#v{VvOUC:nSVJIo[C'Tm-aI^DV2=,}ZGIss)}kKz}5xu~7*2D
    .=J]H+w+YsI2kX5$ea+AlREJ5u=#BiDur^^~aA<BJl;!k=W7,EV_?Oj+2Oz1$z+BATHY;[Ww-X1=
    GzkuV=f[EW3D]$s-e-~;IaYl]2DBK+#EW;n.HY3xT5]TA{I#QWx7*~\_v?3J\*urCi@}h7h;[DJz
    mOHGGC}&E~Bm.o7o{>++R{B*}*u_~r~3rxGY@2RJH'IsIT1=^EZsRtc&hr1Z{W^s33$eU7{2+lkO
    _6HvKB4^JuEn{Q,b$Dr<7?R,GW7K?*-R[<j!@p,RN,COXk<DZOXTVQx7]5#pVKaaal}\kU_RXE<!
    aJR}QTE7s9vVi}~$j^^,2konDW^ZUoxlJHlom<_}:N7V$n#{r!@nUXmD*]zjwl*@zDXG_!V>}3l_
    GV?Op!,J=[aBR1O<BpJ[k3s@'<dEwWe8jZlW<<BpZ*OC}~5'qRu{54]pYs=,TV*mQOc*+ZaDzT+A
    ImT}]=Y}@[,v3~?hh5{u,\>o[p>TQ~-lW)NJw'H3_oW=1#IpO'VJam^pzXI4kA7jeI[V--AjN5@a
    JH<!K<O^GAtK[\G3nv*S@7[uQoJ,.1\^?#D{<'Wl$_uYZ\Yi_i+X;.xi]~np{m}}{^A>G_T1e7VH
    ZnUQUEN}a&*;KsrB5>S7v*ER5?2TQnKv]'~
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#vVAYr{{;~l?]'Z\1swDO\EY}j,jxCjR_}m<j<ria#B52_[x\Y$>$@C[-CJAaajR<VL|doB1
    ED+@Z@aQxvjk}]irz!w$wIi@DFzx^QI}VCX$sD72[jNW'iEl>CH5!UUC]l^Em]a|&Y+@~Er*YpZl
    mBTz?Z5V[aVTa2l}$\<un/QWn-!]vo*~HQb~xl#^;wR:L,H\nh-D#{KE52BrkIkwwT->Y^e@ms;{
    \<Dp^k_1zU]p~I?IuoDoi,{DD#TzUIA{1GeT~v9p@X*EJJ@$HQH6-Y{3rn$[-7<ofYZaQqLfpm{O
    6CUEEt^v_;{$B-YkD1U[7]@=#Y7;YUOxzIS$eloEjIORZ'Z7p*!$wzCC^23c}$DA1i@2n&rU3u]D
    ?HmrD][THZ_eJ*yRunJO~=[}==RBDOECn<KHzC_w7QHzB\e?}H[)>C2,*QX{VX<>YQ{2O}sA>->Q
    v3;~9jEV$2sRQrG]lUzT-B\{*@H>v%#$@[yek~~7{{KIws#RmIj>C}R#^J,}OJUIm_avl@E|vZ7T
    $?}>5@'A\Jj<u^GUG[wm81+DK5H~?AQKEACp_9vUBpv@GsQoB-A-,v'G?E$+@YLr]xl?U}v{jD5l
    WOWl>TxlW$$#$#CWO][#=Hrecmh+Ae}(z<rr!5I$AVXoeAY+D3vZli<U]I[#=dOI~Au=\a$=m$NR
    ke1G5pXE[
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#7p~p1-U=zCWDV2+?@}zBD#@Q[{}E~<m$""=~Yihv!=[oVW}<V'-1nK[[;}<WE~^I>U5v$2O
    oQnO:X<+2U{){Ck;G$k\s{ErsSKwJUz^;};<Vnzx#XX{\VIA2*!lvpD;HJY_wr]H_<jZ_BNe;T+7
    ~>;}^-'{$Z;zZJe!7ps?a27IAleZx*'jmoOG<lBAEV{$XT\yk7+TVV1ZG[2{*VrX$DanHU}-R7;E
    ][[_j}Vx[pYk\_Gk;>r<X5i1Y07xU#s?*$Jr+<GT+r0n<Z[IHr]Q3K;s$V1sZJAV3<{Yi~!?xp,D
    ?{3es1}o11T!,_Qv1uADED^SMn}GQP]Uz_G?H@9@H@\]s@QYpDvX$lu-R>#5?m~Ur@uHj#QGxJHu
    j#Hz7XJTvi#E?>exi_\$Qs#crPAsOJzTSBJ_*Rp3#1*i?lRlol\<K}E$'Hx=!)Y@ulICZQ~^Dv$,
    'Rp[vY!w7JpD?;mjppIT71.bz!Z#'xEw_$!Dt>AI\=N01rI[jjzW^VnBB^1YvEae#^mANBl*$p-G
    H.Q^J7aj1Tj^~16wwJp1>EQSY{wE@XKj<+v[Z1?7\]wOEzI*:ExlVx^!m-UxD}OIp~$^xKe@$z'^
    =k{*Z+U*O]"\mz!+UE]:l3GD5U1a!=Ou&=nduI,[lJKONvIGHWz]lXs?B*D+KU{aae#ek$k-[a1[
    u~sxa3[V?rw3A{BZA]Ae!OIBH]T5kE>l#Yops<n-^XEEK&oOH-w=o!\U}x[vu1l]YTR{>~sX@e~H
    D7#E\<*QCBUD2li+]};{EkG*-[|Y;*zI2>z$@+;:[2BEi]{*WTx@=QjVxRvoE$$!+\@ZedzG?CVO
    IeS>QAD3'3uO]B]~{JXO_!r[2DX,J~a5{smA_;DYumQ=~3VDVrIl<GK1O<G:~eX,N\@RzH{]m8x7
    J<'[>*t+[]~I\*<2YXeQ1IWO$EDVEv{-Rn>U'pQ).vs-z0;7Ei1};=Rk,EIOX-><jVl)W_}C!_=}
    B_k;rxA'C~OJz#;[<*Ej,A>$2-o2pZZZ&Ni'R!y7msim=C_kXw\Ta7W?pk]<GWw#alT=[{e88l2n
    16l2,1@UGU^jr]]rJWxDoUjn\WV~T}G-*mXQpl[,~U>Ao\vimVhoGTeIi'@5u]@nrj2yVV1Ei$Rs
    )Vn='9A,@l<l;'=7#T_rk!)l5[rnB**BK+Y_z}a]HWp89+{U#]7{1hRor\g?I#!GOaO[u}XO=iDC
    ]IQk1;o!D;EI~;7fuRol+_A^d^{1Qv2@HLoDVxcGBx5$aoeWC^<70^!ae?sE<w*vuoI1UV}V!C+n
    ^gZ1A3\V*KQ5J1,EI?x[T}]7lk-a;xF=nKo.UG-2JDa[+^WHWROT=AHGI}[+.'I-\#,aoB_KxNo5
    ?>z\2~H><eVTX@)cE{]=lI3?@CWx*DQHvQ}Qrz73kw*Je*Z5~{'wcI3',R=e=;s}A{_^QKXpXao@
    s05e1RJ<!;yHI{If5I$[mw!HDC{Zpk[aVs@;^rB1zDQJe-I7w1T1:1@[orCrYQkRZ_$\]}^Zzhwp
    2D{1{{w]uZ}KR7eQuX1\J5=U}D$EU]6\+UJ,=Gp?[Ru-w>l@$W1i,is2p{UL;8]mJpQBI115]@;}
    r}s>-C1I'{J[ur5rrUl@H*99YCQr(DY]-m1xev7v1.?[<]\}ur3*nU6xEv;-B>5~js{[+XAt5?Hx
    IRxo_CX}wO!<*,IulrDKzD\,%75]VewKxRZT;UpDimeAlM*KAOnxO5CuszOa}^'pX<[u^*'o@l~D
    A{ZU{>}nB\?w5lAC*r?jYHOQYZxE1+,jsR3T^^Os>2JXCu7;e7MV^M^2C\_k_!veAD5IkH5kD5?o
    ?7kQV}BH[H<nzJ}J+;OA+Z2O5l~U=@AAE'z[?^<]EjT>Y?Z'px$mWY5Q's5];J1eCxO1kOs5^!U^
    jBYp3QKjQa_kLv}[C]x2u#naEBo]p=xlJO3nurX$!Ejk[lO1mz_?Tc1?eW]rKAp<}UA-7~s+U1G'
    ln21KCrICade{Os'-JR5+rK;T=jhG7}-w,Wj%?<$2.vBr{@xx]-5{sanX!9vAHCypk;5,D{l>H*w
    kT2n4Gi5w,EuBnx>,]m\lGuZCo*v{{eu$Nm]-T~\5[P,@L1\Qj~Xsv'}?[KAEv1w~X
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#G8pY_l(O}3^>eZw<GXoxBX*CC+nsGmHN13$_|.]m$$wTU*[R_k[<^mVNC#HZ6HT_i6!$3D,
    Cz{<'k$_rPlwJ#*+[[o_n=['CW!Ul$HUlIH-BAI_#QE{U_[CxUh7k*#|,jaR[7DTtHr}a^vA^tlJ
    +nFE\@2Kjiko_5QXU@>K$>2z'VYK(}wl2/a{=i<s3?H><>B,?A;=xDKI5z<GZ23s@xoxi[;zeEJp
    <le=kj;OT]o{uY!+52J5xpYB-Xc5X'~9BAG#JQpT!CT7'wxE^$C*?RATAO#nGTcx}ps}1CwI^(}3
    <C=VJez,]^U,3aIXX~-IHo>x3mAo=3+=kwVGD*s>CmiUQOU[vrTr;m:r1*_7uz<rZvZE$si{<,$\
    V2!h*AK}U}oo7$+eUoQ]+ov?~aWwuRA>W$@@1lZoI?z~1!TVI~s]'RW_aR~1Q<WJTr\Z]G1=lvB^
    m]Wue;H*r]@m^oYXfB#UQy~w_U;5HHF?<Kp;=1Tp-Ew'>{REvnp~VBGgAOX35153*~r3WDviv~u2
    ,WAeA<Te>_+=5pDk<n5@$q+BE@*7-?cAe2ZxziA'ARoi{$7op'Xz@A<pjI^5a<RmqluX{n,YQ@wx
    Y$=?z~7n@sQ5YE<]WP"$pY$V[\l5GQpz~JOu^oYV\?D'<@=I!!<5CxoHwVaaqnoji;s_'"3op7T7
    AudhNCzi@9^B$[lr++7?2H>UI-xmWkqA7w[\>7$^AzkXoZV}{6f[~JCCs}R;U$xQDAQb[;1lC5^[
    0WA$[5=w35'k~h[DlXSDI,DD$puIEr!'^oUa5*qa^e=V5K}Xs72VI?1e=>{1jE,ZAQZ;XXIs;]*H
    vaJ+a*2,p'\=m{e,$oGN0IleOb^UDH~{-@(qt_weiB;*2v]#3$35its7sJrmw1Tnp^@7i]f1j]}Z
    Ck[G;o=_X7\@Yi{r{!^7;-<jXsO'1m>VoQ[RnwejseTDT,e7noihOi*#Vcp'D!+7CZRYm*<ApAVx
    j<V_1k{+RY[m=TOREJ]oQHIv33-^'@!lE=VOvJG{{r'ip{ET-'$wCZ1#uoUUUo]Rjm3p<JVwozre
    ]9u1n51Co,v{W#?RXB=vM$#A5l_IB^w1'0FUAB,q\$!lHY@+Z]{+IwV\,mwEnTK\p[>Kz5Q$0}rD
    U&Rs#?5,_>u[2Z}eAX+D-!I3!j%p+jw>[Z*C1,zxQ+IERV[nA[xQlZJZBxilW$]NiAKH]-oB*Qp!
    O<R'ln<oBxju7ep$sU]oZ}}~q\~}slr!Z5<7C![WVDCBua_[zYr#?2]*'G1#C/GUF=1u3Y1=Z{}C
    ;F'jw$w-+2zOE7_2Cr[slsRsQ-=nO51>C~[l{#JNn1k!+pQvO\J]i>l\xm*W}G@To{e^)~,${T\3
    7>=Cl}uApn+n~_,#rgOGpKBBwe'Q_1QuK^5'Wm1Anmi*x2WHT++vB}*35#pop=>jr?#ICWs3jCVT
    X;^^UOx[nm*[kEuIzp2Yp-_[<^_px#DBo=n=U7CsUW[uJn:pr@mp*mz[*e}^TpZ}G~[*p[z5v7ob
    k{[O5Q[vc$n5o#$+^pylj{HAXCkj+$'2A'z]J,[e^O~'#u5ns@2jrM7B{@OYXC$==}?z+YEp;#ua
    C@xU~vs~I-LIraBraoYYn;B@U>'^j\Qr<3OqGZ>\jB@TV=5T!_3jK$[3plOHe#5aa^X]j2p[GW,Q
    |()A5ao_lIv+zJ~=QQ$3Ul@84ZBGvuTJXmlo^kOnnO$*'KH*m_naI,x'Bj{wvVW'{M^]_U3X>'lo
    ?^3orvqT5\+G$x]XGA]6Q=Up=;T12\*mSs{LloaCT^e~wsU}H]x*s@7mOs$,fB@7eW]#2e5+{WQ=
    2<7*i_bj~uG{eH?g
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#[U$R^,C!c1Rj;[J1j5o$O@w3vvCX1v~li13$_|.YB[jw5',[W_T]<*ey*knU*jQkT7iTKo!
    EwXxuC|z3C[EfkQ'Be-e]iXa[zx^QI}VCX$sD72[jNW'iE$mnH(v^7\NyI1TE!DJHu<*W0aj?pr_
    Y7mA'^"&1zB\E>s1>Rv5{7yApO75+e!H5o'Cj~!{$KARX_#LSOpKuvBxan*',$iACt.B5_A@lkrX
    eY~DAxpl#jZ<sJ'UoCp~j\mx5AA^]3QAnBTW=_#^kxmGoX#Pp}~;D#p;>YXwKsB<B{;~DX1BkD1l
    Aw<m0<>>Xd6ZQ]#!v-#F<'r7;ji,^^m}'2B;I3eR-w1aIm;J?jeO-[xJ8l-1Or<n3|.6gEZ*~IHz
    5|kB7@.5jwCv@K\z#;J{}E!CTw##>^Un\Y~EVxj]*oK1ZaBB~!HVjWBIpKWTa~?{l'awDsEf~DmQ
    _CV*CbEXv@+=><(rv[EJn'@^K*[\A1Q\Arr#<lJUHX<uEDo0*C\{pvQ_C{Opxa}{koe~XYCZvQaK
    ,au-_1E[3vOBuT\<i7p[B3-UfV3{xt@wrAQRU>&zM,#GW7U2^=#\X'2,;CYcgzE~YBIm]r_v=*!x
    w7=R*Ho>XGQl?-ovUBA[~O-<j3+B_)ree5Nu[w#]Qz@Oi\^\3}@AH{@oAQ\Eo$*_7>@){$}^]Z!Y
    **Ja?]n3YRHGWR2$I@p1{{;#r;1jRCmUU^IZ]_K,#jneOwQ[}z]ikRK<E>Q^o[WwaTDrhe${<!z,
    C2$m\d1njmQiEHyS]Ir57;E<^JWxiVeGK]rWrTAo.x;a>zp25zo]Z-wj^~lzw#-YIe=wz_}pJwAU
    U!Eup<_al*Q2xnpl!!Iip=}Y@IEa1)xHC,>A1?u1nDrJQCGIx+x\^x6pu5Z|{DBE}OUoQOVw=mau
    j(r1rJRQ$z!rI,2_+-\VX{DWGQjp%V-]jQ#AVTj5]bsEm~12$ZT_^nVV+laaWJHR,CM,]1RVw]>K
    YA3U'2vIkn7|e[*+4,T_r@w!+dX5}zgJ$nZ*?I]_3eeywHX}Sl115pT7{SlkJ<?[-eO<\vs;{ue?
    [VoiTeCC~,CYn*e3A5"tN37e5Q\]
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#ToQ-EJC7VC'_'zV,'O]m5B@Q[{}E~<*%"=~YihIW]=oZW*TVV-=THk8FhnsxTl-2KckoHE>
    5zT<'k$_rPlwJ#*+[[o_n=['CW!Ul$HUlIH-BAI_#QE{U_YU-{KriE7XV$3{;#i=laq=?[\y=qtI
    5vv3Evo"L7GnxDHl#ye+Vzs;zI^_QDRV-a<5siR+H_DkCZrj++h3|YwIihQYUur@viEvR!fxIk1.
    m{!Ky="e;@zl+,KhUI$<\{2A1U+\xn}O$BGJ|{CBv1^z5JEQiIoeKBX[A|iUQT=ZBk73=36?UZm1
    x_u&GVXT=o{-jaY?sjxp_G!zwT_pC!+Xt>5AzJC3YktYWZu@z1$7TU*lJXEarY;W,AD]v#v3r\ID
    $KrR}E!ZaGH}}wwW-Wvxx#E]vxG@$W@iD{_jlYnrrx@Ov$v#Y^o[YpD>zxJI>u5}k{;_oeZTEnX\
    Q+Z#CA5<1;;RYws)l@a?2v7u)'rA~~*!^-,w!U[_5B#mXEA<ZZCmX+{~2HTR\Bs1z6~,zOl]w7GI
    OC|}#a#B'QU=r#{=E!E/!o^#H]Bsae#[e#OU:=[Ri57_#BnUx1-[Z_oij[!1[1WZ,w}TAVI+!xl^
    [BZK]kXz78m_axYjm}l;-K>\!HzUDuKvqu=!r's~{[wrm\C?#_szx}{GVoa}~1~<oTjEErR-{7__
    'sV_,yt{*Y\2,ZvVn3oKp?1_5~JsRUXuoon\-\$YOxZ'r\>A]^Wy]H$Jv;{!HzO2XV=o@Dj}BJj2
    Ri*1DDVH6C\AZn'arK\<ww{'B|E7KeCU'CCnEJ>E+jpUQ=)=@Hr^uIoi>1[W[xlosCvB}I2Ms'\$
    vKM\;E?c^us=}@T?DWYa~[B'raxQ~evI!a3!QeOshi,VW7Z3p9>}n_uGXx,;mOTnGYI<,Jo\}D>j
    pW4Kl?DO*Ikrs^R=awAZa]1QQ]!HDk#ZE<a5j]!^QKE'U=l!vuvI{5]+T,l[Q--YZ,2-}AYDUl7U
    -D5},zrT$n[*Ea!^x!z^U7j1D?{2G,[ZH\R;GI5T\#~1vNGVvm%_R<o_r{Y'@VroaAEGC1]|,x[Y
    U]Q^eW^OA72KC$}3yv<KE8^22k9al{V7A<?=Z!OmQae-Oip#a2TP<BV1!1G>Wx,3sV'wz!R+]{+'
    VA,w-I'JTruY@1*xte_;D_sskK,O37Q!~Enm,^YaYLKXTE*FboD]x}kUWU]}@1HA[#=er>n6*H{[
    Tx7ep\a~U-Y'Y#AZwT]l!<Tr[:G#<vw{lE>]X!?1As0?zr~}#I?[C!+^$A*]57*^x{n]Wl[1b$G$
    K6pp@k-]UJR2wmGzOu7@$a!^{TsVA7jQ2Go~RZnY>IVA<RSka]IY#]i:!UDEF>OwjB#AZle;{HGZ
    m{Bx*slp@x!*JD=^W&,B?3T1v@~xl>Bv1k7_x]UpzB<zn=ww_jd$VHa'l[l~}3Y^CUR^}wxpT>55
    wB7]KEv72>UOe7u,o}5.zi1E]J7ii+X*ujABvxU7C}'kUUo=_PxH_VQ]wp?a[@]?j^h?*#[XQ"ge
    Gi=v0*a>Bx_'$H>K_>+mmx!R]SmQC^YKZ*[C\#vQR*VT,emH>U[2V1+}omz]}AQm]Q&O@w$B+*e{
    RX<$W-[tZpVWCk.yADv!\2XGHaIs~,\Ws.oJzHqVHQ$1,^l+sEl0RzIiVEVJXT^rOs*IZvRWv{XY
    I{-KLIaD{$5HzYWB5_T*GkH~3~oRuE>WR6[Do;CJH'e>[]sH!ZoyZOeisCA2-w;Q\rEj6j,oXz5#
    CZVJD,JIZiO_o@jw,j$Iz-p1u*J*YI=+\az{p[VT5v>TnD\l$w(^$m]@oIC*<!{;=#I\Rj~;w-<o
    }X!;s-Y3[Y3{e,njke]n1EExfjk;{$$27#-7oy2=TrVx#ej=GoUYe_*jzp^++['C;<u{l-0w^][Y
    U<Em[iVY<<T[{!XHAV-,3l><z;]KA_pI+mDsWQ\lH>=@s*TrAB*E7V~lT'R5pBV[u5QK&N^brD^Z
    {75"r2wAEU]vRr?vO$>5A<eiLJs{{>r^@eQ$EoQ}]QHjKz!''*W*jwU@2IYnp7!3YWoeipnnV,$Y
    @8-$@BrO#p_Wx]Z7]a*uUr~HIwqA-D\b?nE$^?;uqbv*\ss-1ZgTvas>j?lZ<5]izsTeDw2pO*eD
    {+uO@7^U^7*C6e}T{7eZnGs#@!\EeiOTl[1?J,lkBS=DikcHrjG;QC=cJE7a<Qkp[EKzvW{=W{<]
    ;-,@8f\HaZ5aE\=Zvr|pA2X'a\Ym5o2~-^Z2B[Zw^32_sA3z<J^WGR;UD2^w[uajs-K1v]5I1VeJ
    XwxEsZzDEg[UIx\[3pGe-^Pj?Yowxm_U{'GmwAp:{snszZoDoIoYAzA<BOl}2jGn\a'ne[!Kw\mr
    1ixx<{TY%VH+@7A-kIY@\5n]+CQml^Oa<'<8}p'WD?[BEBp'{xunZruG3}+';B_'J]OIrk^#,YZ>
    |7-*B^}[TCGm}[H=]Vsx3[kH}JaHVBsjp#n^m1+J\Ggn};ofh_A>]bk[@@-VH2'i<@g]Q3#z<HA<
    roue\Yz}BQsO<+Cd9UpZXS~5vx+VxYK,>Q1[;O.7w5{wXzT2e*iJ$WIB@-Zlp-wnCi$K]kTT,u_Y
    XBGFY$#D%j2!BKpKJ7O?[{\ET=-3A#=<QRa<>RC}++Hpu7A7E1Ko}!\[E-Vs~ow<#7kAWf]2Q2i+
    3z)UX>,,Co@v=B7Y!DBf!D5'VAa[m$,nH7pEx1?>zG=ZH{aWv$s^PB2QnRn}ejQxUE;[BfvwnTiT
    <$S[Osn\aTZjl1IliD1#-n]1!tR\X5Cnvva+-p]G}I>EaBR7?J!,lKw]1sl3-ArZ;'ZY*+\;,YBj
    1[=Wrzl?;UC_]rWG-,oH}Ko3{#B,_GR@uorrRz(wvo7Dn+5?CxT3OY2^!5CnrwpZCQYH[2Wi&pOI
    zKvve*Il_Dm1U_!G;5$Ee-AD}6*l=1~=sHz2J@TXzOxH7DODj3Y3p+8-rs1}XW+T}}Y-1uA+G$aY
    Re{2w-R\K}DQCOAYJxCBcyOTw}(CW_-QsYDkpl_Zjm{]^xJ2TB===nDGlJRB#_s<[{UDwGliwvT+
    1T+[IU1$,3w$n[2DI}R3+7]YmGsDu7=oO_u?{X'v~Z,E+,+$7G1YRl[~$veeC+$;rGI!UQ1V~~{Q
    }5^5Y{-?o~e?{53kppYul@l#l?p;{U!p?pi1?$Vx!!@.ReWRUv_A~A7^DQ~ppo}<B5sHzAICR#[p
    QBAZcWv3-WeQ-:M&ArApDElVXaue}*aZqD+w7}$Iswj2*|eYY~j>1ny|z2U2,b3jADQnTZkO_^Y&
    Jj]Jzepio+z>=XIC2l>D}Ie1V\7531n1y:pWsE2]a+A\n$~}<3i]XjO\X<gQ,n}DW^@^xO_2T-~U
    Yn$*aY[~96,*JK2<mrgwOnH$$]B}rDDp~J$]5=x>,*r3Ae{'vZ\+nv5ek_O]jYDeCJoD!m7e>xv|
    PaR_AOz?!2RB^1>C#2oAH'?eWj}D{\CAk=X_,CHY_R5z~uov@YKn[r\]!oU7XYDU**ek'RACl}KY
    1#<7s-QCWH<QZ$XwKOD~e*?C^|\<mHWp5[jA,RpW@HE'"IVH;epD3Q@~?STEmkJ+Zki'x^jzMl]k
    TEC1UVA3KU*D7w91YR!'4{[Q#R}u$wEwD[{W-k\+H?7V[w'KBQGRa]Il$,E]!oB3_-'X^E1<Y8,n
    X2O+lEiUwkVB2rZDt-rkrxoA3M5UnT\>GV^EoHesY7r27W^DE#|m}o;8=vu#Cz{^+O[u+n^@<Tj!
    i+m,C$zH2H$D}$+57j*#RHBwVO$3mvx#tBKB*@[T5?>}3?\OBBJoY<OAa!j}n7BZeQaJ3vZ\n_d2
    oU5uB}YkB';L7AxueUEk]~K='O\1Y[\vo3zv'oD'{<-,<O5a'GuUv>D;><puG=7{Q{*A5GB]>nm_
    lkxo$7k}lvA{wj#E<ojE+qO),Ws_ArVCfN,Re\@,1C*kei!$n5=QYKCZX]7Tu>jOTmSv*Wr(hHOi
    >aQXXi=Z,VBBI*^}oZDX$\VH<^m$X0qxo<<naBDA51'oKTub+}np?wUl,YB\RY$ZwTZ+\;^Vl5kE
    TRAakGpi*k~xE~lK=}3}o>0|~*sK>][~
`endprotected
//pragma protect end


`timescale 1 ns / 1 ns
//pragma protect
//pragma protect begin
`protected

    MTI!#EIr\T^uG0XnOrI~G~2zo-@w3vvCX1oz:13$_|.uBajw5',[W_T]<Bey*Wnu*7lW=Y?<'lpK
    B+~Z@aQxvjk}]irz!w$wIi@DFzx^QI}VCX$sD72[jNW'iEvKlpq]llBkjkes-B[&YA@<^5Eo?{p^
    jU+JxG!A37p~a8H_O+jH>s7gz<LVwr_HRj_]k$!GpzVC<*?1n!3s@z]]{^k7[BA$w=vAaQr=1\@'
    1*3IoOO5li<4<GB-Ive#u}EYF]DCer3@P:8=~<^|Q2T*^aRkmY'O$E[riU$i;]=ODR,#h?UrAU1J
    o\e3[5237/>]+vQs{ZKrB^7'+;2$WDE<VBO'kAr\#2wH>UTYu>_U8ka-@GzoRR15xDl]aY2m}~$p
    E#92-CJ<V@!9R{'E*Q,j1Yp~,Y$pkjI]ORH#!\X+&65}k#YR#HK[Q+Ln$K]1^XX<{D51'uaA*#u&
    ^eenF|p?{1>D]Jg-1WB_~e>_;av_^ul(qxV<?sWuj&'}{a{xQ~EmK'o1kzV'IBYsY^i=Q5BnV+=+
    5\,{Q;|r-5r*#*rrBvAJal}8meYR4VW]3K<,{BiJ\#_+Y?O[Q<p+mTA@m@A1n7?[uD+vwr#}krG}
    iHTA2]uVV{nOqnp[iY;W]u^Eu<GXo?XGW!YrsHCx][^C_D#JJpmpaXw]Xl^+TkQ\Y-nW\Dt*k3e*
    v~YpH'ZG!^^=~r=B<,7zC2mo\n;kssoA&qX|cT'<KfEu3}gx$uToe{o%Mp^]iLuo#W5v{j,VUQM^
    <2?;DDuATZErA=TB#u@oMCf,i^u{E^^K'^XDwD}R9$U}A,]QCD;Z5>rZ{g'rA}02xJ[v==D[Ra-p
    -CRgR_;O]AO}H[}!GH]a5pZ!ovWzsZY<F$7H}EnI}*>'\oiu#Ie[r#w@~2=]'-{~27>@+|QA3-5v
    DuQi\{H[U~#{<7laa?@pzrNkI'lPfI5u?CDp]H<x_73T=\oO{J,Y3|YORi2$^[&D_z]xx$mx7Am\
    KK2el^]o#5Q[sQVsBu5o*v^_1r[GgHrTsPHaD5O$2m${[H[}5Bz~YRR1slL{j\a@>wHV=xU*e1=Q
    WCGkTVnokVlwx,G$Zm~OVm2ZUa#7eDx~RV?Q,Q2Y*+a^{KZoa_^b~o\s3>un*Di<0[C?ZV*^CCu;
    ois?aYj;K?>aa^avaoD\7A12RiG@*y}<x@QP-1=Q*X$B2_E_UEu7z^nH_jUAw=IH$vTE,a;[x'ZU
    :>oVlvX{G3XwBs3'u#=?@*2oTVD_U_-okG<~^T7\VdVzpmi'QrZ8IE7u2vBaIKD2z[}1CW*i^UTj
    Im{#1oBA7p;=wpE3%K1TeaO}-RD+3BU<HruB}U=@}rj7u,p!s'Hj=XUI$C[w{Q>C1o*Bk<'AlCu~
    [-5Buo<zWa*k3loU=p~}nCi\r<jxKCZ+3OTesOVBA+XKnR=337x3Hq]}+-B;n7\E?UUs7]awC@!j
    v}e3~XB$QCZ$?px<-m@=e*jH]~!,aaJHGi[unA}_[mnVQo;we@p'2uVZE1[ElD>^EWo;CrV_\B$z
    ,IjT<*>*E,=<mu#A<\g,El+]KmsZzmr~-G\Z[U3]eiGK-Wr#1'Q3rAmp;JVWGnH?ps32},vuj+H?
    5{3Ela>[iBZaj[v|Yse$g'2nxDCnjv>O77JQpa5_Upp#?[\x'xeJwluWazU2eV\dT=!u}vDH.esW
    [Uzwo^Kz\;DD}~{H}oo$*^GXJ.EA5-$V[#$^D-V]mpRG^^5*WY~w>nsW>>RZVuz^~$EuXzERUT9e
    k!K=+'kQmzw,vCYR,Q,CnO\z^ZQlneZ;}Vz$~^DtB*'n7V}BvBD#VimG''I^5eWw2X~3E=wAUXQ]
    s^eCp2ZO=w'[oXJ@G!_JZzD>vO,'CH^]nlA>UoZK/VQu?C+2r7Bis{*<*(><;j/\$]_z}KpjWRQ#
    -}m(H5]K7Y>EVp\*DQG[^#CaZ$=oUDo>9[?wmr{-?R+*W@{e*(Jl>wmo\s5xm}\J!oO*1xD!=,5l
    zmWII~e5~a'o_Hl'72ARU$I{$wl?>DWoA*!_s;@5#BsaBO57DBo!I$[HsnHH{#^sX?o-K!sl5ZvV
    2wknUa6?\A{2C[jjQrof'{XaLJ+5OrWEDO?X'@lnpjnQp$,oV)p2C^G\\V}s'!-o#$^C<kE;'p,w
    @JA*2,ek>-DnZCeD$uBp]\4-{w_i-ns''~E+tRJx5{oA,3'!]e?'mRR{Wr^+^_ArGID*'7}kB'R>
    @,iOje!2rU]_]^H,Gvv'vL:*nT*vQ2[#UG=oCDo7GnpxJK~_7+r*_*xsW{nB*zs3*A@$B,D,U1*^
    Qw,[=A=wOxlBu}}\?Tu?,WX?(jOIm1_=V'}T1}L,ZUW?1lYUX2u{R1z_WuUWInaz>8}x7noi1C^K
    u_12<GIOGKcu'p},n{e!>2lQv!R;o>upi^=<rz>xpYeLV5#jl1RkDzmBlZY'\xu[zRnk+UvGz;T~
    *k{n<D3r/YIH+RiEo{{nBQA@z^iQ_7m,,AG{m{a[rtpR;~nR[!=~s{n[oG~]\2#v\ocj_V<pza}'
    KIDW7[s_AXI[1\jRnpC96sa^DQn=u[-joH+I,XB>K,YJZTBm-wz^x@pUrIWZU.=#wxgz<Dl,'Z,N
    wn^G/B{$^rD'A*z;XrKOmnUTm4t2=WvB@H2^U<Tk_+TpD*ZxBCap@Du>xr7OCrl5I7C[{@pG@CZ3
    '$l'>unU*Z]_52AAjX}<XK?H+VzseUK+aU>I)7__{^'aDpa_Z$Vznna}z^-,^BB>2seeABOZ2aGp
    1zx^v\uW@RU1Zk>@$"Un-~5ov52&L7A>]H>RD3_TwxGTplCu_1}<1l]<sD<3B{,zU1]+~:lek+mj
    oIlVD5Sg#7w@{[uT;w5~&ljR}T\Bo5KZ@m=+jEfS-XBrC=3HKo;G\->*ZaBsAUs=p?{jy~HrlnDu
    WYa*vk'nHmn5Ur#Wr_+Ko&xT$DrK.=5]zrep}UU^$v!AH^GnrA_iE?we;vG?I8\;{7-OXO%H*AY/
    [Bn$T$'ITQ[_$3J<<]G27m+Ep#w{gx_Dxop!#Euu{_}zA]j12PH=Bsi[sIYa@oT_#>IBQ3DQp;un
    l;4E#r{?T7<]}~J7,53OYoi}!O!CTu^<=lVR2O@JIYTsV-'Mr[~zw{B$2Us~A<uK_^X-w[TCW\Gp
    ]E;~QCCrG1V<^p3eMVGmj6*+>V*N+5-p,D;zg_[=G<[A=R$a+*5{CAU$aK[7[EXVW{ep#sIvm;A*
    7i^$Bp<\op!2KoQsOXxpB]!\sOr!EwaRVQE]w#UsI]nQ>r-\-@DCBp^Ci-x?3*;X-T7#AW1u-T}?
    []$poz5p#A[R_ByJD3_!+<<7p;vJapmsW{QPG\H,'#3'g,j=iT,uHN%nG'm?aWJF'sAx@OV$BKTB
    Xle!&I2>Glh>x*T+QRo"pTEzUIjnK}DH~O,J}KY}5$5vO%,hiQe1o?1?cE+erWs\1d-V{\CQHlx"
    -t3*ue:v+,m2^A[p>!I3-<GTv^Gix[j'w@BP&N[~QjrmU!E}->[GoA8_2R[$7+sepji+^HE1~5EG
    rE1BlT#^$kT,Ia}=+=l1H7TzuD#M]T3uIQNi7wUxxD$K[aCY}!])~7a#nYWk#[}?HDnr',Xx-*=i
    Aom+,Qs1sGk]@>{oDz5TA==a~aAXC+CTZXRI]'IxIpi\J}5H^C*Z[?5C[Ej^<C3[xCzphZ1!;~r$
    37CWu?eRn@'5BHe-~2eU7@>ECwEzeQ,_sQ@s!<}>>{<H!>}u~I+7!{Qao6fDRZ~X_sexVA'>j\rk
    X+*V73uKGBXpJ7,^ej{[z\K1'l[vE-,_}J,GawT*!-H1Om*u]z^\oVvBCH1QvlvA==DsKD?#T{7E
    v=nt[Z1_68$[i>Z}+A'e_'<n2ZFyKoa{sHH_=,ssj}HJNB=Y[7E?<\B#+,,VkK]UwE,<raz+RLit
    oH2v3Ir\GxkIKXO_x7!e_VVr''_^:Fk[<Dm}vk|^An*7[^KYU_zXe!}%R'{e'_2~ZD$}7{HTx[op
    CO;xOCapRw<J$i>3*,_m?+R1M)C,mTw9xCO?!Q1r"YOi*@Y#k5Cp$O!+]Vzps#T>G%7;\~C\uKe#
    EH~UsV[ut~o{X_aIpZp[#uVXnI*TkO,IDAzu^L|1".G-eDVK,Z~,DsBl~!okK~#j]O*<>{,uOKTT
    xK}~*QkCQ}f1jJBrvH'A}$#l?7mi-A?]Yn[[}*[MzWV~$x_v_UHj*2AJTs!XK}EY'+<H}k\RTXB{
    H'JH3Uj7hAp=RRYoX1m-2}*GZ}JD@]}@+}G!'Ep{O7HZ~IwO\C=W7UG5-+-!*RH!u7#GUD#zY={v
    $ApR\kH!#BRv5{oEeQ}mv+x?z^Z{,R^VkRX~m5ix~y\DY\5l~\QdwA}Wj$<oHn<YCu$vW}<wv^IE
    :[,OGHD*_J{7;zUA;?7HeAGKeXC='<e7,BZ;B%QviskYR]rw_KA'n{$QjVp}j=n*II-wv*^;2?YE
    #^53>!]K^[mBj3TH~lm^w{PH]5ku,p]]*Z3weE?X[~,[;2_a5lHQxXlTH\uEGY!'GW~l*-a1xWp-
    $5R=vC~qBBC[j^aY^v_{(/y^l,CI<3XTCQu4p+CQWUrQT[!3'e~?iErOIHw}w$[a:'erkIrVkIlX
    J=eY']{aA&e<mEU'7=2nw*_7Ipy-_$RJD7s&vv1uxkXu<oKuu<@A>R_I!,-uCs<@HR'#YWp5#[}H
    owI~U[VKTp<eGRRK<}D?3<rv:q1QU~~T^{>+uGxGUBwzrOQ!1,Iaz!xY*RyO!EE^vK~{sEW=kl?[
    jCBSwsWleWOuM%sKpHG~5>-UOK+R_vYu'}Y@7@B=;W1uC.{^27E7Dw]3u3]!RTejzAJ'3GIp7]=i
    V3CArEsz3Y!>}-OTZk}a^{*AK2RIO=njV7@5j2\nH}7[Io+DUmvOKeWz*WKwD^9Ix}]mHQ5j1@^[
    jxi_?j7Op{x&sm}AxuoxSwrX_'!~G7Bv[OU_V5u,-WR>]@\U+XEvO[5~]__VIVQ5#DHKwWp[*so{
    ?uUmT]$@ukTAmqfvC<W*vZm;s1uZsV$HHC\>v+wkzu?k|@<Vsy$^ZUJ>}Yom]G\o+I7WloYs>T<r
    vHUs5QG_lGO=]GT''jYXQYp>sHE13E4'-Wj3j^^7+up/+IO$C'CV2xE^A=J^KXpx;HKVw*GG3QGU
    7{CTMVsk1@_1u+_n5!Cps]\};3*B1wTJ1ronvv*=ez_!n$vUn'UK@U]Oo+w+1z]rQ[,}J!sD!35j
    jxs5sH,51d}k1_1esVHDxA}j,~+rG>{_ma2exT~UGG1C1EIo1ApvJRGAm@j}A7,oDxqn^u-^n!,p
    #wB[B21z_+zerjK@[DIRNqj7/o~OX$7^HlS^{Y!P$zK$oJ_T5]p{pVl\lDKuzp!{#xGRwR#@~C'2
    aX_V2zzQKXCCVwO*_uAp*?lAV*aGeR5xhmYjO>QpHGDnrr7ojlO5vRrw@i]EUO[;CT5_2?,?!9>N
    Iz-nlZKop1OvuxQ[rawlRW^v$Wzazipv5'yR&3X_u?[ku-T$rH{u^.mGwv";T=2jVG<}2oD-Ha+Y
    jCl,z5D3p{UA}r77zD<V}aX~B*l#7VpW-rBY<DuwsB_z3\a7Z5iU{{aKlp]1T_#5lm+^*o^VQkKj
    }TmW<!E=$~2s<5#h<T{3R3aQ#'>-OmQ@r>a7jE]>Zw[o#RQD+>5D'Gs;Y'$'@\#?I:zlG@ap;o@'
    2C3p~W7Vexo\ol\s#\CH'r$TGH*TDv>'?YKrm#0I2RIw}kj15{vmeB>[TruK{$CsY!~Y_3QLWj7s
    $RO5kvDGF@Yk1Aok{,}+,Jv,w;X$!}pJDH}OEJ-$-mX2uxQ<l#vooilv*,\ow0{_72~H$5@EO>li
    <QU7U'u7jke!5rk<]Vo!jXUeQuY,G$XnmX3R{]Dv,J7AToIj}oX_Jk-G~uUwwD"T-~z12v5UvHeo
    +2a#sv-LDZam=+a-^7siJTQA5Im>YYm^WA7*'u{5S8&D?Jlx-lXx>Y+QH3$JGoVx2e#>Q-j^]WIY
    Vl!X+{YY3Oe~DpQe?12x<m]0YuYGKxk!{e<]+UCZxWj}rUpwkrBe]ds]R{[r2V>5TrV[_R=R;V0A
    'V?Hj!?Wjv22wsHJHs+$susHI#J$${=?HT?CmDwJOHH;BJ,9Ik^^]+7ARBQT$<W+R[iTiTB!A[se
    oHx~H*D}MmxzUt_\}v=+'kKzxXYDU<l^~_=In}TRBBGTJ'?aaW*s+$!sj~ssOm5p>vOIu%z*[ia}
    +=b1^J<1s[GA_{o\i*{Z5VIJ{AHI-_G{{@sX1,Y*~BrQ\#so!z]\pvku[oHOK77+5\lFv;<wDwED
    ^C'R5znOUrl{u>W^EuTo5$_s2^\oo=lo4+H{<<p?p\v+_QxH=<$}72^ZW@{;u;GZajrT>E!~r~$;
    @}WeK(mL-[ku3>_*[p*#aE@_e-m!9~o{3k_vJ7XY\xOVTGOrwQa[=a_<mK{XX~l!$Qrl^Ik5oe;G
    Js~$p,3[2s7RR^mOi>_x_A<T3;1!j!e@vxkBZrnCD"w1WppEW1#n!!?7_KU*+IH_nK$7pJXH,Y,o
    nxj,lXws]ermx}Dl{x=v?@*3jETp>$]MB2*?A5]D;rKU~A\I4XIarYul7fAHp'2_lC8W]Z>Ic;z#
    z'{XD?{x'o{;#E^!^1rpz-BBn,YXJ#,\iUOAvDsam^T23hvO1O,1$[3spGIpTu2pV\rD2H;YQDY<
    ;@uBxvWYB;fUnx$27vBpJY]!'weG]+YcQ<7Kl^~{_}erl|njR5"Y@p!XQGkiH;vNz;oxbB'n$;Y_
    Ovq0DQUG<U+j_AoQ?5$XH$H?#{l-Ei@l_*R7ksiQQ]p}kGCe*uJ=Ke,o-z[uB{1j5>UVZw\'+InC
    *>U1d-AK>WN->,\FiGW*UXz72*-IKOr1^?T,[^\r{r^u^C2,_eb_7e1Y%l7R]=W3IVn=KE3TW$Rn
    J]V_~,='vmzRX]XxE_1HuYC-pK^Ovv-1E8,!Dm<o^[yv<r@n1\!6b+7u+k{v+ARJDxaslp*O5N2Q
    iTu^3oGZ]of=e{1O2V\vQ'z5a1!_A\!!szKYk;@BR,u1[kem$anH{_V~Uo{)!<v#BsDl];;E{a;'
    $wQjl$]{T^z@1+CGM[!oiel<DK7AkV<][4=]2uz]KGc^n-7qEB4rlGY\GT'v;!=#G#~7Cv[E\iK'
    RpB_5D5T[_KJoQXn+W#Y*>vEA31s5a@H^+$3w7#~5!E%EH+nYV?^B{WkC[aQGY$K_AKs]H{k&m*<
    ]lnI,^-uj^5&l@ap-sRIvA,!+\~;5jR$U5IUTEwA4'z>GQnuAs4Q\$~=QoU\<lBkB\=E>RQ=1nRo
    m]rV^#pC*U~>er$k,xWj]]HB-uRp3jC9.7I},/j[YiEaVkePd=5Ukm}p*!E+Y+o*n[aWjW<sCp;A
    lHpUo_a,I{vVGV7+=.,7ZV=#OpOv!Yl-5OjO>HQCgQU!eY@eCA,Y<Zr3e-esi,a>k_r~-2^+]zaB
    ZjuvYlVXI,#Hj@\]DvuT,V#mr3e1J_oK~,[w35*W-^~x?E\lVTs*U%lH5W}\U#l7,W57Zs8;+vsu
    $sGrGzve?Op1s,7;\v!J]-x!DT5E}kDj7r{-Y}ojCK{,j+O];5wP_JzTi\G3]>^5HpQzoC=5ap$a
    YY*o)V>~,K+ssq*-*IE-D-RK]VIV}WNQ**COv,T1'';'~zlBu\mxGwYUHY=oY}H}+U]$Bumek$;$
    5n$_lG?vnY@u+\\]iVRG@KA+^UHH'T>'9[sv;[IOjEW[x^XKJn}Jm,rZ3vEpCm^rXr}pzea\3[m~
    1-a~XuY$mf8757QTC$*wXXEaD1325i]$laa_T__U1i<5,}@'>'vZ5+rG\#D\}2VK=EpBb{IBOHG\
    '|BjRv_7OxtBWvz!<OalIQZ#]]usQ<UnRH{y,KGB$zvAM4_DUr~GzsYxu}[vWxvGa!^MR1oR"En@
    $-D->FC?!'Ix25prUnCGwk#7+='BV>jVG?,EaxsZT]'zUE$2>zZD,jeaWHU>+~xvRBPwXQ}krW_U
    ,ZBQz1{>l1AJvD5BiEU]w(c7\O_|[;RJ]R\**^~wPkTX5HVC^j{\Zez11loO5j_@Us5l\.GBj]-x
    {Q]XrJRG@jQ}l!oHr<#5n?~<'X)asA5)@G\1___{=_#3$7ovRBn1$Tr;{UelGAQK3E,vu-o~[ppB
    .}>Q,~_#'Omp~43]_j]$p*>-X11T]5_1,aIGiI<lJ7xVX?V#u2BkW3kB$^B^L}~H115VZ2V?=}Rm
    QCX>;;U+^kL,[3u8zr2r1A!QXOZ;l)\I=X-DUX,1eTG|pK,zV#r*fk'-DK[w?rKGG{&q>jiRB<-\
    a5Q^]*#HBD7v[[?+$YGiBB^zcRbvB#+]vvZHI-,~*G[>=?!v*=~B\7?Q@]e]YU{i<$uv|5B{BUrD
    Bt\3I<)*>IQ=w+nIo]x!r?QL~=VnjH_?VDwK*#p]o1[{9o'=o#'RehI@wke,zvOlj=,ewlee!pg,
    $pY{Y1B3^OIGfe>ERGMUGO!0,n}J=Jp}'PNY$I#,HV^CYlJ}wj]ir];1C5DC++BG,H5goeX|w5=?
    WrErRa+B_KzV5AB]a7I2YxuwX]aB5*'?5w\<MoW-}uAp?*B~Tza7ix~{]I[ZR>oK*Aj1R%~He>#=
    7QV2EDxH*^e5~aBk[OZ,w?3R'^U\KEXs]5sllWZxeKBlV\.J+uo}B~m3p_aUEDs3wIQlU[KJV12#
    lKz171r+<^+5@jVs*3^}KH3_sJDOolz({aT[eI3-YTe_}uYHB@=Jlra3x+D1}D]*7=vWpDaKU'Bk
    y,OlY1#H7A{ZGV^sj-^m!i7G$DY~7&Qjsu1w\mQ5^Ho^X*UrnBnUG$@sp3eVXa#-HJV7rE!\<!H*
    ]Eiv]RB>^+u1*Rwv1}SurasC_Ynp{x3O<aBzCz-$Rr]{TWp<eowXz#{Pl;1;-o_i\D;nwHB1HR{2
    Hs!s}^+>r5+^o\}ke5eZ#<pB:5l?,;C5;sVXD-_YI_s1pWe]E)U$u>n,w'IZX+kzxA]1>w->Q512
    3?oe{;Cm[RplK*IjKs^XaXB{IrE*AwGB#vlH~nG2m~pC1-v[,{os2R8GV2zGB*^Y*e]u{}rGEike
    CJaI_vo:zYHI]^#e,@53;Q3uws#J!sw[,aJ2Oj,;YjjQxzuXG,#\(RjZn1&'n!>j-32M%5-_V\$S
    WVGjzU{JJvY\,-;+m7,ee,1=KRD*/ACQ5VmlIC~OnhT,n?Xw<xjT,]!7CEV3eao\<>np=3w,-J>-
    +VCDvOIVm5rJ+<w*'+HTv=G!;}^eC#B[+zS'>Q!OVIEck}a\T^xX<_5ZKrY7wCX^[#5[[2>?roH5
    io_rOkm3B+apZ5A]+>mX@rxv2j$WZ$?=s1B-V]>@'oR5rvYR~>v*'$TTwY>;}3Zk*TG,i+vn17T1
    7;X$}1QOnrxmZUDmGA<G}*@7.D\v\r>CU+^QK$zw<c*o$B#+_-d-nJJ'sprC{RUV?$']Oajs,WTo
    Y\<@Q#5CVR'_l}]E7*+px<TbcVnvBYC=pE7#}B2av]!,2V@\Y$275>n+uF>*+WFsGYxg}zpu1JKk
    EvZAL#CTZ\>$~:{Q!H[r\$T'ojrzU!WrXCHjX;vkxRmo]1M<5Gr2\Koxzve%<qsZ<Tzw$IO<5<H,
    ;U>Y?aJXrp#]a7x@wB=j{$C[?71RKvYnOe
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#lAuuDV@mZ<Xj=Ii]mw$-H,[aq\HjAk[^iY#ZJN/%c[#'^=o3AG@V{hjB@7ppU+~T'BrKWQR
    ?WO4X[+2U{){Ck;G$k\s{ErsSKwJUz^;};<Vnzx#XX{\V={}_M-s+<O-m@1~mA;r?=>$!{,+lZNz
    ;(|OfC{\roT7sZ1\P'3}_=>o,GD?mkrip5'jm<Xl\lmJ$VOk@GGGR}OQ^r{z@V>n[0z;X$qQ@Olh
    BO+pD=Im|J>D#~e<XkQnBXE-#v?u-tCJwKl-Z$wEkjz]X_J[p=FUGoIZ=nxl,}xfVAw2RnY]M_7V
    [A_V+Q#'~k<<rol<##=EX}?ru5UpwU'xvGk3}-pOv3rZ<_C'I_2-zN6k=5-p;!]lD=,'s#Kz@*<Z
    nX*By2{>7OovX<<,r7_xTJUllU<@J<jnnXn-lmGsYcOo33lWzTpvD~1R\oJU}_."5@A}p@'}_*vO
    KXQ'H^zCr-uoo!7-9T[x'RaszK$#^D]5]_KBj<=2Q$Kpa7wUlv^@xoaVEADi{&;\;<UXZ~e7_c-U
    JxjK'jZ5rI]o{=+1a=&\ZVmIHD?o*?TqBU]OH-B^?Oou6I~AO*IGXf'w$\H=?Yn,Xp.M(=r\krIx
    !>nzW~zj'KEWzC7G^^U27l,GE^}i[3=\2X7ZrQ,nuYn}G#vU^i$m}Ia;Cl,;1Y$<EBWI7o!V#,+7
    K=jC2uVETPZ1#TRi!;2CVvr,2r!,,?e1w$lnxO12oueA[2B,3@yX[o@I5l,G]<*l2UA;O!xv2Y+}
    <Z}Jps~o\!YJ5#_,A<2@>TZQwR_w+o2*7JaD;Q]nCX7_n~V}zukxj^C1jKV[wHCZ{D2iC+G?C5Cl
    #@1N"G*AW,un+B_sQiomU^ra3{z7XsXTIKa22QO$u|P"I"kDE/HE5Twa{$eW<D25Oak+WA**QX=a
    {7I~uK=TIEG?J2xOms\AG!#XUjG>WvHwsx,~r*enXYv}e;D[+2\O1T07Rv^nI^amrleDC~Ciwr]n
    --esm-e,C,<*=xpQKI@<DD$Q=>kVBuE,\KszQGGYGnBz#''ma3*kn^B*5,[VBIv?Q+Y|Vu\KrZ7V
    7-p$<G_wG=!]@1!_vBA+-r3ZK\Iu[!<~av7DP#1jXEBY2T-EZbajLwe<*Ulm7jAQuojuYNJ'uKq9
    ?<;U1VsBHD-IAH[7+Rn$[Uj$,{X@ck5CGsT]vO5YJ_alY<ezzXxwK^B,j$-5>=AeYBvkB\3s{RlW
    !insAOA!w52[#^VBT^|w'Cu$e!;]v#e7m^Qr^Y}K\]x{QBXiz5T!*I'{T5vYI~wv^{BaCKY\XR<A
    n*zOqzeKH.\3l'laU{>('Gp~'#vZ=a<Bmsu$iBHk@G{Or5,Re3VAeAAYf^'p71A>rZ{DOE#<X&p,
    z,=n2WU{vs7;puE^IW1_1Hh~,\'*7RRR>aCF9pBCZ'oX]Cz,VRwwsI<X\G?w3k^BB$un$ep{y+R{
    oZ->u1xmK*!{QlAenL^RK!p<j'R-\[\5vU@wmx.'m*7V[XUx\$$xG{@=KV+3|p+D\#<7Z,2vI3}+
    _C2aOowxEO=i~W7on_'V2O+rlJ^xl1*#7EUC+#wIrl2AAs!HQnrKHI;Ew5j-W}=+@Rp}KEC=Q+'$
    X;}-^UD!^$ZG~oEkw$e3,aY^2AG~v}*K}s!@KB?m}fA{p1s"Hrw!V'Uw7s#;osKB$=V^R;zuNjGj
    @72-1;pKaECU{FY;lT$QCjK[QOq:f<<VW|-xlX1v227C!zOJrU{[o@z*O{U{JB_7n{5#o>x#1Q1[
    \KQVxC[@u]G,YJrYXn++OB#G5YoC>Zja{,%ct5=T<QIvoR;G}=sBmjJ^U$iU#gXx!'I>UX}9)Y!1
    kn<C'!DX]I\*uE*jpQmoaIUu5Q7GO=9oIk_sEXns=-l_?vn~G]=[$Q?E=[^=i3x|{[QK,R']sk{l
    Y1lpDCNGaZQ5B<A{U~W'ZQE2jkZ_^kxxvm@X=s+O@@C5${'O\O'R\i\B_u*Z*DGflKHG?Rkp3C>-
    <wpI\'elzIz[I1zVKXY*P!eRC=uQr{<[2VDe\sX*lB=ijC7mBzu2p^z-?{$#+{C__z?v]"ARaCL,
    ~YA'v$=}TxXb?a!]C_-BU*{}+x<D{o,>'lC#oRp,7%XzE^pu,{?\5n6'B'~pC_ZTvnuE~^R!lr~?
    pC?=sDicUTQovJ][v*j+sua2XXtm*I!Y;\'G$x*JD[^y55J1VzmjI3K$!Dj5,^Q!vEQ~K>=!\IIx
    v'p!<<T!IUKwL=;W<]5$^XxnA[a3=5REK]xGIZBnu^p?>xX@Ef}wa^87>V>FrQ\$!1[jjsEB_;w>
    K5V<\jpe+ep#lo?;+],rm1YV=i3u]>Dsl*wmRK;TxBA\l!xl@AI1!S13Do),iWT%9p+Ju~5?#2lH
    E-1>'x#_vnl=B&CRZeSAsuR|&H*nze&:BiXAO\#]5aRoRBKQ[@-]FEe$@=i^=l]}iX*=v,u1J2E<
    [[2]iu+Jl-_xm}oY@~N}2CI2pXrU]Ex]#{\711UAji7v_^vQ\IZ'11pj=['11}]xEzUi,2>UBm[R
    5r-\G[,!n^WH7{@~},3r+;[C44[p1xUxCG<TZlkwZ?X'?aZsxu$w\>}?Y-p5{n]SBHKXD,J2n-Uk
    m}#Yz3}e*s@5O={XI<AB[OvUn5AuJ^v!*UTskXEJzVTAIX{YY7Vj\k,}'3*$)p=<~{+Ze}w2E,Z*
    pz]I]@I^nv!<Ju*un?A*?,wYKxun-+AwQZR7JD+TQR>TnzDBX;1{wvY#_Oz+vi1^sErQ'n$YpsBE
    VCR+}HoZeHT-aB>@3DsopJo<3$#CX;}$;knmlOE[@il=I7!$mX\-!7sxr2rr$ej?,?<Droem]\vm
    z}e-wpC#,BJunw'[si\1R12DjJ]?~Em2zz?DmAB<=uDB}m\B3B\W\^!vOQww;x<<<4t1i[7xs}?2
    $s#]v-7[Yp[XB1Z;5_,?*75Ue@+$l>]P_z~uI!T^x#J]KYprwo5QUUQQ]su1knKeO3IuR?Y2*Da[
    :1Io]J<+O8[[^BvVWp5u*jcr[>TknQ';'QQ3^p\;vU<F!a+^aA;>V25Wy?ODuS|dBnTJLvz\CMvZ
    naM|B>ZW[U3J-e2Or^b,lHG#^m3*@Q,Saw$@$^jUEv^@lY2X*Kr,~^pOi={[0u]QO<R2$Ho+[<1m
    E^s@;CrkW,YBVN3B}5uEio,la]ena]C\}?)ED1u?$]lsV<<xwRk_?+B1ABZ/kL*I{;F(t/HjoI=J
    D~|B}awj[5{SlI\K;rC_Hj!Z7arXbJ<_o-^2XAB+uUao}}ZjQPmHoDg+jj;.sWBvVO#pW+2#<wW\
    #vv]iU$$5D5w[BB=]93Em={Da3SY33u>X\B!sWjlO{_HQ.=zAu,Ej_[JplpEr$D7i[zOvd>DDiF]
    eZHG+_<Bxr?rGZ2DaXn}CRlmx']G%'HmRLz.^nATNIU>5;^_$_Xp?H*G>6@,$lzA]XR~G!![Jo#C
    DXk1rv^Ok#PRJeo7=j@eDn,riBe=]KB;B~lUxRX_eC}Or5-DBzVe}*pC<^RHx?aAp1#1W{A\;ERy
    J{BvVr'oh0$J]W}+Do\Xe*A-WUj[<W|y'B7Qr7Y,noI$aC_Te}vI(QAvo*rXConJ7E*5OUBprnHD
    [^}m7@B5$ICE#Q'>ChiHKTX[WOYw2Q1vAWCooli*xH.^gG{W;Xrv;Y7p,\B7DSsY;r{Cp;IYZ7'W
    ~=#<5i)BaRC-Y[1yIo<;7Wa<TEcl$zzQ$KEf$w75p1\2v+,2^|"0ZCK#Kanl_5]rpB<;nCTxks$e
    *^}sxHw}Z[E}KUZliCpzIWe]BC$7!}~ubUTT\Bd/+_[Ri5vY!V;G,s$C~VIn?=XR[znCarmC?^Gk
    kOIHZUp+j~'~IRDx5k_mC+Q1;w-*grFPi[u^)|lE{m3a,1-AmR\RXznp=p'?Q?t]kAWBJ=CE55T1
    !pHQ2rz7zCJInHoCllIlUQ;+$DDZT{32CKN5,D}xu~zC=B-/W\@zQeYJ\<\w7E!CBI-OdaT\5GjB
    ~lWWK{v_Bk]O;G;B'<Q-553U1TGE#k,QU>]+ju|VHvl3[TCnwuKe}W>!jVE~^^7XA{UC#@XHHK,e
    /=JXu1aVD[S[+G+zUaTlGiQoiBj$kA*~nRY-^E[voeC.~pQwY}@?'_IxXv5~_$#lz!wQoa]nUOK-
    5QAuMGu[[1;p5HGD^@U+l+->Js<'!g3V#$ne*<{s?-i^#OaABYx<lpa{Z5@D{~J*E=ll^3i*jY>j
    {>cDi*u-nr_z#*k5jpEHQWz:{TrCe*uHlH{E@.as=K}vO7pkw!A}YOO$C?Ka<]52lwes9v]7O>wV
    @$-ArWv-v:{l]!*v}2lZ]Ro{7Wzl53senz*G*+cY3r^\!A#OV27T}*~{T]!!>$}pko]7x-A$U[;_
    aj*RJ5pTE,+NI;XJ}~HRVG\Iu]-A.UUmJW{}+Yj+K*1J[,2V-rm*\V-A+QHJmQ$_W*?u!'UDe6WV
    T@~Is3-l+I6u_V}H[VI]*J$=Q;Jz>BGJC-xvGH^=Bwv_rGB9BXwCCaDY^oC;R,T=cWHTIo]uZp5K
    TL!r{'1\ZX#U3$5m$rUC@@Qm-<QJ<1YEi@3\K[.z$p\YJD')5kpH9pe}\CsYYW7K^#euol\TJbDQ
    [2=AW[XHwQ[[rpa>2Bn{YoBa>J\{IZ<{w}4G5n{+C{]3hw<2T^eiuzE-3j2@~V@Jj11aQC5;o0;E
    <ouB~k,*xl>>3K*}J+<]<!x<R@v7H-eJBG+7UJ2VkA\H22s;]=$pl~lu~#LdK\+e^@DQ_{swV};H
    !p]RLp3M$Dvn'A}=55wB57zm!7QYH]OvvnO[hAX<~~lw}-a]\\TV'3v<OGnm?Y-Im.^R#l,;5+'[
    oQ(BV3mn'0*H@-irv-wU^7C#vp2s?,los{pk_Q>^^rv?Q2Wers1s$oBoO2oQEk%voCwu[zo3D2ps
    2[Evl-?p=]T|d*jU,vkAUOfznT1~{TYgQ!JVFY*_'cR=ujz7B5Y[mmiI*uoRE{Emj3v}A'pp{]C5
    X+zATE*m},E\[G2[2RIE~s?GJk-Tu_ZE")71JVz2Y@fKA}jb/!zzYs+pEB@2?<,iIfaQ;3lD^5He
    ]U$HlDQbJ+mG&=5T?+VA+0zDIv^Hz[JIB?Fi'+-z7IK/jVIDC<r{o_TJWHX?PH5Tmz7jI-1lEHAW
    Tq><m{I?jRers}*!UnBp[CXa1>}A3+27=JJ<{{A-lI1'iUiV32yg;wj@s_3!5#X@1*ls_$5\(>Ip
    x8G?YGGB><Vk27wD~}*E45vH]eu<k'<{ld*]Re#jH}AVJ!-VBYB$pYkO${Vxpu5>Jr'5{xm'2Z^2
    <\?^#+?saA''ko@zeW*_Jk}m}!fV!A!gW'sXuI]~?}e1X\\,lRo[w^G=w^i3fuICHC~{k}x*KV,H
    H5I2JIis,sE\>=Q\-+=J]HeV2p@2]($E-mXQ-H7@jDeK*H1[{OGrxpBR*+-GixC>2=!Ow#'*T3I#
    ]vBYeIo]}$r#,[2QR,ZOY=[B3XaBR>H5>eHRU]!<=UA[;BO'RVw$*-7xosari?*>H7YD/x^]<,Qr
    [7KAJz{nB{Dj=$A~?9]mR_SZEWo}eoEgol?}1$<Gb@\?Z{{,AQUUQJtU>m]5<=7H\~kNo,r\nw\D
    @z;*}w;zOYA$pH>U~^upz!3lY#!D2<C=YCs?CH,lWD@3OW@]wxA3=)p=~*W}X]eu<VaTW<V<R}BR
    W=Iu-G7s@nW<7]>TB@ck=~r3XCBWxozTw-ovO@GvjuW2e-7O<7Rzps]5'zzE*wO5};Hllo\2HR'1
    AXGeo$*NETQwX^r,oO^_d=x+=2=HAdK+sk.V2QZOj]es<$YUVJuT'>TU<['[}@mN#E$i:i'Q2OJ{
    Yjzi*ZxlmuHlU4>-QCJ]'VAn-Q>Q;^j1!$^Gwl[+<xZXnpiHGZT>IQ^~DwO'%sv}s1Jw#BGu>b#5
    Q>Jj+T$~wT&LP$k=l;[,3i5H}m$V^aD=\]*AE:-nrz~AU,{Y1-]#W[WOmw,'m-_vT2jWe{l7]'H>
    R}m{-!^jV[a7e5IoBm2U3w^s5+{BH7VZ[G{5B}1wso5<xR\s?o^-A!yT^HKmnp2Q*vYR>{~+D~T,
    Kswz!}H{o$\2v3*-x*5Klm]?lnDuUrQ$=Arw[3}B7xQ3q,mTo>_<[y(53jrF@x;sL-$CelkZp#h\
    ]k*~-mp[7jo;5!=eHZTKVu][s;,=3xiiCa,]A;_^!s+T\nnXB>DA,-[sTlu$$z1s3J=]ex-B#]]H
    5jx{]ez9V[QpD>lA,_D[Ep7TI>wO$-QAr[EZ1BzH,ioi75?1uTo2cV-KIzrHYHBKVBC'C_Za+pW,
    ~(E=?ZIY~\+rVD'>J<~_ak-Vo?PHj<=x+!{~>'vi'nc,1Azj~;Dx$K]<To;=#W,|$4A{,eTpl=}H
    JC:12l@*+KJHRWKJ[usIGQIO5Bwk,r]w-5X~Q=E3I+W!+-On_#BO,kvRJv;r*A+PqG#jIBWC*$X[
    _TU+=Or_@%@UY}Q+Yw*#5^Ixkl+'mRK8'x!{wV@;v1=1y%&e_EE<^HWz}3=3-W3BCa]fzOR_m><'
    Kx5!RlzYOpJ]GA{W2}*iw^D,XsUx2C1+r\+p#eK{UsW}Gs5$VBk~}!V}mC>-u>_3IKJ!<eEjLA>X
    +yrz_aT=le?\K<]]uKo-eZx=H-J-e5X}$^BZZxl%8"nGuvmE7~ZXKCT[@l{aH?,rj?UD>^smaZ/D
    |\jZ}d,X[o'WVJm_;Qne*i7JBnsXH@'nv]@rAm[$j}-]l\O{Z'=_<sQ*HX-YT]}u{a^UB>YR2USA
    7KwHTzZP~__{p5D1g]H;_$=(KD}D(nasA?_EAWlKB#HBvVB^j_HRVNslJ{7*jHX,51V<e[6rBOp]
    O*{(+p;J$xj*bR7DGeiIXAR{;j\3[p;[RI+sZ*Ej7YaDm$OIse'oZ[w1=7K5}+U3?;_pTHQ$\Wev
    e=in+@1i?;R>k_}~~O5Rse#;osZHZ[[JlYOzJUR^aV2**+$KU[lO@7p==h'koGa^W[>7m[<DGkY_
    7+1]W[Gliu*W7K~<^p8G^3V#HuXhIDTGQQ~$ov3lKp@a+'Vk/ev?;L@=VTr7Y+p!De1<zkpuOuet
    mDx5,IA-<R~T1TH#6]{EK{lnJ1rvXS]ali=ix2C1x7I!Ax_i>ZrY5J,zxBse3Z@V-XGTKz2UY=es
    <3,$*^Z5\5W]QlClT}OEusTw5VvgOGWIV=zs!He5slr=G^2D?\pU}Dk=~Yv@Z+V[wo^]8'YiveG_
    s_zeDHlvYaI-jWs]Wxa=!'X'~ElTU1-a\7<WKKal5v7*pwpzC6QEE5r#]ra]n<>-}M*+_*[5w>lr
    Rsl+~T<pD'Ar5\{_@7H*j,'AQ;aeGTD_Z3CBW34zHQW3G#D"oAu]pGqr[jsT},7]V{p+xK3};]KG
    mA{9+V\$>,3vB#^$lY?WO<wGvDr=>Y@mk>l='uIo]V<wGpQRXpE+gq[D[GG#v+R*uxN}G7T9'kVO
    WCQ@<zTwv<o5va1'7]^>3_C!ZeT'B}riBC=2siGno[s36$'TTD7#O@V+V+[oXziG#rE<3W5*ARHp
    Ul+e?ZX<s)ez{TYZ^\$*B{+Bw!8w}e#wOO}-wKC?}iO^llxwGmD=77T@Xj[zCXK5_Qk}R5B_J1T3
    VTCpw<YklJG\a,p-Y#p#z$>1I>@+r<Km^omdx*~r5YT;uADzCTvC3,,R/TT<sOe@@Evis\u_!=pH
    Asr1x[iC'[,x[xRBvEJpwjr@e=vRV{C>n<[R#Z+O']^Wj^#VA{n'E\WAE.*$Dm5^wpRxOsnn<!2n
    wY7VWk\a!kPC+*I?55~2*s'[jx~za,ZV?zZQW-Y!=1~QU<RY*_J[_u?-x?Y2pUY~>;WAURY4r^{\
    $x?+--z]vv-VZ}o26A$X?DlEkT\r,=r#E#TCDm'ol>RJ#=n<rN}JmX'+nze!D19,=]DhpeY$*i5>
    saWJ?5*Ar!sjE1R*Ua^vVAK}^p]WI1w=]<Ol<-Y\YB#$$xlz1DAa2s<uJ&}}=kDJQuU'U$}vRYa>
    UDujl?jB@K1^?a3aU,vs-rD2@k:r]?jC3wj;jQ3vDsKRm^nI7}Yr<A\qUslszr,pl~=lp[Tmsu>j
    jCr@?ekEB#R#A=1Q-{Jk->aIsODi>=<eZG<27!CAY_@p7>zvYnUuavU>rUZIV![<ND{jioK$px!-
    CVaj<O]iGLeR5wwV7k!Tv3z{+jJ_]RxZ8VC#^*7T2r5e@_.-X,[1m$Y}Q?}Mp2,=LjrZ[)r!\n~+
    r3
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#X*WlIB;ztl$o>^TG\O\;AEK*>O>vV_kY[G?Hk7"C752elxpYv;$@CBi_WX}TE'}EEW'},V*
    UY~Jq$--s~{BBY6+1$'Q,AmOoXA31,['>Kj^-1V-]-]_7Bz'G\o^ACm@]pkl-_pR-^V7,<IHria$
    lAB!sr'-(A_5[y%\TvzF,C{7@,T21>r>=uXZnHA=OZ5[Z+[',G_G@e2UQ4;OwREUWT/&+[Qo+U<u
    <Eu_rJZU-sYp\}u,B3l$Wl'2}Z+l2U1AzRA[s@;,|es'v[+2uSAeAGlo?r3a<ZlHw5v+eKD$2!~x
    w[-*VUH_RUYT2\JADJGJQJuRDp_jBia[\DG*>Z]X[KvZAnsp$;sYz*^ppB_sn-a\]i+z{z+QY@p3
    A#5m1!,i{+eGOlVBWWRxz*^'I@$K@sV_iE>-W^G,$J!a*@vIRsWDU~&/@=r>O>-^7@pscnCn5IC<
    H#CuH~xCO'*-EZ$sKY5,jK,o?]Kv]v5O@7]W!=KV7JRpVe^>eClKzr_J2rJrR>E+?]s[3!&rux-i
    wvu#BxrwU1le4oG{o2UeO>XBW~C3nsVWYr[i+Ix\~bl;EI$WVQB7Jj5]UBr2;@ven]YA_m}~Vjr]
    =ZYanxoJtu+l'77kW?oQujfpaB[$O~\<s=m:Wz,V^u1JL<$]e#7u~]\?ZX-XJWjwo>GT[CYlrw72
    @*.T'[#!,Jk%G15~Vl*\wE>$zXp=a,D?G,x!C#UVl[G@1+jA@aBB#U7io,'KoRvl]eB>-v[oXQ'J
    r^w,%!<C~s~~Nn_-I$J,\vr>G=rWO*?xs*DxDU\{-}\x_HE<=HI=Q,zAAVQA2YGJE6N[jjAeon_e
    H@1|UvWG[arKk[<GSUV#Jm7J@.rI$[ICOBJE;WWG3~ApI}UDXZD;_v1xIX#1Iw=3IQtw^zO:'^x2
    UO$wZna<gr-XKxKJAf{'oxx;@l[p!1HEWJWGA2moCem<Aa3sC5)er,#2C^Wx5[,=XE}U]86<z1ry
    X'jmO52nr9jl3}VpsYTzQ=_xV^\^,*!5}R}VVU-[2os|VIu'~n<rbR$UB3weuuG==hH{1iTVi#VE
    #kvu\!AUs7B5CW1>5VJB_i>omU*#~KIc=izvmRk*OQ~Ih>T^RFjIZ@7};*Z^}>p*jpiA'Uan2}*K
    0jD*ZwovAOo,p73z\N-<G;}^7o:;-}HbaQnw@'+1E]+<)o[J<5n-?B5<V>UvD5^s[]DUr(*k_27u
    Gz;Lv_'K1WY<[jUZ}Dri?okJIlmQ=-a<Cr?[\G$oUXlZY?ma_z#_TYuWKYn2s('3}m@='$1h^I#Y
    ^XG[HVJHm-w7wGOe)I_[pooDHvkTRnX>@sUrj';X~oks'g$a+=<7~W}!Jo|A>^K'}Xj>XAUa^j'i
    s[?BW{@5xp~]$;{~p'UMa]ae~HHlGpTaTI2sfas\eB<R>RnzHx[p$mEAE*m2,)2OWpxBw-[XGXo,
    X<~p'rQek#7#D#wnE-5{WQo~+x*\msYpC;fDpk;nsYv]B'*}RUxe},#y@Y?upGlZ%:|i*[kp$~3d
    RoAotreZ2]w;z7r1evWeaQXEBa(,f<<W_eG<{BD[rXCi}o37l[[{\"g,>@AH_vDnjY'J1YaxDrrq
    rETC=zlK*T*ElnQ#arrEo_oQYTJ_<onZ0}_a1WUO=~7,X_sw>yWDxnmwTnq*Z>KmC}l,l=r#UC@=
    Y]]-Y7@<'*xUG>aBB#Ai]oQO~YO^!W^RvI~m'oHvx<>G^2U~oA7+$HQHX-\l&p!Z,|B='_*p*16a
    G};]Dur[V\\J5#7X[l#iz27~^1ReOieWj<rN+1lJmH@Xmlx]Eg1GEljJ1o!<<C>O]Y#p$=!$1};a
    z,Te'CxJXePj'!I^GX^ze^#$VsZJY-;QJ,Re,,k7-1vJspl,jQlmCeB?G?Xgv|]Y@z)uvvsz,+?\
    Q1a_}w;%LO{z1Ow1Qz'$z/;nEJ>IlsNHx;U]+~WY[D*E$;ZB$T=Ce!^yxEEQ1araiU=Q3\s@eU~_
    <]K32hrB=Ai*Iw*}[;S$Vu#oE'RIRoV_rnCX7ksC@Kexk__AsJ]F$}T[Gf^{~5<TXaY{^X+jeGz2
    Il+^ER}C@{ipX1^@n'}}1T<s_;lY11xW$K\V<*G717CizYZ[wBZ_-'}>lTeBY#qu+TkImE\i1@a9
    tk5Gz[YY#]E=7Q;vDu^,jCsjOYno*AEm@DlIZ\kr+eoZ}x;*WFUrs!:!zBuHnueXU~]^iBn_m1a?
    RGlE}H7$#u~Lq?B@]DX7D'TBZv~AA\2U];E[{2YwziH{KCd[ijjreewl@mJZUIH[W~[B_Ow}xDJ-
    T'[jmt|wokjIJKzxo5aV$*A&-ERif,Uo7Oo1;Te'U1?X]D@Kr2VUjE-Ca]I;-;_~Qljj'7E?KO\#
    3+-oA{,vk]k5=PQ*Za\wZ5]$1Q{l[;V>7?U^[l3V}J+\2UI#[nns+7y<]kA@neYsn\,=J-z+a]}]
    x{m01M^2H#[jk{-H^lQ,'eK\vp!>$2K^TB@5zV=<X3}<FrjU+7X}ZX}HT2e[{r-5pD]#lm1!@lR<
    HzO1H+XsWL>nWA'2{3s<v5EjCH.]*_-tkD3EFbNl~zDU*VxJBoxYkT!IJm2wQKk9uoHBSj1an=nz
    Tx_<T7+{}Zl7a-O=[0fQ5?Cew+#up=_a=DTX1*=YuzOrpKED@@U-7krKI[=WOTYk_+<w5>naDzms
    ,aAY{U}Xl7s^A}oHDwY=8f~o{-8ZQjsr!xxZpwE?Q]5JH,^D,ZQGOInO*Hs?YD?rx7k}@W{7]OO3
    {px3aBzRE<WHQ>V]]YuT}*juBIs7<ek>x1U{>@R=Z1mxp5Ep_HBmVj5r!J<e^Y@\ZaK7l_^vvA^Z
    {_ioG_RWAZWcnRK2k1+!o>u?UlvT)k]WD*W!o"N1}$}2>O$!nIYZ51av-*}QJs]^*YK2\nl(sVw>
    ))_e{Tns-H1YN(YZErsjUkc/({<3>rju#k1C3Q+ZATAJDd*,wEJ11_V}a!\#NE>}T[J<Ama;2pT!
    K:ITe*e-l=<[aRDR=K>AB#{s1[=za3IIrvx_o~CIB?E7>~v}=,Fa,73G}3HI?\^]1vDPI*kX^X''
    {X*3$iQ@;+xZ\UI^QoDsGo>Z#^vw0J7O<E*!X)XC~Y3Dz?1-@1Hr#nH$rA>j2J2EJaEO_nW^*kK5
    sw^ADlxnsaQU7wc}-22{o-7RCEHxIv'sAUkFTXToP1}EmTa++*~JAW<U+l^u,H5+,ur=5aE;BR1v
    3$?$w-ovalV\+:lv_wlJp?QRUsiAB_S5*oHyQ=JR!wm>?G1n)Xzeeq]lIG$I,D]HI7ZI^?jQ7J?v
    T\-r\w-DrWAar12-@#U<Cr_[\{DipAg?&w*u+Bv}O'=G~aTK]x_G>o;5ni_T7@+Hpu+lBu1nXIX1
    #v+l*V^=TsWn]0A1#<?E{D@s?BUzXBTs>=pBKBY]aTQa@+z^@D|r~!}*EJAR1+[e+e3I$]rH*l}#
    e,*Cd$jC^^D_nr>Oie#!v,-l~{za1}BJp{=#$Gr{[i1H-DU*B}ux<=ximaGrXr*?AO}Ja@\Aorj5
    wTeAK@Y+{#^=]{eAUWowwok=WjX$pU*erl3pO[B7e9+Y@+TpkkaErH1B#=1Rz]~w^]Ox}O#]7+sO
    UED*x-D-zH^2rJ'AV3=DAxaXw2MjEGC2r\1Xn!-6Wj>sO5Ia^-$1wUlef?wTsWI>ujn-[GEH,r}K
    z=zxTIAEp'1oAu*+GKA-3+Q-whE**[6{{*\vB-npHn2+Ron}'opQQx+-TK[#-~<rr1wiwo5o7m7M
    I$oKr]QDA^xaw}aC#I'nLKCkMEqDm[x=!*C65'5n$Wos;ErE!H_Vo,QZRpp]BC$w*B2JqiaGoe5D
    +1ne<[JpDDCs!_Uz-pJeJ\\TYQ#j]OKG\nxwv~5_w<n^mYvl@EU<D5O<!7xvWY^@!$CxC>YDCzo~
    [!>,<5vem3x#2Oxv*e~=EUxEe]E1\#V\A{zQp}D?Qom<x{\'Ii+2e{o_u3aQ[Y<Z@t}~o[d55?Cm
    jx*G*QHnwD#Y,Rv;QuVEp>@>zlC=-^A[nXRZliITwTe}mZWTY~<ql[,_3nG$ov@*]*CK8rT3zi+>
    sEoeD-$W{]zA^_<IV3*_^!{Kw_UHo4rjTXB-W7_,]*1HwZ{rG^mU5eJI$=LE{pCx=^nD}~z?_mJ{
    j\_5o$*p$+~VHwjHE++QEjO{]pDWC==J+n571E#exi$i7oYU\}-j[={9a=rDVU~]_emB6R;$*.>7
    >_[!2WcKXB^pr+n;-}Hs}ves@QQx<]}7+Q!}xzr3rVwuo!'Ow}T~s#$^|+]r^5sC]VI\\QX{c_,^
    H^_lpC*X'IH*7vv~=}AzW|Ol,-.J{@Q$i~QE@o[|B_jVUrU-i'A*Nq)*{5+g-[m$H'KT_1wrJB?2
    o+e^#}ixH5R[~O@3YVZ!j5VDI~R[t7-,j=OK>T_on6&Ye_>{nlO~<5poj<_B2DRKHla_!,'tZ51\
    ~[{Y\D2QN<D!5{O+BV+n1jArJ}-5s1a]CEC2!uBaasupzON.$>j<HVYk9[;-$.w]iz2eZDDOk'[s
    BO]UvXsO@~i53Y>{Qst$KQW|Y1-#_RWD=AAC/H{Tl52UJ<IsX1B7u)zrm7:u==ijrsul?+R>'Z<k
    1HV$,BE$Ejs2'Dv-_+u:Ip3#E*T+*!-r4v5Tx]T'k4p^$p2e;WYJj\HRlomjTDmjCzR3$WRa=\O1
    Um<lDukr$?UGlZIen'i=1wnHOT\orsIiroOJ<E'?XDOr?@C~CO]-GveiEz\}Y-Y7]zL$B~~&QIzZ
    5W-AZaxCzQ{_b}=@o7;7o;j$oQ;a2;UBC'm{}\il@+'kzRCEG8o?]j>^oj_Y}@HV_}y}-KQxK'lZ
    pJ5V\vJX$nB>R<<+YsRive~W*n,G.*Ua~vE$]e<nXmXH2\e~l=Xjo?YkXMBOnsVX<!$G^KQ#O+mH
    ICU7o-(>'Z?n>KQ_->m},>$#'XGt{nEiCZz#5j,#yF]GZ~:CDzK=poDI>o?X,T@x5wsbR*_@Mjxa
    r7XRZ^v~?3CT3u-$]$A5n1l=+Xo7_I?D]+YK5@G@k[YXu7?jRUC~OjT1#nl@sWE1uW\5Cl_i,~$V
    J@\WQ1HvZu^jjU}$V;E~{YA$$)(C>l[alR>JjQJX9JAlRl7ikV--V;jT#.1|,xH5@8sK{]%RH7Hk
    Yv~FC~DYonlQiYRucK<{GZ}j@BUJ};+UQ#[^WBE3^{A;7n<V3EB+?*5T7ZAzZY!HH.>IU#h{oaY{
    B![ApD}Ov2<}p##kU1^[]p[&A-3E]5=Z,G{35{s1JUna(>}5xeluW!,r1t{zOsI1K=\I$;5;D^X<
    ^lZ5>YqUCE]Jjeu\+E>Q12D{>Dp$s+neK@E%+IZEj}oQ|5jklGBvn@l]~EX^OA'w\\vHn]TAaXO*
    m5sR;X*-GAn},>>[E+aD^*C@u8HRTA_O{;@rvm%Mc#]wW<,kp8JU'_UA!r[l}#K[DTaj53'@ZJel
    TrQ1+Hj1KzB5[YnA5ekQRK}=^O+B*m=rHazCrE3AZCnz[*lZowVnQ3Ae+D)CV{Iv;@!zuD^+Q?o;
    ,1kkO3wov>U<QwBp}#B@_T[pC,jVHV}=R-U1$kQ]^BBV5p#JzIRs3r~RlU27[KjZ<_R~xuAO3mH2
    AmYle1ZYnp[_{x1CXXG\eDG1#5jeHnA'k]+TQwTxHTG5m<@epkOcij^=vWv{N=W\=>D_oBa_\\sI
    CBaTY0FG}To;5CXK=]?fvK][[JjiKwnHG@C?os>rk-$vpw5Q]*WGw]-n2^Xkr5,$@Em+$UjR=>xB
    5yHwlrvl\5w-,!=i7>Bv<C1z5j3pzAnI$iVV~>@j+@E7k3{^@,@<_>w,A;cgWx+#TG^{f2ezXtTT
    ARqBZTw;=G,G~B_Y1]~KAX#)rDV=lJTJeemoCvp==AXH$11JO35!_i^av[a_5i1T=[Q\-[UAJ>~=
    j*Qw&wB}]An\kJspkow~]\d[A=?^H,79[,*[1}z5O2GakT3#KYmvD7Qp"w^~I'1^Ro^9;A<zrIJs
    |fE^[jsKOVE*QTCsmknl3?]ue{tp'vJ!XXoGR1DVt5z#'vjErcRGe+UYoVsK}o5@]2Y~],-$v$D_
    IzijDCG3+V]pXwgrR<jz^Jw^;5$^lv@E#,j+DpeB5KWCGu=+]E>!eIJ5pC+HBsKnO]_+j>Z\#CKL
    ouORZ]Hm@'W*\RvK3Aa~6=5Xu[nRab1\GG*^--sA{aI>O#d!sl{=uTW,WllVXe}GaK[U}kZ1UT+s
    \W[I~H3{*>I;[
`endprotected
//pragma protect end


// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_dsi_tx) #(
    parameter tLPX_NS = 50,
    parameter tINIT_NS = 100000,
    parameter tLP_EXIT_NS = 100,
    parameter BTA_TIMEOUT_NS = 100000,
    parameter tD_TERM_EN_NS = 35, // for bidir rx mode
    parameter tHS_PREPARE_ZERO_NS = 145, // for bidir rx mode
    parameter tCLK_ZERO_NS = 262,
    parameter tCLK_TRAIL_NS = 60,
    parameter tCLK_PRE_NS = 10,
    parameter tCLK_POST_NS = 60,
    parameter tCLK_PREPARE_NS = 38,
    parameter tHS_PREPARE_NS = 40,
    parameter tWAKEUP_NS = 1000,
    parameter tHS_EXIT_NS = 100,
    parameter tHS_ZERO_NS = 105,
    parameter tHS_TRAIL_NS = 60, 
    parameter NUM_DATA_LANE = 4,
    parameter HS_BYTECLK_MHZ = 125,
    parameter CLOCK_FREQ_MHZ = 100,
    parameter DPHY_CLOCK_MODE = "Continuous",
    parameter PACK_TYPE = 4'b1111,
    parameter ENABLE_V_LPM_BTA = 0,
    parameter PACKET_SEQUENCES = 1,
    parameter HS_CMD_WDATAFIFO_DEPTH = 64,
    parameter LP_CMD_WDATAFIFO_DEPTH = 64,
    parameter LP_CMD_RDATAFIFO_DEPTH = 64,
    parameter MAX_HRES = 1080,
    parameter ENABLE_BIDIR = 1,
    parameter ENABLE_EOTP = 0,
    parameter PIXEL_FIFO_DEPTH = 2048
)(
    input logic           reset_n,
    input logic           clk,				//100Mhz
    input logic           reset_byte_HS_n,
    input logic           clk_byte_HS,
    input logic           reset_pixel_n,
    input logic           clk_pixel,
    // LVDS clock lane   
    output logic          Tx_LP_CLK_P,
	output logic          Tx_LP_CLK_P_OE,
    output logic          Tx_LP_CLK_N,
	output logic          Tx_LP_CLK_N_OE,
	output logic [7:0]    Tx_HS_C,
    output logic          Tx_HS_enable_C,
    
    // ----- DLane 0 -----------
    // LVDS data lane
    output logic [NUM_DATA_LANE-1:0]         Tx_LP_D_P,
    output logic [NUM_DATA_LANE-1:0]         Tx_LP_D_P_OE,
    output logic [NUM_DATA_LANE-1:0]         Tx_LP_D_N,
    output logic [NUM_DATA_LANE-1:0]         Tx_LP_D_N_OE,
    output logic [7:0]                       Tx_HS_D_0,
    output logic [7:0]                       Tx_HS_D_1,
    output logic [7:0]                       Tx_HS_D_2,
    output logic [7:0]                       Tx_HS_D_3,
    output logic [NUM_DATA_LANE-1:0]         Tx_HS_enable_D,

    input  logic          Rx_LP_D_P,
    input  logic          Rx_LP_D_N,
    
    //AXI4-Lite Interface
    input  logic          axi_clk, 
    input  logic          axi_reset_n,
    input  logic [6:0]    axi_awaddr,//Write Address. byte address.
    input  logic          axi_awvalid,//Write address valid.
    output logic          axi_awready,//Write address ready.
    input  logic [31:0]   axi_wdata,//Write data bus.
    input  logic          axi_wvalid,//Write valid.
    output logic          axi_wready,//Write ready.
                          
    output logic          axi_bvalid,//Write response valid.
    input  logic          axi_bready,//Response ready.      
    input  logic [6:0]    axi_araddr,//Read address. byte address.
    input  logic          axi_arvalid,//Read address valid.
    output logic          axi_arready,//Read address ready.
    output logic [31:0]   axi_rdata,//Read data.
    output logic          axi_rvalid,//Read valid.
    input                 axi_rready,//Read ready.
                          
    input logic           hsync,
    input logic           vsync,
    input logic [1:0]     vc,
    input logic [5:0]     datatype,   // data type of the video Long Packet
    input logic [63:0]    pixel_data,
    input logic           pixel_data_valid,
    input logic [15:0]    haddr,
    
    input logic           TurnRequest_dbg,
    output logic          TurnRequest_done, 
`ifdef MIPI_DSI_TX_DEBUG
    input  logic [31:0]   mipi_debug_in,
    output logic [31:0]   mipi_debug_out,
`endif
    output logic          irq
    
);

logic [NUM_DATA_LANE-1:0] TxReadyHS, TxRequestHS, TxRequestEsc;
logic [NUM_DATA_LANE-1:0] TxUlpsEsc, TxUlpsExit;
logic [NUM_DATA_LANE-1:0] TxUlpsActiveNot, TxStopStateD;
logic TxLpdtEsc, TxValidEsc;
logic [NUM_DATA_LANE-1:0] TxReadyEsc, TxSkewCalHS;
logic [3:0] TxTriggerEsc;
logic [7:0] TxDataHS_0, TxDataHS_1, TxDataHS_2, TxDataHS_3;
logic [7:0] TxDataEsc_0;
logic [7:0] RxDataEsc;
logic RxLPDTEsc, RxValidEsc;
logic TxRequestHSc, TxStopStateC;
logic TxUlpsClk, TxUlpsExitClk, TxUlpsActiveClkNot;
logic TurnRequest, turnaround_timeout; 
logic TxStopState_1P, TxStopState_2P;

`IP_MODULE_NAME(efx_dphy_bidir_tx) #(
    .tLPX_NS              (tLPX_NS        ),
    .tLP_EXIT_NS          (tLP_EXIT_NS    ),
    .BTA_TIMEOUT_NS       (BTA_TIMEOUT_NS),
    .tD_TERM_EN_NS        (tD_TERM_EN_NS  ),  //bidir param
    .tHS_PREPARE_ZERO_NS  (tHS_PREPARE_ZERO_NS),  //bidir param
    .tCLK_ZERO_NS         (tCLK_ZERO_NS   ),
    .tCLK_TRAIL_NS        (tCLK_TRAIL_NS  ), 
    .tCLK_PRE_NS          (tCLK_PRE_NS    ),
    .tCLK_POST_NS         (tCLK_POST_NS   ),      
    .tCLK_PREPARE_NS      (tCLK_PREPARE_NS),    
    .tHS_PREPARE_NS       (tHS_PREPARE_NS ),   
    .tWAKEUP_NS           (tWAKEUP_NS     ),
    .tHS_EXIT_NS          (tHS_EXIT_NS    ),
    .tHS_ZERO_NS          (tHS_ZERO_NS    ),
    .tHS_TRAIL_NS         (tHS_TRAIL_NS   ),
    .HS_BYTECLK_MHZ       (HS_BYTECLK_MHZ ),
    .CLOCK_FREQ_MHZ       (CLOCK_FREQ_MHZ ),
    .NUM_DATA_LANE        (NUM_DATA_LANE  ),
    .ENABLE_BIDIR         (ENABLE_BIDIR   ),
    .DPHY_CLOCK_MODE      (DPHY_CLOCK_MODE)
) dphy_bidir_tx_inst (
    .clk                  (clk),
    .reset_n              (reset_n),
    .clk_byte_HS          (clk_byte_HS),
    .reset_byte_HS_n      (reset_byte_HS_n),
    //To LVDS clock lane   
    .Tx_LP_CLK_P          (Tx_LP_CLK_P), 
    .Tx_LP_CLK_P_OE       (Tx_LP_CLK_P_OE),
    .Tx_LP_CLK_N          (Tx_LP_CLK_N),
    .Tx_LP_CLK_N_OE       (Tx_LP_CLK_N_OE),
    .Tx_HS_enable_C       (Tx_HS_enable_C), 
    //PPI clock lane
    .TxRequestHSc         (TxRequestHSc),
    .Tx_HS_C              (Tx_HS_C),
    .TxReadyHSc           (),
    .TxUlpsClk            (TxUlpsClk),   // Transmit Ultra Low power on clock lane
    .TxUlpsExitClk        (TxUlpsExitClk),
    .TxUlpsActiveClkNot   (TxUlpsActiveClkNot),
    .TxStopStateC         (TxStopStateC),
    //To LVDS data lane
    .Tx_LP_D_P            (Tx_LP_D_P),
    .Tx_LP_D_P_OE         (Tx_LP_D_P_OE),
    .Tx_LP_D_N            (Tx_LP_D_N),
    .Tx_LP_D_N_OE         (Tx_LP_D_N_OE),
    .Tx_HS_D_0            (Tx_HS_D_0),
    .Tx_HS_D_1            (Tx_HS_D_1),
    .Tx_HS_D_2            (Tx_HS_D_2),
    .Tx_HS_D_3            (Tx_HS_D_3),
    .Tx_HS_D_4            (),
    .Tx_HS_D_5            (),
    .Tx_HS_D_6            (),
    .Tx_HS_D_7            (),
    .Tx_HS_enable_D       (Tx_HS_enable_D),
    //Bidir mode data lane
    .Rx_LP_D_P            (Rx_LP_D_P),
    .Rx_LP_D_N            (Rx_LP_D_N),
    //PPI data lane       
    .TxRequestHS          (TxRequestHS),
	.TxDataHS_0           (TxDataHS_0),
	.TxDataHS_1           (TxDataHS_1),
	.TxDataHS_2           (TxDataHS_2),
	.TxDataHS_3           (TxDataHS_3),
	.TxDataHS_4           (8'h0),
	.TxDataHS_5           (8'h0),
	.TxDataHS_6           (8'h0),
	.TxDataHS_7           (8'h0),
    .TxReadyHS            (TxReadyHS),
    .TxSkewCalHS          (TxSkewCalHS),
    .TxRequestEsc         (TxRequestEsc),
    .TxTriggerEsc         (TxTriggerEsc),
    .TxStopStateD         (TxStopStateD),
    .TxUlpsExit           (TxUlpsExit),
    .TxUlpsActiveNot      (TxUlpsActiveNot),
    .TxUlpsEsc            (TxUlpsEsc),
    //LPDT mode only supported in DSI
    .TxLpdtEsc            ({{NUM_DATA_LANE-1{1'b0}}, TxLpdtEsc}),
    .TxValidEsc           ({{NUM_DATA_LANE-1{1'b0}}, TxValidEsc}),
    .TxDataEsc_0          (TxDataEsc_0),
    .TxDataEsc_1          (8'h0),
    .TxDataEsc_2          (8'h0),
    .TxDataEsc_3          (8'h0),
    .TxDataEsc_4          (8'h0),
    .TxDataEsc_5          (8'h0),
    .TxDataEsc_6          (8'h0),
    .TxDataEsc_7          (8'h0),
    .TxReadyEsc           (TxReadyEsc),
    //Bidir PPI signal
    .TurnRequest          (TurnRequest),
    .TurnRequest_done     (TurnRequest_done),
    .turnaround_timeout   (turnaround_timeout),
    .RxUlpsEsc            (),
    .RxUlpsActiveNot      (),
    .RxLPDTEsc            (RxLPDTEsc),
    .RxDataEsc            (RxDataEsc),
    .RxValidEsc           (RxValidEsc),
    .RxTriggerEsc         (),
    .RxStopState          (),
    .ErrEsc               (),	
    .ErrControl           ()
);

`IP_MODULE_NAME(efx_dsi_tx_top) #(
    .tINIT_NS                  (tINIT_NS),
    .tINIT_SKEWCAL_NS          (100000),
    .ENABLE_INIT_SKEWCAL       (0), //always turn off for soft DSI
    .ENABLE_EOTP               (ENABLE_EOTP),
    .NUM_DATA_LANE             (NUM_DATA_LANE),
    .HS_BYTECLK_MHZ            (HS_BYTECLK_MHZ),
    .DPHY_CLOCK_MODE           (DPHY_CLOCK_MODE),
    .PACK_TYPE                 (PACK_TYPE),
    .ENABLE_V_LPM_BTA          (ENABLE_V_LPM_BTA),
    .PACKET_SEQUENCES          (PACKET_SEQUENCES),
    .HS_CMD_WDATAFIFO_DEPTH    (HS_CMD_WDATAFIFO_DEPTH),
    .LP_CMD_WDATAFIFO_DEPTH    (LP_CMD_WDATAFIFO_DEPTH),
    .LP_CMD_RDATAFIFO_DEPTH    (LP_CMD_RDATAFIFO_DEPTH),
    .HS_LANE_FIFO_DEPTH        (512),
    .MAX_HRES                  (MAX_HRES),
    .HS_DATA_WIDTH             (8),
    .ASYNC_STAGE               (2),
    .PIXEL_FIFO_DEPTH          (PIXEL_FIFO_DEPTH)
) efx_dsi_tx_top_inst (
    .clk_byte_HS          (clk_byte_HS),
    .reset_byte_HS_n      (reset_byte_HS_n),
    .clk_pixel            (clk_pixel),
    .reset_pixel_n        (reset_pixel_n),
    .clk_esc              (clk),
    .reset_esc_n          (reset_n),
    .phy_clk_byte_HS      (clk_byte_HS),
    
    .axi_clk              (axi_clk),
    .axi_reset_n          (axi_reset_n),
    .axi_awaddr           (axi_awaddr),
    .axi_awvalid          (axi_awvalid),
    .axi_awready          (axi_awready),
    .axi_wdata            (axi_wdata),
    .axi_wvalid           (axi_wvalid),
    .axi_wready           (axi_wready),                        
    .axi_bvalid           (axi_bvalid),
    .axi_bready           (axi_bready),
    .axi_araddr           (axi_araddr),
    .axi_arvalid          (axi_arvalid),
    .axi_arready          (axi_arready),
    .axi_rdata            (axi_rdata),
    .axi_rvalid           (axi_rvalid),
    .axi_rready           (axi_rready),
    //PPI clock lane
    .TxRequestHSc         (TxRequestHSc),
    .TxUlpsClk            (TxUlpsClk),   // Transmit Ultra Low power on clock lane
    .TxUlpsExitClk        (TxUlpsExitClk),
    .TxUlpsActiveClkNot   (TxUlpsActiveClkNot),
    .TxStopStateC         (TxStopStateC),
    //PPI data lane       
    .TxRequestHS          (TxRequestHS),
	.TxDataHS_0           (TxDataHS_0),
	.TxDataHS_1           (TxDataHS_1),
	.TxDataHS_2           (TxDataHS_2),
	.TxDataHS_3           (TxDataHS_3),
    .TxReqValidHS0        (),
    .TxReqValidHS1        (),
    .TxReqValidHS2        (),
    .TxReqValidHS3        (),    
    .TxReadyHS            (TxReadyHS),
    .TxSkewCalHS          (TxSkewCalHS),
    .TxRequestEsc         (TxRequestEsc),
    .TxTriggerEsc         (TxTriggerEsc),
    .TxStopStateD         (TxStopStateD),
    .TxUlpsExit           (TxUlpsExit),
    .TxUlpsActiveNot      (TxUlpsActiveNot),
    .TxUlpsEsc            (TxUlpsEsc),
    //LPDT mode only supported in DSI
    .TxLpdtEsc            (TxLpdtEsc),
    .TxValidEsc           (TxValidEsc),
    .TxDataEsc_0          (TxDataEsc_0),
    .TxReadyEsc           (TxReadyEsc[0]),
    .RxClkEsc             (clk),  // for soft DPHY it is using clk, hard DPHY is using clock from DPHY
    .RxLPDTEsc            (RxLPDTEsc),
    .RxDataEsc            (RxDataEsc),
    .RxValidEsc           (RxValidEsc),
    
    .hsync                (hsync),
    .vsync                (vsync),
    .vc                   (vc),
    .datatype             (datatype),
    .pixel_data           (pixel_data),
    .pixel_data_valid     (pixel_data_valid),
    .haddr                (haddr),
    
    .TurnRequest_dbg      (TurnRequest_dbg),
    .TurnRequest_done     (TurnRequest_done),  
    .TurnRequest          (TurnRequest),
    .turnaround_timeout   (turnaround_timeout),
    .irq                  (irq),
`ifdef MIPI_DSI_TX_DEBUG
    .mipi_debug_in        (mipi_debug_in),
    .mipi_debug_out       (mipi_debug_out),
`endif 
    .Direction            (1'b0) // hcteh 230223: Direction port to indicate if the DPHY in TX/RX mode, when BTA triggered. use for 2.5G, tie gnd for DSI 1.5G.  SIP-306
);

endmodule

`undef IP_UUID
`undef IP_NAME_CONCAT
`undef IP_MODULE_NAME
