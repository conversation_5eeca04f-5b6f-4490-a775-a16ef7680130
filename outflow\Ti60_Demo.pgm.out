INFO     : Efinix FPGA Bitstream Generator.

INFO     : Version: 2025.********** 

INFO     : Copyright (C) 2013 - 2025 Efinix, Inc. All rights reserved.

INFO     : Compiled: May  7 2025.

INFO     : Using source file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr/Ti60_Demo.lbf"

INFO     : Generating HEX programming file for device "Ti60F225", family "Titanium"

INFO     : Using periphery source file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.lpf"

INFO     : Generating bit file: "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.bit"

INFO     : HEX Generation Options Summary:

INFO     : 	Listing options specified to bitstream generation:

INFO     : 	bitstream_compression                    = on

INFO     : 	oscillator_clock_divider                 = DIV2

INFO     : 	enable_external_master_clock             = off

INFO     : 	spi_low_power_mode                       = off

INFO     : 	active_capture_clk_edge                  = negedge

INFO     : 	enable_remote_update                     = off

INFO     : 	release_tri_then_reset                   = on

INFO     : 	enable_seu_crc_check                     = off

INFO     : 	seu_mode                                 = automatic

INFO     : 	seu_wait_interval                        = 0xFFFFFF

INFO     : 	jtag_usercode                            = 0xFFFFFFFF

INFO     : 	io_weak_pullup                           = on

INFO     : Finished generating "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.hex"

INFO     : Efinix Bitstream Generator took 1.79342 seconds.

INFO     : 	Efinix Bitstream Generator took 1.45312 seconds (approximately) in total CPU time.

INFO     : Efinix Bitstream Generator virtual memory usage: begin = 2.432 MB, end = 9.764 MB, delta = 7.332 MB

INFO     : 	Efinix Bitstream Generator peak virtual memory usage = 166.592 MB

INFO     : Efinix Bitstream Generator resident set memory usage: begin = 7.576 MB, end = 18.04 MB, delta = 10.464 MB

INFO     : 	Efinix Bitstream Generator peak resident set memory usage = 169.92 MB

