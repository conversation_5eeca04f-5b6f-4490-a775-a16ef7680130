
# Auto-generated by Interface Designer
#
# WARNING: Any manual changes made to this file will be lost when generating constraints.

# Efinity Interface Designer SDC
# Version: 2025.1.110.2.15
# Date: 2025-07-22 18:24

# Copyright (C) 2013 - 2025 Efinix Inc. All rights reserved.

# Device: Ti60F225
# Project: Ti60_Demo
# Timing Model: C4 (final)

# PLL Constraints
#################
create_clock -period 20.833 -name dsi_refclk_i [get_ports {dsi_refclk_i}]
create_clock -period 23.810 -name dsi_byteclk_i [get_ports {dsi_byteclk_i}]
create_clock -waveform {2.232 5.208} -period 5.952 -name dsi_txcclk_i [get_ports {dsi_txcclk_i}]
create_clock -waveform {0.744 3.720} -period 5.952 -name dsi_serclk_i [get_ports {dsi_serclk_i}]
create_clock -period 2.604 -name tdqss_clk [get_ports {tdqss_clk}]
create_clock -period 5.208 -name core_clk [get_ports {core_clk}]
create_clock -period 2.604 -name tac_clk [get_ports {tac_clk}]
create_clock -waveform {0.651 1.953} -period 2.604 -name twd_clk [get_ports {twd_clk}]
create_clock -period 10.417 -name clk_sys [get_ports {clk_sys}]
create_clock -period 13.441 -name clk_pixel [get_ports {clk_pixel}]
create_clock -period 6.720 -name clk_pixel_2x [get_ports {clk_pixel_2x}]
create_clock -waveform {0.336 1.008} -period 1.344 -name clk_pixel_10x [get_ports {clk_pixel_10x}]
create_clock -period 20.833 -name clk_lvds_1x [get_ports {clk_lvds_1x}]
create_clock -waveform {0.744 2.232} -period 2.976 -name clk_lvds_7x [get_ports {clk_lvds_7x}]
create_clock -period 62.500 -name clk_27m [get_ports {clk_27m}]
create_clock -period 31.250 -name clk_54m [get_ports {clk_54m}]

# GPIO Constraints
####################
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {clk_25m}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {clk_25m}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_ctl1}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_ctl1}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~1}] -max 0.691 [get_ports {cmos_data[0]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~1}] -min 0.461 [get_ports {cmos_data[0]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~6~1}] -max 0.691 [get_ports {cmos_data[1]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~6~1}] -min 0.461 [get_ports {cmos_data[1]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~12~322}] -max 0.691 [get_ports {cmos_data[2]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~12~322}] -min 0.461 [get_ports {cmos_data[2]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~214~322}] -max 0.691 [get_ports {cmos_data[3]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~214~322}] -min 0.461 [get_ports {cmos_data[3]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~322}] -max 0.691 [get_ports {cmos_data[4]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~322}] -min 0.461 [get_ports {cmos_data[4]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~212~322}] -max 0.691 [get_ports {cmos_data[5]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~212~322}] -min 0.461 [get_ports {cmos_data[5]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~10~322}] -max 0.691 [get_ports {cmos_data[6]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~10~322}] -min 0.461 [get_ports {cmos_data[6]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~197~322}] -max 0.691 [get_ports {cmos_data[7]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~197~322}] -min 0.461 [get_ports {cmos_data[7]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~216~322}] -max 0.691 [get_ports {cmos_href}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~216~322}] -min 0.461 [get_ports {cmos_href}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~18~322}] -max 0.691 [get_ports {cmos_vsync}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~18~322}] -min 0.461 [get_ports {cmos_vsync}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_ctl2}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_ctl2}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_sclk}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_sclk}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_sdat_IN}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_sdat_IN}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_sdat_OUT}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_sdat_OUT}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_sdat_OE}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_sdat_OE}]

# HSIO GPIO Constraints
#########################
create_clock -period <USER_PERIOD> -name cmos_pclk [get_ports {cmos_pclk}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {uart_rx_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {uart_rx_i}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~95}] -max 0.263 [get_ports {addr[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~95}] -min -0.140 [get_ports {addr[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~36}] -max 0.263 [get_ports {addr[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~36}] -min -0.140 [get_ports {addr[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~70}] -max 0.263 [get_ports {addr[2]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~70}] -min -0.140 [get_ports {addr[2]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~50~1}] -max 0.263 [get_ports {addr[3]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~50~1}] -min -0.140 [get_ports {addr[3]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~25}] -max 0.263 [get_ports {addr[4]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~25}] -min -0.140 [get_ports {addr[4]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~142~1}] -max 0.263 [get_ports {addr[5]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~142~1}] -min -0.140 [get_ports {addr[5]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~59}] -max 0.263 [get_ports {addr[6]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~59}] -min -0.140 [get_ports {addr[6]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~110}] -max 0.263 [get_ports {addr[7]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~110}] -min -0.140 [get_ports {addr[7]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~26}] -max 0.263 [get_ports {addr[8]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~26}] -min -0.140 [get_ports {addr[8]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~127}] -max 0.263 [get_ports {addr[9]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~127}] -min -0.140 [get_ports {addr[9]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~138}] -max 0.263 [get_ports {addr[10]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~138}] -min -0.140 [get_ports {addr[10]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~60}] -max 0.263 [get_ports {addr[11]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~60}] -min -0.140 [get_ports {addr[11]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~35}] -max 0.263 [get_ports {addr[12]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~35}] -min -0.140 [get_ports {addr[12]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~71}] -max 0.263 [get_ports {addr[13]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~71}] -min -0.140 [get_ports {addr[13]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~109}] -max 0.263 [get_ports {addr[14]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~109}] -min -0.140 [get_ports {addr[14]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~143~1}] -max 0.263 [get_ports {addr[15]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~143~1}] -min -0.140 [get_ports {addr[15]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~74~1}] -max 0.263 [get_ports {ba[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~74~1}] -min -0.140 [get_ports {ba[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~132~1}] -max 0.263 [get_ports {ba[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~132~1}] -min -0.140 [get_ports {ba[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~126}] -max 0.263 [get_ports {ba[2]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~126}] -min -0.140 [get_ports {ba[2]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~49}] -max 0.263 [get_ports {cas}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~49}] -min -0.140 [get_ports {cas}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~123~1}] -max 0.263 [get_ports {cke}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~123~1}] -min -0.140 [get_ports {cke}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~150}] -max 0.263 [get_ports {clk_n_lo clk_n_hi}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~150}] -min -0.140 [get_ports {clk_n_lo clk_n_hi}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~149}] -max 0.263 [get_ports {clk_p_lo clk_p_hi}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~149}] -min -0.140 [get_ports {clk_p_lo clk_p_hi}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_ctl3}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_ctl3}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~95~1}] -max 0.263 [get_ports {cs}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~95~1}] -min -0.140 [get_ports {cs}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~131~1}] -max 0.263 [get_ports {o_dm_lo[0] o_dm_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~131~1}] -min -0.140 [get_ports {o_dm_lo[0] o_dm_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~83~1}] -max 0.263 [get_ports {o_dm_lo[1] o_dm_hi[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~83~1}] -min -0.140 [get_ports {o_dm_lo[1] o_dm_hi[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {dsi_pwm_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {dsi_pwm_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {dsi_resetn_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {dsi_resetn_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_blen_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_blen_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_de_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_de_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_hs_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_hs_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_pwm_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_pwm_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_rst_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_rst_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_vs_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_vs_o}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~73~1}] -max 0.263 [get_ports {odt}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~73~1}] -min -0.140 [get_ports {odt}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~48}] -max 0.263 [get_ports {ras}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~48}] -min -0.140 [get_ports {ras}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~139}] -max 0.263 [get_ports {reset}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~139}] -min -0.140 [get_ports {reset}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {spi_sck_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {spi_sck_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {spi_ssn_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {spi_ssn_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {uart_tx_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {uart_tx_o}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~94}] -max 0.263 [get_ports {we}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~94}] -min -0.140 [get_ports {we}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl0_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl0_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl0_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl0_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl0_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl0_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl1_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl1_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl1_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl1_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl1_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl1_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_scl_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_scl_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_scl_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_scl_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_scl_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_scl_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_sda_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_sda_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_sda_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_sda_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_sda_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_sda_oe}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~148~1}] -max 0.414 [get_ports {i_dq_lo[0] i_dq_hi[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~148~1}] -min 0.276 [get_ports {i_dq_lo[0] i_dq_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~150~1}] -max 0.263 [get_ports {o_dq_lo[0] o_dq_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~150~1}] -min -0.140 [get_ports {o_dq_lo[0] o_dq_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~150~1}] -max 0.263 [get_ports {o_dq_oe[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~150~1}] -min -0.140 [get_ports {o_dq_oe[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~157~1}] -max 0.414 [get_ports {i_dq_lo[1] i_dq_hi[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~157~1}] -min 0.276 [get_ports {i_dq_lo[1] i_dq_hi[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~159~1}] -max 0.263 [get_ports {o_dq_lo[1] o_dq_hi[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~159~1}] -min -0.140 [get_ports {o_dq_lo[1] o_dq_hi[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~159~1}] -max 0.263 [get_ports {o_dq_oe[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~159~1}] -min -0.140 [get_ports {o_dq_oe[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~164~1}] -max 0.414 [get_ports {i_dq_lo[2] i_dq_hi[2]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~164~1}] -min 0.276 [get_ports {i_dq_lo[2] i_dq_hi[2]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~166~1}] -max 0.263 [get_ports {o_dq_lo[2] o_dq_hi[2]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~166~1}] -min -0.140 [get_ports {o_dq_lo[2] o_dq_hi[2]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~166~1}] -max 0.263 [get_ports {o_dq_oe[2]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~166~1}] -min -0.140 [get_ports {o_dq_oe[2]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~149~1}] -max 0.414 [get_ports {i_dq_lo[3] i_dq_hi[3]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~149~1}] -min 0.276 [get_ports {i_dq_lo[3] i_dq_hi[3]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~151~1}] -max 0.263 [get_ports {o_dq_lo[3] o_dq_hi[3]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~151~1}] -min -0.140 [get_ports {o_dq_lo[3] o_dq_hi[3]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~151~1}] -max 0.263 [get_ports {o_dq_oe[3]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~151~1}] -min -0.140 [get_ports {o_dq_oe[3]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~189~1}] -max 0.414 [get_ports {i_dq_lo[4] i_dq_hi[4]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~189~1}] -min 0.276 [get_ports {i_dq_lo[4] i_dq_hi[4]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~191~1}] -max 0.263 [get_ports {o_dq_lo[4] o_dq_hi[4]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~191~1}] -min -0.140 [get_ports {o_dq_lo[4] o_dq_hi[4]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~191~1}] -max 0.263 [get_ports {o_dq_oe[4]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~191~1}] -min -0.140 [get_ports {o_dq_oe[4]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~156~1}] -max 0.414 [get_ports {i_dq_lo[5] i_dq_hi[5]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~156~1}] -min 0.276 [get_ports {i_dq_lo[5] i_dq_hi[5]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~158~1}] -max 0.263 [get_ports {o_dq_lo[5] o_dq_hi[5]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~158~1}] -min -0.140 [get_ports {o_dq_lo[5] o_dq_hi[5]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~158~1}] -max 0.263 [get_ports {o_dq_oe[5]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~158~1}] -min -0.140 [get_ports {o_dq_oe[5]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~165~1}] -max 0.414 [get_ports {i_dq_lo[6] i_dq_hi[6]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~165~1}] -min 0.276 [get_ports {i_dq_lo[6] i_dq_hi[6]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~167~1}] -max 0.263 [get_ports {o_dq_lo[6] o_dq_hi[6]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~167~1}] -min -0.140 [get_ports {o_dq_lo[6] o_dq_hi[6]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~167~1}] -max 0.263 [get_ports {o_dq_oe[6]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~167~1}] -min -0.140 [get_ports {o_dq_oe[6]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~188~1}] -max 0.414 [get_ports {i_dq_lo[7] i_dq_hi[7]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~188~1}] -min 0.276 [get_ports {i_dq_lo[7] i_dq_hi[7]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~190~1}] -max 0.263 [get_ports {o_dq_lo[7] o_dq_hi[7]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~190~1}] -min -0.140 [get_ports {o_dq_lo[7] o_dq_hi[7]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~190~1}] -max 0.263 [get_ports {o_dq_oe[7]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~190~1}] -min -0.140 [get_ports {o_dq_oe[7]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~82~1}] -max 0.414 [get_ports {i_dq_lo[8] i_dq_hi[8]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~82~1}] -min 0.276 [get_ports {i_dq_lo[8] i_dq_hi[8]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~84~1}] -max 0.263 [get_ports {o_dq_lo[8] o_dq_hi[8]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~84~1}] -min -0.140 [get_ports {o_dq_lo[8] o_dq_hi[8]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~84~1}] -max 0.263 [get_ports {o_dq_oe[8]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~84~1}] -min -0.140 [get_ports {o_dq_oe[8]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~39~1}] -max 0.414 [get_ports {i_dq_lo[9] i_dq_hi[9]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~39~1}] -min 0.276 [get_ports {i_dq_lo[9] i_dq_hi[9]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~41~1}] -max 0.263 [get_ports {o_dq_lo[9] o_dq_hi[9]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~41~1}] -min -0.140 [get_ports {o_dq_lo[9] o_dq_hi[9]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~41~1}] -max 0.263 [get_ports {o_dq_oe[9]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~41~1}] -min -0.140 [get_ports {o_dq_oe[9]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~22~1}] -max 0.414 [get_ports {i_dq_lo[10] i_dq_hi[10]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~22~1}] -min 0.276 [get_ports {i_dq_lo[10] i_dq_hi[10]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~24~1}] -max 0.263 [get_ports {o_dq_lo[10] o_dq_hi[10]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~24~1}] -min -0.140 [get_ports {o_dq_lo[10] o_dq_hi[10]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~24~1}] -max 0.263 [get_ports {o_dq_oe[10]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~24~1}] -min -0.140 [get_ports {o_dq_oe[10]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~92~1}] -max 0.414 [get_ports {i_dq_lo[11] i_dq_hi[11]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~92~1}] -min 0.276 [get_ports {i_dq_lo[11] i_dq_hi[11]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~94~1}] -max 0.263 [get_ports {o_dq_lo[11] o_dq_hi[11]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~94~1}] -min -0.140 [get_ports {o_dq_lo[11] o_dq_hi[11]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~94~1}] -max 0.263 [get_ports {o_dq_oe[11]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~94~1}] -min -0.140 [get_ports {o_dq_oe[11]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~23~1}] -max 0.414 [get_ports {i_dq_lo[12] i_dq_hi[12]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~23~1}] -min 0.276 [get_ports {i_dq_lo[12] i_dq_hi[12]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~25~1}] -max 0.263 [get_ports {o_dq_lo[12] o_dq_hi[12]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~25~1}] -min -0.140 [get_ports {o_dq_lo[12] o_dq_hi[12]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~25~1}] -max 0.263 [get_ports {o_dq_oe[12]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~25~1}] -min -0.140 [get_ports {o_dq_oe[12]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~38~1}] -max 0.414 [get_ports {i_dq_lo[13] i_dq_hi[13]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~38~1}] -min 0.276 [get_ports {i_dq_lo[13] i_dq_hi[13]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~40~1}] -max 0.263 [get_ports {o_dq_lo[13] o_dq_hi[13]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~40~1}] -min -0.140 [get_ports {o_dq_lo[13] o_dq_hi[13]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~40~1}] -max 0.263 [get_ports {o_dq_oe[13]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~40~1}] -min -0.140 [get_ports {o_dq_oe[13]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~11~1}] -max 0.414 [get_ports {i_dq_lo[14] i_dq_hi[14]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~11~1}] -min 0.276 [get_ports {i_dq_lo[14] i_dq_hi[14]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~13~1}] -max 0.263 [get_ports {o_dq_lo[14] o_dq_hi[14]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~13~1}] -min -0.140 [get_ports {o_dq_lo[14] o_dq_hi[14]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~13~1}] -max 0.263 [get_ports {o_dq_oe[14]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~13~1}] -min -0.140 [get_ports {o_dq_oe[14]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~12~1}] -max 0.414 [get_ports {i_dq_lo[15] i_dq_hi[15]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~12~1}] -min 0.276 [get_ports {i_dq_lo[15] i_dq_hi[15]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~14~1}] -max 0.263 [get_ports {o_dq_lo[15] o_dq_hi[15]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~14~1}] -min -0.140 [get_ports {o_dq_lo[15] o_dq_hi[15]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~14~1}] -max 0.263 [get_ports {o_dq_oe[15]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~14~1}] -min -0.140 [get_ports {o_dq_oe[15]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~172~1}] -max 0.414 [get_ports {i_dqs_lo[0] i_dqs_hi[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~172~1}] -min 0.276 [get_ports {i_dqs_lo[0] i_dqs_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~174~1}] -max 0.263 [get_ports {o_dqs_lo[0] o_dqs_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~174~1}] -min -0.140 [get_ports {o_dqs_lo[0] o_dqs_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~174~1}] -max 0.263 [get_ports {o_dqs_oe[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~174~1}] -min -0.140 [get_ports {o_dqs_oe[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~30~1}] -max 0.414 [get_ports {i_dqs_lo[1] i_dqs_hi[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~30~1}] -min 0.276 [get_ports {i_dqs_lo[1] i_dqs_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~32~1}] -max 0.263 [get_ports {o_dqs_lo[1] o_dqs_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~32~1}] -min -0.140 [get_ports {o_dqs_lo[1] o_dqs_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~32~1}] -max 0.263 [get_ports {o_dqs_oe[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~32~1}] -min -0.140 [get_ports {o_dqs_oe[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~173~1}] -max 0.414 [get_ports {i_dqs_n_lo[0] i_dqs_n_hi[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~173~1}] -min 0.276 [get_ports {i_dqs_n_lo[0] i_dqs_n_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~175~1}] -max 0.263 [get_ports {o_dqs_n_lo[0] o_dqs_n_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~175~1}] -min -0.140 [get_ports {o_dqs_n_lo[0] o_dqs_n_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~175~1}] -max 0.263 [get_ports {o_dqs_n_oe[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~175~1}] -min -0.140 [get_ports {o_dqs_n_oe[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~31~1}] -max 0.414 [get_ports {i_dqs_n_lo[1] i_dqs_n_hi[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~31~1}] -min 0.276 [get_ports {i_dqs_n_lo[1] i_dqs_n_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~33~1}] -max 0.263 [get_ports {o_dqs_n_lo[1] o_dqs_n_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~33~1}] -min -0.140 [get_ports {o_dqs_n_lo[1] o_dqs_n_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~33~1}] -max 0.263 [get_ports {o_dqs_n_oe[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~33~1}] -min -0.140 [get_ports {o_dqs_n_oe[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_int_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_int_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_int_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_int_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_int_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_int_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_scl_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_scl_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_scl_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_scl_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_scl_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_scl_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_sda_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_sda_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_sda_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_sda_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_sda_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_sda_oe}]

# LVDS Tx Constraints
#######################
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -max 0.378 [get_ports {hdmi_txc_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -min -0.140 [get_ports {hdmi_txc_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -max 0.378 [get_ports {hdmi_txc_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -min -0.140 [get_ports {hdmi_txc_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -max 0.378 [get_ports {hdmi_txd0_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -min -0.140 [get_ports {hdmi_txd0_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -max 0.378 [get_ports {hdmi_txd0_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -min -0.140 [get_ports {hdmi_txd0_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -max 0.378 [get_ports {hdmi_txd1_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -min -0.140 [get_ports {hdmi_txd1_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -max 0.378 [get_ports {hdmi_txd1_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -min -0.140 [get_ports {hdmi_txd1_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -max 0.378 [get_ports {hdmi_txd2_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -min -0.140 [get_ports {hdmi_txd2_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -max 0.378 [get_ports {hdmi_txd2_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -min -0.140 [get_ports {hdmi_txd2_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -max 0.378 [get_ports {lvds_txc_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -min -0.140 [get_ports {lvds_txc_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -max 0.378 [get_ports {lvds_txc_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -min -0.140 [get_ports {lvds_txc_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -max 0.378 [get_ports {lvds_txd0_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -min -0.140 [get_ports {lvds_txd0_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -max 0.378 [get_ports {lvds_txd0_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -min -0.140 [get_ports {lvds_txd0_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -max 0.378 [get_ports {lvds_txd1_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -min -0.140 [get_ports {lvds_txd1_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -max 0.378 [get_ports {lvds_txd1_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -min -0.140 [get_ports {lvds_txd1_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -max 0.378 [get_ports {lvds_txd2_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -min -0.140 [get_ports {lvds_txd2_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -max 0.378 [get_ports {lvds_txd2_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -min -0.140 [get_ports {lvds_txd2_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -max 0.378 [get_ports {lvds_txd3_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -min -0.140 [get_ports {lvds_txd3_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -max 0.378 [get_ports {lvds_txd3_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -min -0.140 [get_ports {lvds_txd3_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -max 0.420 [get_ports {hdmi_txc_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -min -0.175 [get_ports {hdmi_txc_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -max 0.420 [get_ports {hdmi_txd0_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -min -0.175 [get_ports {hdmi_txd0_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -max 0.420 [get_ports {hdmi_txd1_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -min -0.175 [get_ports {hdmi_txd1_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -max 0.420 [get_ports {hdmi_txd2_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -min -0.175 [get_ports {hdmi_txd2_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -max 0.420 [get_ports {lvds_txc_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -min -0.175 [get_ports {lvds_txc_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -max 0.420 [get_ports {lvds_txd0_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -min -0.175 [get_ports {lvds_txd0_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -max 0.420 [get_ports {lvds_txd1_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -min -0.175 [get_ports {lvds_txd1_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -max 0.420 [get_ports {lvds_txd2_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -min -0.175 [get_ports {lvds_txd2_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -max 0.420 [get_ports {lvds_txd3_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -min -0.175 [get_ports {lvds_txd3_rst_o}]

# MIPI RX Lane Constraints
############################
# create_clock -period <USER_PERIOD> -name csi_rxc_i [get_ports {csi_rxc_i}]
set_output_delay -clock csi_rxc_i -max 0.315 [get_ports {csi_rxd0_rst_o}]
set_output_delay -clock csi_rxc_i -min -0.140 [get_ports {csi_rxd0_rst_o}]
set_input_delay -clock csi_rxc_i -max 0.512 [get_ports {csi_rxd0_hs_i[*]}]
set_input_delay -clock csi_rxc_i -min 0.342 [get_ports {csi_rxd0_hs_i[*]}]
set_output_delay -clock csi_rxc_i -max 0.315 [get_ports {csi_rxd1_rst_o}]
set_output_delay -clock csi_rxc_i -min -0.140 [get_ports {csi_rxd1_rst_o}]
set_input_delay -clock csi_rxc_i -max 0.512 [get_ports {csi_rxd1_hs_i[*]}]
set_input_delay -clock csi_rxc_i -min 0.342 [get_ports {csi_rxd1_hs_i[*]}]
set_output_delay -clock csi_rxc_i -max 0.315 [get_ports {csi_rxd2_rst_o}]
set_output_delay -clock csi_rxc_i -min -0.140 [get_ports {csi_rxd2_rst_o}]
set_input_delay -clock csi_rxc_i -max 0.512 [get_ports {csi_rxd2_hs_i[*]}]
set_input_delay -clock csi_rxc_i -min 0.342 [get_ports {csi_rxd2_hs_i[*]}]
set_output_delay -clock csi_rxc_i -max 0.315 [get_ports {csi_rxd3_rst_o}]
set_output_delay -clock csi_rxc_i -min -0.140 [get_ports {csi_rxd3_rst_o}]
set_input_delay -clock csi_rxc_i -max 0.512 [get_ports {csi_rxd3_hs_i[*]}]
set_input_delay -clock csi_rxc_i -min 0.342 [get_ports {csi_rxd3_hs_i[*]}]

# MIPI TX Lane Constraints
############################
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~200}] -max 0.378 [get_ports {dsi_txc_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~200}] -min -0.140 [get_ports {dsi_txc_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~200}] -max 0.315 [get_ports {dsi_txc_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~200}] -min -0.140 [get_ports {dsi_txc_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~187}] -max 0.378 [get_ports {dsi_txd0_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~187}] -min -0.140 [get_ports {dsi_txd0_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~187}] -max 0.315 [get_ports {dsi_txd0_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~187}] -min -0.140 [get_ports {dsi_txd0_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~215}] -max 0.378 [get_ports {dsi_txd1_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~215}] -min -0.140 [get_ports {dsi_txd1_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~215}] -max 0.315 [get_ports {dsi_txd1_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~215}] -min -0.140 [get_ports {dsi_txd1_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~230}] -max 0.378 [get_ports {dsi_txd2_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~230}] -min -0.140 [get_ports {dsi_txd2_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~230}] -max 0.315 [get_ports {dsi_txd2_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~230}] -min -0.140 [get_ports {dsi_txd2_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~175}] -max 0.378 [get_ports {dsi_txd3_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~175}] -min -0.140 [get_ports {dsi_txd3_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~175}] -max 0.315 [get_ports {dsi_txd3_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~175}] -min -0.140 [get_ports {dsi_txd3_rst_o}]

# Clock Latency Constraints
############################
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 1.827> [get_ports {dsi_refclk_i}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 1.183> [get_ports {dsi_refclk_i}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 1.827> [get_ports {dsi_byteclk_i}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 1.183> [get_ports {dsi_byteclk_i}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 1.827> [get_ports {dsi_txcclk_i}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 1.183> [get_ports {dsi_txcclk_i}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 1.827> [get_ports {dsi_serclk_i}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 1.183> [get_ports {dsi_serclk_i}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 0.050> [get_ports {tdqss_clk}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 0.032> [get_ports {tdqss_clk}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 0.050> [get_ports {core_clk}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 0.032> [get_ports {core_clk}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 0.050> [get_ports {tac_clk}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 0.032> [get_ports {tac_clk}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 0.050> [get_ports {twd_clk}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 0.032> [get_ports {twd_clk}]
# set_clock_latency -source -setup <board_max + 0.026> [get_ports {clk_sys}]
# set_clock_latency -source -hold <board_min + 0.054> [get_ports {clk_sys}]
# set_clock_latency -source -setup <board_max + 0.026> [get_ports {clk_pixel}]
# set_clock_latency -source -hold <board_min + 0.054> [get_ports {clk_pixel}]
# set_clock_latency -source -setup <board_max + 0.026> [get_ports {clk_pixel_2x}]
# set_clock_latency -source -hold <board_min + 0.054> [get_ports {clk_pixel_2x}]
# set_clock_latency -source -setup <board_max + 0.026> [get_ports {clk_pixel_10x}]
# set_clock_latency -source -hold <board_min + 0.054> [get_ports {clk_pixel_10x}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 1.827> [get_ports {clk_lvds_1x}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 1.183> [get_ports {clk_lvds_1x}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 1.827> [get_ports {clk_lvds_7x}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 1.183> [get_ports {clk_lvds_7x}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 1.827> [get_ports {clk_27m}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 1.183> [get_ports {clk_27m}]
# set_clock_latency -source -setup <pll_clk_latency_clk_sys_max + 1.827> [get_ports {clk_54m}]
# set_clock_latency -source -hold <pll_clk_latency_clk_sys_min + 1.183> [get_ports {clk_54m}]
# set_clock_latency -source -setup <board_max + 1.773> [get_ports {cmos_pclk}]
# set_clock_latency -source -hold <board_min + 1.182> [get_ports {cmos_pclk}]
