// =============================================================================
// Generated by efx_ipmgr
// Version: 2025.**********
// IP Version: 5.14
// =============================================================================

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2025 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//                                                                       
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//                                                                       
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.                                                
//
////////////////////////////////////////////////////////////////////////////////

`define IP_UUID _157b779458a24b49aa47770753590abe
`define IP_NAME_CONCAT(a,b) a``b
`define IP_MODULE_NAME(name) `IP_NAME_CONCAT(name,`IP_UUID)
module DdrCtrl
(
    input clk,
    input core_clk,
    input twd_clk,
    input tdqss_clk,
    input tac_clk,
    input reset_n,
    output reset,
    output cs,
    output ras,
    output cas,
    output we,
    output cke,
    output [15:0] addr,
    output [2:0] ba,
    output odt,
    output [2:0] shift,
    output [4:0] shift_sel,
    output shift_ena,
    input cal_ena,
    output cal_done,
    output cal_pass,
    output [2:0] cal_shift_val,
    output [1:0] o_dm_hi,
    output [1:0] o_dm_lo,
    input [1:0] i_dqs_hi,
    input [1:0] i_dqs_lo,
    input [1:0] i_dqs_n_hi,
    input [1:0] i_dqs_n_lo,
    output [1:0] o_dqs_hi,
    output [1:0] o_dqs_lo,
    output [1:0] o_dqs_n_hi,
    output [1:0] o_dqs_n_lo,
    output [1:0] o_dqs_oe,
    output [1:0] o_dqs_n_oe,
    input [15:0] i_dq_hi,
    input [15:0] i_dq_lo,
    output [15:0] o_dq_hi,
    output [15:0] o_dq_lo,
    output [15:0] o_dq_oe,
    input [7:0] axi_aid,
    input [31:0] axi_aaddr,
    input [7:0] axi_alen,
    input [2:0] axi_asize,
    input [1:0] axi_aburst,
    input [1:0] axi_alock,
    input axi_avalid,
    output axi_aready,
    input axi_atype,
    input [7:0] axi_wid,
    input [127:0] axi_wdata,
    input [15:0] axi_wstrb,
    input axi_wlast,
    input axi_wvalid,
    output axi_wready,
    output [7:0] axi_rid,
    output [127:0] axi_rdata,
    output axi_rlast,
    output axi_rvalid,
    input axi_rready,
    output [1:0] axi_rresp,
    output [7:0] axi_bid,
    output [1:0] axi_bresp,
    output axi_bvalid,
    input axi_bready,
    output [7:0] cal_fail_log
);
`IP_MODULE_NAME(efx_ddr3_soft_controller)u_efx_ddr3_soft_controller
(
    .clk ( clk ),
    .core_clk ( core_clk ),
    .twd_clk ( twd_clk ),
    .tdqss_clk ( tdqss_clk ),
    .tac_clk ( tac_clk ),
    .reset_n ( reset_n ),
    .reset ( reset ),
    .cs ( cs ),
    .ras ( ras ),
    .cas ( cas ),
    .we ( we ),
    .cke ( cke ),
    .addr ( addr ),
    .ba ( ba ),
    .odt ( odt ),
    .shift ( shift ),
    .shift_sel ( shift_sel ),
    .shift_ena ( shift_ena ),
    .cal_ena ( cal_ena ),
    .cal_done ( cal_done ),
    .cal_pass ( cal_pass ),
    .cal_shift_val ( cal_shift_val ),
    .o_dm_hi ( o_dm_hi ),
    .o_dm_lo ( o_dm_lo ),
    .i_dqs_hi ( i_dqs_hi ),
    .i_dqs_lo ( i_dqs_lo ),
    .i_dqs_n_hi ( i_dqs_n_hi ),
    .i_dqs_n_lo ( i_dqs_n_lo ),
    .o_dqs_hi ( o_dqs_hi ),
    .o_dqs_lo ( o_dqs_lo ),
    .o_dqs_n_hi ( o_dqs_n_hi ),
    .o_dqs_n_lo ( o_dqs_n_lo ),
    .o_dqs_oe ( o_dqs_oe ),
    .o_dqs_n_oe ( o_dqs_n_oe ),
    .i_dq_hi ( i_dq_hi ),
    .i_dq_lo ( i_dq_lo ),
    .o_dq_hi ( o_dq_hi ),
    .o_dq_lo ( o_dq_lo ),
    .o_dq_oe ( o_dq_oe ),
    .axi_aid ( axi_aid ),
    .axi_aaddr ( axi_aaddr ),
    .axi_alen ( axi_alen ),
    .axi_asize ( axi_asize ),
    .axi_aburst ( axi_aburst ),
    .axi_alock ( axi_alock ),
    .axi_avalid ( axi_avalid ),
    .axi_aready ( axi_aready ),
    .axi_atype ( axi_atype ),
    .axi_wid ( axi_wid ),
    .axi_wdata ( axi_wdata ),
    .axi_wstrb ( axi_wstrb ),
    .axi_wlast ( axi_wlast ),
    .axi_wvalid ( axi_wvalid ),
    .axi_wready ( axi_wready ),
    .axi_rid ( axi_rid ),
    .axi_rdata ( axi_rdata ),
    .axi_rlast ( axi_rlast ),
    .axi_rvalid ( axi_rvalid ),
    .axi_rready ( axi_rready ),
    .axi_rresp ( axi_rresp ),
    .axi_bid ( axi_bid ),
    .axi_bresp ( axi_bresp ),
    .axi_bvalid ( axi_bvalid ),
    .axi_bready ( axi_bready ),
    .cal_fail_log ( cal_fail_log )
);
endmodule

//pragma protect
//pragma protect begin
`protected

    MTI!#ro#}#5V7jTlTG=[Zz1[Evpa-w^jCT1*O}3-wYd)[72X?XRe2^uT{A;rvf^]o;)oB![bYlwE
    s+nZ@aQxvjk}]irz!w$wIi@DFzx^QI}VCXvsQV5T3N)V#A2Y$^!]^l\;Am^V#zKR,G,w]nHL}#=k
    -+Ap=\!uk_$n=<OCDJ<<FqhkaHre@{7G5u<*YrDz*w5IawUA$i'++-$j$~\#=]-@jmU7oU@7BOkz
    $1YV2$O^z;^}iuG9I_\A7+e^To-3^G++;<-x[u@oFXo[Z@NeaJzRD[XKn5$=#@$}>[nI_7_|DIDA
    CEJ'\=V2VivGIE7x*'2zyYJT2fGj3rk=o+C2>lUs@v>REvRJU$A5v$Trn'ajEpksQ~x#7J}vpeY[
    <[*-B@mT2A5nG]i|Nx^o[>T^zYDw2'V_X$jRB^/#r='}ei}'U-?,RKO_rvGKz>{q3=v['zJJ$\\=
    AC@;eo?BOwWon>QUvj{Qzx1zQ5RkIzV^JD@3L?vABv=_!DEmI?aV'Ua]!IG#2QGJ>i[]E|AY13W6
    E+I*&iw{]}mZOXDmKceskJvpB~Qmv@B>vz*[+BJw;@uT=Gi]$G}apv)%iEEVrEJ$)KD#nv^(';!m
    ?'QW$}{,;Yu+^--O<5?Dx'p~7;*m]zXH}KJz>vvZ5i5[]RV]2\V~AH]7?QmoTrEnK>HXoj@G,uX,
    W]\~Y@,W,QsXfe3r2X7#k?^Tv~{>2~'@7+rk]>'n,L&t*n[DL5<@u35wA>1}<H7=pA']Uwjw@}ei
    D1H-5KI2Ca7J];<[={|;a7egM{BOHOExT'#,pfr2_}FEJ;Wr^5eK7T;s+W[I~aG{eH#Yi
`endprotected
//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin
`protected

    MTI!#3Y@uZIe>er<mj+YoGHpvmsJE}=EKUH_,FqE@J[6xesE5}xVYX>$pTY?y)uC^;C!Z.y,Q}Ek
    X3@naA'vjk}]irz!w$wIi@DFzx^QI}VCXvEOomIaN|}lQW^uzA7!77BYsuhqel#n7CRTV+!$Z']7
    e}ARaB2z%+]Bp<jks]bi,;x-IDsy'7>O-+K#X<m;M#s$z5JU$?+uZ{RrnK7>z6~R]UgH7C1x>X<j
    kQCuX^@T}~U'Bs1'aRYRY=Om'G7G]UAaaQu=eUQ\zB=@wx~'IDlxx1ITXQ-VUReVrs3=,RAqQ;UQ
    *ojYWzO<Tr!~$V]KYA^;KQj@rvvu*RWBIZnCYW,Ov$p2MSWC?$7e2-zV>AYQr*Iu]CUVY$O=CulJ
    z+]W7v1]Wl;IO$,rRQbQarHpkp!3>,!OGHe0B'>[l;ro*!u#7m7~(Xw$^=;BlL^Nn,vs}#5[vZ$A
    _R>@B>\;OQRVspapmx;$rIz-X=z7om{WtnROw|2LXj]7k{<JfB3T!e-D'z^='_']B1W+^]J,Ujo}
    $*!Hro\Ta7#E12}K5}j>+DZGXzZx[R?*<,*eC>,R{j;KYB@[2e7i@T+1iEG~nisEWRD\!,KVY}[A
    oHI-p{e~*AX{2@Ur<M{=j+}$5Rjuo3s~~RrJ=k}UsUzszK_iRAv?,28tZCI[*OC2=kChQ~K2x'3~
    *GWsDHHai|Z<lZr3WmA$oEnY5<di\#'=k2BH=o5pl{,+a@pYZQ~dpel!},$,KUVi0#57puTCOEB!
    alJsDi<{o_53oHU@{XIin'm]5rU{prT*!MXr5$Z]G2sQ^-m_~vi]S=js2!xU?_><7wCrK[Z@+^zm
    l3Vmr^Kc=;}VAUm;<Srsi{}kOw$2j][wG<vYI5H]'@@Y~C"BA-,oM?xx5VGKu+G8O@CGgbqGRa]F
    g3rXDp\J^(%=!-{\1{OrU]B1uXrv@^n{>ZT-p!YxU7{]}D1nVm@QDm#.EpE[|15CkUn@G]=oZ#OK
    IezC[CKOW-CTw\=3TarGje!_{UsI;{v]2=<oi'~Hk|%7,"&DvG[gmaT}TU!a-Di2n{<BLuTaGnHy
    YJ2KN.GB"Ci[#L2NF3}k<n\C!kH][B2K,us~x)g2$!ws!W,nOeU+p^A4R]I~-{=AKa}Jl]<n.*U{
    _~<'D+&-A^By;aD#H]H+%rfIhl\zr^A'Y=A$Q3-KXnrlBgpwYu=i~>q?viTvXD@el7n_Dw}n_Yrc
    vaX{a*?^kTG}lwT^uGQXmXnIJR}^Ba!ZjauwE_jYzAK2koEZ4#7?Yn=GT~x!*@E<Qe}_w67\we}~
    vz'D!A7FZ,O=[*DeVi-{n_kv"_Bpo\#w~{Xor=7Qn6"wsQ'W]QwOa~K[l$H<s*@-HA$[L_zBmM)x
    1J*j~<[_kDJv2;o[V^C.}1p7>=Cvl*D5AXQY?U2+rEo1pt_?]XAnuj4EwYVj)JAO#v@Jn8\\?;DC
    5v['Qiz#R?b7ow;fQzC>]Urz)uYQ^XsXVD~v\@r\7K[w~Rr]w*e2^*A}?ZE[?UTD2^*R#VOvw8US
    D&m7[~HX,]t:"l!>@V\lEU*7H>C<=p}{^DpG257]]$_~;lRx~={nV|JHTJBvxmaXXY5im7_x++/j
    @jJa'~\RRmxw+GV^*G_UBlRZ{@Bg^-K?r;-$Q~j7&G=o-|(z-p#GxV}E5^_#Co!OV~[<nA3Bav{i
    BsaVeG7Trvuw+1V'>Qi~pVp!+Z\BoIYU5v]rBJQRIjGKCaYsv<7X{}UwGs@LprxYO<RzeEwYTljZ
    YEl=}}},DTDp#[Xke!+{#[>s1'K1O5?uzBvJk{ve3}z]1uZ^@OC}Zp@3HI$Z'p_pjWJ^;[I?#oz;
    Gsww1eAxwD;=3vxw;{V7lU^XH'}VxCrJ1A!GZ]'vv;V]pvust*h^;+wguCCR7ZY#GT;<U1_RUDI]
    k*uD]5upG~u]wX{#={$5(KaQrC'>pJvw2DB;O'5Z{z]-m^#-nxv'laa+V+'[C=+!,}nwJ=$UK*DZ
    _TCG]E<1WjV@JtiE1+eCm3*='B.sz\$;1rGB3-~YAa@-o'Jx}n#Xokp=r*re^}3@rEXw_noa-2WP
    ErwJrKHT8ksHtC}Ou{O2RasJCjD$_oV$Y5iIZ+7vX;E*Hy=X+=.Q#rE,B,Yk5j<vU~W@o_l$UB]7
    -}?CTXZ[i{sxdrOH{?AGzoOz>ok<a[5=EGKwmh=p<x^+AGneEl1QQ_YU2=K_$k#s$AK_!zY7x_<1
    CX~-=jY[~#G4{7V2ke_'Lvb{U2\FCW1jEoBZQ3a;?<-C2^DTuA5}^9\RG~GHl3z]n!{E?1HGiOI?
    vO2[^w[C![[3U!+jv?d\I]UoTB2Y>m]XUT]Hp5Y^3xkvXYaU$~-We=[iIT}Va<<wwR+RCn+<,x5T
    x~eiDYDp$3ElATpe'DsBOQ>r?x3V_BGI;z}aE}1z,x*<Yz?-T-QDzCZBZ+7Or7e_s+[ODCWQlps1
    RWjpoZuj\~Wsi7Y,>1eJ]\KuBYKwGiE:E1-uE7,vv=-WC$D{dL<$#U7p3!#YXr,xp*7eD'v+;{5u
    [7c!$v7\Zsrg1Tr-:aqZv![/IAjTjX7A1K*X3j-3-pa]!rXHC}V<sjO?6&L*BuUz'!+=+^a1Q@@s
    uK~LYW^I}ZV[oTYCi'zooJ+Zg=WTao3*]I_m-35OHj7{2MbfD7XvP_KuIuU[iBYee,mOB%STo}}g
    aRU,N^-jO-DCizDY!@'+@e\w#]wZxn}-~E\>k35?,yppRkwa<{*iua6*JTGTx#@|wlZTAR^xr2l!
    zQW!mDX2n1amG?QpG#RvbGk\YC*#21wVTZ[pEt}E}l51rUV}>ljJ$A\]wm%~A]I=@YWa,2{xC<2j
    K,x\m{o{\juHV5]\wB-C^!l~'eBURC'B@uuIO,Vej{szno1)lBaZ<YsQ}'T~J1=^=G]QIbP1E+jY
    umGe1MB@jiZYn^iz<rU]@Ho\uBPEJ'We+ee#_,^\uZTZ1_wqRw2~lRzA@-DHY\@}BCD[0^x^?cY\
    il3SC$aEIm!7+'^Q[u3Q$3QTvmJ#5j+;>S*\<^lV!5=H,osKE]bG!IJi_1n-QKx7T,oVja;oV#m-
    jz<ul=onjBv?U*,Z$-#r~2~Hp#5C>Q}jsnU<YDum*#zEHZ[>a<3><w{HsZ'u]n>_JHC,7-5JEv}n
    8EiWzI#REalY^x7?D(sT@X#aJ[=m=5[{OKe\W2rC\kgQv'iyG.Fe=psw_XIE)s@J;_V[iY@3$jVp
    u*3jw'R[IoQ#Vg7mv-GDY<GU7xt6peE7MhT7E]mY5_9[XlEx?o,<Ev-z+Trz7Ru-Q[]~>;A$7l}@
    EvO1eu'xzKDy5^easooHMe[w'zlIleA~,5U7I55?^XQO[KaHj<Tu<JDn7+<X{]k]_[Zu*<sAvx#!
    {\@T*;xRav$^B;N>{-BGJnoN1NzB!niXlBnG*T<\2o~$Yn7I[obi5v7r+@'HoX]iHa{SZH@X=+@@
    so{T1_D*Q2TXe;J-'u[2^WVs=>rB]EOezR~sUU**>wX<o?+JYmep_G-YeijCiTrv*2pD[Qio-R1#
    wCI7'rwEa]VKuav[$vmIo,D^rD}}~+[WY2R$@eRYs+nQROOCZLcwIjD,#s~LU{R>vX!aIU~eVrxe
    pC,k6>QY1@w<U_j>p,-VBZwv!#U==Te<Y'sUwlr;~7VUQ'u,=PI*pm'oUo:9=X2w6K8}Ek;;X)V'
    KHveXXDle'na]?~Cx-]Du3HU7AD[w\IKj@#jp$EIunL=>]1>_jAQ3jro!JWJzK^7/Y2mlvRX3-\E
    TO_G\5CT_*ei'SX*Iu,<I_|[~V^}DI>nIKlBew2iIe2dGv+3VQUwV[]eF12}<$2!J'_Vk#eJ;cD1
    =1'1D;DODi\Cj13>[o$m~'[^pu;o\?}Y-J0{_?lJ}]XijkE\A>~<aE-v!u.v*upXG_*1;\rF[~Ye
    rGQ,_#DO^mn^.<SD;$z>U=KiBUv${,Zq]?HmI1Q=(+_x*;'5$EWW+oe![,~n-z\l{m+]YJjR<m5}
    @h#UQ{v#Y[BFEWrw'EJ<IoDevB,Q!H72!GKmBC!#6~eRiz\Z];TO*;-K{_\@wo5;<Csi~!']iP@V
    k[]um}GM7$<@sAC[i$>lu*i=VCCTCHr*QO*nVj<-I\Ro2CY7\#'ka<aRmEi!$ll2ZjD7'Q?\SBTQ
    k\7XkYA>-]-1z1pi#JB!Yr<xTs}|vBWG?eHnXT]~@oBY*u\<i5Bj7iRKm[3xBClEkrpug\k[Uomn
    24h#<*1rAn#Y+O$Kr_@2w\7]pR!@-'r^HG1]\>aD#_>x\ul[~x<'DBjUIa~smpjFV}]mp#,su*@=
    GH{E>jIuk+psHTDpRi;@[-wBYCZ-j$UVvx=^5w;BaVjJ_IAOB^},WwX1h7{Xx~\!G;$?$3U_[yA>
    @\BBI^&BieD7;^B@XADW^#Z?T]7kwo-cT\#\pHHnIss$A>DUhn{~KUlLfkRQH$7o,oCvurmrR@[I
    !::x;VYxO+5x*23BZGB1{{nraIBZaX>0.]eG}?pE]D~]ijqjJQo7uVYW-G[%urnK9jm;1VPm7T7H
    \Vw@^e{5ra,bTYDOnBeW8k$Ju[E2XXxe2QYjU7YmEtuaYJ\nWTkHAQ/S?HmY2VGnU]EaRI_!#{Wp
    *1V#+]!se+7Dt{oO_J{1o)$oDR/iT{1m}Os2^_QXIJ!Ija=uO<!/\GG]pxnRsuU]A5X\5\vE$i5T
    +N]DJHF-$J*np\lIWJp8}Xx3n'ZoivD$LY#*+]5-IjG2Rul\GvBQU]T]r;]1zq^i-mxHm[H=*a_s
    H5?7$l]eY,YCI2D>ap~BvRImYY'WEYO<_'cWUQ-G=^ndi5w[}ZZ#p']JnzZ}h3OrA-RU{}/>H!GJ
    Il!#hzk\;sIx*L(lUBo[K'Qr5i@g{_}O;x*[D+uZxTYTRIl~IaE$VO@'>oalB;xJ!+V[bjkw!<=?
    zGlxo"@,WTZR,J\v\=2}<W3IuGn+}eB2*=H^[vZ'0inaoVUGxaX\{pa^XT-pjg|?xmu4Zl#$9in+
    -<n[_BkAX?RC_ra[\~RW1^jsGAR$wGHaA_w;=_I;2nVT#g}AE^6E-ekTl*-WV_G#=7koXA[upz=*
    ka<lu55ix]5|^#p?OxJp=waJ<&z^nHzlZ3$EYX2Q=m1j<x55Q#^vDDln3e\@xvxek2$pJB[_iWvV
    [\:@E'{^->=Q_EWEsBX>zrO\{}RT*;Jt5v2][wXX9>x7'b5?otpi-w9AwAaY?+U3{R\EJ_H#73ll
    WQ?vDo>G+lO$E<U(=OTv=RVuU<}-,v]K[-^3HUO^O<wDz\}TwV;+1p]T(e~l<OWa7d:DgM<n1E$p
    m]#&S4CU_ix'ji5;sVOl++/R;QlP]UT#v$\IE]%5na+bCK^9OopCL'kQi9,+ezp>epDx@WXT_o#,
    leB2Ejz!-~l^ppGmmr_X*#ju]sijmRO#;rQ[ls{7r[Cv$U*1uH<6<Ye<jADYIUw3;$p^_{D>Ru1?
    iH!s\Q!Rk[n{w}l]:2Vlvo-KH$1uUl*=Kd$E^3_A'I1kw~xT]sIRm{zC\+~p>^v'mpaU<<{$Gk?w
    pLvK!$}=-mEUjp:hT1HD>l5rPARG5)lG5@#7IDwYpCaln1}YYGV]@Bo-$wAC$j=,]1"jHw+[,zA}
    2*VueVul5pl~OTGBS\'$H>+mIzmOB^^YpTU-D*}1Z2RKku+vR.~HE~v\x_uD@eB{C+$7D~^<m=n1
    sr_+T*J>QI$BzHxA]pH-+1}2\H]?~}^}2>1v?},H$jz@eV7nnn0usZeIN'zI]S9|V%pG1kRu_Q\]
    ]-*3*w2B=C)=?wTm\IOT*e=>rXV?RR^),7+s#OX$@G_XD]\W-=ZzHo+[,l-DKj[JxI,<ul~]2[A]
    8zH}pIx3;jT'',#<3z~{zY<*@Y~<,ri~w{];;1!UC]p}@s\eo_Hw+C}@1~<Ko;[{sEiBm#nTwvxZ
    mNKa3l>peaFD,?jlGJ*r,55sH\\n_{QI\sURAV[]iBTNo2Eus]DG^<$uTx1^!R2oR{Z{BkJ[J./T
    rsQ}2'AI@O~O'Dp>'EGET2G3B,vz<Xj2nrJaea,elD>}WjQo1x~_<<1B-+-G$iU&H'ou*aOQzQR'
    li+2EV,$}UIJ5si2BFHDT[Eos\$>2jGvQiYRvsY}Rm#A*^#G$AG,Qw+rC-ep=ufG3)XE>p=KmE2r
    sI,1p1wtl5-+BsJa[_;r%P2E[@LxWl>X1Jas1m,+Eews#DI9-j$rje1p<BO@-Q~n:z7<m-I_X[v-
    ~vpkXtCw7-eD*sZv\la>-~1!KHqG_mO}'J{O]5j+Y?>{9Z$,>lu5+jW!_,R;#Izk#Es5?p{^_dKe
    pZ3$C=^'OTW_,@II#-dZBD-QErX\lkRRsp<G]Q_bgR[rAZ_s_AV\>z\nYUQ}V5T<C;QKV+,B?p!E
    [o}m~SO!$K$KClHI3}UnIeT,sRKvxAVoKkK[x<nTw\olZ_zVe#xv1eYAQkB'^@jGEY%;s=\YT+Q{
    RVu-,r}Vv={_>TvVpk7czaux+'Z{O,ZD7GA7!5B\'z!E_zDX*emplr!_T]5+p,Ueb1Um?S\'1wwG
    ?^ZGErw1Y,aaY;w*CnGC,_{^]o+71^,5-BGuA_@IAs?XA!(~>Or7rI#=R?]<VTU_+O2-{EvaO<o;
    AB;zoUol3TZ;A}K|THp^Bklr5C><NsOY[lJ5Q$voIp_V[2V37vk]@*<Y_B}kO6+v5RDo}\ru$*jK
    W?,1ArZETKxl,>'ozRG'D2_wTu0Z1~5C#}-Re$uO$}Q;jXAA{E3lGm]}=u?i*>1-o}xm+2zV!--@
    _OiQ_I*BE}z7p^nz55EK,zUD]V^GE>_Luz7xOQE_BiT+T1Q_!'!\zn,Q7V31>xa3eGTzvn_l#{^#
    v,ex$7D\\75e-H+]a]i<eimzO<$zn*_ZTz;lYEAX\I]Ism!niIUzIXJv\+K1w[~_j$A@'oK@RCKZ
    Y3'#U^#wr=*E7mY[{'eEXEpzU1n>[?n;CmU\dk+,X{}\?Ij'3\=_e1rX*%U-RKTETKD'QoV5UIy_
    ,<vMY[*5/Y+Z!H-xkTQ_Ro';uzZZO9*Yra[v1Qwz+5GVsI+Q@}Y;T\Jo=[hWnBZ=wQxLvz#[7@ji
    gQKW#zmsTzxe$Ekv*XoY{QC5j#[u~v[!KZ7YZADOXjZ-_W{5Jp#$UI}<;Rr^_\'O#l<Wawlz{%5+
    $A<17ZZxC-ZRYAQ1pRIvCrU=[a;$5A1~\KRO#_mpwnGKaK@5D?_^<J7}Z'IKWIxQzvX[K*Yoen\1
    _HfAHW[eBR+ap;xA'[p_jxG]BX#E-Iz-\#;Vl>>^~wA7p=5Y8UO!7R>zzlIv1x_@Y;lzj%QHx!v}
    UC5<u,VI~Ec\l,^a{C,n72zmVOo*{I]XU,Ot'?^1_<*_-\~+zvKB@}'OII__'OYB,,k3Ok'>#^zi
    GpY,YszD\>'#73]OD,\<!v#\''G-lvUzvoCwD[JrG+x+1ln,5e{sn6@zUBv3VpIV5{!IOv4kQR@8
    ?j}xB35Z^D$1#wxk^Qzm@V+n2]A$HOz~'zjC;7nu\R.2EX1DKR_*J-GBx^lV7aAlz,*e?,zT,{e^
    v]~8#nO=<1}H@RmnUjj~VlJ!eu1,xR$_t'eJ~WI^#_,CJpv1xo1\uIx]K<sEVE\-EvXRuK'_5<eU
    ~hQT57l*xXa<$E~QDruEiI+vK2QVD3^,ol+xpJklT,5rKK[3CJ#X=UeDCAll~_D~}_?zRwY>';,J
    v5<zA2+VOR\?Qoviw@\QQH\RWm+*^JbXwKmi_kE;Q[w_\znVzx=)smTmuU$5o$HnI'2?{Az-KpGn
    pJ$A_lCW$@I*r}W~eHzTZze$\ECmx%P~jG{'e;~w5V\y|;lRI1HIkoGr35$X[Z1Hz]V,?/AA7mK1
    ZZ~lua\'O$Z]i_nq}RrOBB'AnOO$vZYn_$Q2#X5A2xi_Ev*ARDD+mxYzZw7_VYW-w>s!KQ'3,7#~
    71iaan3@KvApKj?+no-AZx3+B13e),p1\kw_xD,!l\ju'OA<>Oru,|i*5z!*sT#Vxe)[TXx#-AzD
    }:TpeTc]we[L@7ZnzoRn^=r$%5?xC$vzX<I?{}@,,[?A^F[+<A={[z3[Q2,uw\G^zoD5+n{=VGCB
    OZTD??Bjm14ex#_3+TG^=GmwQRju,Qn@}u]#VRllo~I]2Yr{jot']AVC+j>IE-;[x>#z}j#$>\@U
    p'<fvWn+-YIzelYkv<T5&_7E;OU!ZO{=_Zx]j}i@B42}+W2AznVJAnTCm{\1[GExHU*DlTx1+7@'
    Wr{E<?pJW^+_;[jC?Z$Tz*j->@v9=!<\l>\5>vRWvW_i.}V+2t<[<e=Q;{Nn{mQKsInk.DkA>3pG
    w^X+$#zK</a}aJsVpwUX7X1{^o0a{n;!E?Rfb=QmIwB[[>>>]BZ^#Av77G#ua55VoC3OOGQr$up\
    $Xl5<,ol-]XG~;}?7&H+je;A2COi3W9(}{lIKxEBKeE=_\{x[E#Oaj[uKOO3VooKH'YBH$'D;aup
    }3x5kATa[2Boi<GE*#22@5-vxiI12vv@VOJ5zQuaKQwVx{s3eU{z>vaAV'C2[suXAvIK_ZH>9^]U
    \QC7{T'x>;7,rB$*1IA~aEC,7B@}sR!vl=d{_D7|<[@;K}1!|"E_C*$ZGsWaZrdX|Gr7Z=?E8BK_
    ~sI}[3^eV^z71w+IawpIp)5C_CG;HR*Kzk<O\UzQ<lGG]B]n-<sTm3?$l'Jrn@'Ca2]R2l!+X->[
    Xs5;QpZU7j!_m<%{Ap{{x#-jm;sR5?2eKEvE_=z
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#@zJAg?TEw@,On0EBroBv[*}Gp5+YG*k=k7GB8S\D<rwrU*[oUw[XE\[g"0<O-l-96}V3[mU
    _-<'k$_rPlwJ#*+[[o_n=['CW!Ul$HUlOCl~^=M>wa-kaz_6h7k7K?E2=/w8[@J$}+XJ9I<!W7XA
    @^;R_elHefR;Vzz_*3{R?jp^V!nDG~{<AwleXr5jI[enl~\RBD$t=i1U!U{UDsoI_$s;oT;#In@D
    oF7eIws_D,popC]\ImowoXjEoW5BzG]V5<OT~m*z$W:@X_(#,]x5wsU$?Y=r^rWHAArR<VHRkY,+
    ^\j;_-R=s?Qxmuv0/}'3CIkou^zWelxW\zjw1RCV*Y'$}[,?^zW@_-.2EExVDuw;<*A$UmQ=Vs~J
    R5IE<;#Z]9kE\e$s}[\V*Z5UW3)i-x{5EBm2VT~~pC^^W!+q=mUA}kl]]+AeFI*D=PM}TZ=@On<V
    3J@e**};o<r5J{#&IEx^5+7@d?6mNJ5O7ATH<ue>>6Aj=5PmxwVov5Kz#BvfG1mmsuv25lu<=esY
    0Orap7]jBTpV*,NUIV3rn]WiE23mG2WyUBT^jk$B$@\i5}<-VZ!nT}#Z?{nU,unvqA,j^V@A+1HR
    ;)wVX]4}I=~7U7,-BJ[C{=2+plEmYIIoE5$e1^rA-R32DmHFI;oseT\rO>K[z$lWJ$!V$~j[sm*p
    NlX<;eKHOytQT,@W1j;1OxvA7XJu7[BI_p<PT9WQZmf+O\}DpUYnV=nCJ,$tRr-u$@7HO,ju}~oZ
    $OB+l5ECV$>7aRKZ#AKX5,[!Xpk{[oXC<Tm7|3,s7-7e2zj<$j?jC6^s{!#T*mB^]7J^zKY1pO<U
    Te3juzWQVAKC_isu$7zB[jllAY<w\7[?=Khmw}kXjJn5oG<#jXVYEAYl<j?wl#]iE'~zGajLj[7Z
    e#$;T<K7q]rZGi5,[R@CDr@CTiLx@+?=KRm]7?!OV=Z#5W^O<H7,Tw2;\Gw*2K1|5_X#5RaEZV?#
    r1Ix-pmnK9+HA[d}^lsBJA$a1\_'$Up_r]KFo|rs@5\$kxQr=vaTT[ji<[T]}RyalH53,!v^pJOv
    uK!S<TTrH'io@$Z~[[l[/0G/mUK$R<IY4AURp\+w~d1amBf#5}eKlDkk*YW#a-p}?Xz#s**}H^*z
    ;wUTa!wm}-YruKCLYyjQ^D8.,WYTvlx{oa5<=5w-2.xAZ21-e$4Y$k+]K,+SjAe-z=_W5HYY'rY,
    R_kj'a+5Y=Tx!535UXA54=Xj_sB#EMzl1s[^]7<GVZ<_#w^uIWt7xJ'Om7'uOWC[=\XUQpw?<e}K
    =?uo15aQOEwS1Go5Yl_zOD?$1WIsJ[l$}WX;S$Z';2{]$Evsjr2B[ju7{7iu\Kz,wU[$O)Dz[^1+
    ZHLu[['"ZBa}vhB*?>oBBa{wJ3+Rn1QG[mI_$r2*$n{xT7"BRu_$K]lznA<=pOQooIJQ1m](za[p
    6dEV_[CD77IiU?}{jvs3;v81\2aN5?a3][joA>UU3$A,=k2Zmjn<V}i2Q3RYACwG$i5J^,zY1l'j
    i<$iPRX\=xp<kIeAl=leXV=V=Xs1DBznQto^2Bxr^x{QruTVGYtkD1CZY3[n[wwk=I?,j2nC5hpJ
    _JTI2sp\r}?_+,^v3#y1->+[u}eB>no@A]'ZIi{GiRjO@,KWa}2oWz<$=pRdinUmQ;RA$ruAomTx
    O5wW2xz{0B>G<7K=<$-YwC3AoMv5-BEw*I=s1kQ=D1Y*!=r,3VN]'zWtVTjjE!xR}HoEQ7_{Jx9-
    xn}XpumNa+@se?!#<\BlJ]!G^?r*5]@Ha+uTe{7s=E\!Uj+OAz]X3r>B{rpDfxmWT]-}5'oA'ioC
    u9~rk[ra<[Qx<eCx,{el1;'5p>,cO77\1X*]^e2nl*@I_iHp\yW]?1%As#-IUQm';OYCJ+-=!Clj
    u<]}-B{,#_Dxr'ssxj~AxHus7v@Iz7aCa[E}Z+R)A5*~B,aG4|<[#*w}i1el^\t=>jkF+'p@o!UT
    ^{llz1nROq~UjBU}>K9[C_vslr_QEk+==$,jW$WMHjp$$=$xz~>WIT\j>D1#rA\@@a~WxX;;'alO
    IslXVET-H_5T@V$=[e<OW-,rp!pG]mrj1]oZw^+}^~>'sZInIk}@u}Y^vT3'.11I@=rEA^HH$V_2
    3=@31|Y}ee;XTv">a-Z3]~!B~JY2Y5Cd7=k$zd8[{'K;r~a
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#+s{+I]}{vHjjo$v3iv3$=$!nY>{$3YAC[:aER"#,~am$R;3OP{V2[$)[B!Z;a**y2}R*s*G
    @fV-ns~{BBY6+1$'Q,AmOoXA31,['>KjrBj'_@-]Tl~nI^DU=V#m[opT^5^B?wpH6iCIU1-evB1i
    Z;OmBx7K*@E[7Hzja@UE;AVUE)f~{'TvX<[=G?,X7h%!E#ZXX32lH\,NxQ\51^+xOE?=GGT<D{pk
    k<l1B_m*#D13RA!!}VA}$@G<Bj*TCHW5fD=s_z+HD2Eisxpn\oHJ}a}BIaGuJ@Q\o].mIz^{VBE%
    l\Zs~sr%e>]v1>\^$B{^J5@^l-WW1mV[kjknu+^i!5rv=^+Is[,}ao{Y]#W=x<7]#Y|Y+@r3jDaF
    xQ#JeGv<<1{<CiC[Y7KXezY^Xlk?Ar;lpX2w[Wp_>+ArGl{RXj@e!e$CK$a,]<p7@&jHj$TGz\jr
    C@7Z'_*spImVripK$!Z<{*jA@kov*WvO}{{YE~qF,ZuRt31ZIM{UA$F,rujC@55uxI-;ps@7kvVv
    ^1xlm~UQ_2H\\1nI~son>@r7KY];_\IE_z{BjT;O_}33H\UxU2AzjIknos{*Z=x]}Q![w=+v-z^U
    $;>^=~vsHDmU^YZRv$W+o<VB-,JQ],U^*|CB*s*lVuHEHG}~o,=15w=]_a-CQWO#HCrxZ7zu]#5}
    <<-n3aT^{nO\l==?$>V?H5r'ljY!<]2E*[r~EE2[531UT+s\W[IWDYGa\Rz7
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#C]os1m>#jlz@=knYXa{[.Bo>K..A{Ym}~{o3hjiH\.a}'+#>_T$&Q^<a3,,;ImWC7kQ2_E}
    H<TZnp;,+|EfkQ'Be-e]iXa[zx^QI}VCXvopoz!DN@'}#$e[AeUWURv#~dQi_#s>Eop?]HIW,BW'
    1_we<}Y_]VR}['TskuKUHOj_;wC$A_X=}s?C@UVQZG=5Dk\l^Bj+}^H-<usxXu_<,#G*1}Nzxu*m
    pB<'!pmlE7iAnvV]J]OL1UxQh2Vo#Y\euI?5?];-QxE;2?H->r\Iz-\sBJa,I=T^}YO*WiYz.Qn+
    !{Tj+;_<ne^R^2Y+.*?vE_H[s=Y]$ICDk'#<5kwU[7D]k1?,p@$UeFZEOx1>>wpRTr+UGJ*3j''_
    [WJvQ<;XU~~wZxB\E?V=GlG9^l*#G+^^NEY*+=(<-7[.EH$$V^v--5kGRx1'Y@[JQD[J,>*Qon=X
    >Di}~B13y[JBKiT~I#o+u1+=-0V$3Yz_2a__U!B-HmekurQZ}k=!DmiRUzY;_H[i]l1x!7\aZ,t,
    Xne=iG*R7X,Z{_}sIij<+]#_i<3KUQ,bqcI-IEFm5@'"2=iD<$$eXYQ2Vjn^=H<lD=rA1IkZ!psK
    3^l?2xTEYJ{n3ECVVh%9Rn*Ee2*7]2Y[O\T@1zT3!{p}CwYuG~Xz}m*z{rkw'YU@mvv;FcxjZnB<
    '*
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#PX>VWKYrvF$D-U$G;DpmCXuXEC\nW}9}3-wYd"BB$X,V5}2\2<{A2[$:v^~+@Rv?}FRX3Y"
    <rA#@D=pgaap<+1$'Q,AmOoXA31,['>Kjr:eD^@LTl~!,C>Y1+B?!{}u:7Jps|4xU{XnwoQGVH]7
    rGup2HvHjz+[,>11!a>ND\7O~<D$N[UT1I=77p!^@=XA3BnmJ@,v7Ia-TlwXj='~xD5XeO3Ri5\]
    _^V+k{[;vY3J};sO}$7vuFJo,jjJT\/-O-D!{D]pd%tUa3Yin{,[+~HXopG}<<J<h]EbKow'Apk#
    Om;<E*}{dEn2oH=EDf$QrzI~o$W.}V32GV@-=9uUDaC#KpO]U#Zsj>O^_3rIJ=@XV}e\_C_k'3Y~
    {p}\$v<U[lBI>;1C*HD'HUQspzp2ZxV_lOO']nRW]#x=>j1xE,XRe\O1j^X[+C5J^7V1UO~HW@?Q
    ?J,*#Z1Xw${az#Vo5xUpx?H7##gzj@HGx[[Fq_UxZaR<uH1XlxrkQ1_#2}zlZH]fvY<3msmvx>Ok
    ~O}Jlpl7!*W[[CGZVQ#5vQBK#a}<!e7x2j5KP'rx''z7]x5]C''+vI5sJEnwJ-{e$WQ?obhfT,<v
    IZ5KWe_upV-Q=*ivL%D&IDpUlzlQCk;'15;?I!w@u[OYSC7Wr1x>$o2mR"Y-eDIl{=Z,_J}jr=-O
    JOvjk,R'loQCY]|nQYA2B3_152DC1m3z1ZR}H5vsr@D&iour7A{$CR=]*TJkH\i-y'r7axz>Be{j
    ,l#1~sIAuj$m,g)H}JVw$w;ZaBl]ZERkp+-lwmH/lQH<nOp],E>52rQ@*p[^j#>-dhv2op5<+'1H
    up/$I32oH{E!VY<{Y;D1u7m,!7iO"TX1<H7>7i$J+_(X}p<?n1G\HH_{H^H<$<]}-CmG<2J2[D~_
    A,r|DOVWtG{Ej!'_>JX$j,p@$iI'{#1JQjO>maDQsQ~2ZI!7srT^@fdk(it*W+{W,,'5i5=r1?]Y
    U=vWTo~
`endprotected
//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin
`protected

    MTI!#w5sj?+1WoA^2Q>RUiE+5,kWYjnI5KjU[[)GB)S*X[[w'UR[o_wPUr-rB,?W4Z[ne~Ck*2'[
    v>7-H#Ull!r#~$!RVN^,]Eemp[KwJUz^;}3[eHoU~XAlz@=k^^<[@BEe<oHE\j7vKve;}+IZ'3u]
    +}\Z;uzd~,UUHUVJ+l$_{X<]homj*x{~I$vX<KwCTDQ+vnCUD3X\#NHoTsBG{zZ=e3He{I0"K[oZ
    ;j$BR,Gm7_}[aBRHDkp?\BB![@YnIm11OGO=+U$nB"[}XrTRz=sS2'+H_l#YADv][nwBht}\IUHD
    'ATBv@EjXz"GZAuX}vB#asAl2voduo@XG_VKGXvke^1Al~ano5sk,*3G5\1B1V-_FET~>ms=puOV
    lED31osQlvwTv8C;_i<YW;Fe3Gl}zX$SH5W2u,rw):@xHTJXXxQDWG[>sC,z3U]_!RszEBIT+,Ik
    WC?D#]u,t-BBO-U$zeWjp=De}r>a5vnxA}Jp<H'K^G\Hen'!zAzs{$Owrel^X]Il+w]iYY~,Y3{$
    [A[XzDsH=rDio__kZ^ju}6B!vlIEQK7K@]lVV*-wpX@vCl~V<Z}oDsYB1]7_]K><wBf)!rOv_iGD
    T[meH5,xVYv\)L[ATXe$Uasz[TI![3Il#mj{aZ3*\r}B=VL|&[BTue#2ulB<D?DYm~sX'OBi@^Y^
    ~[!}x,},<jizRaaCH?U*a*Cz^-RT]CI@1:p#v*Q^B\(qp~rO~lj_up1i_TO[-*I}Bzoi-Nyx-wAT
    rr=:=Q-w$EW-7O^rM=w{!QR![E@@5r\*=HYTezns[q[D'KOs*'
`endprotected
//pragma protect end


`include "ddr3_controller.vh"
module `IP_MODULE_NAME(efx_ddr3_soft_controller)
(
clk,
core_clk,
twd_clk,
tdqss_clk,
tac_clk,
reset_n,
reset,
cs,
ras,
cas,
we,
cke,
addr,
ba,
odt,
o_dm_hi,
o_dm_lo,
i_dqs_hi,
i_dqs_lo,
i_dqs_n_hi,
i_dqs_n_lo,
o_dqs_hi,
o_dqs_lo,
o_dqs_n_hi,
o_dqs_n_lo,
o_dqs_oe,
o_dqs_n_oe,
i_dq_hi,
i_dq_lo,
o_dq_hi,
o_dq_lo,
o_dq_oe,
wr_busy,
wr_data,
wr_datamask,
wr_addr,
wr_en,
wr_addr_en,
wr_ack,
rd_busy,
rd_addr,
rd_addr_en,
rd_en,
rd_data,
rd_valid,
rd_ack,
shift,
shift_sel,
shift_ena,
cal_ena,
cal_done,
cal_pass,
cal_fail_log,
cal_shift_val,
read_back_en_pipe,
axi_aid,
axi_aaddr,
axi_alen,
axi_asize,
axi_aburst,
axi_alock,
axi_avalid,
axi_aready,
axi_atype,
axi_wid,
axi_wdata,
axi_wstrb,
axi_wlast,
axi_wvalid,
axi_wready,
axi_rid,
axi_rdata,
axi_rlast,
axi_rvalid,
axi_rready,
axi_rresp,
axi_bid,
axi_bvalid,
axi_bresp,
axi_bready
);
input clk;
input core_clk;
input twd_clk;
input tdqss_clk;
input tac_clk;
input reset_n;
output reset;
output cs;
output ras;
output cas;
output we;
output cke;
output [15:0]addr;
output [2:0]ba;
output odt;
output [`DRAM_GROUP-1'b1:0] o_dm_hi;
output [`DRAM_GROUP-1'b1:0] o_dm_lo;
input [`DRAM_GROUP-1'b1:0]i_dqs_hi;
input [`DRAM_GROUP-1'b1:0]i_dqs_lo;
input [`DRAM_GROUP-1'b1:0]i_dqs_n_hi;
input [`DRAM_GROUP-1'b1:0]i_dqs_n_lo;
output [`DRAM_GROUP-1'b1:0]o_dqs_hi;
output [`DRAM_GROUP-1'b1:0]o_dqs_lo;
output [`DRAM_GROUP-1'b1:0]o_dqs_n_hi;
output [`DRAM_GROUP-1'b1:0]o_dqs_n_lo;
output [`DRAM_GROUP-1'b1:0]o_dqs_oe;
output [`DRAM_GROUP-1'b1:0]o_dqs_n_oe;
input [`DRAM_WIDTH-1'b1:0] i_dq_hi;
input [`DRAM_WIDTH-1'b1:0] i_dq_lo;
output [`DRAM_WIDTH-1'b1:0] o_dq_hi;
output [`DRAM_WIDTH-1'b1:0] o_dq_lo;
output [`DRAM_WIDTH-1'b1:0] o_dq_oe;
output 						wr_busy;
input [`WFIFO_WIDTH-1'b1:0]	wr_data;
input [`DM_BIT_WIDTH-1'b1:0] wr_datamask;
input [31:0]				wr_addr;
input 						wr_en;
input						wr_addr_en;
output 						wr_ack;
output 						rd_busy;
input  [31:0] 				rd_addr;
input  				    rd_addr_en;
input  				    rd_en;
output [`WFIFO_WIDTH-1'b1:0]	rd_data;
output 						rd_valid;
output 						rd_ack;
output [2:0]shift;
output [4:0]shift_sel;
output shift_ena;
input cal_ena;
output cal_done;
output cal_pass;
output [7:0]cal_fail_log;
output [2:0]cal_shift_val;
output [3:0]read_back_en_pipe;
input wire [7:0]    axi_aid;
input wire [31:0]   axi_aaddr;
input wire [7:0]    axi_alen;
input wire [2:0]    axi_asize;
input wire [1:0]    axi_aburst;
input wire [1:0]    axi_alock;
input wire          axi_avalid;
output wire          axi_aready;
input wire          axi_atype;
input wire [7:0]    axi_wid;
input wire [`WFIFO_WIDTH-1:0]  axi_wdata;
input wire [`DM_BIT_WIDTH-1'b1:0]   axi_wstrb;
input wire          axi_wlast;
input wire          axi_wvalid;
output wire          axi_wready;
output wire [7:0]    axi_rid;
output wire [`WFIFO_WIDTH-1:0]  axi_rdata;
output wire          axi_rlast;
output wire          axi_rvalid;
input wire          axi_rready;
output wire [1:0]    axi_rresp;
output wire [7:0]    axi_bid;
output wire          axi_bvalid;
output wire [1:0]    axi_bresp;
input wire          axi_bready;
//pragma protect
//pragma protect begin
`protected

    MTI!#wSX\pT[!-zz;{keij[1aD>Q#X'OmCAHtg#Uh[?Q_7m_U<R'ER1Zsv=I[0;'m$U<+}6[xCTG
    lZCt};-}]#>}oG{BS4_rpA1?!27?BRNMD^,\3U^+-HHxx7TJa5[[[#v}=:IlN:RD>zJX+#IHAYBA
    Vi>XfmA$]=OTvao=^f|k9]3w~Y@eE_O]I1Izjn=r3>Tja,'?_+siUoR5Ak1;+Lp1*_qq1H}[_5#w
    (*ln*nw7-Is[a1BY^8kAH;xOn7Z'BeBrB_7luTLb3<-p<=5>copTu5^OH^WI1RJ*}Ix1u?>oKf$1
    111OYsslK~e*=T2$;r}lBX~]wjnsG~};;Zo\l=*G;j_=,nXQ<2K*3p=#W}q_Ku'c=$]k$I[@L\pB
    +[A$CvWOjC[j*Rm_Qxm[<Lzo,j1J@mw=<piY{;]ZK*<+'?~sJ$yV{-weHOE.o?$Q=]2Z?CW-+E\-
    rQl*qfr,E=GHx1M}_a]YJli_'#p[ZnB-|#*E3sA+Y-]InH{XD[oT7R!*v@U5-a'WQiXBjk,^>aRu
    k#YunmoQUe=ZGaXAZ)rmzJt]oBY;1=QEp3K=\'W^^$>HU_Q~x\{UxxWs^QD,C\EX1TIDr@xdO${~
    ZOO@Q_eD=7CJ,^so*H_@.9rE-;aTTA5GUIp3n!aXU{*zD['apGOlI1uD*xWa,u]@U<{{B_(aa-[[
    $K{'Z>3zoXK[5z-GCY{QIoU;Tz?!wOl5=GE}{A,V[A{NTRpl,]?v]o_JoKu^,C~z*e[RoeRULz2X
    !GIVZ<73K0+l>1&1I}x'!^,wD<j@<^JZH3?C";}?$y7s<u,~THMw=w+un>KUY?TJnjWWO+z5O#G{
    R-JGmm[e^EQY-*V{}<<]_r^=*Qx[>1EB1TC+GBQ/B-OTVZ5@I-'poB"s'zp\3jOk5vT,V*]]y}_Q
    ZRjpszD5?\H;+,5oWCw-QE{Z[r;TC#U1rl5@T.5x?x\s>kQ*e;=BOv5suH=Di=YBR3$Apm.#Q=\r
    'D<8h+'1$x5QZ;[^}owCjZC[Q14em!e#TQz!,JXslB~aaOwk<1n'K-1{{p2noZu14_a_sVW5CH^[
    *B<WUGD!^<wV=o3+s]1^]VZ]*7#-sQ3ODGC@!C72WY@p,<p=DY2]J3DB@3w3pEre*~{ZI0'YXJv5
    ZYVO+>5G,ppUQE>s;EdCZ1xsB!^=a!-dlYuGx}{}R_?rz[>r\I*$^'!VoWm^CG*$XDvGX=lHm=Q~
    O1{Un]Xz]Y<[7\;eUB[]5Ba}$lE-XnBsip>nHemI,U'5+n==aC\l<O#G_!lsC4\uKx}27KA*D$kl
    >;d:{$KosRRArnWB4v'l$oD-C5WW*#o3@B=]aB!]_RlXo7jzlaU$?rrZ?lR7Rrwaj^Z55\[1Rza{
    TI7Y[7,<zHr,n1@_a[}OE:fN~<2E"ODWBKB2xLz;2H7OKGIWX_[ZZ=__B=R23,=JrE>wAxC[neLI
    ~I~7vmO1mOY+eW-l_m*ZT!!K{W^u\X~s-;YTn\#wYYYTR3YCaU}%o?x_h12^=Wpr>qe-7]3leDn^
    -!eeKQ]@ji@1I?p2AxL_p5?R$H?[eap{+@ei'GQeG}uoX'=c+e_J[=HA}$_KlaaB}#JO_zk[D$ps
    m<vkEVm#{^\\*5mzs;BEoZE'I;,nk[es#{zuDo]jQ_E+b2Q[*1eKXs,/E^KUc>$U;B[?jZxG?iY[
    \Gvw@2EQwLe'5#J]lU?$a\mR\$Zn2C,5z'Bw,ZWj@~y-E^<r*uWGs<<l-$v$IVi>\r]HU^YVD5!e
    ]=w}!j<G;@U^AR-y7{${5=TkviwH2nXIUY{kEs+_{Xw[1Mk7B1vl'{Mo&VGmx=@m>?<2~R2l-(wX
    @[T{7u,i'\}3[ZUR#R>B-p=$<rND2a\@Hjr37
`endprotected
//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin
`protected

    MTI!#^g\OJ\qWUr\Zx$H}n'U,RJE}=EKUHo?FqE@J[6sHKE5}xVYX>$pTY?y)]{^;GZ\u=u*[57}
    3<$Znp;,+|EfkQ'Be-e]iXa[zx^QI}VCX$sD72[jN)j$>zGT~WoH7Hk]eBzj!vMYvu[oJCu*;U-m
    ]<J=^xo^_+>,7Yaswnl|kCZZOk+@&oOwGtv>C{@17~*;I-$^{Ck<}oQXa\-7np^z2+R\jZ5lekf*
    oC}~w1V${O;T[r,Z(5*#C<UjiEjA{j7~Z*~@{^Q=sI;3VHQe{uXAu1*wG[zo@e7]eVa$@=+}l|^*
    bE'_V?-2BR$D*v3C]w<Xke>x?K}*-#Tv!{<O^K,}]\j;k{a1~{s?v7I=#{O3RKCku|{<*UT__p#I
    je2+-<7x[*$rR~BR?eI!R7z\A2iB^#vjHRmauj?,2[v,p1p)]C2~-_uRnvuU|HRvVUw{kWR*r3jE
    'fySa]D}!z#QjqBs]veGl_[5zQx@1RyGO<Hpi!5G2![C@5wEw+;K87_u>ne$CxY*[p4lD1mvJu}^
    z}=v3I3PLV;o[VH6[<,EruAXuTs3mA\i'Qk-C_^}XU[oErK(_]oOJ-l}V1Jr=O=oBBz?I>*AKBCs
    3+VJ\'XzXrvRX7]QjGZ#}Z2QuEs^P3='GxYkz>AW#Sg!<Z'I-_BoJ]@>nOY+C-n9lAr*5r~<{l25
    =e6]I#'Q1<x=@VW<jWRO}U2_xR]]xWul[Im&:ge1:$_k'P(D]rDOu5w2D>BoIVzDT[}O^Y!HweEO
    5;@lblZ{{G1Xu'_5JoG>k/JjeVe#m{R[>KA5uu}i,-g=Cs2L{'@WR\7Er;z\3Q[r*}j}#w^IkBz3
    57QG{aWmaouuAe2jgV_CIb{a{I\Q3+e;wxwXWHD=$uelGAo1\=a\-?o]5}x;JDhOG!2Qu3'A_+>C
    <A[d~^X_\WGit3r?QDQ21$5Gi=<u!}GP5aWek<+'k1Q?QM!aEaX}K~\I<zs7s<[]]T#L${r*pAKH
    >\zs@\VGm=_RH$[?_k+KTTwA?aouJvX]9!s!?=lK{I*;_{[KAU1<n\[}Ii7}iB'm{flYa-W-U?;*
    -oqkau7{1W*DBZnHQeG0\n=rWrk3Dj}^QkX*LrCTp-o\~UQGko75[jK_VRp!]ja7R5{X]p@3D3YY
    x;zG{Dp<]*$*uvjOJJ1{kaUaBd5j1~Gan+mrj!r[u,T1bZ$=1HT5]nlKYjD3D_El]B3DJ'*Zp_+\
    pio,[Awap-xGki7TT9smJ_WX3A[~}Yp#C28WY<{-C,]e\;s\V,B5v~B7v;oZo[C^2>,U]eUAU+Ew
    wEZsulseej[q}vD#UCmu?TjCu<@\aD{Yn5z*DrTs?'Hx:9${+1\'g~IKTZp$B<AuQ#X~I<j#_5D+
    VbbrpR#<}*+Qm+U@V{WR,w?,,T1=za,{o-3tScup!#[7T#mzV_7}\UA('w+xb<_}-<[UVnB>\UH'
    vMB<\s}NCz;7YvlE]X*2URI7*ETBLvnj!V?7s$k2[v\Rko'^^/dD9p3JrwH5\CWm=JQ3]5e<'4Bi
    lX{V7}Z1]a$jD,=Qmv$e7V\^~;Gz}n'CA,Olx^wE<YpW]Aerm[{5JG_B's.^3[Ojuo]HIRI*_?oX
    DW>PeVJ~{>2<;*u;:"_U@m*5k<RO]WYr]GUC2rt[E}Xokj55;,aRR]G,T4|W_[]$]pQj;$Ax0rQa
    JSi8$D6H$Z~5EIBE,[!jKjuplO>uamGfVvJEu}x-DenE,3}D'EIDL5Ge'}OCzB[Top!3\xuI,;*@
    [[7[IV~T2zr-7K+aRl=<<~x}K\AV2}WAXQl7'#vY@]UGa$M,Y^R1mnlU}sR!O+w;=l1'Z$ErC+Bx
    pWsUwW#Xj@}=UKje{wxpC;\'$VrLax;5?Dr'*RD#ClI,g#_s[wvHe=B{XS[3'mkRJpZzKCHT3jo:
    @+9#*,Bu+\[olBmokeCO*J{{AKpuwpn@z_#\=i\_n}u$TA\=Jzm=7~lp[+;2>pu4=b^W+=#XU7Gg
    upxIX}oOZrHn'*O*8\~BB,Oj>DJzz'iw,an_^cvQ*~#oxU}<lv|g/Xr[YupO!Iep=kr[YZr>{!9W
    };;^epu{>wR:\ZWk{woxDUJY$UnTD+_*|OrzwRe'wx{{2H}J#p~Ii&2B@+1^D!uo-psoR~s?x{sB
    E]s<X{2wx@W5]r2,]z*ok_'3_{u^IC;oR3Jw]DJ]R5x5Q+Qnu>QV<*J*,Z\~aj[#XHVZm5zD>3eI
    VA-seH@s]p;oe*2sw-izZxwo\[UrBl#CCX\JUZ%*H;~5Y5r6o2p_W'GG~TAnFRHIro!J_>1^[HYT
    >,C-[#{![~QE~xZ{!jBrk={UewYY$uzuVK\3KVI5lWD\Vua5z~G;=KH}]m],w\llTmA;~Yk~V77k
    J~_Y{PEXD^'Z3AIp;ANye'GAx'I^=Ek2=D5uTqUrORVJ=K%Ij5[,jRII/r<CRpw1uI!'EDvCR@X~
    TY^B{I}3al2VU}*apgxxD@\$aB72rnYYOW;wI2Wpnm&HjJefDqEJ*iE3V#-5YJIO+UOD_^oo2@=I
    vDOH+m_zp^ZUZ#YXW!IEv#r$Bl-ee!wQXV@ss7!el]Xr{\:YC1E\$'B[=O{#YJ'V2O'?a\Q3s,nX
    RQIXGR]Z^KQ'G\]zU<27lUu'>*x5rG_^sDE_7,v%eEXkR;+ITElDv,VkR_,$[A_W'ce,{px<T*'o
    1I^R\KezU?exXwx5={pjXE]sz1wzRzz{DvlWKuApOiZHwnuVQ=ilAT@a3m*Kx~.}W-A2Ep$ao@5i
    >OA$>'W|ED'rI3@R?z$!O@}u\.~HA}2pe-^,D?lTA1u{\HAse'zun\zaso?'_eu\IY0EXAr^vO5=
    WzQ5DYa']i@lZju#{J$VEza*HRWD>YIUsviq{{aZ15U7evaK1+T@&'E?lv3ZzkspH<II!^Bk1zAB
    oV*mTH\_i={xWYI5V2n\1VkC>#[mHqc+XDH,z,Don@m-oT<AaaQFP*}uww]-o'O!vXpeHW]\IK]Z
    !>Qw>7rs_U>$pQrEo&BeAXIom;a<HBiX<w''^_@>3B[pvT5#jpBiI~<<*G/lsu+DR7mO<{5rJ;,l
    CG2lJ\IB8Q^*A';QUZ'A$L^]<wti[*=-n[_YDV^Sl2YUz+rWYQQ_BBU]\CIHO>o?<r}@H^Ap2o~X
    #Upl=G?aQ1W~_VWomE~@w7^?YWV!y8qZ}uDBR<AQICu3n<DH,<B1W1Zm=_\J+HKBu_G3HvAV<R$;
    -wQ1^mk8oW2pG=sm=w<H,an1R$7;^A5xln\GyB]#p>r<5YAnw>n3BkO7iuEHwtzKlv6<{}UV*k?0
    A>ZUmU~jC{[i$x={KC;O+B@BMIRQB<I}Oe!13^TYz<{}7@-{{eT-;9J*G~rG\BlXAYvZ7<zA[J$U
    u#r7D]]r!HeJ]\1B]R3sAOs#3r5L;67BuK9|]uYzx?2'',k\w{3*VaV{ojT1Z7O~@{WW;ROumQ32
    dyD@BB5kjm\BBsg_7Xzo&OuI[b!Ri=duzj{w&|N2>1_p}ZsiavW*oYro>zZBHm{5iQQ2aoo:Q;pz
    BUEu^_{UG>@]pv3O73C\nO^{<'zBdo[DJRaUaJaKajJ<^E71sB'rr>HoaYlma!V]ojT,oRCB+eAv
    p,5$~ws#npV?e\9J=35>GAV=#pWC.lOapQsTjzB15HCTv_Ta1G-mHTproWR?5oOZmzUE!=1H7H=_
    n'XHrZ=vu}va{}#eUsRk+Z]K;6,$DW~,[+rCnmUw'1VEe!MYKx5_<z1eYwkb?x^plC!7!5GO5GV!
    %{'[}]5Ju1Uu-eVs@uvnjjxk^]D*]Y@CGAs-HPasR^r}~\ZEJp*2w+U$@YYgzU\3UrHDV{CalkH3
    oDDwGi'oA$Z?m]T=o_ZwCYl;GV_;AUm~[nAuiDm~DvA[r#EOCaa1Z{3}@CW!<C{X__RunlmCp@Xv
    :DYk,+,@mv[*\}$uzesz+s2Es-RrRs*AW2oee9Ibmn@2Co=Wp_+!ApCx#XDp]kr3r=_*mske\VjZ
    [im3%y\<XJB-e$,mn3}^!lrm77|>O;Rl}WzdAjW$\VuxYR\nD?p-C}U^ZnwGKxH3k{viYU;E"GU<
    Wo_^=L-]mW!H3uC2HXH]!o]~>mL[\z3VRTXv~zBm>2AjV#KwCCsIQK~3YI_2EK[EXa!=WZJlJG{]
    7@+Cp_'[xunOZ~vC2[^B@*=S,}R^o''s2'JuVDa2a<]#?}k7xo=*$k$kqJOz^HHOCQivvD][AU_o
    ?'B@D/:V!A\Xox-urpm[5#3\*$a-Ya#CB!AOoJ_5]}l^{]7rsn=,wJr^[@JipY{ri]27C<o!Xo=5
    ?7!=#V,cWBeJiU$$#T@R1{!Jhv^DTR_Q,0'OE}Y1\o]C~KHTACE+{CYZI[9c7[AQhGr*+=3JVhI!
    Z$Ari^ew!JYOzssKu^["]5T>kxp~O}BAI2_YxRA~72$=?SOW\Tva}~Rm<GVjI1I1,<73l]uBWE6v
    B[W\RO$><|b>$\OrAZW*m5zpmDua'[r\,jrX]pDLf'UwTb=R}{H_lC1Ae\=5Dnj\UzsplO3UDI7j
    s2h[I!$xs&A]@?WC,jD]e!55GW_Il;V,WDO7G]H<;YVH{1Q~o$*2[jEm],pOAVZ}!vu5m@_jQ=x5
    [#tK\A[QB7Cu1{2B^a2Q{*,={-#u<KrCW>+bDojlp[5_^6[X@x=CZnI<1Hz{uj+\!#>jz#95K$p,
    $w@j$-s~j\]~*-,7W1OBB3>\-]_=33znwD7pV35oACDmT<-FpJYAoQZGlATY=UZG&?{H]JaoBbZo
    Vz$*+ns3fmwaG5,G=,oXQ-Rx'rr^CV6vBXR:RC{~Oa1!uU!}g*nwH3si5UY7]lpVTi,5s^oI!-$l
    leJU{^VAVYGlQX-!V^+!CA*j5jY'm2,A!5Hu+$3As^3{Ipla>G=7?5Wx@Fz*Vo!e'$WRJ\}=CCYe
    n7!Xp$l@<UV72?=m3rl{7uG#ozU\_C1V+Rr\Y^_~Q@m'rvgnv5u8A7eE4vv~H1I#upiO=j<x2eTQ
    u=#2U^\\;9_?m+}r$kO>_3O#rTRxa}.[lr_i-V1j-e?Uw-;JTAepEz}M^iBH]p2'8,WeWz,parYw
    wqB,x}eE&[27OOH!zUaQ=H>mT8Q,xnVkV~]@@a>1E#*Y~CH_TZiEA<#wlzw{+mC$-1clBm-+}lm,
    G<H15^'@R~x1ZaCIR+*r=ApvHxv]T4_7CJKO<W^U+T%"G7U>4@X=nO_}up]C1ZVWA]1<u1t2Ovp7
    A{U+s-'mzI$ckDSI@!H==Q]sAKr#n\5~R;3#_-ZV2Q[y$z<xR5$wO!OK}7'*~^2on7\n5gUr*uV!
    -';CW5@,z#E'AV!N\z?InYoEQ+WJ_nEaYW+A{$;mev371OYDm[rQe{5!0-EQO?}UO\,]oR>$<'L#
    -sH~RpT,\2A3|]-v]MzR~$>ejT=pBp=wzDDzjlC5Y!3Qj;oT[s4E[@@*Q!,u.-GiUQ!YEZ>vmEOa
    ~H[oWu}Ce1Y<Di'3niA<ej*?ITp,i2w2*.)<Uo7Do5v"1$w#$m{@;[j=^eRITvW^w{IC(?oH},57
    a$_xTom[Qbr5Tj[xK1fk}35|}?IR5'<xsmmOva*VsGB$+>-lQHDr}+{T7=Qu*zO=-{{nOimIx.q>
    =7K}Oj<$QiEjV}~3]$i:<.]A_em*BaC3XozmIi3<D\:r_~[Y*i^r2IlDsW{V#rpwXKCz\WvxOx\V
    \='2R5>E^-+7\'=:5JYuzW=jwU1DrmK_B*n!~wzm}_KJpQmC'powB\~J=T7IKv<BY>[1iBV>~{1z
    A{@?nv;Rz.2w{j7lx],U;E$XpIT}+{2*I<z+ViDJ{==?B'W,xoDCOYkHQ^]WQlzxp!=H3~]]K$pT
    T#}xQ7Z\e-E>*U[^\Bl<@p<UV,~RE}[x}U+-ouQ#IxEa,$?}X^=k[@,moaYvYeo7mjE>jYj~,w~Y
    xkNs}w?d[BnmlX3[=VWBx}Jr]l<uV=J,C~rQ7ixWR>'URpYwsC~?<$[!E~l#='}Q};2]#5<I!R=e
    1D~}zXm~v*xBi{-*<<e;Tl^{FaTzo21V;BI1wozE;snZAyl?{*-jwWK[UQJOr<=Qr{x=jQ-'IkAj
    {miCku1THRPRiIvA,Z}W\J_|ws_RS<'WzCzv?'l{7lJw}Y22[KpQApwQi<v>3@*[+uz*7\L;IJC.
    n<RC;UJD*>wGI3\32_Qn\$o,ne?oQBQDRsn+,Tm^Co1Z_7@WX[w-,<$YZ]AZt?V,O;1[[@'n+KRD
    B}4BWzK*Y<}K}jIY8@sj}*r7!UR+Yi<-+I\Vk$2j~|H<U=s_k?[vT2l^7J'7T+fQi@OjYp~pYD~K
    t!*J@5ZmJBlE,>sD]>HarmAvY<p#kPIlJk?H~7<]D!u6jAuT|dD}z']U}UBln7u1rKME!<Yq#<2n
    ^@pu^!X#]Jl7_e~+Ujn*e;Aw[b<pUK+OI_
`endprotected
//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin
`protected

    MTI!#''Cm7EunZ\;R_k[~^ZJ1<na-w^jCT=Bw}3-w*jirj#UT?ws*2IZ2<A~2_Nx2>Cc?<}^C!UV
    &mX_@<|OZz%lwJ#*+[[o_n=['CW!Ul$HUl=Hl~ArO#_}lU#p2IaKr#$-Ow^Jse{Fa5lxoH^W]^{C
    UjD*v$+*\$Y[cw'7ITe#}7^5ul@O}BZBDk$-nx$2GlvBJ3EQR#OW]6[,G;-z@m}l{Kj-nk><T]rn
    xvG~]-}Y+\s@<>0XURwRIaJ(g+\zk_X<O/+B~]VvTerJ7l*a3Tz$O]=5\-_v77z^uWnpJ[3<~BLa
    n{R=-X5jZw#nasuD}DXrO,@=RA#;GjEDzK7qusAsZEiW#e5R*{euHT]Q~]AQrr\<OyOorJe+ArIK
    ;lZoXk:fC#}ZET]eBgR-5*Qi3?,Ur,{$X_"BYjZ5KeZ*-rmm_W+]l-sJlD]#U@spD#swz*iPKEXa
    One~CnvEf#>T\(o#Km{-EHgY>n{U*n,<5B_H*Tu.rl5ul#2>\kJUf<x2=2RA}KH+[>_2GWRT@k^B
    $H,2x2>AxC\[aXYm=0Ln}7;73QevDmZs7Vo1wQD|R=@]7df#5p]7\YGvWYj,\+D^^Q$:doT-\^D#
    Cl.Znakn=<BT]#>/T\$J$[XHnxr$1s'a^7u$^>R#xJYkTBkrv,kXHTU<-]P&U$+3A$n{&AxtZT]p
    5w!uj$CnC3Ou&kwa-^Ra]Y1R#HQ1C+l-DH_@asazCnOv^O23O>*\'SlrsDE3A?Y{VpQe,rj2IldY
    A@re_YD<x32G^louv3Oz7n2*Gl$lRkz'\~wIKVzpaK_:pn=\+_~}o\W>Q17@-11oVF^uC1nlI3el
    BYZ$aWE=]Znaeearsz/sjREzp=[QJ@uz@V*DouOBC~!$Y\=y7PiC,X'BT~*a-eCG[pQ^TI=~jAE=
    TWwAepp!BxqtN"Q$KlCmOwj'{AG>Qz?rQ,Qr##QlYDvHRj'XACO!!{_C;u;r]=YVerGD1Y+>>JVx
    Jj]~}'QjvRA_YuD?,v~<$@7<<G-,=kvYazUnr]k=]i]^[YKa$>rso[[HY=9L:eDk,HCZjUQze05v
    *5epkK-O1\G\*'<Xm,rmW~UsnnrZH$31Y!5a^@?XW+]_o<wX~~oe5~e*=mW=-v$i-Z@r!=Y+ln[u
    E}>QGEkC]i-YukoQwzIWKlcLIo>u6A7os#rW,x\~e+GGEEkjz3jex]D^]79qr*$mx3nl2+7@_$"<
    E]Z2}T>WwwUz>1p!r}!!$O\c\U^##=-o}3_Xr\;Ko=wjImW2've@'Y{mwI7TO@\u'zzoKsGZHsp3
    ];^2VI#[oDIQ>1A1ETV{{^VTn\3rOUKJOVT?:%qQXn3^Gz*ves$U\>E#OVje'R\jUm721,-m>l5z
    bo{$?AaG'f^xBOJs<{eTQe\Bin{QA}IQAk8_K~W#zBp=x{[i>55vM!]VjnwQ7suVT[mHu_[!-Ck}
    G%!^wIAl+[M*Tuj]Tun@]H[k{nKxpRVUzJjrjEU7{BE]@~oa7T>p@s3JDZr)#-A?VDITppaVx+3E
    Ge?5}G-#sIvuj?r>9G-o2a-^lGHKv{]CV.2Rs!}n17><xn{oQ_C~rQY@us12*a~wT'}Im@x#Cj73
    2V||~DoD~-C]BY}H,@1<p_]1?YCD2=-'vf]cvB#~DkGp}-]}&V)*e,x1w$eDzCmkr<5ZxD#u5#Kr
    Z2'|^Z^D2YBpQuC{GHB>H<wI,2a'7?C-T1<T^e5>;<GmXxRib}oRi]#KT2r_=eD{avYlV1{$ZVEu
    QiY;3]vN|+w-o$p#O*~TpE$Cp,;z[=AGoI?Us~*VZcTTZDYV$mIC'xIu$JR,>XQjeElw[sen-T}@
    KGTAl$o\R\G3+XEY<@nOo][G?j!H7=[7,;*k;-5<zETVk#,O-IA+7mpvYZ6,ko^vWo~t2\C\F7m;
    {K1kR1;noY?+jICIQTB}^Fvs1Vuv=sxI~V$m*JM+X5rj{=1mEwK3T;;+v#TG>$DTz@KCZ1~\>>@$
    juV~$KD>^KQ,Tj<_k-\m*-}w_<$vW+$GWV^B2C[77AU[+E~sK!Vksp1i$vl:Gv[<?\isRoQ]l+x]
    3z$^O5[$!['kUpx<}'BnF_HVn2n'k[a-U)C$RDri@U2}v=ww,Ho_sw#7A@$iOzm'BlaoIa2LD^jY
    L7r!1,'Ta2pw!@wJ'[^X!sA2nKAT]!z>RO3A<3s{Vlp|bB^JJ^TesCwBAz7]ro\e2'd_5H5b#^K3
    jJa_XDOWj)5]@<Xj\!]n}X)|nRW[KCYCC2=AXl@}po<'$D3EKH~D[;o@Coar,D7Qs1p_4y~Y[w0X
    li5Ge33],lu[2B=K,o+$wIvk7$^OCksoJ7?-NQ#OG}n<V{D@})B3_k?-}u5nwzE2]]>}n\>]?PaG
    ![PI=UBNbs]+Bs>Q5KrR\H1o;$=X$IZeQ4!Gz<i}va+XWXB*Z<IJ3mhB=Vi.xs[~R[?@lunZ::qv
    ^luYG=rKXC^U{rRfujk<Q\s*Qw*-JYvCo_xn#e,\nX[!CD@o};~]B-Q={7-B{Xm]pN^p[CG45@_~
    GuxiW*o$]WAU}'E~Qp]ODHA+i^C=CReT3C*,E3nXO!^ppp^$n+)/$@3!EKK+M1#u=@U<Hkj>-7pf
    x#'?aQmujuH-K<n\>G?UV}>X$Ir@zYRQ!D=3\Yk;*p5~]e_x1J@>GE!QRpk~'\GWe*X[laBG9D}r
    @m$GY>>'vAvI2vk;J_IOukBJ2\isKMjp?#~X*=.,X-\rVQooaJXLLJIa[SlaUrzD$$&,~5[z{VZ:
    wHEUD~a-_A1rWr,1[3TQn1~BJGe7+O*Qfw+Z{GJZ},anV1j=;hCxRDkoo]1R\v07?~^jU}e5!YxD
    i}On-wm[KUZW&XzsZVvn?iG3E~srko+!=DK7<DX~[~a_+Az-=zE^lPLOa]'OOl@ev1}aU;Jr?Hop
    ?Ri5t3E!$]RkQXG[p*3-*=uQZ$rm]@l#C$C{XVl~~=HA_k_HsoBu>DQi*=7usm-!}$*aZW71a;RG
    UBB2m,\z~;wBTR'QrGxa]lDEHv>J5LIY],Gr_I$wZu_,k5}\-O$ol3'XI7^CBol{*~;V!'BQ2Y)m
    HcW,u]QVm]p!]umRlT>U~a{>z3DoK*xm2]XGsw$A[ptsDG'jrv7d}_s!:[CnVRDrT>E-7,',Z?6m
    Vvek{+O15*27*_VrY,E;Is$_]xoSae1=/m+E]K\Er-C5nNV@[s2n={Q5n^1te$=Rj'r-io-,pK<K
    $J5Xo?7r6DDDlfxjk@mC{xO*>=eiHXWTpoKa]?r!I=6CKWlWj7u-5i@$Rr+jxI_LjQOY^>-1GbQa
    $*B_oZQ$pi/R>jH$Gx-3^ZHBDrrCD}}j}+phkH~-A'ZWm+9zk+BhlOZoo-A@<{*aK^#vsua;aRpu
    kR[@wllaiU2{KoiD~*<<glX_nvC=s[3Kaun^rEDpoBaTYv2=<DHQ@,n_l:$'!RZen$,!EA5-X$L^
    C8PK\Xm|?UJTDoR5%XE2lkp-!rOE~=p@n0DpolB[21_B1E;IA>TD@E,Cr$'Pls_Kpi^#TES=TED'
    *iEnDZQCB-~OJE!AHeTW{~<1[a_Ise=eX2^}*Cw,{QI_3e@3-Ae@Coe!CzzQWVG?V,U=w_x2Xlru
    CkVOvCuzHC<$m[YhB1p1K=J5@$HeG2WZ,$ru6B"v]AUUrWYuG5peDs7I<A]GvnYZH]i*;<;zkX7f
    Jlx!{CTZY,-eqq[@OH/|=p}~r?Ya$AHrj[UWD'1H+^IRjK~<(}UYZR$_eX="BDJw3n}p17,3lu1r
    T,[nEuve@'p7=A2}u*>l72$7]e-]<pr{95_QKWH[^iTT#e=*=iXx;^2~z.'3w??zx1<7ZaxZA]jw
    \T7-V?K=nx+a[n?s;rjR[RKr<*i>W]Yk*kQWm_J$GaB2wr]zF1u*VHloEs]veT^$\4}X;XH{u=;Q
    <V7@p-GK7vjv1wU+~ZaBOm0{zJUWQ2DspJ?kEuEsJu]h4YQ'$eHmrCT15Dar?u1AUAnD[G2EIYO{
    5n+-ou,Y<GrlzQO'\3=j^mC'wKq;loIk7e]T_GJIJ\mIep#ROZn)oI=aVm7r*k,DTpmv=QQ@*HE2
    3DG$x!<e9==u<z^~u[*^K{h.IB$>o{A70',22<E^1QuW=jj{u$3j<v3eO)[Bm$^3w]?z*ekRj=Qw
    -H3DBVxTzs'1+=GUR1Y^Qa'i+@YJQo#>xXG#\a9z{u,@]*QM{*$X$WGjgter<n*$pajm*A-luv,k
    wZQrm]oXGee^sG,I<23V~~+V'_Z}]RUO{$llJn,UEs^YXl8{eKe~Vm^G~z$9$CZocmz<Qv3-+[<~
    _V{\!!eRC*XrnK'W=s[]^3oUOsW|6"C9C5{YRU3?EYRRVHe~53-{WD];2'DwrOwE^@D^jX=G'A-J
    B'jX5Q<=9uOG;byREQ;8Ron5U\Xl?awe<jI37K*B{U{EsK]_xx=^?DvlTInQsnA#G@j#qs;JjOxY
    RTjUp.mV=Vk+1HY1^,CXjANrO{7SWxQpQU*]v=CR,iDE,aKzN*mpC+[O,DV7}oWx_ZvzU#1Tlk}I
    =>wojG73*-YsV]GpGA+}7CJ$[s3lnlXC1)s1]Oi+Y>W+KW~$xm91EZ-Vpv?7_jniQjZK>pOEi<B7
    E,UQzVr%^^=R~,$5r[GrCBT\R$R@\*rkjE<R>.^w{u9BxoV@o_pP1>ED*sI^a{<WHa*xT1TAT=zZ
    zrjOrk]Wsz1D'Tzze^i@#D'3>$Vp^C,ui,7H2BrGZ]onH1;~z5V+IGJ>,G{ps[B@JxD=_K]'J=ne
    IHQ-p^Cw\HDw$f|mC5BrJ7!UE!*RH[Rs1HYiY;\='<e,e-Ek$+I,jH^TBv=eokm:{1u^I3uje337
    =Cin&-Y]^^]Q7S2>@Ka-5rnv'UD3>27e}uieeH'[{W}nu\],OzRu'#5fV_u}-{n{o<E]lE;VlJQx
    kzD6#TV]xlmW+oD*gesi[~a<1F}K+[)XY!mcv4R={Z~DYoo]*?Gu{@L+H7IYuED9~-2peeI,WUAX
    T-!-oDqNmI'1[{[$[U\=TUl=t&9]}onWx=1On_p{7{uEmHOl5Wu5$E\wprl_EQBJ[{Gb5?mQy!$p
    Y^l;usp^-aQZocl~EV]?!_*#nEax$B]*X+uH3]9Z_X;?=jHsTQ~Qkv>CW17b\jK@xa5DD@x112w,
    IGmue~I=s$><%#'lJeu$\u1{Z%+\5\0+sz?*OH*71;CWGzHHYx*iU>vp$xUlHZeVnvX7z2jinD2=
    rv'2]QVO'],v]2xEznp*za=B@12w<Rw5Cn1T_k]GKzi'@svCDD+Fo]a],VETO[C2.AEJl5OT;O=2
    ;[@{AoCx_RaZ@zW^}Vs!G3n>jDT!X*U+T*!B2TvQKe'x3R5r-mj'CP|!>,'HT]k.=^aZ$xeu%[p[
    $%QnG1]{>atXG2+EGrIXUGl(;OB~;5~_F';;u?IXYxoTmK_2!;R5$3pK@Y5Kkk+DUhx1A-+YmV$r
    I~kwKR<]G{jZx$T-5U~,~2s!RRHl2'*[Ul>}lW6{E,3-U+<zYDoAwr2[*1~xI]O2Dsa0~_'HTC!e
    Qk2C;UZIqnaR\PpD>7HUT'DEO;BXA!CoHO.k1m>C,UxNmDX;[}-EI]D7jOOB;[K\s'{!D{T$Cu,@
    =0fN2X\vwAW]Lwz<JBW3eZQHYOj2lTOQr1*k[jY'$EYnjO-+O{a[opI$oZ<eZETo+Kp=!K*\?><I
    [#>AkvHRW3n'mpns]_<R=<-s^'RZeIoU~uVnH7}RlFvv1lAoO\MDu$ky^xOQEYwE2B+Jpi[DvjwG
    TTzQa{,G=p$G^O^Y)"}k3@{^RBBIwkU,A-R;uz^9D,Q3B'xJ'R~aYA*z\RxV27sp^Jb=;7z<=I-I
    zo?-D?E8Haw-_po~[^uQ=Q\!,+z#xwVo;D;w}5HXpEp@',wJi<!QH]Q'\[T@!'GEa}Qj:9OH;XR^
    xn[*jJTpo@si]1u[#}O}>Q|m_5C_^==lIVao"[DzKloT!jVD?OlBYr_#};X$x^1E^<+<C;U{Jmx2
    ZkpxUQUY=]aK,?avoJpUCY(iVTxu[V]OQ#l7~-7jnAwj;A5$YZ$JnUpeOR}tGr1@$*J+ZG;]e!\v
    VmHU|7v/{EvARGm3TUan?,<~eCoE%\AIYU\Q^oY;CvKp2?z$ki]npZUZ+I\To*iO\<$jHos_[DIW
    UpZ128[7'[;]+#R=HUVj*e\>w;RX~KQH@w|E~<-$_VUeenHzs=A'=7?hX{AHa8kn71z?m=laVa!7
    O#%]Gi-[<KUrnm!,sr2+AKJwOsefE++\ronKCJI{ri]eo$s}~{{K\3s]XR5sVcw-o^_k=s+wl}{]
    l<25\UzD#AzoBZDEuH[#KYR#jB-{lBDxZp$@xV>r[=iasRZQZEO^Ko_GO>[r#>&o#wn[!an_]<pm
    1,kln1^<BeoZxa'julA7rCx#$i2\&B*=X1sxD*nTGX>oVAG$*[$v^*7QX'C=<|Q^e_"DnHZT+BVF
    (<aoD(5o{*p;uTr,pe~T^}?R2]y'3HxKE3^o]=;<E~2}{3W!=;=O517_Cn_G*TI-+lkY'I$rDR-\
    #jY'v+Za,*?'pkwz3o{R?C;o*_R~jV3a-@\c2_THQ><KCUO?HE,Au_[?iUz3+Q-[Oz3GuapWE$2U
    B5^oVnWjji-3<1={VeK@jpB?w,I'rW5GBx~}uU;<;OaUm_H-u=?W\D}X<BXRCY7Z%l{.9*;3=zI}
    R@Y?[_a'r4qeBV$OmIp<^*-qQ}\BQto5~3G#AU[WR1RDTUn_C<\1-=X}Z<oJ!OWOVs}JUnz']jl?
    -wY]1zL#-l!Eo$ZD{*mAU{rK{K}=UBI"[|YeCC2xX\BK,!K\{m?w;w)C7ZZJ<^D7@H*g$xX<BG51
    RYXWHH;kKT!H<7WJUUORXU;[D,?v?a]7m1}^Yev>'^[Ua_lAbbGj??$r@#3T^@$^I[oW_X3DCTB[
    \Q?O!I^W[U4,=il--RY-{ZWO~Yj$*pI0Cy#1Cp1<WOG^,,pvuEhw[~wO|!EmKAern15Eiu^o3@,J
    D{S=v[xUa337o}''>_eelTCJ15]0e75UC3sU'I<{ORVvs,@T[sU{=G+Q_lJUHB7CpAOKSOk5j3rO
    H02}ps{'3s@w=sTrA3[5E=%}2lXu]v~p\\H#s{pu,>^YWB,Wvjz}RX\j<^sFzV=?$m}+s1J3<o{z
    ea-v"W'=@3{[5,^K\]?RIe1DXzWv=/QR>m<\O$1#mo^ZEzGU}O(Aal*VHe~!vO-aVG_t.<rev'Qe
    jx}}'/-YwIN@>^w5aA*K,-<k]$,:~*1~{w+K[mnKK[5r=+Q~=W~{IOnz}VWVioa+7B+sHVZDQEIx
    D,Q{@w'mq37mmm\>["cVa7<FE#51<HHRkOi'xG3oE1h^@2o!Urwz'oTG1=?2C!-U-uVAYA}o'A2l
    7__1ZmeG2DTP{G!!p}#R$_XE}'iuvr'[>]-T~^k*z[Js;XC\l}*aEE-\nNmsK-zJYI~]'=Dw@o>[
    ~KVKp1hB$kR,YzZ]jDmDJm>r^!TsETRPS}-$'Z_5<K\[XxR2sZI~s>nD]J$+w_rml]I]Yrx}ko>Z
    {,Rjaw*$z\{TTvJ5T-X_x];2QWXDUDXa]m{7jo-A}G52v+l,Dd732uU>I^21wv-Ru?|4#D^lQ0DG
    ;HErpv@s<Zc{]Wn7'YUa7U@\zI}7,HVW*#^=vs~_j\;6<\!uQT5{E~~*Lp<*iCT^pmr;BOv_G\*Q
    ]3Ba@meA1I/K-RH'u[#D^*Cem+T]kRwz,1s1]JZpTz?D'!QC]7n3,RCeEu_7G_e$sxCB23}Z+'pf
    _VAr{AC-zkXRt@El3jB3WQx{Kr-H+IWQIy]~1DlUAuEDs*nAJe7n'{T$]zYBH^<arsr~E?o;Vx1u
    JU7zV$ek[5dn'T=eT-*D-uXL'mXxr.(jjOJBj;#pjTG++IiL*x#w\]I<q,ulB-AWGx.-<2u?>a7,
    l3\hzS3RC\O$ux+GoI$j?Xv#C~[]?CI*^Ex;+D[WrI(VGF(o#o$}><p{$~$]>ZC]5IRjKTm<osz[
    !o5AT}s?w=xavT#m*<B1;]+O$V*L;$oruD$^?>}GU-X-\#A@m{~B'A^lI&>j$UOKJz>U=YvVnx}~
    !XAo3VzQXVl$1>aYnu1Q!n_X+4#'Av:QDQVx12_ne-{/C~D@bYV'kR5<KFExH;CdneG7Y<AjKCA$
    TeT>_u@@h_-{?v'i;s4OA>\"1>1lkr7w!DlD*V\@l$'uTR[Ex1WW3$X_/v<X#@l#5Zw[mB[aZx7B
    }seOyiIwuV"R3lEseZm{{sAWDI]IJAQTn'UK\*A%}2]wJ7H]o][jm5W~2\G>e;!WK{I>Q*3mA{}n
    -XrOalVk',lZYkUa1Rkwk{!v.px!e%},GYBG[pH*DB$_RJs\kssGTkl';~{+^1,;~DVX!#j^jpye
    >V,YOU#lZov1sYm5}@KlQ3YY_a}JxBJ7=AXdW*Hx^2-]x1JTE,nU<\^+5?,E$wAw^s55Wxv;I1Xa
    ?>!Dsl$3d]!e<KO~r,*[HqJ$=<MT^DKWxCOze@aou~piaAz@l3Hu=G<QjVGQ{rr1J''ou2_5mHsq
    AGm>NI}ET^#-[Ii-D-oY>]s7H|Wsa3J]Z}92wr-Guok,#T<_AYZY=s+HGECTIG}jj+xc#>ARGCBH
    7OYJQz1AGdlB{{3+O'M;,Gp-eBm=Aur-w5JEdxZr7g^#W]oC$~ETux5aJ1up}7Ax@W+.ans;2$Qr
    ArrpHEmm[7Z@v#&zI*~ZXIKBI]?WeR\I}e{:'e7Xh3Yz*,$\inl7EIaKEl*@Uyv=kXQx7^RH@mm$
    wU#C1XvGm=$^EsTH*Ds#s]xR*~x],vK}<VIZsV'TUW,!GJ-Oi-e~,z6az$,:=EH$5^z',ZHC;Re\
    svK]7a[*}@\*+nnV-+v_J*^*IuW*pw++BTVZpC']=I7<B{]u*e=5s]#C>r_Zsk!EJEV_yJ5{w]Rn
    'Zr~YRsHYTr+^2-B@[HwGZ'_J}#OoKEA't@Dv#{R$w!xHl'x-@IR<n{YDKD~rB5+~'2E}KWxx~=3
    wD=BK1;a*H9[AJTH<TV%g7WU*$^k}'2RWD*;p2zQR{nz]P'5!I12v$,]DZ,xx]CHVr;pZ2)wD\sl
    [ZRknzWYA>\HwKziz2oHe;Qa[+!I}YY@X*]5ApYZ_V+oR<7EIQ{lQV1(qB!XJ'oY>e@7]:5,Z#Ck
    Ee+w^nE]dDlm<VZ|;$Xu};+XiGX\o;!3^Co>m=7EO-KWKpY+&ww27^5A}~w<x.=;xzb~_5a.\M~X
    W+iA~}EJ7<!'C$c1lB@E^r?}k+$%!e_*7mHnY\BvEkQXo0lnDYC;GXE^=IaB;BAvr@nAH!{7IEY:
    @<o,?r?%)+{mZ;]#@kzU{IG{U#+0%@]^a&xoW=yxaEsaDB]C]}<'Y,G[A~{5Zv3k\B!L)o^e]C7r
    5WY1G$aaQ3[QK/=(wBZj4Cj?zV6<>BosroD1X1,E~!]L+olH5ZWm@Nz22#Ws{;TBV<sCOx8ze]^K
    -YG_]#AGV-pN-GuA#Ii=>>X^xJJY-X}<JzrGRvBZXaz_sIA@CzWsqnQ-RkSiru3R?IiIZvG5?CX{
    Xnl[#^e6DAOV$7Hlooo7e';2\;3*Q'G^BZ5KLin~pTr5u_,~mrvv*zk_V'nAj;^1{jn+ruGCoE(!
    o+]^nO<JaU_5sEVZ7;UCuY!re;1]-RDwv-,ro}CD'{;AO$waB1k;O1YEKU*e1p#wH\p;nK[z+=Z>
    [C2JhNR5U5]p2w#U~sQiZ?gQ!5o7IwV-r#e1\Tx8^Bj>'<x5aH',#I1YBY^x]ar]J_{r1*Z,v?{m
    ~>3$Ho}~;*=uI@'?A=##[s;+c/@\Qi2se!oz+~w^A*JVz?oWT?^Bm,gz+-~6'UvKnsOo-Ou~r7*?
    fL^BQ#~\Y[FX]QG,+j@wEkO7'ip?zn_5YB?\;B@>]i-Z1[w^H+K=znKQTj2ls,xQQzO5^+XAUDa|
    V<E?@=?lTp$Dn,IE!Rr\wOU!(BOvr$I@]{EKC&Y4YWHC@GT=O!=Y7@nsi_oGsOE2,:B5u_cC}uJ5
    *iY-1^+,X-3m[aT^aruzW+^nnz!GoJ@TaVlTOcM7a\'[[D=u_1xO21#aTTko?z$#$^Dj^[v)[}~D
    2HZxnQrs\=Qo6qaV!2B^[kT],Z8DXI;WT]$J{=~XwV5G@>G{Xej?\OGewnw3[IwY\Q?VjA?o=5K[
    KY#AAJv<l*';HIe+1A7xOl-_5GUq-nAw=*<R,P,'E^8Q[k{c]YK}U\@lxw>VkIo=R?Z?[u{V<Hx'
    }TRvxWxuIQ-*_>e#u[WoY~_jJE1U*e2rIo]oGn\_nwp~3x~}+<'5juaKW5>x{AW3z<{uvEOK^zI*
    U}+7{Cp*MDO1~]=p<oJ;nN1lQ^F7~zw7Z-s,*i-xRZBV[xzY[lk}UD'*7o~Tn2IN=7]XO-5JvEZ?
    E_eAHU;zO;T*EBXvGr_K#'I?='Ja14NG[;mti1@,Y+^~@*[3VZ,HiO}^)hdOsK+/A-!!#,;_*?Q{
    Y--_jK2UJop}hG>YUj2IGn{BoUje2BmQVx];pDn<nUp^}p2DU11EQUn[$&o]s^4*Z$oXxBm_Z!')
    I$]_]ZVnIXJU=Tr5!*Ul,Ea^l;2'YmHlUeG<(ar7iUp\>}iQCE>QRAlD3s5!T3oO\kp3{v]n-m\W
    ]PluH\sJ]vsmG{Tx{R&lDrZkCwZj#O3iUBE=Q*v=_Z]^+[]lrVOij#G$I52}uYV:7zGJPW^<E3}^
    E}iT2B5'[hl?Y~N@Ru-{>$2Cr\C7u}<I=I{p!uR?vop}I{^:{+pCY^URI+CV]*ZG5Hx~pUlE"@OC
    p/Q=<B]A*?d73^?KTTGl=^5s'7][llRz\7;&vKjszdr#HEiVOrUN-\ue'sJ^GmKJoE#r[Y5iVs5s
    '+-R+'5'X^1<jDxmp~V-,YHIOa}UI#'i=z#EwH<BH[B<}X2,w+^!,J@'s-zD5XpAruRxpu{p\Yw1
    Hz5W3nEl>HeB_AK@Q\_>ou2lpmBiv$?A?\{s$A+5*UV]WE@_r{DJ!AKnul1pjnnQG[6rjV=j<jir
    C!j-aK'5}}a#s[DZj{l~Hn$OEkDj'jm9W}Ko\#>7piBDLZH=#'#*2^xZGS1Oj\C'!kE]X'2E%i[_
    Qo#oi$3\!a[mURQ'G4Y_C+b^jBGOrs27>;EBTD[rvRJia[sR]T+s-nJ_=H$UlH;nl}-R'V\N^jas
    6Kxm{V!{-!X+[yEVjm&'\n5C[13mDe5aR$^k{Eif~Bs3BX*sXaxIBExuHBBu1pKXhwRQ^;v>^"kT
    }pP\e+'QmHlH>,UHnVCz?xl';,wB3Kky[*XR9h\Q-n2.mzT>,so7],E~lUK}[mm]r\~2aBr!E=n<
    l${,J,B-'xv7Hl5a;1eXi>Xv#^*euVGoOn=ooeIBTjeXQ07,HG-1}[sYms@n5jDGpO3RV{'@Tn^i
    jk}#VXpQ?xI;Y'$rO}G[*rTUKQ^{_ZCp<1ToYrur+sTRXJCJZ]=~ev#G,^\Xn^U<uk[zD2B)sn--
    JIootO;3^>Xp>SKD2W*R-W$=eAxZ2T>=V+^1r_i^QGB1O]ri~mE)B,WlXaG@-+=,oR,I3'ER)uEC
    D}w>2BD2@W{3nxDX@$DTBVGe}v~W\G;V{vmC@yE?IpIxJ2piHO\YGo*r[nO<]C2opEL!_>sm[Yv?
    C]j]p<[@TrwJ}k+lousAGu\#XEs$<;W2UO[JO@=\HVB9[joOJ^'nT[p5Q]~zuElkCGZ]ApX*nRE1
    ~_-e737;ITX*X$}GksC55Uzn,w<ILX=-*YIK~XvOvww}EoIYVWzQ>3p+7TOJ[UO2v1m=D/pUZG5j
    ##T}^x#vB]+]'a/G-Rivv+2[EjrX}p3#_o;C+jRrrx-\#ER@RY+KIRI\pp~jI[~}v]UV'>K!wAEJ
    _U;LD~FK><lpemV#x1pj'WUNSaAWKQr=H
`endprotected
//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin
`protected

    MTI!#a};l$G+Q}@VCV5n[HT>#Du!nY>{$3*>?[:aEYirJ1iS:E}2VuHl#UD-a}dv#xT#Xspxm_lh
    ma_H<sk7_rPlwJ#*+[[o_n=['CW!Ul$HUlz.?\*Q'JRrck^m*<1Di9oGw}5r=x};-Vmq5nnu=L<+
    Rs5vQoUV-T#Yo]!5+TCk*G?rzU,KT\{>}!$k;W-,E@_*u>_saZ*#ZGl7!}z<~Tl5l^?YXx"57?*,
    pn@)i{{lWx'\SX\#jx=3Il!KzsI7!e{\EW}1_>7,r61;Dm72'zJpEU&{$JB$AaBMo@uuVo[llH@T
    r#s}Z<w7UjU3v#X_h}3wuJ}JO;O!ZB'*HOAoWWU<JK_[^pUa@-<}H}-+vNYToZUxEoa}W>{\KAwo
    kH_;A\=suKv?IHv[Az}+x_Noe7Ds$O-E{Qu'CQiC[;T01ee<TaEvIZ=#+1J'"DN'OTv|0<AQo{om
    wp!6@[B2p_p?3jnlIA@->aTe{52JJ7n_@w'zBo${K['>5,XY3CkACrQ?Y-;]r@YE2}<lF6~}X<\$
    #X1+mps{JZEOiY'}os?RXpAj+3ozV*p!5ATrnIewT-*BQpxwl]+z71!1_l5mjAlX<spK{EJozl-U
    xX*vs{$w5,E2_3_W-G1\+U}kDZU-z@KlI{p;p53Ox1_5@JoO{r>V{aeY>+@H~51]jofw'KDH5+wQ
    x+[W+}GUrRw=k!Y5lm^MBLYwo3&/rRR$GHx-Q;YEr'<QV,vTU<Vs!nQ{?Cm-xQ^U.}wPe$*XtA>A
    Ckrp\kj^>,2~p=kRTa-e'#=E-,|h^H1-72^TQ,E#'[Zk{-V#GWH5X1i$QJu!tsX>TIVEw'sI'$YX
    #^B3,\K]XaaC,\]u]u*;DjCk'epYspc]!t${l,VEE-1~eoz]n>^B]72'UkuCuv_]C>$BZC$g}iT!
    cVp]OoBY[dkr]j*An#a-;{az~UG;]Xlsi-}2+?MYIi]>$QH}klD<'uQj\BjliO*-j--mEmH#R^J\
    ,X=w_k'1ATsl+,#'>@w|unD7cfDGY#jU\QEH\R}!]xIXj!*,;UQ~Tw'oXBR+~k5o5s_VC[VE{V_7
    iwaaARi$Br\OK7j^>pFP7?Jv5ZO_YX!wv<r1)3U=@k$+zP"p_DzUVHw+wO@[@_}6_mB]#CITjs-u
    ^>$v1{}Ek<B~=!<Rin<^0I,*Ws-z}UQljTv!O@]<?nQwl^a!V-$C#l@*G$;Sc]IzE*:Y$H;aXm+9
    ,Wm2M53u]}@OI:xw3{o!WU}kv7iUKjjTeVBCUEl~\?RCH{/=T5vV}ZZ4h[{^}Yh+v^7Q5-WIvK>]
    $\k7^\!_Te7"E}x+o>JasrD+ozVaeW\A$xrCmxi2sY2KCe9TQ?}k]vp^A1?O$$>Rs[A{j<Dlnuuc
    o<!,.JlUlBJa!qsxjlYJ,2?VRs-R_!s*eKxQeGGrKIJ}m]e<~?9,}Yvbe9xQi?)*<Y*!EAule#mK
    5*kvuTuye=KwR3]ozIO1lD[-1UW,#+YT]OJ@T\>@e!_Kv5>{&"P-+Q^1<YUsTln!<UWEW1>Jji~Q
    O<xnY[-={I@s1<2v}=wBuJ<)b'SGr~eZE*XB7;lD=!QR2@\<z3ki52-]KQ~,6_U$7*_D\C0;{lWS
    kh]}'_>Ya#K\H]gLwe#kOrQx.[-=>Ql3-:[~ewvenz3aX^Ux@7)<IJhC>WCFEC[T>G1!^{~!+p#Z
    mYW5FH>7DGuK>'EO7',AVQ-+Oj'A1]i=@xxJr[r75=X[_,nG~u5{VVVp{\3mUEks;E7\IeZ1pGGT
    ^Bwo2^{J5Q_^^$^v_XjD31I$5{OO*JB5^B#\a5"o!Z5=keItY#35'VY'pRs~KU_YO'e,xyTnzk'}
    v>=?aW3>K#:z-K@t}W;p<5Q}nSX5\2=<n#J$3ne=7B3s<#pKToy,?@!NsmJnspD,TO#R#\I{('hG
    ==s6w=s#iT\;ZV$*coGVJ3l>lqVieA!$T}>}V7kIJ+_K12~'TC^X+DLw'1B13j~{tp5DE@'rp;DX
    *A+-7:[^!5U}B'GDY{l!}w%UEVp#v~*beZeAh13V-2$-Yk1za!<WY2]lZ$I<\\D}Kp~KXl^]RCR_
    F$5Z~$G\njY?#wE$3>R{mA1w-;xurk_i2IO[GsWr]U<J\l^zn!ap[=+vv{5EecwQw,RowCR$oT7$
    zewT+ZBHKeD[[-B=mK+C#koaKvJU1Ex1\D&mXroGR!2XxIGzs?{b@<3],B{}DXGwa=~#9[m'wE$[
    5-aBw_KwTDrG-P'uE+]DnE*jID7u*#aw>IU'~}x1,{x+JJ7eiYXXI=Cjsiv'VT@<<@xDJr#,]@b8
    ^OWEx?]}=areDsoTRY~T_i^B2o+!!r1z5}=xeL^j#KiRnQI7/vQx#p@@o{C#uj,UvTG}WH[U;e[{
    Jrr++u5H{[E>#<&b<UIs-alZ-\:=J_+n<T~g#U'2aasx+R5Rm1AIl'a!9oIcth7TIExHV3W{!rvs
    iXJl<~IVQvD-[QFAH~BAa3a7Q'5g>_XTGoAwG^J?b9dJ9;p$]C~HO>1XUnoyvO_z[Ha5Rdj~jE0?
    x@er2QjuC#[-e?7Hwp\F7{G\B_!rBRY_iAeEF#rQo'Rus-8\o$nB+p+]H*<J>XjQ7Bk2QI,zR!jE
    'Dm}xiYYH[pQOo7OsKr]X>w\TjeUB{xa[D5v'kv@aKlei2EH+'joJziZ^D\<}<A-xBRT<R-RT;7{
    1d)}O'z'*DpiEQ^CHHrm]ZGY=-Z=$p?KUTok{HK[~arc,!O#o7Xe$+vY5X^vNIJ3D3R#*+romiL,
    J_B.w'?A;_-oar5RDy}vW}QQjor-=X-7X]\KQ<[UZur>nQ$J~C]TnnRq]GU{*[GUTsAjo=T{@lAG
    ?^>#JIrJMsVsZia,^Z+1'=i\orD\5SE#Z{{IZ'CXzaF72>5s=@'S\}^E?E^;+<-UOUsvW=#wOSGG
    BB1pY@Ir_Qv-vE3waBej7Q,oKsz6n7J]TCW;gXAu}(A=_EwAQx~V2G3HDsBHe>r\T\b;\G+=i$eF
    ,i-B3Xj^lK-Jy\*+p/Z_p>l4}I>WVA1?'![23j@~$?V]K*>+''Q5Z]OW,mTvT[Y[Llx_2#_pH17W
    R;Dln5,Wj,n7nWB]vH^^[Wp'K*E{ERJDw\Ye]e5z#CKs[cznlzC=Em\r_~(}p+$=KYz^uJH\_<G(
    =o>7aC,_CBE+a*OHz_{DOs?\gBu1~vk'_a,<@2al[@BuDn+p3[JDVxe$nZ'TQz#uBLQKC_{[<C<}
    }xK=WZsW>kRC7}V5]w[;pT5JlzjeizDZU;r*+Ww^DBnr*U~_Jme_Z^,UJ3$V7xSX*=moKUs5^pHp
    s5w7uv!}op5iwU*]pkVHA}J[aUHTjK]R12#lzm}'RUjXSRW{ulU{m<aWa9C+}uUp,iET^AYx2'Op
    'oCIK=yZzx@}UVD}XJxBxnGUaGZelunzE]{4YwEGo[iA5V[zoVT52H}UH7775>K-\EjiU'{W6+V{
    'VixvVkl<fmO+$@-m\!RAX^AB-5uC}Mj;n!Iox1=!Au\YHoXD@'J(2rz[ZBUar3o7HD-o$z>-&<}
    {]|2E1n['<A>X1H>_VKQX_-XEnZuO''CV?UV*{}Wz{m@s<K,5\+]A,X&6d=QYkPfx1k?}ko]{oi,
    }C~D"'sk<kX1KerwVU$:E_~~CkEH,$@!ABYHr~DV<}=av$iI_k-Q$~WTCXp#js!R7u2n0{Yxl]za
    I6djr!nE''jxR3\_5!@Oa}nH}<,sG,zKUO!elv=I1xuQ-R=(}YIRCkQYa[13OWHw&V^a}Q^'rY_I
    CB@TY/+EE#rjD{rz;^,#pGxv#a\3{sD>5~QEB'+],uvmW$_3jemEI^13~!8n[WK^DY<;XlTqgwr\
    'Q5TRCsop-n^B'wzRn{U+7iDB,Vj2{+D<Xj17'l?3@HO^ZCx]}RXpVCOn-YiBn-'_:YvWQJ-zw~}
    BDNVe\!1J,E5[GXz{Kw'rKGCnU@*iBAI;[_]m3}<7+}nE;[,\[r}3']oY,3uEOTeE-e{[*;PlQVu
    ,BH$V;R5Xe$a1+UHapp@P!_?lRlCU$5o^^[_xWa{en+jQ_-H+0HRnJXs?eeKTB$zCp#+-Iw1;sC\
    *U\GU]|aTHI_3pvpB7>p2Z;E,[UX<+Yc'1COD+[Hmj-C#8eHm{D'-?eOCuajIm-<*zn$+DVz7~QV
    $IViJ*$Z,u'zYWC#>~om*!U$IXDN\BB{x*;Ht#skuK\<sawwm*wRQ0.o>_1WC,TsJQiBeOXLaQ2p
    zRv[EGoXt5u{VtAXB]P*T!7}X>+-R35,i5@{{rzk[BwV?}u>Uu_eCKVJ*OEV{_@lE'-O;pKY{<}G
    Ba#p*1TisYuYY-3]rAZ_XY{YU{,@n$@rGDp}>!#--,}GmH#@xl][k;YizZ;[Uj,Xs$Ir2*-kDBO}
    5KmCjo^'b\E;@<aEo5nX[ere55XX>GJI]3juW1]?\Z}XISpBBDr23m~vI<@5xKTOapVQ_^G[=2l[
    <YvEKQ"Kjk?!DX5$?_V7JCxmV'B^o~@v=r*iV\CqGj[{@1p}#1p^@H*3B5$JjA[,EYD@Ru1!>Ewu
    z2+BV\DuyDJ7Z#zxaoi1=X75RR^+aKTZYQGJ+TvE,m^ZVWAIY|?oVuVVv?,51lao[["EeVA!R#Dj
    =#lvR_TpsXT-1]o9'^']o@Eu=3w3VTXC?>~+Ts~}I<w}V;V?.[Kz2Tosu*pkT-1+2W<CIZpU\V{v
    T_kmp2enD-<TvC^[#me,U2{5Dc_inBgwvw;lTzJw$T;.Q~w,kvXU@Q^[*uUsBuOCv?vH=Yl3r<9o
    }7?5#I!^2K]Ze7}B3sr\T-[{vRDjn-lJ5@I,KT7Q_@@QWAKapmW-1aDizYw@[n<N=XYAW+HZHD]B
    [$kEr1ovBKzjAE$s6Vi-Gc<1-aEYp-,{@{tmsE72sU=L@Ew]l~mxjpp@IRzorGB_z{Qn$UO{>->=
    Yz1_6QK]]1U\TD^U^rZp?C#li*5=xOweO#sV1C^}D1[5T*)Oc#X\$V_>1'<urcQAKJ]j;wK=x#AQ
    w@#eR]}<+G\I^\Q>-VR;\CRDCG__I@F#*RK-w3V1x+wm6E3p1xYvQ]5;kovaD!X}\7qlQ_Y2TljB
    5-oU]Hx5ImD;<Uzj#5!m_G=#w<G%'-Dam{r[7H*B_=~n4|k=DQsliAAYr3gPa+aG@1@2On{27Vz^
    H{voEl7$'QT>v'}1>Y_}sOka^*o@IO2B+aCmkYz!1apCO?x!}=@}Yl=E2h\YCs~}lr27#E<T2XN^
    T^j<rAxWU'l5K_wfr-=1}wjBN,lmwCj+$2x5-_;3!=V[nUUea,EuDI{Z~*?mv^}UDRpJERa'#'jw
    Z+DVWsU@@Q2GI#_iQ:"^<ml#5u=I$a$B{T@e#V!:GUwAW{AepDTki$5{irI7lYI,OrBwna[*5><~
    vnUWHs=mARipC!vi&Y5~DBOY5zIE'yf7'^mBH[>i$QkBI^v}n*-/GV#x?rZ[GvwoxRC+dEj$^=!Q
    u^lXC5!'
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#27\e5a]!KB[nlo[!meAD3A=AUUQ2WU>5"=3oa|.[G}jk5R,P]--~r,QT$i1ImzY7;he#7]1
    \@rqC|z3C[EfkQ'Be-e]iXa[zx^QI}VCX$ovVOuVNWr}XzRTAB'YUj+^[iU;pv&om]YLp7'wv~1W
    aT92O'<,eAT3wWA*;-5_=z~[lOB_YxZ>VuT,wu]U]Q?h[Q#Wr\lUg>RV,iT3;ok+XuAV[+C35EXr
    EA1i'&eW@?;'H*}!G!~aRxe]#so<v{]C]n7;aout?T,2X{>ZE!3V~nn7n<3H:rAZz}Xs<)1H57Dj
    <CV5Gu,WXjWrvww7l>q;_2<[p{[5;^5T1u!^7C^<XGw]~IIr\[3c!<>W{&=pT#I;@CA^$<_KQDr3
    E1'_xuE]i27vjiNhG"ujyiDj7R'VWR[mpfarI]F'Z'rJj[@N]i>u?eG]'-_\3_OGYCU2=Z[#J{!p
    i5NYAv=hem'ZYlv@IXX<J<^i**Tjr\C+!53]C2=ib3I\nH5HU
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#k1RjB+*QK]#['U7VR-ok*aUDoDlrA{[J}~{o3hj~]!.asa?#,5!7{~ss|IkA#pZjud6<1x,
    AwOU<sk7_rPlwJ#*+[[o_n=['CW!Ul$HUlRC[HEIMUE2>-rkT;5L0kDm7-+![i[/sQw17K;#'Oxj
    *o3eN6p;J,msAIO=zasD$p-ae-iar3WRuC[O?k$QKW=?]7Wpx?oW>e$Cp'<5V;u^WIARzEGTYWEq
    >T[[<nUC,O;GlPO>l,+T7;2CKTnRJYP9!&!aOT:D_,U}_x1lmzrVa~zVCl1u=vWx!p,Ixa[IKjmr
    J+@|#A};a&9n7+I=l~D7mY,[YR,4E=U=[OoZEr,o$*,sZQnD7]u~f<YZ\5UQxYWQo}!>Ue__rh9G
    EYTCYo@YK\{_5BWgY?,K{<ZEBe2@j~Oj?YB^%/ouU*lZYJ'rA57X>B=&,<XCH$^[E;O{o?p1{Ou'
    msD{;rYHu{D^uR-[1a(6,iD[ppWAB_'Zlb0rY\n^{GZ
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#]G>'Q}KVR]UOD,[ri$^DpHO2@>o'Gv?>Ng#XP"bH'iOm$;O$j{\,}k]_GroaluG<1?[x53{
    #]mu7-Hs~{BBY6+1$'Q,AmOoXA31,['>KjB#ox-D3]5#32jR;v\lIm|-]sG$k*KSA'iUC#VQQzu1
    q2=CXEQ^~~woU=f(i-w^Cva>QX=\Q'Bui-[r^?p$~s{nK=D_d1?1J1_@!=+[e+DIl,H~;<[C!aD~
    -9,#DJ\<@j~C]u$KV5AD\}Q_xl6G}}IX<QxPxG@o}A+W9j)EbzkKa[dsU,@3$,[!wnjrRiwkT5[[
    *<k51[+fN(7i-#ZVRn16ppi?-*xQu}+++Dae16'@Jrv-*J>1kG\~ETkA7],gRip'i5(Gjk!l3zaj
    l5z=u*@R~E2u=o#O^O'+xB_*E<!}=1ui'Ex7I[-o3KTn9=E^jw$U,FCxmUEs$W1!QX!YD_Ra*7<U
    z]]nCs,IY;VR?QX1C1c777paHYlZ_p=D$D2vTn[j#Vx7kv?9Eq,VYuRaxlxz<w^Y]WR>=?lOr}!5
    ,RZGW1YQ~}@'H<H$Jm*E@A=*J<?GQiv2Z1_\@RepZ]QE]AQ#Y\]pru\VNn-avzA{<Go\-<jeWG{X
    Z7[D21-!uQ,'D{zErG;@*Q\~elp2+;=Y3GpKs\?AoXa<pr5~>G?uWfxzpi!x-,2nwJ<p^+r@o+xB
    w-c^O5Xx3mJ+ps;RKZXe7\Y>>u?V3H~U*Gmhre1]yJURTvj~[DV*Z;<1]015]$=\Ql[?w=2w'EE#
    YA<VeV[1-JRDHB=Y$}[?~Y|F+E!CET2HaajvGB?x5u*#w{>~JCB],,2*o~;oAz>U!Y,Gx]Bi*U,a
    T>C=3RA#EY@+Y_uu|etxP8wzBO"3jv]-j@vK=!H\[I1~]x^}\}?cEZ[]|Nk+_I[1kK8jrW<!5#,v
    b"5[[@zfR!_}47$A;2DB'e@jJ\\Y~Z{K?*"-{{[xj_}SwUonr*V@;R^AeZ'K+E?\rZwWI?J^lkj5
    HsZlFRsTp975ETX}cBj_R6Q_\=aIDl+jiAWNH-p}a<B+[@{Hl>}+e1pO82jpGi<l!2xG^l'A-L,U
    ],<\,lC~>3Y[*^v2$]i{oBUYvWP\Fo;R]X1Y=V]!?*-X;(OG@AI%xrpanjI~zr5sD_YTDXJs5V]}
    7+3X$GU+[DYkpwT!]leU_?H2,,;Il\>7o^wlq'+KZzsV5x+;+0*325-Tx~}KY1|''H'az}Ao$<!y
    'UawoeD3zZY_Ng/#CJ\]V<~7<G,WR?@p_}se?lWuE,$]?$*1<7uxewKBj=Ul{A$2+<pW*_JceU}[
    om+Dtn=+CoUB{nju'gD5D,=o=IReT}35=JanG}3j*#i'w#;Hva!UJnBGz[sCmQi_]2x,sni}KYGX
    n}wU**lO+$5+Ej'#IQIG_#UX+eoell\3DAvslpV=Bw#v3EM75'$X'o+\H=V1*J[s^]A_~Yx''-'?
    ]-Ez-HDn^^*i=w@'nmo5jorx'*k?7<!J+~GtDqenu]^v!+N0[[Gr1~w~Ov?=CT[pKoev:P$[J_Ir
    1>RsQ]@tQJWQNz>@2pk=rBap}a<!oLW+x^,s3JwU=[GC*}D@3$GE^ZUC[alVkT'KVDMYB}^${o,}
    H5QoDY}^3amvs#G}3pvJG<<KY{vcEnn~-C?saUI@-5{+za_@jVW\Yemn?BJ_bvn$#Y;7roVOl'D=
    v?RD{I2s<7~l-X'iGGC{@?+~][O!]{p]V;{->C~J$~DxAO!{x!=C,+jIp!n=G[/#>'-l^2Hld$}p
    >}G\=NH=#CC#QGotFS2A3?ZXYZEa1E|$CK[trA-!AzI^6n]}I;OHB}5Tz=.gEXIkoxxBxwwDwXU'
    UOOnWjGUkOe\xvC{v}mB;C=UmUC@Rs<79KXz!W\H?>7<>me\1eTU7-zQHj+5R]we;JaUZ{X{^vkB
    -V#}78C[5e?]}'I!@[}5~#l}5Yz>,]I>{vasQBFjQmXlQ}TGJOv1J},m5{<w'\I\5]+*_[eDQCa\
    Wrro@n}6YXHBQxGIO}Q-<o+{KjvRom~Kx}YCgIw{1*^A~3a5$]JAOHl7o5R\roI}z?eQ_QWms}+e
    ^s@pULB+zEe57?FJwlzO]}K\?B}C$=<y7J^-plAo,DRWG'@5@{Oewwu?p\?wY,aoJr[w$@-Je1zr
    l+ZlwE5_,vG\kT-v-eDuk<o3BaTAKGV?^?G*k,r<]Z*~![rEa]@=ZB~#2}W[u'e[/9us^?'BZV7T
    u[V~{5Y'=AQA]a)zC~*}WTY}~Do,5_Q~Apsj$RkUwRQ3[j@1RsixZmZ'V}U-D*WIkU;=~E-1~{J=
    }ZzZ$u71OO]t4s[k$rpeij\Ej~XrnB[i7]7vn3>pv
`endprotected
//pragma protect end


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_top) # (
    parameter FAMILY             = "TRION",       // New Param
    parameter SYNC_CLK           = 0,
    parameter BYPASS_RESET_SYNC  = 0,             // New Param
    parameter SYNC_STAGE         = 2,             // New Param
    parameter MODE               = "STANDARD",
    parameter DEPTH              = 512,           // Reverted (Equivalent to WDATA_DEPTH) 
    parameter DATA_WIDTH         = 32,            // Reverted (Equivalent to WDATA_WIDTH)
    parameter PIPELINE_REG       = 1,             // Reverted (By default is ON)
    parameter OPTIONAL_FLAGS     = 1,             // Reverted
    parameter OUTPUT_REG         = 0,
    parameter PROGRAMMABLE_FULL  = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_FULL_ASSERT   = 27,
    parameter PROG_FULL_NEGATE   = 23,
    parameter PROGRAMMABLE_EMPTY = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_EMPTY_ASSERT  = 5,
    parameter PROG_EMPTY_NEGATE  = 7,
    parameter ALMOST_FLAG        = OPTIONAL_FLAGS,
    parameter HANDSHAKE_FLAG     = OPTIONAL_FLAGS,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter WADDR_WIDTH        = depth2width(DEPTH),
    parameter RDATA_WIDTH        = rdwidthcompute(ASYM_WIDTH_RATIO,DATA_WIDTH),
    parameter RD_DEPTH           = rddepthcompute(DEPTH,DATA_WIDTH,RDATA_WIDTH),
    parameter RADDR_WIDTH        = depth2width(RD_DEPTH),
    parameter ENDIANESS		      = 0,
    parameter OVERFLOW_PROTECT   = 1,
    parameter UNDERFLOW_PROTECT  = 1,
    parameter RAM_STYLE          = "block_ram"

)(
    input  wire                   a_rst_i,
    input  wire                   a_wr_rst_i,
    input  wire                   a_rd_rst_i,
    input  wire                   clk_i,
    input  wire                   wr_clk_i,
    input  wire                   rd_clk_i,
    input  wire                   wr_en_i,
    input  wire                   rd_en_i,
    input  wire [DATA_WIDTH-1:0]  wdata,
    output wire                   almost_full_o,
    output wire                   prog_full_o,
    output wire                   full_o,
    output wire                   overflow_o,
    output wire                   wr_ack_o,
    output wire [WADDR_WIDTH :0]  datacount_o,
    output wire [WADDR_WIDTH :0]  wr_datacount_o,
    output wire                   empty_o,
    output wire                   almost_empty_o,
    output wire                   prog_empty_o,
    output wire                   underflow_o,
    output wire                   rd_valid_o,
    output wire [RDATA_WIDTH-1:0] rdata,
    output wire [RADDR_WIDTH :0]  rd_datacount_o,
    output wire                   rst_busy
); 

localparam WR_DEPTH      = DEPTH;
localparam WDATA_WIDTH   = DATA_WIDTH;
localparam RAM_MUX_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? 32 :
                           (RDATA_WIDTH <= WDATA_WIDTH/16) ? 16 :
                           (RDATA_WIDTH <= WDATA_WIDTH/8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH/4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH/2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH)    ? 1  :
                           (RDATA_WIDTH <= WDATA_WIDTH*2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH*4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH*8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH*16) ? 16 : 32;           

wire                   wr_rst_int;
wire                   rd_rst_int;
wire                   wr_en_int;
wire                   rd_en_int;
wire [WADDR_WIDTH-1:0] waddr;
wire [RADDR_WIDTH-1:0] raddr;
wire                   wr_clk_int;
wire                   rd_clk_int;
wire [WADDR_WIDTH :0]  wr_datacount_int;
wire [RADDR_WIDTH :0]  rd_datacount_int;

generate
    if (ASYM_WIDTH_RATIO == 4) begin
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
            assign datacount_o    = wr_datacount_int;
            assign wr_datacount_o = {(WADDR_WIDTH+1){1'b0}};
            assign rd_datacount_o = {(RADDR_WIDTH+1){1'b0}};
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
            assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
            assign wr_datacount_o = wr_datacount_int;
            assign rd_datacount_o = rd_datacount_int;
        end
    end
    else begin
        assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
        assign wr_datacount_o = wr_datacount_int;
        assign rd_datacount_o = rd_datacount_int;
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
        end
    end    

    if (!SYNC_CLK) begin
        //(* async_reg = "true" *) reg [1:0] wr_rst;
        //(* async_reg = "true" *) reg [1:0] rd_rst;
        //
        //always @ (posedge wr_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        wr_rst <= 2'b11;
        //    else 
        //        wr_rst <= {wr_rst[0],1'b0};            
        //end
        //
        //always @ (posedge rd_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        rd_rst <= 2'b11;
        //    else 
        //        rd_rst <= {rd_rst[0],1'b0};            
        //end
        

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_wr_rst_i;
            assign rd_rst_int = a_rd_rst_i; 
            assign rst_busy   = 1'b0;
        end 
        else begin
            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_wr_rst (
               .clk   (wr_clk_int),
               .reset (a_rst_i),
               .d_o   (wr_rst_int)
            );

            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_rd_rst (
               .clk   (rd_clk_int),
               .reset (a_rst_i),
               .d_o   (rd_rst_int)
            );
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end

    end
    else begin
        //(* async_reg = "true" *) reg [1:0] a_rst;
        //
        //always @ (posedge clk_i or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        a_rst <= 2'b11;
        //    else 
        //        a_rst <= {a_rst[0],1'b0};            
        //end
        wire   a_rst;

        `IP_MODULE_NAME(efx_resetsync) #(
           .ACTIVE_LOW (0)
        ) efx_resetsync_a_rst (
           .clk   (clk_i),
           .reset (a_rst_i),
           .d_o   (a_rst)
        );

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_rst_i;
            assign rd_rst_int = a_rst_i;
            assign rst_busy   = 1'b0;
        end
        else begin
            assign wr_rst_int = a_rst;
            assign rd_rst_int = a_rst;   
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end 
endgenerate

`IP_MODULE_NAME(efx_fifo_ram) # (
    .FAMILY        (FAMILY),
    .WR_DEPTH      (WR_DEPTH),
    .RD_DEPTH      (RD_DEPTH),
    .WDATA_WIDTH   (WDATA_WIDTH),
    .RDATA_WIDTH   (RDATA_WIDTH),
    .WADDR_WIDTH   (WADDR_WIDTH),
    .RADDR_WIDTH   (RADDR_WIDTH),
    .OUTPUT_REG    (OUTPUT_REG),
    .RAM_MUX_RATIO (RAM_MUX_RATIO),
    .ENDIANESS	    (ENDIANESS),
    .RAM_STYLE     (RAM_STYLE)
) xefx_fifo_ram (
    .wdata (wdata), 
    .waddr (waddr),
    .raddr (raddr), 
    .we    (wr_en_int), 
    .re    (rd_en_int),
    .wclk  (wr_clk_int),
    .rclk  (rd_clk_int),
    .rdata (rdata)
);

`IP_MODULE_NAME(efx_fifo_ctl) # (
    .SYNC_CLK           (SYNC_CLK),
    .SYNC_STAGE         (SYNC_STAGE),
    .MODE               (MODE),
    .WR_DEPTH           (WR_DEPTH),
    .WADDR_WIDTH        (WADDR_WIDTH),
    .RADDR_WIDTH        (RADDR_WIDTH),
    .ASYM_WIDTH_RATIO   (ASYM_WIDTH_RATIO),
    .RAM_MUX_RATIO      (RAM_MUX_RATIO),
    .PIPELINE_REG       (PIPELINE_REG),
    .ALMOST_FLAG        (ALMOST_FLAG),
    .PROGRAMMABLE_FULL  (PROGRAMMABLE_FULL),
    .PROG_FULL_ASSERT   (PROG_FULL_ASSERT),
    .PROG_FULL_NEGATE   (PROG_FULL_NEGATE),
    .PROGRAMMABLE_EMPTY (PROGRAMMABLE_EMPTY),
    .PROG_EMPTY_ASSERT  (PROG_EMPTY_ASSERT),
    .PROG_EMPTY_NEGATE  (PROG_EMPTY_NEGATE),
    .OUTPUT_REG         (OUTPUT_REG),
    .HANDSHAKE_FLAG     (HANDSHAKE_FLAG),
    .OVERFLOW_PROTECT   (OVERFLOW_PROTECT),
    .UNDERFLOW_PROTECT  (UNDERFLOW_PROTECT)
) xefx_fifo_ctl (
    .wr_rst          (wr_rst_int),
    .rd_rst          (rd_rst_int),
    .wclk            (wr_clk_int),
    .rclk            (rd_clk_int),
    .we              (wr_en_i),
    .re              (rd_en_i),
    .wr_full         (full_o),
    .wr_ack          (wr_ack_o),
    .rd_empty        (empty_o),
    .wr_almost_full  (almost_full_o),
    .rd_almost_empty (almost_empty_o),
    .wr_prog_full    (prog_full_o),
    .rd_prog_empty   (prog_empty_o),
    .wr_en_int       (wr_en_int),
    .rd_en_int       (rd_en_int),
    .waddr           (waddr),
    .raddr           (raddr),
    .wr_datacount    (wr_datacount_int),
    .rd_datacount    (rd_datacount_int),
    .rd_vld          (rd_valid_o),
    .wr_overflow     (overflow_o),
    .rd_underflow    (underflow_o)
);

function integer depth2width;
input [31:0] depth;
begin : fnDepth2Width
    if (depth > 1) begin
        depth = depth - 1;
        for (depth2width=0; depth>0; depth2width = depth2width + 1)
            depth = depth>>1;
        end
    else
       depth2width = 0;
end
endfunction 

function integer width2depth;
input [31:0] width;
begin : fnWidth2Depth
    width2depth = width**2;
end
endfunction

function integer rdwidthcompute;
input [31:0] asym_option;
input [31:0] wr_width;
begin : RdWidthCompute
    rdwidthcompute = (asym_option==0)? wr_width/16 :
                     (asym_option==1)? wr_width/8  :
                     (asym_option==2)? wr_width/4  :
                     (asym_option==3)? wr_width/2  :
                     (asym_option==4)? wr_width/1  :
                     (asym_option==5)? wr_width*2  :
                     (asym_option==6)? wr_width*4  :
                     (asym_option==7)? wr_width*8  :
                     (asym_option==8)? wr_width*16 : wr_width/1;
end
endfunction

function integer rddepthcompute;
input [31:0] wr_depth;
input [31:0] wr_width;
input [31:0] rd_width;
begin : RdDepthCompute
    rddepthcompute = (wr_depth * wr_width) / rd_width;
end
endfunction

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ram) #(
    parameter FAMILY        = "TRION",
    parameter WR_DEPTH      = 512,
    parameter RD_DEPTH      = 512,
    parameter WDATA_WIDTH   = 8,
    parameter RDATA_WIDTH   = 8,
    parameter WADDR_WIDTH   = 9,
    parameter RADDR_WIDTH   = 9,
    parameter OUTPUT_REG    = 1,
    parameter RAM_MUX_RATIO = 4,
    parameter ENDIANESS 	 = 0, //0: Big endian (default)   1: Little endian 
    parameter RAM_STYLE     = "block_ram"
) (
    input  wire                     wclk,
    input  wire                     rclk,
    input  wire                     we,
    input  wire                     re,
    input  wire [(WDATA_WIDTH-1):0] wdata,
    input  wire [(WADDR_WIDTH-1):0] waddr,
    input  wire [(RADDR_WIDTH-1):0] raddr,
    output wire [(RDATA_WIDTH-1):0] rdata
);

localparam MEM_DEPTH         = (WR_DEPTH > RD_DEPTH) ? WR_DEPTH : RD_DEPTH;
localparam MEM_DATA_WIDTH    = (WDATA_WIDTH > RDATA_WIDTH) ? RDATA_WIDTH : WDATA_WIDTH;   
localparam LSB_WIDTH         = (WADDR_WIDTH > RADDR_WIDTH) ? (WADDR_WIDTH - RADDR_WIDTH) : (RADDR_WIDTH - WADDR_WIDTH);
localparam RDATA_WDATA_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? "ONE_THIRTYTWO" :
                               (RDATA_WIDTH <= WDATA_WIDTH/16) ? "ONE_SIXTEENTH" :
                               (RDATA_WIDTH <= WDATA_WIDTH/8)  ? "ONE_EIGHTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/4)  ? "ONE_FOURTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/2)  ? "ONE_HALF"      :
                               (RDATA_WIDTH <= WDATA_WIDTH)    ? "ONE"           :
                               (RDATA_WIDTH <= WDATA_WIDTH*2)  ? "TWO_TIMES"     :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "FOUR_TIMES"    :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "EIGHT_TIMES"   :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "SIXTEEN_TIMES" : "THIRTYTWO_TIMES";

(* syn_ramstyle = RAM_STYLE *) reg [MEM_DATA_WIDTH-1:0] ram[MEM_DEPTH-1:0];
reg [RDATA_WIDTH-1:0]    r_rdata_1P;
reg [RDATA_WIDTH-1:0]    r_rdata_2P;

wire                     re_int;

generate
    if (FAMILY == "TRION") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                end
                r_rdata_2P <= r_rdata_1P;
            end
        end
        
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            if (ENDIANESS == 0) begin 
            	integer i;
            	always @ (posedge wclk) begin
                	for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                    end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                	end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
        end
        
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  ||RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
            //integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end 
            end
            else begin //endianess == 1
            	always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end
            end
        end
        if (OUTPUT_REG) begin
            assign re_int = re;
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end
    end
    else begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                    r_rdata_2P <= r_rdata_1P;
                end
            end
        end 
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            //integer i;
            if (ENDIANESS == 0) begin
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
            
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
        end
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  || RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
        	//integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
            
            else begin //endianess ==1
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
        end
        // NIC
        //if (MODE == "STANDARD") begin     
        //    if (OUTPUT_REG) begin
        //        reg re_r;
        //        always @ (posedge rclk) begin
        //            re_r <= re;
        //        end
        //        assign re_int = re | re_r;        
        //        assign rdata  = r_rdata_2P;            
        //    end
        //    else begin
        //        assign re_int = re;
        //        assign rdata  = r_rdata_1P;
        //    end
        //end
        //else begin
        //    assign re_int = re;
        //    assign rdata  = r_rdata_1P;
        //end    
        if (OUTPUT_REG) begin
            reg re_r;
            always @ (posedge rclk) begin
                re_r <= re;
            end
            assign re_int = re | re_r;        
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end    
    end
endgenerate

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2021 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   gray2bin.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Gray to Binary Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_gray2bin)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] bin_o,
 // input
 input [WIDTH-1:0] gray_i);

//---------------------------------------------------------------------
// Recursive Module
// Description: reduction xor
generate
   if (WIDTH > 1) begin
      wire [1:0] bin_1;
      assign bin_1 = {gray_i[WIDTH-1], gray_i[WIDTH-1]^gray_i[WIDTH-2]};
      if (WIDTH == 2) begin
	 assign bin_o = bin_1;
      end
      else begin
	 assign bin_o[WIDTH-1] = bin_1[1];
	 `IP_MODULE_NAME(efx_fifo_gray2bin) #(.WIDTH(WIDTH-1)) u_gray2bin (.bin_o(bin_o[WIDTH-2:0]), .gray_i({bin_1[0], gray_i[WIDTH-3:0]}));
      end
   end
   else /* if (WIDTH == 1) */
     assign bin_o = gray_i;
endgenerate
   
endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   pipe_reg.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Parallel Pipelining Shift Register
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_datasync) #(
    parameter STAGE = 32,
    parameter WIDTH = 4
) (
 input  wire             clk_i,
 input  wire [WIDTH-1:0] d_i,
 output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] pipe_reg [STAGE-1:0];
integer i;

always @(posedge clk_i) begin
  for (i=STAGE-1; i>0; i = i - 1) begin
    pipe_reg[i] <= pipe_reg[i-1];
  end
  pipe_reg[0] <= d_i;
end
assign d_o = pipe_reg[STAGE-1];
   

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_resetsync) #(
    parameter ASYNC_STAGE = 2,
    parameter ACTIVE_LOW = 1
) (
    input  wire             clk,
    input  wire             reset,
    output wire             d_o
);


generate
   if (ACTIVE_LOW == 1) begin: active_low
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (1),
         .RST_VALUE  (0)
      ) efx_resetsync_active_low (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b1),
         .d_o     (d_o)
      );
   end
   else begin: active_high
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (0),
         .RST_VALUE  (1)
      ) efx_resetsync_active_high (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b0),
         .d_o     (d_o)
      );
   end
endgenerate

endmodule



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_asyncreg) #(
    parameter ASYNC_STAGE = 2,
    parameter WIDTH = 4,
    parameter ACTIVE_LOW = 1, // 0 - Active high reset, 1 - Active low reset
    parameter RST_VALUE = 0 
) (
    input  wire             clk,
    input  wire             reset_n,
    input  wire [WIDTH-1:0] d_i,
    output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] async_reg[ASYNC_STAGE-1:0];

assign d_o = async_reg[ASYNC_STAGE-1];

genvar i;
generate
   if (ACTIVE_LOW == 1) begin: active_low
      if (RST_VALUE == 0) begin: low_reset_0
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: low_reset_1
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
   else begin: active_high
      if (RST_VALUE == 0) begin: high_reset_0
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: high_reset_1
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
endgenerate

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ctl) # (
    parameter SYNC_CLK           = 1,
    parameter SYNC_STAGE         = 2,
    parameter MODE               = "STANDARD",
    parameter WR_DEPTH           = 512,
    parameter WADDR_WIDTH        = 9,
    parameter RADDR_WIDTH        = 9,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter RAM_MUX_RATIO      = 1,
    parameter PIPELINE_REG       = 1,
    parameter ALMOST_FLAG        = 1,
    parameter PROGRAMMABLE_FULL  = "NONE",
    parameter PROG_FULL_ASSERT   = 0,
    parameter PROG_FULL_NEGATE   = 0,
    parameter PROGRAMMABLE_EMPTY = "NONE",
    parameter PROG_EMPTY_ASSERT  = 0,
    parameter PROG_EMPTY_NEGATE  = 0,
    parameter OUTPUT_REG         = 0,
    parameter HANDSHAKE_FLAG     = 1, 
    parameter OVERFLOW_PROTECT   = 0,
    parameter UNDERFLOW_PROTECT  = 0
)(
    input   wire                   wr_rst,
    input   wire                   rd_rst,
    input   wire                   wclk,
    input   wire                   rclk,
    input   wire                   we,
    input   wire                   re,
    output  wire                   wr_full,
    output  reg                    wr_ack,
    output  wire                   wr_almost_full,
    output  wire                   rd_empty,
    output  wire                   rd_almost_empty,
    output  wire                   wr_prog_full,
    output  wire                   rd_prog_empty,
    output  wire                   wr_en_int,
    output  wire                   rd_en_int,
    output  wire [WADDR_WIDTH-1:0] waddr,
    output  wire [RADDR_WIDTH-1:0] raddr,
    output  wire [WADDR_WIDTH:0]   wr_datacount,
    output  wire [RADDR_WIDTH:0]   rd_datacount,
    output  wire                   rd_vld,
    output  reg                    wr_overflow,
    output  reg                    rd_underflow
);

reg  [WADDR_WIDTH:0] waddr_cntr;
reg  [WADDR_WIDTH:0] waddr_cntr_r;
reg  [RADDR_WIDTH:0] raddr_cntr;
reg                  rd_valid;

wire [WADDR_WIDTH:0] waddr_int;
wire [RADDR_WIDTH:0] raddr_int;
wire                 rd_empty_int;
wire [WADDR_WIDTH:0] wr_datacount_int;
wire [RADDR_WIDTH:0] rd_datacount_int;

assign waddr     = waddr_cntr[WADDR_WIDTH-1:0];
// NIC
wire [RADDR_WIDTH:0] ram_raddr;
assign raddr     = (MODE == "FWFT") ? ram_raddr[RADDR_WIDTH-1:0] : raddr_cntr[RADDR_WIDTH-1:0];
//assign raddr     = raddr_cntr[RADDR_WIDTH-1:0];
//assign wr_en_int = we & ~wr_full;
assign wr_en_int = OVERFLOW_PROTECT ? we & ~wr_full : we;

assign wr_datacount  = wr_datacount_int;
assign rd_datacount  = ASYM_WIDTH_RATIO == 4 && SYNC_CLK ? wr_datacount_int : rd_datacount_int;


generate
    if (MODE == "FWFT") begin    
        // NIC
        //assign rd_en_int     = (~rd_empty_int & rd_empty) | (re & ~rd_empty_int);
        //assign rd_empty      = rd_empty_fwft;

        assign rd_en_int     = 1'b1;
        //assign rd_empty      = rd_empty_int;
        
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        init_set <= 1'b1;
        //    end
        //    else if (~init_set & rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //    else if (~rd_empty_int) begin
        //        init_set <= 1'b0;            
        //    end
        //    else if (rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //end
        // NIC 
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        rd_empty_fwft <= 1'b1;
        //    end
        //    else if (rd_en_int) begin
        //        rd_empty_fwft <= 1'b0;            
        //    end
        //    else if (re) begin
        //        rd_empty_fwft <= 1'b1;            
        //    end
        //end          
        
        //if (FAMILY == "TRION") begin
        if (OUTPUT_REG) begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid <= 1'b0;
                end
                else begin
                    rd_valid <= ~rd_empty;
                end
            end
            assign rd_vld = rd_valid;                
        end    
        else begin
            assign rd_vld = ~rd_empty;                
        end
        
        assign rd_empty = rd_empty_int;
    end
    else begin
        assign rd_en_int     = UNDERFLOW_PROTECT ? re & ~rd_empty_int : re;
        assign rd_empty      = rd_empty_int;
        
        if (OUTPUT_REG) begin
            reg rd_valid_r;
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid_r <= 'h0;
                    rd_valid   <= 'h0;
                end
                else begin
                    {rd_valid,rd_valid_r} <= {rd_valid_r,rd_en_int};
                end
            end
            assign rd_vld = rd_valid;            
        end
        else begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid  <= 'h0;
                end
                else begin
                    rd_valid <= rd_en_int;
                end
            end
            assign rd_vld = rd_valid;
        end
    end
    
    if (ALMOST_FLAG) begin
        assign wr_almost_full   = wr_datacount >= WR_DEPTH-1;
        assign rd_almost_empty  = rd_datacount <= 'd1;
    end
    else begin
        assign wr_almost_full   = 1'b0;
        assign rd_almost_empty  = 1'b0;        
    end
    
    if (PROGRAMMABLE_FULL == "STATIC_SINGLE") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_datacount >= PROG_FULL_ASSERT;
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end  
    end
    else if (PROGRAMMABLE_FULL == "STATIC_DUAL") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_prog_full_int ? wr_datacount >= PROG_FULL_NEGATE : wr_datacount >= PROG_FULL_ASSERT;   

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end              
    end
    else begin
        assign wr_prog_full  = 1'b0; 
    end
        
    if (PROGRAMMABLE_EMPTY == "STATIC_SINGLE") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty  = rd_datacount <= PROG_EMPTY_ASSERT;
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end
    end
    else if (PROGRAMMABLE_EMPTY == "STATIC_DUAL") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty = rd_prog_empty_int ? (rd_datacount <= PROG_EMPTY_NEGATE) : (rd_datacount <= PROG_EMPTY_ASSERT);
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end  
    end
    else begin
        assign rd_prog_empty  = 1'b0; 
    end
    
    if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_ack <= 1'b0;
            end
            else begin
                // NIC
                //wr_ack <= wr_en_int & ~wr_overflow;
                wr_ack <= OVERFLOW_PROTECT ? wr_en_int & ~wr_overflow : wr_en_int;
            end
        end
    end

    if (OVERFLOW_PROTECT) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else if (we && wr_full) begin
                wr_overflow <= 1'b1;
            end
            else begin
                wr_overflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else begin
                wr_overflow <= we && wr_full ? 1'b1 : wr_overflow;
            end
        end
    end

    if (UNDERFLOW_PROTECT) begin     
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else if (re && rd_empty) begin
                rd_underflow <= 1'b1;
            end
            else begin
                rd_underflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else begin
                rd_underflow <= re && rd_empty ? 1'b1 : rd_underflow;
            end
        end
    end
    
    localparam RATIO_WIDTH = (RADDR_WIDTH >= WADDR_WIDTH)? RADDR_WIDTH - WADDR_WIDTH : WADDR_WIDTH - RADDR_WIDTH;
    
    if (ASYM_WIDTH_RATIO < 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:RATIO_WIDTH]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:RATIO_WIDTH];
        assign wr_datacount_int = waddr_cntr - (raddr_int/RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int*RAM_MUX_RATIO)-raddr_cntr;
    end
    // NIC
    else if (ASYM_WIDTH_RATIO == 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:0]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - raddr_int;
        assign rd_datacount_int = waddr_int - raddr_cntr;
    end
    else begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:RATIO_WIDTH] == raddr_int[RADDR_WIDTH-1:0]);
        // NIC
        //assign rd_empty_int     = (waddr_int- raddr_cntr*RAM_MUX_RATIO) < RAM_MUX_RATIO;
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:RATIO_WIDTH] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - (raddr_int*RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int/RAM_MUX_RATIO)-raddr_cntr;
    end
endgenerate

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr   <= 'h0;
    end
    else if (wr_en_int) begin
        waddr_cntr   <= waddr_cntr + 1'b1;
    end
end

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr_r  <= 'h0;
    end
    else begin
        waddr_cntr_r  <= waddr_cntr;
    end
end

always @ (posedge rclk or posedge rd_rst) begin
    if (rd_rst) begin
        raddr_cntr   <= 'h0;
    end
    // NIC
    //else if (rd_en_int) begin
    else begin
        //raddr_cntr   <= raddr_cntr + 1'b1;
        //raddr_cntr   <= raddr_cntr + (re & ~rd_empty_int);
        raddr_cntr   <= ram_raddr;
    end
end
// NIC
assign ram_raddr = raddr_cntr + (UNDERFLOW_PROTECT ? re & ~rd_empty_int : re);


generate
    if (SYNC_CLK) begin : sync_clk
       if (MODE == "FWFT") begin
          assign waddr_int = waddr_cntr_r;
          assign raddr_int        = raddr_cntr;
       end
       else begin
          assign waddr_int        = waddr_cntr;
          assign raddr_int        = raddr_cntr;
       end
    end
    else begin : async_clk
        reg  [RADDR_WIDTH:0] raddr_cntr_gry_r;
        reg  [WADDR_WIDTH:0] waddr_cntr_gry_r;
        
        wire [RADDR_WIDTH:0] raddr_cntr_gry;
        wire [RADDR_WIDTH:0] raddr_cntr_gry_sync;
        wire [RADDR_WIDTH:0] raddr_cntr_sync_g2b;
        wire [WADDR_WIDTH:0] waddr_cntr_gry;
        wire [WADDR_WIDTH:0] waddr_cntr_gry_sync;
        wire [WADDR_WIDTH:0] waddr_cntr_sync_g2b;

        if (PIPELINE_REG) begin
            reg  [RADDR_WIDTH:0] raddr_cntr_sync_g2b_r;    
            reg  [WADDR_WIDTH:0] waddr_cntr_sync_g2b_r;        
        
            assign waddr_int        = waddr_cntr_sync_g2b_r;
            assign raddr_int        = raddr_cntr_sync_g2b_r;
            
            always @ (posedge wclk or posedge wr_rst) begin
                if (wr_rst) begin
                    raddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    raddr_cntr_sync_g2b_r <= raddr_cntr_sync_g2b;
                end
            end
            
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    waddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    waddr_cntr_sync_g2b_r <= waddr_cntr_sync_g2b;
                end
            end
        end
        else begin
            assign waddr_int        = waddr_cntr_sync_g2b;
            assign raddr_int        = raddr_cntr_sync_g2b;        
        end
      
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                raddr_cntr_gry_r <= 'h0;
            end
            else begin
                raddr_cntr_gry_r <= raddr_cntr_gry;
            end
        end
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_bin2gray    (.bin_i(raddr_cntr), .gray_o(raddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (RADDR_WIDTH+1), .ACTIVE_LOW(0)) xrd2wr_addr_sync   (.clk(wclk), .reset_n(wr_rst), .d_i(raddr_cntr_gry_r), .d_o(raddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_gray2bin    (.gray_i(raddr_cntr_gry_sync), .bin_o(raddr_cntr_sync_g2b));
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                waddr_cntr_gry_r <= 'h0;
            end
            else begin
                waddr_cntr_gry_r <= waddr_cntr_gry;
            end
        end        
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_bin2gray  (.bin_i(waddr_cntr), .gray_o(waddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (WADDR_WIDTH+1), .ACTIVE_LOW(0)) wr2rd_addr_sync (.clk(rclk), .reset_n(rd_rst), .d_i(waddr_cntr_gry_r), .d_o(waddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_gray2bin  (.gray_i(waddr_cntr_gry_sync), .bin_o(waddr_cntr_sync_g2b));        

    end
endgenerate
endmodule


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   bin2gray.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Binary to Gray Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_bin2gray)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] gray_o,
 // input
 input [WIDTH-1:0] bin_i
 );

//---------------------------------------------------------------------
// Function :   bit_xor
// Description: reduction xor
function bit_xor (
  input [31:0] nex_bit,
  input [31:0] curr_bit,
  input [WIDTH-1:0] xor_in);
  begin : fn_bit_xor
    bit_xor = xor_in[nex_bit] ^ xor_in[curr_bit];
  end
endfunction

// Convert Binary to Gray, bit by bit
generate 
begin
  genvar bit_idx;
  for(bit_idx=0; bit_idx<WIDTH-1; bit_idx=bit_idx+1) begin : gBinBits
    assign gray_o[bit_idx] = bit_xor(bit_idx+1, bit_idx, bin_i);
  end
  assign   gray_o[WIDTH-1] = bin_i[WIDTH-1];
end
endgenerate

endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


//pragma protect
//pragma protect begin
`protected

    MTI!##<*Q>X@]8O-,BCOl_Rw1@Q?\7lUTa7n$i=ZQ2="Bm15,v_rJ\W7+H@[S7UQ{;IeUaj<ao[W
    sJTJ]Q-Cs~{BBY6+1$'Q,AmOoXA31,['>Kj}[EsCZx]5l3#xP',!Z{Rx~<Er-Fo~HQ?,\3#IX}Dz
    J[KwCn_=s7CD128o^?}KDG;}@o}7<XTQ}kOO>Um27A#EBnva>mU2[p@d\@nsla>#L<}]Kb_B*a[J
    ;{VJJT;X$ixz5*7]sI#Y@l,?>;!T{-^WpQ8r'l!F/_C!GBV$}I;ADn}r{g,!\#UrX\a>'U=\!3L<
    *@lr,-=*xBEw}<TOW}{XD#plr7'$!_O'7R'$Zz?6^Hs[B73@!V;J_uO[0Kvipma<WiU*5-1+'Uv~
    Geu+]@+IuzlAnsT{TP>'[3>sXBD#^sj,7n-s3XIlX!iD,Z}a5xw'krQRa$]7rs~1Tl=rY1^J0Grs
    _)u1[W'Q>eO-;'.o{$sIkO{rCWRq*>s+{VV<jN(*^OOJY';IqB$7?Ci*eIC2A_]?3[;H$*{DYrWY
    sR_nEz<eD@7V}:Bpj#n7Zun[K77$u[b7_IsBH-u~\Q#]JU+.Dzi$_xw@6YeH@=,1e1eXAVl#~(5B
    a<-+7[H[Tp{5ov=P%ipvBw]$nAauY]rZ?wjOm[QWCu]\BwaA<Gp@2?\#nE--VsijA\_'$2R-Ye2j
    53Y1!'@wsJnY[!O#w[j?Ck[C2~Bv^aHGE\s*rRzpi^Csk|S+'$pe_$1Y5v=sV>En,p^P[?viHD$O
    KEi,kvJ[!EEkJ6OkV]{UG+/=1D{Al_E{>jwM<1}_!+-V'1_upum}<DWXm$EIZ^IvjU2*]\~Z$?-k
    ^TDv;+7n6FJ7~lfp'h?ov@)@9,HCl$Kc@lRI*}l'J,i==?I]aQoO1>+xu*@2FhNBKoDNK}A^l-51
    ]1#]J>lv'XuW
`endprotected
//pragma protect end

`undef IP_UUID
`undef IP_NAME_CONCAT
`undef IP_MODULE_NAME
