python.exe  -m "efx_dbg.DbgWizard" --device  "Ti60F225" --family  "Titanium" --in_profile_name  "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/debug_profile.wizard.json" --out_dbg_vdb  "debug_top.post.vdb" --out_profile_name  "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/debug_profile.wizard.json" --out_dir  "work_dbg"
efx_map  --mode "speed" --max_ram "-1" --max_mult "-1" --infer-clk-enable "3" --infer-sync-set-reset "1" --fanout-limit "0" --seq_opt "0" --retiming "0" --dsp-mac-packing "1" --dsp-input-regs-packing "1" --dsp-output-regs-packing "1" --bram_output_regs_packing "1" --blast_const_operand_adders "1" --operator-sharing "0" --optimize-adder-tree "0" --pack-luts-to-comb4 "0" --min-sr-fanout "0" --min-ce-fanout "0" --seq-opt-sync-only "0" --blackbox-error "1" --allow-const-ram-index "0" --hdl-compile-unit "1" --create-onehot-fsms "0" --mult-decomp-retime "0" --optimize-zero-init-rom "1" --insert-carry-skip "0" --mult-auto-pipeline "0" --use-logic-for-small-mem "64" --use-logic-for-small-rom "64" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/W0_FIFO" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/R0_FIFO" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/dsi_tx" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/csi_rx" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/DdrCtrl" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/W0_FIFO_8" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/W0_FIFO_64" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/FIFO_W48R24" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/R0_FIFO_8" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/R0_FIFO_16" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/W0_FIFO_32" --family "Titanium" --device "Ti60F225" --keep-gclk-in-module "1" --root "edb_top" --v "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v" --write-postmap-module "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.post.vdb" --generate_sig_profile "false" --work-dir "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg" --output-dir "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/outflow"
efx_map  --family "Titanium" --device "Ti60F225" --mode "speed" --max_ram "-1" --max_mult "-1" --infer-clk-enable "3" --infer-sync-set-reset "1" --fanout-limit "0" --seq_opt "0" --retiming "0" --dsp-mac-packing "1" --dsp-input-regs-packing "1" --dsp-output-regs-packing "1" --bram_output_regs_packing "1" --blast_const_operand_adders "1" --operator-sharing "0" --optimize-adder-tree "0" --pack-luts-to-comb4 "0" --min-sr-fanout "0" --min-ce-fanout "0" --seq-opt-sync-only "0" --blackbox-error "1" --allow-const-ram-index "0" --hdl-compile-unit "1" --create-onehot-fsms "0" --mult-decomp-retime "0" --optimize-zero-init-rom "1" --insert-carry-skip "0" --mult-auto-pipeline "0" --use-logic-for-small-mem "64" --use-logic-for-small-rom "64" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/W0_FIFO" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/R0_FIFO" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/dsi_tx" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/csi_rx" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/DdrCtrl" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/W0_FIFO_8" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/W0_FIFO_64" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/FIFO_W48R24" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/R0_FIFO_8" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/R0_FIFO_16" --I "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/ip/W0_FIFO_32" --root "example_top" --vdb "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/outflow/Ti60_Demo.elab.vdb" --vdb "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.post.vdb" --write-efx-verilog "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/Ti60_Demo.dbg.map.v" --binary-db "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/Ti60_Demo.dbg.vdb" --merge_vdbs=1 --conn "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/debug_profile.wizard.json" --generate_sig_profile "false" --work-dir "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg" --output-dir "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/outflow"
python.exe  "C:/Efinity/2023.2/scripts/efx_pt_jtag_util.py" --device "Ti60F225" --project "Ti60_Demo" --design_file "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/Ti60_Demo.peri.xml" --action "add" --jtag_user "JTAG_USER1" --output "D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/Ti60_Demo.dbg.peri.xml"
python 
