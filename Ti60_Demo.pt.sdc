
# Efinity Interface Designer SDC
# Version: 2022.2.322.2.14
# Date: 2023-05-13 21:06

# Copyright (C) 2017 - 2022 Efinix Inc. All rights reserved.

# Device: Ti60F225
# Project: Ti60_AR0135_LCD
# Timing Model: C4 (final)

# PLL Constraints
#################
create_clock -period 20.8333 dsi_refclk_i
create_clock -period 23.8095 dsi_byteclk_i
create_clock -waveform {2.2321 5.2083} -period 5.9524 dsi_txcclk_i
create_clock -waveform {0.7440 3.7202} -period 5.9524 dsi_serclk_i
create_clock -period 2.6042 tdqss_clk
create_clock -period 5.2083 core_clk
create_clock -period 2.6042 tac_clk
create_clock -waveform {0.6510 1.9531} -period 2.6042 twd_clk
create_clock -period 10.4167 clk_sys
create_clock -period 13.4409 clk_pixel
create_clock -period 6.7204 clk_pixel_2x
create_clock -waveform {0.3360 1.0081} -period 1.3441 clk_pixel_10x
create_clock -period 20.8333 clk_lvds_1x
create_clock -waveform {0.7440 2.2321} -period 2.9762 clk_lvds_7x
create_clock -period 62.5000 clk_27m
create_clock -period 31.2500 clk_54m

create_clock -period 10 [get_ports {cmos_pclk}]

create_clock -period 6 [get_ports {csi_rxc_i}]

set_clock_groups -group {tdqss_clk core_clk tac_clk twd_clk}

set_max_delay -from [get_clocks {core_clk}] -to [get_clocks {clk_sys}] 4.5
set_false_path -from [get_clocks {tac_clk}] -to [get_clocks {clk_sys}] 
set_false_path -from [get_clocks {twd_clk}] -to [get_clocks {clk_sys}] 

set_max_delay -from [get_clocks {clk_sys}] -to [get_clocks {core_clk}] 4.5
set_false_path -from [get_clocks {clk_sys}] -to [get_clocks {tac_clk}] 
set_false_path -from [get_clocks {clk_sys}] -to [get_clocks {twd_clk}] 

set_false_path -from [get_clocks twd_clk] -to [get_clocks core_clk]


set_false_path -from [get_clocks clk_sys] -to [get_clocks csi_rxc_i]
set_false_path -from [get_clocks csi_rxc_i] -to [get_clocks clk_sys]


set_false_path -from [get_clocks clk_sys] -to [get_clocks clk_pixel]
set_false_path -from [get_clocks clk_pixel] -to [get_clocks clk_sys]

set_false_path -from [get_clocks clk_sys] -to [get_clocks cmos_pclk]
set_false_path -from [get_clocks cmos_pclk] -to [get_clocks clk_sys]

    
set_false_path -from [get_clocks dsi_byteclk_i] -to [get_clocks clk_lvds_1x]
set_false_path -from [get_clocks clk_lvds_1x] -to [get_clocks dsi_byteclk_i]


set_false_path -from [get_clocks dsi_byteclk_i] -to [get_clocks clk_sys]
set_false_path -from [get_clocks clk_sys] -to [get_clocks dsi_byteclk_i]

set_false_path -from [get_clocks dsi_byteclk_i] -to [get_clocks clk_pixel]
set_false_path -from [get_clocks clk_pixel] -to [get_clocks dsi_byteclk_i]


# GPIO Constraints
####################
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {clk_24m}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {clk_24m}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {clk_25m}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {clk_25m}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_ctl1}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_ctl1}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~1}] -max 1.191 [get_ports {cmos_data[0]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~1}] -min 0.961 [get_ports {cmos_data[0]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~6~1}] -max 1.191 [get_ports {cmos_data[1]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~6~1}] -min 0.961 [get_ports {cmos_data[1]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~12~322}] -max 1.191 [get_ports {cmos_data[2]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~12~322}] -min 0.961 [get_ports {cmos_data[2]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~214~322}] -max 1.191 [get_ports {cmos_data[3]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~214~322}] -min 0.961 [get_ports {cmos_data[3]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~322}] -max 1.191 [get_ports {cmos_data[4]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~322}] -min 0.961 [get_ports {cmos_data[4]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~212~322}] -max 1.191 [get_ports {cmos_data[5]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~212~322}] -min 0.961 [get_ports {cmos_data[5]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~10~322}] -max 1.191 [get_ports {cmos_data[6]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~10~322}] -min 0.961 [get_ports {cmos_data[6]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~197~322}] -max 1.191 [get_ports {cmos_data[7]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~197~322}] -min 0.961 [get_ports {cmos_data[7]}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~216~322}] -max 1.191 [get_ports {cmos_href}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~216~322}] -min 0.961 [get_ports {cmos_href}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~18~322}] -max 1.191 [get_ports {cmos_vsync}]
set_input_delay -clock_fall -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~18~322}] -min 0.961 [get_ports {cmos_vsync}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_ctl2}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_ctl2}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_sclk}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_sclk}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {led_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {led_o[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_sdat_IN}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_sdat_IN}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_sdat_OUT}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_sdat_OUT}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_sdat_OE}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_sdat_OE}]

# HSIO GPIO Constraints
#########################
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {uart_rx_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {uart_rx_i}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~95}] -max 0.763 [get_ports {addr[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~95}] -min 0.360 [get_ports {addr[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~36}] -max 0.763 [get_ports {addr[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~36}] -min 0.360 [get_ports {addr[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~70}] -max 0.763 [get_ports {addr[2]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~70}] -min 0.360 [get_ports {addr[2]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~50~1}] -max 0.763 [get_ports {addr[3]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~50~1}] -min 0.360 [get_ports {addr[3]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~25}] -max 0.763 [get_ports {addr[4]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~25}] -min 0.360 [get_ports {addr[4]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~142~1}] -max 0.763 [get_ports {addr[5]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~142~1}] -min 0.360 [get_ports {addr[5]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~59}] -max 0.763 [get_ports {addr[6]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~59}] -min 0.360 [get_ports {addr[6]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~110}] -max 0.763 [get_ports {addr[7]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~110}] -min 0.360 [get_ports {addr[7]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~26}] -max 0.763 [get_ports {addr[8]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~26}] -min 0.360 [get_ports {addr[8]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~127}] -max 0.763 [get_ports {addr[9]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~127}] -min 0.360 [get_ports {addr[9]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~138}] -max 0.763 [get_ports {addr[10]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~138}] -min 0.360 [get_ports {addr[10]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~60}] -max 0.763 [get_ports {addr[11]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~60}] -min 0.360 [get_ports {addr[11]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~35}] -max 0.763 [get_ports {addr[12]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~35}] -min 0.360 [get_ports {addr[12]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~71}] -max 0.763 [get_ports {addr[13]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~71}] -min 0.360 [get_ports {addr[13]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~109}] -max 0.763 [get_ports {addr[14]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~109}] -min 0.360 [get_ports {addr[14]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~143~1}] -max 0.763 [get_ports {addr[15]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~143~1}] -min 0.360 [get_ports {addr[15]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~74~1}] -max 0.763 [get_ports {ba[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~74~1}] -min 0.360 [get_ports {ba[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~132~1}] -max 0.763 [get_ports {ba[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~132~1}] -min 0.360 [get_ports {ba[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~126}] -max 0.763 [get_ports {ba[2]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~126}] -min 0.360 [get_ports {ba[2]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~49}] -max 0.763 [get_ports {cas}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~49}] -min 0.360 [get_ports {cas}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~123~1}] -max 0.763 [get_ports {cke}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~123~1}] -min 0.360 [get_ports {cke}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~150}] -max 0.763 [get_ports {clk_n_lo clk_n_hi}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~150}] -min 0.360 [get_ports {clk_n_lo clk_n_hi}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~149}] -max 0.763 [get_ports {clk_p_lo clk_p_hi}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~149}] -min 0.360 [get_ports {clk_p_lo clk_p_hi}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {cmos_ctl3}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {cmos_ctl3}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~95~1}] -max 0.763 [get_ports {cs}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~95~1}] -min 0.360 [get_ports {cs}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~131~1}] -max 0.763 [get_ports {o_dm_lo[0] o_dm_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~131~1}] -min 0.360 [get_ports {o_dm_lo[0] o_dm_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~83~1}] -max 0.763 [get_ports {o_dm_lo[1] o_dm_hi[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~83~1}] -min 0.360 [get_ports {o_dm_lo[1] o_dm_hi[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {dsi_pwm_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {dsi_pwm_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {dsi_resetn_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {dsi_resetn_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_blen_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_blen_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_de_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_de_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_hs_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_hs_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_pwm_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_pwm_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_rst_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_rst_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_vs_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_vs_o}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~73~1}] -max 0.763 [get_ports {odt}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~73~1}] -min 0.360 [get_ports {odt}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~48}] -max 0.763 [get_ports {ras}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~48}] -min 0.360 [get_ports {ras}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~139}] -max 0.763 [get_ports {reset}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~139}] -min 0.360 [get_ports {reset}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {spi_sck_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {spi_sck_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {spi_ssn_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {spi_ssn_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {uart_tx_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {uart_tx_o}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~94}] -max 0.763 [get_ports {we}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~94}] -min 0.360 [get_ports {we}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl0_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl0_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl0_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl0_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl0_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl0_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl1_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl1_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl1_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl1_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_ctl1_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_ctl1_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_scl_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_scl_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_scl_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_scl_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_scl_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_scl_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_sda_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_sda_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_sda_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_sda_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {csi_sda_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {csi_sda_oe}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~148~1}] -max 0.914 [get_ports {i_dq_lo[0] i_dq_hi[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~148~1}] -min 0.776 [get_ports {i_dq_lo[0] i_dq_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~150~1}] -max 0.763 [get_ports {o_dq_lo[0] o_dq_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~150~1}] -min 0.360 [get_ports {o_dq_lo[0] o_dq_hi[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~150~1}] -max 0.763 [get_ports {o_dq_oe[0]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~150~1}] -min 0.360 [get_ports {o_dq_oe[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~157~1}] -max 0.914 [get_ports {i_dq_lo[1] i_dq_hi[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~157~1}] -min 0.776 [get_ports {i_dq_lo[1] i_dq_hi[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~159~1}] -max 0.763 [get_ports {o_dq_lo[1] o_dq_hi[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~159~1}] -min 0.360 [get_ports {o_dq_lo[1] o_dq_hi[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~159~1}] -max 0.763 [get_ports {o_dq_oe[1]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~159~1}] -min 0.360 [get_ports {o_dq_oe[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~164~1}] -max 0.914 [get_ports {i_dq_lo[2] i_dq_hi[2]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~164~1}] -min 0.776 [get_ports {i_dq_lo[2] i_dq_hi[2]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~166~1}] -max 0.763 [get_ports {o_dq_lo[2] o_dq_hi[2]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~166~1}] -min 0.360 [get_ports {o_dq_lo[2] o_dq_hi[2]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~166~1}] -max 0.763 [get_ports {o_dq_oe[2]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~166~1}] -min 0.360 [get_ports {o_dq_oe[2]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~149~1}] -max 0.914 [get_ports {i_dq_lo[3] i_dq_hi[3]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~149~1}] -min 0.776 [get_ports {i_dq_lo[3] i_dq_hi[3]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~151~1}] -max 0.763 [get_ports {o_dq_lo[3] o_dq_hi[3]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~151~1}] -min 0.360 [get_ports {o_dq_lo[3] o_dq_hi[3]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~151~1}] -max 0.763 [get_ports {o_dq_oe[3]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~151~1}] -min 0.360 [get_ports {o_dq_oe[3]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~189~1}] -max 0.914 [get_ports {i_dq_lo[4] i_dq_hi[4]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~189~1}] -min 0.776 [get_ports {i_dq_lo[4] i_dq_hi[4]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~191~1}] -max 0.763 [get_ports {o_dq_lo[4] o_dq_hi[4]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~191~1}] -min 0.360 [get_ports {o_dq_lo[4] o_dq_hi[4]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~191~1}] -max 0.763 [get_ports {o_dq_oe[4]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~191~1}] -min 0.360 [get_ports {o_dq_oe[4]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~156~1}] -max 0.914 [get_ports {i_dq_lo[5] i_dq_hi[5]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~156~1}] -min 0.776 [get_ports {i_dq_lo[5] i_dq_hi[5]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~158~1}] -max 0.763 [get_ports {o_dq_lo[5] o_dq_hi[5]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~158~1}] -min 0.360 [get_ports {o_dq_lo[5] o_dq_hi[5]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~158~1}] -max 0.763 [get_ports {o_dq_oe[5]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~158~1}] -min 0.360 [get_ports {o_dq_oe[5]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~165~1}] -max 0.914 [get_ports {i_dq_lo[6] i_dq_hi[6]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~165~1}] -min 0.776 [get_ports {i_dq_lo[6] i_dq_hi[6]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~167~1}] -max 0.763 [get_ports {o_dq_lo[6] o_dq_hi[6]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~167~1}] -min 0.360 [get_ports {o_dq_lo[6] o_dq_hi[6]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~167~1}] -max 0.763 [get_ports {o_dq_oe[6]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~167~1}] -min 0.360 [get_ports {o_dq_oe[6]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~188~1}] -max 0.914 [get_ports {i_dq_lo[7] i_dq_hi[7]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~188~1}] -min 0.776 [get_ports {i_dq_lo[7] i_dq_hi[7]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~190~1}] -max 0.763 [get_ports {o_dq_lo[7] o_dq_hi[7]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~190~1}] -min 0.360 [get_ports {o_dq_lo[7] o_dq_hi[7]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~190~1}] -max 0.763 [get_ports {o_dq_oe[7]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~190~1}] -min 0.360 [get_ports {o_dq_oe[7]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~82~1}] -max 0.914 [get_ports {i_dq_lo[8] i_dq_hi[8]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~82~1}] -min 0.776 [get_ports {i_dq_lo[8] i_dq_hi[8]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~84~1}] -max 0.763 [get_ports {o_dq_lo[8] o_dq_hi[8]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~84~1}] -min 0.360 [get_ports {o_dq_lo[8] o_dq_hi[8]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~84~1}] -max 0.763 [get_ports {o_dq_oe[8]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~84~1}] -min 0.360 [get_ports {o_dq_oe[8]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~39~1}] -max 0.914 [get_ports {i_dq_lo[9] i_dq_hi[9]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~39~1}] -min 0.776 [get_ports {i_dq_lo[9] i_dq_hi[9]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~41~1}] -max 0.763 [get_ports {o_dq_lo[9] o_dq_hi[9]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~41~1}] -min 0.360 [get_ports {o_dq_lo[9] o_dq_hi[9]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~41~1}] -max 0.763 [get_ports {o_dq_oe[9]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~41~1}] -min 0.360 [get_ports {o_dq_oe[9]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~22~1}] -max 0.914 [get_ports {i_dq_lo[10] i_dq_hi[10]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~22~1}] -min 0.776 [get_ports {i_dq_lo[10] i_dq_hi[10]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~24~1}] -max 0.763 [get_ports {o_dq_lo[10] o_dq_hi[10]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~24~1}] -min 0.360 [get_ports {o_dq_lo[10] o_dq_hi[10]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~24~1}] -max 0.763 [get_ports {o_dq_oe[10]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~24~1}] -min 0.360 [get_ports {o_dq_oe[10]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~92~1}] -max 0.914 [get_ports {i_dq_lo[11] i_dq_hi[11]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~92~1}] -min 0.776 [get_ports {i_dq_lo[11] i_dq_hi[11]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~94~1}] -max 0.763 [get_ports {o_dq_lo[11] o_dq_hi[11]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~94~1}] -min 0.360 [get_ports {o_dq_lo[11] o_dq_hi[11]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~94~1}] -max 0.763 [get_ports {o_dq_oe[11]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~94~1}] -min 0.360 [get_ports {o_dq_oe[11]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~23~1}] -max 0.914 [get_ports {i_dq_lo[12] i_dq_hi[12]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~23~1}] -min 0.776 [get_ports {i_dq_lo[12] i_dq_hi[12]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~25~1}] -max 0.763 [get_ports {o_dq_lo[12] o_dq_hi[12]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~25~1}] -min 0.360 [get_ports {o_dq_lo[12] o_dq_hi[12]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~25~1}] -max 0.763 [get_ports {o_dq_oe[12]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~25~1}] -min 0.360 [get_ports {o_dq_oe[12]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~38~1}] -max 0.914 [get_ports {i_dq_lo[13] i_dq_hi[13]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~38~1}] -min 0.776 [get_ports {i_dq_lo[13] i_dq_hi[13]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~40~1}] -max 0.763 [get_ports {o_dq_lo[13] o_dq_hi[13]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~40~1}] -min 0.360 [get_ports {o_dq_lo[13] o_dq_hi[13]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~40~1}] -max 0.763 [get_ports {o_dq_oe[13]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~40~1}] -min 0.360 [get_ports {o_dq_oe[13]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~11~1}] -max 0.914 [get_ports {i_dq_lo[14] i_dq_hi[14]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~11~1}] -min 0.776 [get_ports {i_dq_lo[14] i_dq_hi[14]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~13~1}] -max 0.763 [get_ports {o_dq_lo[14] o_dq_hi[14]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~13~1}] -min 0.360 [get_ports {o_dq_lo[14] o_dq_hi[14]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~13~1}] -max 0.763 [get_ports {o_dq_oe[14]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~13~1}] -min 0.360 [get_ports {o_dq_oe[14]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~12~1}] -max 0.914 [get_ports {i_dq_lo[15] i_dq_hi[15]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~12~1}] -min 0.776 [get_ports {i_dq_lo[15] i_dq_hi[15]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~14~1}] -max 0.763 [get_ports {o_dq_lo[15] o_dq_hi[15]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~14~1}] -min 0.360 [get_ports {o_dq_lo[15] o_dq_hi[15]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~14~1}] -max 0.763 [get_ports {o_dq_oe[15]}]
set_output_delay -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~14~1}] -min 0.360 [get_ports {o_dq_oe[15]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~172~1}] -max 0.914 [get_ports {i_dqs_lo[0] i_dqs_hi[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~172~1}] -min 0.776 [get_ports {i_dqs_lo[0] i_dqs_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~174~1}] -max 0.763 [get_ports {o_dqs_lo[0] o_dqs_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~174~1}] -min 0.360 [get_ports {o_dqs_lo[0] o_dqs_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~174~1}] -max 0.763 [get_ports {o_dqs_oe[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~174~1}] -min 0.360 [get_ports {o_dqs_oe[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~30~1}] -max 0.914 [get_ports {i_dqs_lo[1] i_dqs_hi[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~30~1}] -min 0.776 [get_ports {i_dqs_lo[1] i_dqs_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~32~1}] -max 0.763 [get_ports {o_dqs_lo[1] o_dqs_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~32~1}] -min 0.360 [get_ports {o_dqs_lo[1] o_dqs_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~32~1}] -max 0.763 [get_ports {o_dqs_oe[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~32~1}] -min 0.360 [get_ports {o_dqs_oe[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~173~1}] -max 0.914 [get_ports {i_dqs_n_lo[0] i_dqs_n_hi[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~173~1}] -min 0.776 [get_ports {i_dqs_n_lo[0] i_dqs_n_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~175~1}] -max 0.763 [get_ports {o_dqs_n_lo[0] o_dqs_n_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~175~1}] -min 0.360 [get_ports {o_dqs_n_lo[0] o_dqs_n_hi[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~175~1}] -max 0.763 [get_ports {o_dqs_n_oe[0]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~175~1}] -min 0.360 [get_ports {o_dqs_n_oe[0]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~31~1}] -max 0.914 [get_ports {i_dqs_n_lo[1] i_dqs_n_hi[1]}]
set_input_delay -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~31~1}] -min 0.776 [get_ports {i_dqs_n_lo[1] i_dqs_n_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~33~1}] -max 0.763 [get_ports {o_dqs_n_lo[1] o_dqs_n_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~33~1}] -min 0.360 [get_ports {o_dqs_n_lo[1] o_dqs_n_hi[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~33~1}] -max 0.763 [get_ports {o_dqs_n_oe[1]}]
set_output_delay -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~33~1}] -min 0.360 [get_ports {o_dqs_n_oe[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_i[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_i[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_b7_0_oe[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_b7_0_oe[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_i[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_i[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_g7_0_oe[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_g7_0_oe[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[0]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[0]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[1]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[1]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[2]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[2]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[3]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[3]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[4]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[4]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[5]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[5]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[6]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[6]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_i[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_i[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_o[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_r7_0_oe[7]}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_r7_0_oe[7]}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_int_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_int_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_int_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_int_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_int_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_int_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_scl_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_scl_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_scl_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_scl_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_scl_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_scl_oe}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_sda_i}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_sda_i}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_sda_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_sda_o}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {lcd_tp_sda_oe}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {lcd_tp_sda_oe}]

# LVDS Tx Constraints
#######################
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -max 0.878 [get_ports {hdmi_txc_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -min 0.360 [get_ports {hdmi_txc_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -max 0.878 [get_ports {hdmi_txc_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -min 0.360 [get_ports {hdmi_txc_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -max 0.878 [get_ports {hdmi_txd0_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -min 0.360 [get_ports {hdmi_txd0_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -max 0.878 [get_ports {hdmi_txd0_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -min 0.360 [get_ports {hdmi_txd0_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -max 0.878 [get_ports {hdmi_txd1_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -min 0.360 [get_ports {hdmi_txd1_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -max 0.878 [get_ports {hdmi_txd1_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -min 0.360 [get_ports {hdmi_txd1_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -max 0.878 [get_ports {hdmi_txd2_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -min 0.360 [get_ports {hdmi_txd2_o[*]}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -max 0.878 [get_ports {hdmi_txd2_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -min 0.360 [get_ports {hdmi_txd2_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -max 0.878 [get_ports {lvds_txc_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -min 0.360 [get_ports {lvds_txc_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -max 0.878 [get_ports {lvds_txc_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -min 0.360 [get_ports {lvds_txc_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -max 0.878 [get_ports {lvds_txd0_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -min 0.360 [get_ports {lvds_txd0_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -max 0.878 [get_ports {lvds_txd0_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -min 0.360 [get_ports {lvds_txd0_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -max 0.878 [get_ports {lvds_txd1_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -min 0.360 [get_ports {lvds_txd1_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -max 0.878 [get_ports {lvds_txd1_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -min 0.360 [get_ports {lvds_txd1_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -max 0.878 [get_ports {lvds_txd2_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -min 0.360 [get_ports {lvds_txd2_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -max 0.878 [get_ports {lvds_txd2_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -min 0.360 [get_ports {lvds_txd2_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -max 0.878 [get_ports {lvds_txd3_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -min 0.360 [get_ports {lvds_txd3_o[*]}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -max 0.878 [get_ports {lvds_txd3_oe}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -min 0.360 [get_ports {lvds_txd3_oe}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -max 0.920 [get_ports {hdmi_txc_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}] -min 0.335 [get_ports {hdmi_txc_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -max 0.920 [get_ports {hdmi_txd0_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}] -min 0.335 [get_ports {hdmi_txd0_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -max 0.920 [get_ports {hdmi_txd1_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}] -min 0.335 [get_ports {hdmi_txd1_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -max 0.920 [get_ports {hdmi_txd2_rst_o}]
set_output_delay -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}] -min 0.335 [get_ports {hdmi_txd2_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -max 0.920 [get_ports {lvds_txc_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}] -min 0.335 [get_ports {lvds_txc_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -max 0.920 [get_ports {lvds_txd0_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}] -min 0.335 [get_ports {lvds_txd0_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -max 0.920 [get_ports {lvds_txd1_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}] -min 0.335 [get_ports {lvds_txd1_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -max 0.920 [get_ports {lvds_txd2_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}] -min 0.335 [get_ports {lvds_txd2_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -max 0.920 [get_ports {lvds_txd3_rst_o}]
set_output_delay -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}] -min 0.335 [get_ports {lvds_txd3_rst_o}]

# MIPI RX Lane Constraints
############################
# create_clock -period <USER_PERIOD> [get_ports {csi_rxc_i}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~278}] -max 1.025 [get_ports {csi_rxd0_fifo_rd_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~278}] -min 0.570 [get_ports {csi_rxd0_fifo_rd_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~278}] -max 0.815 [get_ports {csi_rxd0_rst_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~278}] -min 0.360 [get_ports {csi_rxd0_rst_o}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~278}] -max 1.130 [get_ports {csi_rxd0_fifo_empty_i}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~278}] -min 0.920 [get_ports {csi_rxd0_fifo_empty_i}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~278}] -max 0.987 [get_ports {csi_rxd0_hs_i[*]}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~278}] -min 0.825 [get_ports {csi_rxd0_hs_i[*]}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~294}] -max 1.025 [get_ports {csi_rxd1_fifo_rd_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~294}] -min 0.570 [get_ports {csi_rxd1_fifo_rd_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~294}] -max 0.815 [get_ports {csi_rxd1_rst_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~294}] -min 0.360 [get_ports {csi_rxd1_rst_o}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~294}] -max 1.130 [get_ports {csi_rxd1_fifo_empty_i}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~294}] -min 0.920 [get_ports {csi_rxd1_fifo_empty_i}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~294}] -max 0.987 [get_ports {csi_rxd1_hs_i[*]}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~294}] -min 0.825 [get_ports {csi_rxd1_hs_i[*]}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~245}] -max 1.025 [get_ports {csi_rxd2_fifo_rd_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~245}] -min 0.570 [get_ports {csi_rxd2_fifo_rd_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~245}] -max 0.815 [get_ports {csi_rxd2_rst_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~245}] -min 0.360 [get_ports {csi_rxd2_rst_o}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~245}] -max 1.130 [get_ports {csi_rxd2_fifo_empty_i}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~245}] -min 0.920 [get_ports {csi_rxd2_fifo_empty_i}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~245}] -max 0.987 [get_ports {csi_rxd2_hs_i[*]}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~245}] -min 0.825 [get_ports {csi_rxd2_hs_i[*]}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~255}] -max 1.025 [get_ports {csi_rxd3_fifo_rd_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~255}] -min 0.570 [get_ports {csi_rxd3_fifo_rd_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~255}] -max 0.815 [get_ports {csi_rxd3_rst_o}]
set_output_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~255}] -min 0.360 [get_ports {csi_rxd3_rst_o}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~255}] -max 1.130 [get_ports {csi_rxd3_fifo_empty_i}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~255}] -min 0.920 [get_ports {csi_rxd3_fifo_empty_i}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~255}] -max 0.987 [get_ports {csi_rxd3_hs_i[*]}]
set_input_delay -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~255}] -min 0.825 [get_ports {csi_rxd3_hs_i[*]}]

# MIPI TX Lane Constraints
############################
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~200}] -max 0.978 [get_ports {dsi_txc_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~200}] -min 0.460 [get_ports {dsi_txc_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~200}] -max 0.915 [get_ports {dsi_txc_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~200}] -min 0.460 [get_ports {dsi_txc_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~187}] -max 0.978 [get_ports {dsi_txd0_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~187}] -min 0.460 [get_ports {dsi_txd0_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~187}] -max 0.915 [get_ports {dsi_txd0_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~187}] -min 0.460 [get_ports {dsi_txd0_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~215}] -max 0.978 [get_ports {dsi_txd1_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~215}] -min 0.460 [get_ports {dsi_txd1_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~215}] -max 0.915 [get_ports {dsi_txd1_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~215}] -min 0.460 [get_ports {dsi_txd1_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~230}] -max 0.978 [get_ports {dsi_txd2_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~230}] -min 0.460 [get_ports {dsi_txd2_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~230}] -max 0.915 [get_ports {dsi_txd2_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~230}] -min 0.460 [get_ports {dsi_txd2_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~175}] -max 0.978 [get_ports {dsi_txd3_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~175}] -min 0.460 [get_ports {dsi_txd3_hs_o[*]}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~175}] -max 0.915 [get_ports {dsi_txd3_rst_o}]
set_output_delay -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~175}] -min 0.460 [get_ports {dsi_txd3_rst_o}]

# Clockout Interface
######################
# cmos_data[0] -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~1}]
# cmos_data[1] -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~6~1}]
# cmos_data[2] -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~12~322}]
# cmos_data[3] -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~214~322}]
# cmos_data[4] -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~4~322}]
# cmos_data[5] -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~212~322}]
# cmos_data[6] -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~10~322}]
# cmos_data[7] -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~197~322}]
# cmos_href -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~216~322}]
# cmos_vsync -clock cmos_pclk -reference_pin [get_ports {cmos_pclk~CLKOUT~18~322}]
# cmos_xclk -clock clk_27m -reference_pin [get_ports {clk_27m~CLKOUT~209~322}]
# addr[0] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~95}]
# addr[10] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~138}]
# addr[11] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~60}]
# addr[12] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~35}]
# addr[13] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~71}]
# addr[14] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~109}]
# addr[15] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~143~1}]
# addr[1] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~36}]
# addr[2] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~70}]
# addr[3] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~50~1}]
# addr[4] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~25}]
# addr[5] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~142~1}]
# addr[6] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~59}]
# addr[7] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~110}]
# addr[8] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~26}]
# addr[9] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~127}]
# ba[0] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~74~1}]
# ba[1] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~132~1}]
# ba[2] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~126}]
# cas -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~49}]
# cke -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~123~1}]
# clk_n -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~150}]
# clk_p -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~149}]
# cs -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~95~1}]
# csi_rxd0 -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~278}]
# csi_rxd1 -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~294}]
# csi_rxd2 -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~245}]
# csi_rxd3 -clock csi_rxc_i -reference_pin [get_ports {csi_rxc_i~CLKOUT~218~255}]
# dm[0] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~131~1}]
# dm[1] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~83~1}]
# dq[0] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~148~1}]
# dq[0] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~150~1}]
# dq[10] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~22~1}]
# dq[10] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~24~1}]
# dq[11] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~92~1}]
# dq[11] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~94~1}]
# dq[12] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~23~1}]
# dq[12] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~25~1}]
# dq[13] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~38~1}]
# dq[13] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~40~1}]
# dq[14] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~11~1}]
# dq[14] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~13~1}]
# dq[15] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~12~1}]
# dq[15] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~14~1}]
# dq[1] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~157~1}]
# dq[1] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~159~1}]
# dq[2] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~164~1}]
# dq[2] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~166~1}]
# dq[3] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~149~1}]
# dq[3] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~151~1}]
# dq[4] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~189~1}]
# dq[4] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~191~1}]
# dq[5] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~156~1}]
# dq[5] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~158~1}]
# dq[6] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~165~1}]
# dq[6] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~167~1}]
# dq[7] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~188~1}]
# dq[7] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~190~1}]
# dq[8] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~82~1}]
# dq[8] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~84~1}]
# dq[9] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~39~1}]
# dq[9] -clock twd_clk -reference_pin [get_ports {twd_clk~CLKOUT~41~1}]
# dqs[0] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~172~1}]
# dqs[0] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~174~1}]
# dqs[1] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~30~1}]
# dqs[1] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~32~1}]
# dqs_n[0] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~173~1}]
# dqs_n[0] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~175~1}]
# dqs_n[1] -clock tac_clk -reference_pin [get_ports {tac_clk~CLKOUT~31~1}]
# dqs_n[1] -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~33~1}]
# dsi_txc -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~200}]
# dsi_txd0 -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~187}]
# dsi_txd1 -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~215}]
# dsi_txd2 -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~230}]
# dsi_txd3 -clock dsi_byteclk_i -reference_pin [get_ports {dsi_byteclk_i~CLKOUT~218~175}]
# hdmi_txc -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~63~322}]
# hdmi_txd0 -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~195~322}]
# hdmi_txd1 -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~31~322}]
# hdmi_txd2 -clock clk_pixel -reference_pin [get_ports {clk_pixel~CLKOUT~55~322}]
# lvds_txc -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~39~322}]
# lvds_txd0 -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~163~322}]
# lvds_txd1 -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~155~322}]
# lvds_txd2 -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~71~322}]
# lvds_txd3 -clock clk_lvds_1x -reference_pin [get_ports {clk_lvds_1x~CLKOUT~171~322}]
# odt -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~73~1}]
# ras -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~48}]
# reset -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~139}]
# we -clock tdqss_clk -reference_pin [get_ports {tdqss_clk~CLKOUT~218~94}]
