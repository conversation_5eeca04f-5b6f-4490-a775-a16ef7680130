           
           Efinix FPGA Placement and Routing.
           Version: 2025.1.110.2.15 
           Compiled: Jun 21 2025.
           
           Copyright (C) 2013 - 2025 Efinix, Inc. All rights reserved.

           
           The Tool Is Based on VPR of University of Toronto,
           a free open source code under MIT license.
           
           ***** Beginning stage netlist pre-processing ... *****
INFO     : Successfully Read in Verific binary Netlist dump file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.vdb".
INFO     : ***** Beginning VDB Netlist Checker ... *****
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(11): Input/inout port clk_24m is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(12): Input/inout port clk_25m is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(21): Input/inout port clk_pixel_2x is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(22): Input/inout port clk_pixel_10x is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(30): Input/inout port dsi_refclk_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(31): Input/inout port dsi_byteclk_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(32): Input/inout port dsi_serclk_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(33): Input/inout port dsi_txcclk_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(61): Input/inout port clk_lvds_7x is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(62): Input/inout port clk_27m is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(63): Input/inout port clk_54m is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(97): Input/inout port i_dqs_n_hi[1] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(98): Input/inout port i_dqs_n_lo[1] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(121): Input/inout port csi_ctl0_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(125): Input/inout port csi_ctl1_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(129): Input/inout port csi_scl_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(133): Input/inout port csi_sda_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(156): Input/inout port csi_rxd1_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(157): Input/inout port csi_rxd1_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(158): Input/inout port csi_rxd1_hs_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(165): Input/inout port csi_rxd2_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(166): Input/inout port csi_rxd2_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(167): Input/inout port csi_rxd2_hs_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(174): Input/inout port csi_rxd3_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(175): Input/inout port csi_rxd3_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(176): Input/inout port csi_rxd3_hs_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(235): Input/inout port dsi_txd0_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(236): Input/inout port dsi_txd0_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(237): Input/inout port dsi_txd1_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(238): Input/inout port dsi_txd1_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(239): Input/inout port dsi_txd2_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(240): Input/inout port dsi_txd2_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(241): Input/inout port dsi_txd3_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(242): Input/inout port dsi_txd3_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(247): Input/inout port uart_rx_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(257): Input/inout port cmos_sdat_IN is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(262): Input/inout port cmos_pclk is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(263): Input/inout port cmos_vsync is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(264): Input/inout port cmos_href is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(265): Input/inout port cmos_data[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(266): Input/inout port cmos_ctl1 is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(316): Input/inout port lcd_tp_sda_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(320): Input/inout port lcd_tp_scl_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(324): Input/inout port lcd_tp_int_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(344): Input/inout port lcd_b7_0_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(345): Input/inout port lcd_g7_0_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(346): Input/inout port lcd_r7_0_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : CE port of EFX_DSP48 instance mult_184 is permanently disabled [VDB-8002]
WARNING  : Found 99 warnings in the post-synthesis netlist.
INFO     : VDB Netlist Checker took 0.0192739 seconds.
INFO     : 	VDB Netlist Checker took 0.03125 seconds (approximately) in total CPU time.
INFO     : VDB Netlist Checker virtual memory usage: begin = 119.228 MB, end = 119.932 MB, delta = 0.704 MB
INFO     : 	VDB Netlist Checker peak virtual memory usage = 119.94 MB
INFO     : VDB Netlist Checker resident set memory usage: begin = 95.804 MB, end = 96.928 MB, delta = 1.124 MB
INFO     : 	VDB Netlist Checker peak resident set memory usage = 96.932 MB
INFO     : ***** Ending VDB Netlist Checker ... *****
INFO     : Reading core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Successfully read core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Net cmos_pclk promoted to global due to interface constraints.
INFO     : Net clk_pixel_10x promoted to global due to interface constraints.
INFO     : Net clk_lvds_7x promoted to global due to interface constraints.
INFO     : Net clk_27m promoted to global due to interface constraints.
INFO     : Net dsi_serclk_i promoted to global due to interface constraints.
INFO     : Net dsi_byteclk_i promoted to global due to interface constraints.
INFO     : Net dsi_txcclk_i promoted to global due to interface constraints.
INFO     : Found 0 constant generator nets.
INFO     : Pass 0: Swept away 0 nets with no fanout.
INFO     : logical_block #0(clk_24m) has no fanout.
INFO     : Removing input.
INFO     : logical_block #1(clk_25m) has no fanout.
INFO     : Removing input.
INFO     : logical_block #5(clk_pixel_2x) has no fanout.
INFO     : Removing input.
INFO     : logical_block #9(dsi_refclk_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #33(clk_54m) has no fanout.
INFO     : Removing input.
INFO     : logical_block #149(i_dqs_n_hi[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #150(i_dqs_n_hi[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #151(i_dqs_n_lo[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #152(i_dqs_n_lo[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #171(csi_ctl0_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #174(csi_ctl1_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #177(csi_scl_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #180(csi_sda_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #202(csi_rxd1_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #203(csi_rxd1_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #204(csi_rxd1_hs_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #205(csi_rxd1_hs_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #206(csi_rxd1_hs_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #207(csi_rxd1_hs_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #208(csi_rxd1_hs_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #209(csi_rxd1_hs_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #210(csi_rxd1_hs_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #211(csi_rxd1_hs_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #215(csi_rxd2_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #216(csi_rxd2_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #217(csi_rxd2_hs_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #218(csi_rxd2_hs_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #219(csi_rxd2_hs_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #220(csi_rxd2_hs_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #221(csi_rxd2_hs_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #222(csi_rxd2_hs_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #223(csi_rxd2_hs_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #224(csi_rxd2_hs_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #228(csi_rxd3_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #229(csi_rxd3_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #230(csi_rxd3_hs_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #231(csi_rxd3_hs_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #232(csi_rxd3_hs_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #233(csi_rxd3_hs_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #234(csi_rxd3_hs_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #235(csi_rxd3_hs_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #236(csi_rxd3_hs_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #237(csi_rxd3_hs_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #310(dsi_txd0_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #311(dsi_txd0_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #312(dsi_txd1_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #313(dsi_txd1_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #314(dsi_txd2_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #315(dsi_txd2_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #316(dsi_txd3_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #317(dsi_txd3_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #318(uart_rx_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #327(cmos_sdat_IN) has no fanout.
INFO     : Removing input.
INFO     : logical_block #331(cmos_vsync) has no fanout.
INFO     : Removing input.
INFO     : logical_block #332(cmos_href) has no fanout.
INFO     : Removing input.
INFO     : logical_block #333(cmos_data[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #334(cmos_data[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #335(cmos_data[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #336(cmos_data[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #337(cmos_data[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #338(cmos_data[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #339(cmos_data[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #340(cmos_data[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #341(cmos_ctl1) has no fanout.
INFO     : Removing input.
INFO     : logical_block #439(lcd_tp_sda_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #442(lcd_tp_scl_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #445(lcd_tp_int_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #500(lcd_b7_0_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #501(lcd_b7_0_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #502(lcd_b7_0_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #503(lcd_b7_0_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #504(lcd_b7_0_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #505(lcd_b7_0_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #506(lcd_b7_0_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #507(lcd_b7_0_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #508(lcd_g7_0_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #509(lcd_g7_0_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #510(lcd_g7_0_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #511(lcd_g7_0_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #512(lcd_g7_0_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #513(lcd_g7_0_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #514(lcd_g7_0_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #515(lcd_g7_0_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #516(lcd_r7_0_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #517(lcd_r7_0_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #518(lcd_r7_0_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #519(lcd_r7_0_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #520(lcd_r7_0_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #521(lcd_r7_0_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #522(lcd_r7_0_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #523(lcd_r7_0_i[0]) has no fanout.
INFO     : Removing input.
INFO     : Pass 0: Swept away 91 blocks with no fanout.
INFO     : Pass 1: Swept away 0 nets with no fanout.
INFO     : Pass 1: Swept away 0 blocks with no fanout.
INFO     : Swept away 0 nets and 91 blocks in total.
INFO     : Removed 0 LUT buffers
INFO     : Sweept away 91 nodes
INFO     : Successfully created VPR logical netlist from Verific binary DataBase file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.vdb".
INFO     : Netlist pre-processing took 0.149215 seconds.
INFO     : 	Netlist pre-processing took 0.125 seconds (approximately) in total CPU time.
INFO     : Netlist pre-processing virtual memory usage: begin = 119.228 MB, end = 119.932 MB, delta = 0.704 MB
INFO     : 	Netlist pre-processing peak virtual memory usage = 119.94 MB
INFO     : Netlist pre-processing resident set memory usage: begin = 95.392 MB, end = 97.344 MB, delta = 1.952 MB
INFO     : 	Netlist pre-processing peak resident set memory usage = 97.348 MB
           ***** Ending stage netlist pre-processing *****
           
INFO     : Load Global Network took 0.663184 seconds.
INFO     : 	Load Global Network took 0.125 seconds (approximately) in total CPU time.
INFO     : Load Global Network virtual memory usage: begin = 119.932 MB, end = 119.932 MB, delta = 0 MB
INFO     : 	Load Global Network peak virtual memory usage = 119.94 MB
INFO     : Load Global Network resident set memory usage: begin = 97.432 MB, end = 115.212 MB, delta = 17.78 MB
INFO     : 	Load Global Network peak resident set memory usage = 115.216 MB
INFO     : Reading C:/Efinity/2025.1/arch/.\rr_pb_hier_mapping.xml
           ***** Beginning stage pre-packing ... *****
           ***** Ending stage pre-packing *****
           
           ***** Beginning stage packing ... *****
INFO     : 	[PrepackAnalyzer] Cannot pack adder chain size=14, u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/add_29/i14
INFO     : Generate proto netlist for file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.net_proto" took 0.021 seconds
INFO     : Creating IO constraints file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.io_place'
INFO     : Packing took 0.05972 seconds.
INFO     : 	Packing took 0.0625 seconds (approximately) in total CPU time.
INFO     : Packing virtual memory usage: begin = 136.732 MB, end = 143.912 MB, delta = 7.18 MB
INFO     : 	Packing peak virtual memory usage = 143.912 MB
INFO     : Packing resident set memory usage: begin = 142.272 MB, end = 150.076 MB, delta = 7.804 MB
INFO     : 	Packing peak resident set memory usage = 150.08 MB
           ***** Ending stage packing *****
           
INFO     : Read proto netlist file D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.net_proto
INFO     : Read proto netlist for file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.net_proto" took 0.016 seconds
INFO     : Setup net and block data structure took 0.084 seconds
INFO     : Reading core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Successfully read core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
WARNING  : Found 99 warnings in the post-synthesis netlist.
INFO     : Reading core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Successfully read core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
           ***** Beginning stage packed netlist loading ... *****
INFO     : Read proto netlist file D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.net_proto
INFO     : Read proto netlist for file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.net_proto" took 0.014 seconds
INFO     : Setup net and block data structure took 0.125 seconds
INFO     : Packed netlist loading took 0.141651 seconds.
INFO     : 	Packed netlist loading took 0.140625 seconds (approximately) in total CPU time.
INFO     : Packed netlist loading virtual memory usage: begin = 215.576 MB, end = 215.576 MB, delta = 0 MB
INFO     : 	Packed netlist loading peak virtual memory usage = 215.576 MB
INFO     : Packed netlist loading resident set memory usage: begin = 221.48 MB, end = 221.524 MB, delta = 0.044 MB
INFO     : 	Packed netlist loading peak resident set memory usage = 221.528 MB
           ***** Ending stage packed netlist loading *****
           
           ***** Beginning stage pre-placement ... *****
           
           ***** Ending stage pre-placement *****
           
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:14] No valid object(s) found for 'dsi_refclk_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:14] No valid pin(s) found for clock
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:14] Unable to run 'create_clock' constraint due to warnings found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:24] No valid object(s) found for 'clk_pixel_2x'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:24] No valid pin(s) found for clock
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:24] Unable to run 'create_clock' constraint due to warnings found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:29] No valid object(s) found for 'clk_54m'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:29] No valid pin(s) found for clock
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:29] Unable to run 'create_clock' constraint due to warnings found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:35] set_clock_groups: Defaulting to -exclusive since none of the required option was specified
WARNING  : No ports matched 'cmos_data[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:78] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:78] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:79] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:79] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:80] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:80] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:81] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:81] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[2]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:82] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:82] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[2]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:83] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:83] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[3]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:84] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:84] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[3]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:85] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:85] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[4]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:86] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:86] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[4]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:87] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:87] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[5]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:88] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:88] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[5]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:89] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:89] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[6]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:90] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:90] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[6]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:91] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:91] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[7]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:92] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:92] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[7]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:93] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:93] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_href'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:94] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:94] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_href'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:95] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:95] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_vsync'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:96] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:96] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_vsync'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:97] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:97] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_lo[0]'
WARNING  : No ports matched 'i_dqs_n_hi[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:341] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:341] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_hi[0]'
WARNING  : No ports matched 'i_dqs_n_lo[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:342] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:342] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_lo[1]'
WARNING  : No ports matched 'i_dqs_n_hi[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:347] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:347] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_lo[1]'
WARNING  : No ports matched 'i_dqs_n_hi[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:348] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:348] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:576] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:576] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:577] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:577] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:578] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:578] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:579] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:579] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:580] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:580] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:581] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:581] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:582] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:582] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:583] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:583] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:584] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:584] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:585] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:585] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:586] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:586] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:587] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:587] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:588] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:588] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:589] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:589] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:590] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:590] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:591] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:591] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:592] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:592] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:593] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:593] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:594] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:594] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:595] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:595] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:596] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:596] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:597] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:597] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:598] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:598] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:599] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:599] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:600] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:600] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:601] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:601] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:602] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:602] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:603] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:603] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:604] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:604] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:605] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:605] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:606] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:606] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:607] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:607] Unable to run 'set_input_delay' constraint due to warnings found
INFO     : SDC file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc' parsed successfully.
INFO     : 15 clocks (including virtual clocks), 36 inputs and 232 outputs were constrained.
INFO     : Reading core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Successfully read core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Writing IO placement constraints to "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow\Ti60_Demo.interface.io"
INFO     : Reading placement constraints from 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow\Ti60_Demo.interface.io'.
INFO     : Reading placement constraints from 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.io_place'.
WARNING  : Clock driver, csi_rxc_i, should not directly drive output pad, led_o[5], in the core. Use the 'clkout' mode in GPIO instead.
INFO     : Reading core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Successfully read core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
WARNING  : Clock net led_o[5] should not drive a normal pad, led_o[5]. Clock should only drive clkout pads defined in Interface Designer.
INFO     : Found 235 synchronizers as follows: 
INFO     : 	Synchronizer 0: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i35_2
INFO     : 	Synchronizer 1: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i21_2
INFO     : 	Synchronizer 2: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i33_2
INFO     : 	Synchronizer 3: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i32_2
INFO     : 	Synchronizer 4: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i31_2
INFO     : 	Synchronizer 5: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i30_2
INFO     : 	Synchronizer 6: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i29_2
INFO     : 	Synchronizer 7: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i28_2
INFO     : 	Synchronizer 8: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i12_2
INFO     : 	Synchronizer 9: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i20_2
INFO     : 	Synchronizer 10: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i19_2
INFO     : 	Synchronizer 11: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i18_2
INFO     : 	Synchronizer 12: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i17_2
INFO     : 	Synchronizer 13: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i16_2
INFO     : 	Synchronizer 14: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i15_2
INFO     : 	Synchronizer 15: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i14_2
INFO     : 	Synchronizer 16: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i13_2
INFO     : 	Synchronizer 17: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i22_2
INFO     : 	Synchronizer 18: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i20_2
INFO     : 	Synchronizer 19: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i12_2
INFO     : 	Synchronizer 20: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i20_2
INFO     : 	Synchronizer 21: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i19_2
INFO     : 	Synchronizer 22: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i18_2
INFO     : 	Synchronizer 23: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i17_2
INFO     : 	Synchronizer 24: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i16_2
INFO     : 	Synchronizer 25: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i15_2
INFO     : 	Synchronizer 26: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i14_2
INFO     : 	Synchronizer 27: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i13_2
INFO     : 	Synchronizer 28: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i22_2
INFO     : 	Synchronizer 29: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i21_2
INFO     : 	Synchronizer 30: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i19_2
INFO     : 	Synchronizer 31: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i17_2
INFO     : 	Synchronizer 32: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i15_2
INFO     : 	Synchronizer 33: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i14_2
INFO     : 	Synchronizer 34: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i13_2
INFO     : 	Synchronizer 35: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i30_2
INFO     : 	Synchronizer 36: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i12_2
INFO     : 	Synchronizer 37: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][7]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][7]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[7]~FF
INFO     : 	Synchronizer 38: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i18_2
INFO     : 	Synchronizer 39: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i17_2
INFO     : 	Synchronizer 40: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i16_2
INFO     : 	Synchronizer 41: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i15_2
INFO     : 	Synchronizer 42: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i14_2
INFO     : 	Synchronizer 43: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i13_2
INFO     : 	Synchronizer 44: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i12_2
INFO     : 	Synchronizer 45: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i11_2
INFO     : 	Synchronizer 46: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/dff_9/i2_2
INFO     : 	Synchronizer 47: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][2]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][2]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[2]~FF
INFO     : 	Synchronizer 48: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i20_2
INFO     : 	Synchronizer 49: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][9]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][9]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[9]~FF
INFO     : 	Synchronizer 50: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][8]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][8]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[8]~FF
INFO     : 	Synchronizer 51: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][3]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][3]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[3]~FF
INFO     : 	Synchronizer 52: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][0]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][0]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[0]~FF
INFO     : 	Synchronizer 53: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][4]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][4]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[4]~FF
INFO     : 	Synchronizer 54: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][5]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][5]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[5]~FF
INFO     : 	Synchronizer 55: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][6]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][6]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[6]~FF
INFO     : 	Synchronizer 56: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][7]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][7]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[7]~FF
INFO     : 	Synchronizer 57: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/genblk2.genblk1.efx_resetsync_wr_rst/active_high.efx_resetsync_active_high/async_reg[0][0]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/wr_rst_int~FF
INFO     : 	Synchronizer 58: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/genblk2.genblk1.efx_resetsync_rd_rst/active_high.efx_resetsync_active_high/async_reg[0][0]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/rd_rst_int~FF
INFO     : 	Synchronizer 59: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][10]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][10]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[10]~FF
INFO     : 	Synchronizer 60: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][6]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[6]~FF
INFO     : 	Synchronizer 61: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][9]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][9]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[9]~FF
INFO     : 	Synchronizer 62: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][4]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][4]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[4]~FF
INFO     : 	Synchronizer 63: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][5]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][5]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[5]~FF
INFO     : 	Synchronizer 64: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][6]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][6]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[6]~FF
INFO     : 	Synchronizer 65: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][1]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[1]~FF
INFO     : 	Synchronizer 66: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][0]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[0]~FF
INFO     : 	Synchronizer 67: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][3]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[3]~FF
INFO     : 	Synchronizer 68: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][4]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[4]~FF
INFO     : 	Synchronizer 69: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][5]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[5]~FF
INFO     : 	Synchronizer 70: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/genblk2.genblk1.efx_resetsync_wr_rst/active_high.efx_resetsync_active_high/async_reg[0][0]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/wr_rst_int~FF
INFO     : 	Synchronizer 71: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/genblk2.genblk1.efx_resetsync_rd_rst/active_high.efx_resetsync_active_high/async_reg[0][0]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/rd_rst_int~FF
INFO     : 	Synchronizer 72: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/genblk2.rd_rst[0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/genblk2.rd_rst[1]~FF
INFO     : 	Synchronizer 73: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_26/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW28_int~FF
INFO     : 	Synchronizer 74: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_24/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW16_int~FF
INFO     : 	Synchronizer 75: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_23/async_reg[0][0]~FF
INFO     :  w_csi_rx_vsync0~FF
INFO     : 	Synchronizer 76: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_20/async_reg[0][1]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/vc_sync[1]~FF
INFO     : 	Synchronizer 77: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][2]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][2]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[2]~FF
INFO     : 	Synchronizer 78: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 79: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/dq_pipe_ap[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/dq_pipe_ap[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/dq_pipe_ap[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/dq_pipe_ap[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/dq_pipe_ap[7]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/dq_pipe_ap[8]~FF
INFO     : 	Synchronizer 80: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 81: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][11]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][11]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[11]~FF
INFO     : 	Synchronizer 82: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 83: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][8]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][8]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[8]~FF
INFO     : 	Synchronizer 84: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 85: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[0][3]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.xrd2wr_addr_sync/async_reg[1][3]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.raddr_cntr_gry_sync[3]~FF
INFO     : 	Synchronizer 86: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 87: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 88: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 89: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 90: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][12]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[12]~FF
INFO     : 	Synchronizer 91: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_25/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW24_int~FF
INFO     : 	Synchronizer 92: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 93: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][11]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[11]~FF
INFO     : 	Synchronizer 94: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 95: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 96: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 97: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 98: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 99: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 100: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_14/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/YUV_420_8_int~FF
INFO     : 	Synchronizer 101: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/popToPushGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 102: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_13/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/YUV_420_8_legacy_int~FF
INFO     : 	Synchronizer 103: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 104: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_12/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/YUV_422_10_int~FF
INFO     : 	Synchronizer 105: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 106: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 107: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][10]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[10]~FF
INFO     : 	Synchronizer 108: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 109: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][9]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[9]~FF
INFO     : 	Synchronizer 110: 
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/genblk2.genblk1.efx_resetsync_rd_rst/active_high.efx_resetsync_active_high/async_reg[0][0]~FF
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/rd_rst_int~FF
INFO     : 	Synchronizer 111: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 112: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][8]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[8]~FF
INFO     : 	Synchronizer 113: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/popToPushGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 114: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][7]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[7]~FF
INFO     : 	Synchronizer 115: 
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][2]~FF
INFO     :  u_afifo_w32r8_reshape/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[2]~FF
INFO     : 	Synchronizer 116: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 117: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 118: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i27_2
INFO     : 	Synchronizer 119: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 120: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i36_2
INFO     : 	Synchronizer 121: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 122: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 123: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i29_2
INFO     : 	Synchronizer 124: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 125: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_20/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/vc_sync[0]~FF
INFO     : 	Synchronizer 126: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 127: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/pushToPopGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 128: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i28_2
INFO     : 	Synchronizer 129: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 130: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_18/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/embed_8bit_nonvideo_int~FF
INFO     : 	Synchronizer 131: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 132: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i26_2
INFO     : 	Synchronizer 133: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 134: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_17/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/user_define_8bit_int~FF
INFO     : 	Synchronizer 135: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 136: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i25_2
INFO     : 	Synchronizer 137: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 138: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i24_2
INFO     : 	Synchronizer 139: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_16/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/gen_long_pkt_int~FF
INFO     : 	Synchronizer 140: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 141: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i23_2
INFO     : 	Synchronizer 142: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 143: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 144: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i22_2
INFO     : 	Synchronizer 145: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_15/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/YUV_420_10_int~FF
INFO     : 	Synchronizer 146: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/popToPushGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 147: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 148: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i21_2
INFO     : 	Synchronizer 149: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 150: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/ram_rd_ack_ap[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/ram_rd_ack_ap[1]~FF
INFO     : 	Synchronizer 151: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 152: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i27_2
INFO     : 	Synchronizer 153: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/pushToPopGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 154: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 155: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 156: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 157: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 158: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i18_2
INFO     : 	Synchronizer 159: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/r_wr_start_p[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/r_wr_start_p[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/r_wr_start_p[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/r_wr_start_p[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/r_wr_start_p[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/r_wr_start_p[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/r_wr_start_p[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/r_wr_start_p[7]~FF
INFO     : 	Synchronizer 160: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 161: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 162: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/genblk2.rd_rst[0]~FF
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/genblk2.rd_rst[1]~FF
INFO     : 	Synchronizer 163: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 164: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/genblk2.wr_rst[0]~FF
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/genblk2.wr_rst[1]~FF
INFO     : 	Synchronizer 165: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 166: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 167: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 168: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ar/popToPushGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 169: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 170: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 171: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 172: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_wr/pushToPopGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 173: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_aw/popToPushGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 174: 
INFO     :  u_axi4_ctrl/Gen_FIFO_W32.u_W0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i34_2
INFO     : 	Synchronizer 175: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 176: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 177: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 178: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rdpush/pushToPopGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 179: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_11/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/YUV_422_8_int~FF
INFO     : 	Synchronizer 180: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 181: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 182: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 183: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk2.lane_aligner_inst/genblk1[0].u1_inst/genblk2.a_rst[0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk2.lane_aligner_inst/genblk1[0].u1_inst/genblk2.a_rst[1]~FF
INFO     : 	Synchronizer 184: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 185: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 186: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 187: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_ack/pushToPopGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 188: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 189: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 190: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 191: 
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][3]~FF
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[3]~FF
INFO     : 	Synchronizer 192: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 193: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_0/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW6_int~FF
INFO     : 	Synchronizer 194: 
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][0]~FF
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[0]~FF
INFO     : 	Synchronizer 195: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_1/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW7_int~FF
INFO     : 	Synchronizer 196: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_2/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW8_int~FF
INFO     : 	Synchronizer 197: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_3/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW10_int~FF
INFO     : 	Synchronizer 198: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_4/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW12_int~FF
INFO     : 	Synchronizer 199: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc/buffers_0[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc_io_dataOut[1]~FF
INFO     : 	Synchronizer 200: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc/buffers_0[2]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc_io_dataOut[2]~FF
INFO     : 	Synchronizer 201: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_5/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW14_int~FF
INFO     : 	Synchronizer 202: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc/buffers_0[3]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc_io_dataOut[3]~FF
INFO     : 	Synchronizer 203: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/syncreg_2/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/RxStopState_sync[0]~FF
INFO     : 	Synchronizer 204: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 205: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_6/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RAW20_int~FF
INFO     : 	Synchronizer 206: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 207: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/ram_rd_ack_a[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/ram_rd_ack_a[1]~FF
INFO     : 	Synchronizer 208: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc/buffers_0[6]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc_io_dataOut[6]~FF
INFO     : 	Synchronizer 209: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_7/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RGB444_int~FF
INFO     : 	Synchronizer 210: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/get_wr_ack[2]~FF
INFO     : 	Synchronizer 211: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/dq_pipe_ap[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/dq_pipe_ap[1]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/w_wr_fifo_rd_en~FF
INFO     : 	Synchronizer 212: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/pushToPopGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 213: 
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][1]~FF
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[1]~FF
INFO     : 	Synchronizer 214: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.xrd2wr_addr_sync/dff_5/i19_2
INFO     : 	Synchronizer 215: 
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][1]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[1][1]~FF
INFO     :  u_axi4_ctrl/Gen_RFIFO_32.u_R0_FIFO_32/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[1]~FF
INFO     : 	Synchronizer 216: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/dff_11/i2_2
INFO     : 	Synchronizer 217: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/get_dqs_wr_start_p[2]~FF
INFO     : 	Synchronizer 218: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i11_2
INFO     : 	Synchronizer 219: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/get_wr_ack[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/get_wr_ack[1]~FF
INFO     : 	Synchronizer 220: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/fifo_rd/xefx_fifo_ctl/genblk7.wr2rd_addr_sync/dff_5/i16_2
INFO     : 	Synchronizer 221: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/get_dqs_wr_start_p[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/get_dqs_wr_start_p[1]~FF
INFO     : 	Synchronizer 222: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/ram_wr_ack_p~FF
INFO     : 	Synchronizer 223: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_8/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RGB555_int~FF
INFO     : 	Synchronizer 224: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/ram_wr_ack_p1~FF
INFO     : 	Synchronizer 225: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_9/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RGB565_int~FF
INFO     : 	Synchronizer 226: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc/buffers_0[0]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc_io_dataOut[0]~FF
INFO     : 	Synchronizer 227: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/syncreg_10/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/RGB888_int~FF
INFO     : 	Synchronizer 228: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/controller_main/ram_rd_ack_ap[2]~FF
INFO     : 	Synchronizer 229: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc/buffers_0[4]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc_io_dataOut[4]~FF
INFO     : 	Synchronizer 230: 
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc/buffers_0[5]~FF
INFO     :  ddr3_ctl_axi/u_efx_ddr3_soft_controller/genblk1.inst_ddr3_axi/inst_efx_ddr3/top_mc/ac_fifo/popToPushGray_buffercc_io_dataOut[5]~FF
INFO     : 	Synchronizer 231: 
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][4]~FF
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[4]~FF
INFO     : 	Synchronizer 232: 
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.wr2rd_addr_sync/async_reg[0][2]~FF
INFO     :  u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ctl/async_clk.waddr_cntr_gry_sync[2]~FF
INFO     : 	Synchronizer 233: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/genblk2.wr_rst[0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/genblk3.byte2pixel_inst/u1_inst/genblk2.wr_rst[1]~FF
INFO     : 	Synchronizer 234: 
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/syncreg_1/async_reg[0][0]~FF
INFO     :  mipi_rx_0/u_efx_csi2_rx/csi2_rx_top_inst/ready_to_rcv_sync~FF
INFO     : Using optimization level TIMING_1 in qplacer to set options
INFO     : Starting Global Placer with 32 threads ...
            ----------     -------  --------------     -------
             Iteration       WHPWL Worst Slack (ps) Convergence
            ----------     -------  --------------     -------
INFO     :           1     1720218           -1009         2.9%
INFO     :           2      764243           -1117         6.9%
INFO     :           3      577711           -1193        11.1%
INFO     :           4      498002           -1088        14.9%
INFO     :           5      449344           -1057        18.6%
INFO     :           6      392651           -1123        24.7%
INFO     :           7      333789           -1130        33.8%
INFO     :           8      299396           -1008        44.8%
INFO     :           9      277590           -1053        55.1%
INFO     :          10      270318           -1009        61.5%
INFO     :          11      263700           -1075        66.2%
INFO     :          12      261764           -1057        69.6%
INFO     :          13      258467           -1009        72.7%
INFO     :          14      257502           -1117        74.1%
INFO     :          15      257628           -1178        75.0%
INFO     :          16      258390           -1117        76.7%
INFO     :          17      257662           -1117        77.4%
INFO     :          18      256863           -1091        79.2%
INFO     :          19      256174           -1109        80.2%
INFO     :          20      256106           -1117        81.9%
INFO     :          21      255503           -1117        82.7%
INFO     :          22      253800           -1117        84.4%
INFO     :          23      253882           -1109        85.2%
INFO     :          24      252847           -1109        86.6%
INFO     :          25      253662           -1178        87.5%
INFO     :          26      253262           -1178        88.7%
INFO     :          27      253375           -1178        90.0%
INFO     :          28      253492           -1117        91.2%
INFO     :          29      253783           -1117        92.2%
INFO     :          30      254002           -1117        93.3%
INFO     :          31      254219           -1145        94.0%
INFO     :          32      254108           -1145        94.4%
INFO     :          33      254409           -1145        95.0%
INFO     :          34      248462           -1145        95.0%
INFO     :          35      249171           -1145        96.1%
INFO     :          36      249479           -1056        96.8%
INFO     :          37      250049           -1055        97.5%
INFO     :          38      249982           -1055        97.9%
INFO     :          39      250287           -1055        98.2%
INFO     :          40      250538           -1055        98.5%
INFO     : Starting Annealer
            ----------     -------  --------------     -------
             Iteration       WHPWL  Delay Max (ps)     R Limit
            ----------     -------  --------------     -------
INFO     :           0      114588             994        30.0
INFO     :           1      142180            1280        90.0
INFO     :           2      130603            1097        90.0
INFO     :           3      124048            1015        25.7
INFO     :           4      118203             973        24.2
INFO     :           5      111991            1024        22.4
INFO     :           6      108078             988        20.7
INFO     :           7      106875             893        19.0
INFO     :           8      104864             893        17.4
INFO     :           9      103236             893        15.8
INFO     :          10      101729             890        14.4
INFO     :          11      100208             599        13.0
INFO     :          12       98581             578        11.8
INFO     :          13       97663             578        10.6
INFO     :          14       96775             578         9.6
INFO     :          15       95944             578         8.7
INFO     :          16       95030             578         7.8
INFO     :          17       94537             578         7.0
INFO     : Counted delay computer calls: 105755
Placement successful: 9950 cells are placed
INFO     : Peak congestion smeared over 1/4 of the chip is 0.292252 at 115,8
INFO     : Congestion-weighted HPWL per net: 15.3515
INFO     : Post-placement cluster-level checks is successful
INFO     : Checks is successful
INFO     : Placement took 18.709 seconds.
INFO     : 	Placement took 44.8906 seconds (approximately) in total CPU time.
INFO     : Placement virtual memory usage: begin = 131.836 MB, end = 283.936 MB, delta = 152.1 MB
INFO     : 	Placement peak virtual memory usage = 283.936 MB
INFO     : Placement resident set memory usage: begin = 145.34 MB, end = 289.312 MB, delta = 143.972 MB
INFO     : 	Placement peak resident set memory usage = 289.316 MB
INFO     : Post-placement primitive-level checks is successful
