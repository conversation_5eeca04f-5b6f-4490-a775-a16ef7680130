// =============================================================================
// Generated by efx_ipmgr
// Version: 2025.**********
// IP Version: 8.0
// =============================================================================

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2025 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//                                                                       
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//                                                                       
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.                                                
//
////////////////////////////////////////////////////////////////////////////////

`define IP_UUID _4abeba6303274286a89736863be6b459
`define IP_NAME_CONCAT(a,b) a``b
`define IP_MODULE_NAME(name) `IP_NAME_CONCAT(name,`IP_UUID)
module R0_FIFO_8
(
    output almost_full_o,
    output prog_full_o,
    output full_o,
    output wr_ack_o,
    output empty_o,
    output almost_empty_o,
    output rd_valid_o,
    input wr_clk_i,
    input rd_clk_i,
    input wr_en_i,
    input rd_en_i,
    input [127:0] wdata,
    output rst_busy,
    output [7:0] rdata,
    input a_rst_i,
    output [9:0] wr_datacount_o,
    output [13:0] rd_datacount_o,
    output underflow_o,
    output overflow_o
);
`IP_MODULE_NAME(efx_fifo_top)
#(
    .SYNC_CLK (0),
    .SYNC_STAGE (3),
    .DATA_WIDTH (128),
    .MODE ("FWFT"),
    .OUTPUT_REG (0),
    .PROG_FULL_ASSERT (256),
    .PROGRAMMABLE_FULL ("STATIC_SINGLE"),
    .PROG_FULL_NEGATE (256),
    .PROGRAMMABLE_EMPTY ("NONE"),
    .PROG_EMPTY_ASSERT (2),
    .PROG_EMPTY_NEGATE (3),
    .OPTIONAL_FLAGS (1),
    .PIPELINE_REG (1),
    .DEPTH (512),
    .FAMILY ("TITANIUM"),
    .ASYM_WIDTH_RATIO (0),
    .BYPASS_RESET_SYNC (0),
    .ENDIANESS (0),
    .RAM_STYLE ("block_ram"),
    .OVERFLOW_PROTECT (1),
    .UNDERFLOW_PROTECT (1)
)
u_efx_fifo_top
(
    .almost_full_o ( almost_full_o ),
    .prog_full_o ( prog_full_o ),
    .full_o ( full_o ),
    .wr_ack_o ( wr_ack_o ),
    .empty_o ( empty_o ),
    .almost_empty_o ( almost_empty_o ),
    .rd_valid_o ( rd_valid_o ),
    .wr_clk_i ( wr_clk_i ),
    .rd_clk_i ( rd_clk_i ),
    .wr_en_i ( wr_en_i ),
    .rd_en_i ( rd_en_i ),
    .wdata ( wdata ),
    .rst_busy ( rst_busy ),
    .rdata ( rdata ),
    .a_rst_i ( a_rst_i ),
    .wr_datacount_o ( wr_datacount_o ),
    .rd_datacount_o ( rd_datacount_o ),
    .underflow_o ( underflow_o ),
    .overflow_o ( overflow_o )
);
endmodule

/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   gray2bin.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Gray to Binary Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_gray2bin)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] bin_o,
 // input
 input [WIDTH-1:0] gray_i);

//---------------------------------------------------------------------
// Recursive Module
// Description: reduction xor
generate
   if (WIDTH > 1) begin
      wire [1:0] bin_1;
      assign bin_1 = {gray_i[WIDTH-1], gray_i[WIDTH-1]^gray_i[WIDTH-2]};
      if (WIDTH == 2) begin
	 assign bin_o = bin_1;
      end
      else begin
	 assign bin_o[WIDTH-1] = bin_1[1];
	 `IP_MODULE_NAME(efx_fifo_gray2bin) #(.WIDTH(WIDTH-1)) u_gray2bin (.bin_o(bin_o[WIDTH-2:0]), .gray_i({bin_1[0], gray_i[WIDTH-3:0]}));
      end
   end
   else /* if (WIDTH == 1) */
     assign bin_o = gray_i;
endgenerate
   
endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   bin2gray.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Binary to Gray Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_bin2gray)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] gray_o,
 // input
 input [WIDTH-1:0] bin_i
 );

//---------------------------------------------------------------------
// Function :   bit_xor
// Description: reduction xor
function bit_xor (
  input [31:0] nex_bit,
  input [31:0] curr_bit,
  input [WIDTH-1:0] xor_in);
  begin : fn_bit_xor
    bit_xor = xor_in[nex_bit] ^ xor_in[curr_bit];
  end
endfunction

// Convert Binary to Gray, bit by bit
generate 
begin
  genvar bit_idx;
  for(bit_idx=0; bit_idx<WIDTH-1; bit_idx=bit_idx+1) begin : gBinBits
    assign gray_o[bit_idx] = bit_xor(bit_idx+1, bit_idx, bin_i);
  end
  assign   gray_o[WIDTH-1] = bin_i[WIDTH-1];
end
endgenerate

endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_top) # (
    parameter FAMILY             = "TRION",       // New Param
    parameter SYNC_CLK           = 0,
    parameter BYPASS_RESET_SYNC  = 0,             // New Param
    parameter SYNC_STAGE         = 2,             // New Param
    parameter MODE               = "STANDARD",
    parameter DEPTH              = 512,           // Reverted (Equivalent to WDATA_DEPTH) 
    parameter DATA_WIDTH         = 32,            // Reverted (Equivalent to WDATA_WIDTH)
    parameter PIPELINE_REG       = 1,             // Reverted (By default is ON)
    parameter OPTIONAL_FLAGS     = 1,             // Reverted
    parameter OUTPUT_REG         = 0,
    parameter PROGRAMMABLE_FULL  = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_FULL_ASSERT   = 27,
    parameter PROG_FULL_NEGATE   = 23,
    parameter PROGRAMMABLE_EMPTY = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_EMPTY_ASSERT  = 5,
    parameter PROG_EMPTY_NEGATE  = 7,
    parameter ALMOST_FLAG        = OPTIONAL_FLAGS,
    parameter HANDSHAKE_FLAG     = OPTIONAL_FLAGS,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter WADDR_WIDTH        = depth2width(DEPTH),
    parameter RDATA_WIDTH        = rdwidthcompute(ASYM_WIDTH_RATIO,DATA_WIDTH),
    parameter RD_DEPTH           = rddepthcompute(DEPTH,DATA_WIDTH,RDATA_WIDTH),
    parameter RADDR_WIDTH        = depth2width(RD_DEPTH),
    parameter ENDIANESS		      = 0,
    parameter OVERFLOW_PROTECT   = 1,
    parameter UNDERFLOW_PROTECT  = 1,
    parameter RAM_STYLE          = "block_ram"

)(
    input  wire                   a_rst_i,
    input  wire                   a_wr_rst_i,
    input  wire                   a_rd_rst_i,
    input  wire                   clk_i,
    input  wire                   wr_clk_i,
    input  wire                   rd_clk_i,
    input  wire                   wr_en_i,
    input  wire                   rd_en_i,
    input  wire [DATA_WIDTH-1:0]  wdata,
    output wire                   almost_full_o,
    output wire                   prog_full_o,
    output wire                   full_o,
    output wire                   overflow_o,
    output wire                   wr_ack_o,
    output wire [WADDR_WIDTH :0]  datacount_o,
    output wire [WADDR_WIDTH :0]  wr_datacount_o,
    output wire                   empty_o,
    output wire                   almost_empty_o,
    output wire                   prog_empty_o,
    output wire                   underflow_o,
    output wire                   rd_valid_o,
    output wire [RDATA_WIDTH-1:0] rdata,
    output wire [RADDR_WIDTH :0]  rd_datacount_o,
    output wire                   rst_busy
); 

localparam WR_DEPTH      = DEPTH;
localparam WDATA_WIDTH   = DATA_WIDTH;
localparam RAM_MUX_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? 32 :
                           (RDATA_WIDTH <= WDATA_WIDTH/16) ? 16 :
                           (RDATA_WIDTH <= WDATA_WIDTH/8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH/4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH/2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH)    ? 1  :
                           (RDATA_WIDTH <= WDATA_WIDTH*2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH*4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH*8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH*16) ? 16 : 32;           

wire                   wr_rst_int;
wire                   rd_rst_int;
wire                   wr_en_int;
wire                   rd_en_int;
wire [WADDR_WIDTH-1:0] waddr;
wire [RADDR_WIDTH-1:0] raddr;
wire                   wr_clk_int;
wire                   rd_clk_int;
wire [WADDR_WIDTH :0]  wr_datacount_int;
wire [RADDR_WIDTH :0]  rd_datacount_int;

generate
    if (ASYM_WIDTH_RATIO == 4) begin
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
            assign datacount_o    = wr_datacount_int;
            assign wr_datacount_o = {(WADDR_WIDTH+1){1'b0}};
            assign rd_datacount_o = {(RADDR_WIDTH+1){1'b0}};
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
            assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
            assign wr_datacount_o = wr_datacount_int;
            assign rd_datacount_o = rd_datacount_int;
        end
    end
    else begin
        assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
        assign wr_datacount_o = wr_datacount_int;
        assign rd_datacount_o = rd_datacount_int;
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
        end
    end    

    if (!SYNC_CLK) begin
        //(* async_reg = "true" *) reg [1:0] wr_rst;
        //(* async_reg = "true" *) reg [1:0] rd_rst;
        //
        //always @ (posedge wr_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        wr_rst <= 2'b11;
        //    else 
        //        wr_rst <= {wr_rst[0],1'b0};            
        //end
        //
        //always @ (posedge rd_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        rd_rst <= 2'b11;
        //    else 
        //        rd_rst <= {rd_rst[0],1'b0};            
        //end
        

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_wr_rst_i;
            assign rd_rst_int = a_rd_rst_i; 
            assign rst_busy   = 1'b0;
        end 
        else begin
            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_wr_rst (
               .clk   (wr_clk_int),
               .reset (a_rst_i),
               .d_o   (wr_rst_int)
            );

            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_rd_rst (
               .clk   (rd_clk_int),
               .reset (a_rst_i),
               .d_o   (rd_rst_int)
            );
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end

    end
    else begin
        //(* async_reg = "true" *) reg [1:0] a_rst;
        //
        //always @ (posedge clk_i or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        a_rst <= 2'b11;
        //    else 
        //        a_rst <= {a_rst[0],1'b0};            
        //end
        wire   a_rst;

        `IP_MODULE_NAME(efx_resetsync) #(
           .ACTIVE_LOW (0)
        ) efx_resetsync_a_rst (
           .clk   (clk_i),
           .reset (a_rst_i),
           .d_o   (a_rst)
        );

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_rst_i;
            assign rd_rst_int = a_rst_i;
            assign rst_busy   = 1'b0;
        end
        else begin
            assign wr_rst_int = a_rst;
            assign rd_rst_int = a_rst;   
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end 
endgenerate

`IP_MODULE_NAME(efx_fifo_ram) # (
    .FAMILY        (FAMILY),
    .WR_DEPTH      (WR_DEPTH),
    .RD_DEPTH      (RD_DEPTH),
    .WDATA_WIDTH   (WDATA_WIDTH),
    .RDATA_WIDTH   (RDATA_WIDTH),
    .WADDR_WIDTH   (WADDR_WIDTH),
    .RADDR_WIDTH   (RADDR_WIDTH),
    .OUTPUT_REG    (OUTPUT_REG),
    .RAM_MUX_RATIO (RAM_MUX_RATIO),
    .ENDIANESS	    (ENDIANESS),
    .RAM_STYLE     (RAM_STYLE)
) xefx_fifo_ram (
    .wdata (wdata), 
    .waddr (waddr),
    .raddr (raddr), 
    .we    (wr_en_int), 
    .re    (rd_en_int),
    .wclk  (wr_clk_int),
    .rclk  (rd_clk_int),
    .rdata (rdata)
);

`IP_MODULE_NAME(efx_fifo_ctl) # (
    .SYNC_CLK           (SYNC_CLK),
    .SYNC_STAGE         (SYNC_STAGE),
    .MODE               (MODE),
    .WR_DEPTH           (WR_DEPTH),
    .WADDR_WIDTH        (WADDR_WIDTH),
    .RADDR_WIDTH        (RADDR_WIDTH),
    .ASYM_WIDTH_RATIO   (ASYM_WIDTH_RATIO),
    .RAM_MUX_RATIO      (RAM_MUX_RATIO),
    .PIPELINE_REG       (PIPELINE_REG),
    .ALMOST_FLAG        (ALMOST_FLAG),
    .PROGRAMMABLE_FULL  (PROGRAMMABLE_FULL),
    .PROG_FULL_ASSERT   (PROG_FULL_ASSERT),
    .PROG_FULL_NEGATE   (PROG_FULL_NEGATE),
    .PROGRAMMABLE_EMPTY (PROGRAMMABLE_EMPTY),
    .PROG_EMPTY_ASSERT  (PROG_EMPTY_ASSERT),
    .PROG_EMPTY_NEGATE  (PROG_EMPTY_NEGATE),
    .OUTPUT_REG         (OUTPUT_REG),
    .HANDSHAKE_FLAG     (HANDSHAKE_FLAG),
    .OVERFLOW_PROTECT   (OVERFLOW_PROTECT),
    .UNDERFLOW_PROTECT  (UNDERFLOW_PROTECT)
) xefx_fifo_ctl (
    .wr_rst          (wr_rst_int),
    .rd_rst          (rd_rst_int),
    .wclk            (wr_clk_int),
    .rclk            (rd_clk_int),
    .we              (wr_en_i),
    .re              (rd_en_i),
    .wr_full         (full_o),
    .wr_ack          (wr_ack_o),
    .rd_empty        (empty_o),
    .wr_almost_full  (almost_full_o),
    .rd_almost_empty (almost_empty_o),
    .wr_prog_full    (prog_full_o),
    .rd_prog_empty   (prog_empty_o),
    .wr_en_int       (wr_en_int),
    .rd_en_int       (rd_en_int),
    .waddr           (waddr),
    .raddr           (raddr),
    .wr_datacount    (wr_datacount_int),
    .rd_datacount    (rd_datacount_int),
    .rd_vld          (rd_valid_o),
    .wr_overflow     (overflow_o),
    .rd_underflow    (underflow_o)
);

function integer depth2width;
input [31:0] depth;
begin : fnDepth2Width
    if (depth > 1) begin
        depth = depth - 1;
        for (depth2width=0; depth>0; depth2width = depth2width + 1)
            depth = depth>>1;
        end
    else
       depth2width = 0;
end
endfunction 

function integer width2depth;
input [31:0] width;
begin : fnWidth2Depth
    width2depth = width**2;
end
endfunction

function integer rdwidthcompute;
input [31:0] asym_option;
input [31:0] wr_width;
begin : RdWidthCompute
    rdwidthcompute = (asym_option==0)? wr_width/16 :
                     (asym_option==1)? wr_width/8  :
                     (asym_option==2)? wr_width/4  :
                     (asym_option==3)? wr_width/2  :
                     (asym_option==4)? wr_width/1  :
                     (asym_option==5)? wr_width*2  :
                     (asym_option==6)? wr_width*4  :
                     (asym_option==7)? wr_width*8  :
                     (asym_option==8)? wr_width*16 : wr_width/1;
end
endfunction

function integer rddepthcompute;
input [31:0] wr_depth;
input [31:0] wr_width;
input [31:0] rd_width;
begin : RdDepthCompute
    rddepthcompute = (wr_depth * wr_width) / rd_width;
end
endfunction

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ram) #(
    parameter FAMILY        = "TRION",
    parameter WR_DEPTH      = 512,
    parameter RD_DEPTH      = 512,
    parameter WDATA_WIDTH   = 8,
    parameter RDATA_WIDTH   = 8,
    parameter WADDR_WIDTH   = 9,
    parameter RADDR_WIDTH   = 9,
    parameter OUTPUT_REG    = 1,
    parameter RAM_MUX_RATIO = 4,
    parameter ENDIANESS 	 = 0, //0: Big endian (default)   1: Little endian 
    parameter RAM_STYLE     = "block_ram"
) (
    input  wire                     wclk,
    input  wire                     rclk,
    input  wire                     we,
    input  wire                     re,
    input  wire [(WDATA_WIDTH-1):0] wdata,
    input  wire [(WADDR_WIDTH-1):0] waddr,
    input  wire [(RADDR_WIDTH-1):0] raddr,
    output wire [(RDATA_WIDTH-1):0] rdata
);

localparam MEM_DEPTH         = (WR_DEPTH > RD_DEPTH) ? WR_DEPTH : RD_DEPTH;
localparam MEM_DATA_WIDTH    = (WDATA_WIDTH > RDATA_WIDTH) ? RDATA_WIDTH : WDATA_WIDTH;   
localparam LSB_WIDTH         = (WADDR_WIDTH > RADDR_WIDTH) ? (WADDR_WIDTH - RADDR_WIDTH) : (RADDR_WIDTH - WADDR_WIDTH);
localparam RDATA_WDATA_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? "ONE_THIRTYTWO" :
                               (RDATA_WIDTH <= WDATA_WIDTH/16) ? "ONE_SIXTEENTH" :
                               (RDATA_WIDTH <= WDATA_WIDTH/8)  ? "ONE_EIGHTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/4)  ? "ONE_FOURTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/2)  ? "ONE_HALF"      :
                               (RDATA_WIDTH <= WDATA_WIDTH)    ? "ONE"           :
                               (RDATA_WIDTH <= WDATA_WIDTH*2)  ? "TWO_TIMES"     :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "FOUR_TIMES"    :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "EIGHT_TIMES"   :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "SIXTEEN_TIMES" : "THIRTYTWO_TIMES";

(* syn_ramstyle = RAM_STYLE *) reg [MEM_DATA_WIDTH-1:0] ram[MEM_DEPTH-1:0];
reg [RDATA_WIDTH-1:0]    r_rdata_1P;
reg [RDATA_WIDTH-1:0]    r_rdata_2P;

wire                     re_int;

generate
    if (FAMILY == "TRION") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                end
                r_rdata_2P <= r_rdata_1P;
            end
        end
        
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            if (ENDIANESS == 0) begin 
            	integer i;
            	always @ (posedge wclk) begin
                	for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                    end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                	end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
        end
        
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  ||RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
            //integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end 
            end
            else begin //endianess == 1
            	always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end
            end
        end
        if (OUTPUT_REG) begin
            assign re_int = re;
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end
    end
    else begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                    r_rdata_2P <= r_rdata_1P;
                end
            end
        end 
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            //integer i;
            if (ENDIANESS == 0) begin
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
            
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
        end
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  || RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
        	//integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
            
            else begin //endianess ==1
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
        end
        // NIC
        //if (MODE == "STANDARD") begin     
        //    if (OUTPUT_REG) begin
        //        reg re_r;
        //        always @ (posedge rclk) begin
        //            re_r <= re;
        //        end
        //        assign re_int = re | re_r;        
        //        assign rdata  = r_rdata_2P;            
        //    end
        //    else begin
        //        assign re_int = re;
        //        assign rdata  = r_rdata_1P;
        //    end
        //end
        //else begin
        //    assign re_int = re;
        //    assign rdata  = r_rdata_1P;
        //end    
        if (OUTPUT_REG) begin
            reg re_r;
            always @ (posedge rclk) begin
                re_r <= re;
            end
            assign re_int = re | re_r;        
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end    
    end
endgenerate

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2021 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ctl) # (
    parameter SYNC_CLK           = 1,
    parameter SYNC_STAGE         = 2,
    parameter MODE               = "STANDARD",
    parameter WR_DEPTH           = 512,
    parameter WADDR_WIDTH        = 9,
    parameter RADDR_WIDTH        = 9,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter RAM_MUX_RATIO      = 1,
    parameter PIPELINE_REG       = 1,
    parameter ALMOST_FLAG        = 1,
    parameter PROGRAMMABLE_FULL  = "NONE",
    parameter PROG_FULL_ASSERT   = 0,
    parameter PROG_FULL_NEGATE   = 0,
    parameter PROGRAMMABLE_EMPTY = "NONE",
    parameter PROG_EMPTY_ASSERT  = 0,
    parameter PROG_EMPTY_NEGATE  = 0,
    parameter OUTPUT_REG         = 0,
    parameter HANDSHAKE_FLAG     = 1, 
    parameter OVERFLOW_PROTECT   = 0,
    parameter UNDERFLOW_PROTECT  = 0
)(
    input   wire                   wr_rst,
    input   wire                   rd_rst,
    input   wire                   wclk,
    input   wire                   rclk,
    input   wire                   we,
    input   wire                   re,
    output  wire                   wr_full,
    output  reg                    wr_ack,
    output  wire                   wr_almost_full,
    output  wire                   rd_empty,
    output  wire                   rd_almost_empty,
    output  wire                   wr_prog_full,
    output  wire                   rd_prog_empty,
    output  wire                   wr_en_int,
    output  wire                   rd_en_int,
    output  wire [WADDR_WIDTH-1:0] waddr,
    output  wire [RADDR_WIDTH-1:0] raddr,
    output  wire [WADDR_WIDTH:0]   wr_datacount,
    output  wire [RADDR_WIDTH:0]   rd_datacount,
    output  wire                   rd_vld,
    output  reg                    wr_overflow,
    output  reg                    rd_underflow
);

reg  [WADDR_WIDTH:0] waddr_cntr;
reg  [WADDR_WIDTH:0] waddr_cntr_r;
reg  [RADDR_WIDTH:0] raddr_cntr;
reg                  rd_valid;

wire [WADDR_WIDTH:0] waddr_int;
wire [RADDR_WIDTH:0] raddr_int;
wire                 rd_empty_int;
wire [WADDR_WIDTH:0] wr_datacount_int;
wire [RADDR_WIDTH:0] rd_datacount_int;

assign waddr     = waddr_cntr[WADDR_WIDTH-1:0];
// NIC
wire [RADDR_WIDTH:0] ram_raddr;
assign raddr     = (MODE == "FWFT") ? ram_raddr[RADDR_WIDTH-1:0] : raddr_cntr[RADDR_WIDTH-1:0];
//assign raddr     = raddr_cntr[RADDR_WIDTH-1:0];
//assign wr_en_int = we & ~wr_full;
assign wr_en_int = OVERFLOW_PROTECT ? we & ~wr_full : we;

assign wr_datacount  = wr_datacount_int;
assign rd_datacount  = ASYM_WIDTH_RATIO == 4 && SYNC_CLK ? wr_datacount_int : rd_datacount_int;


generate
    if (MODE == "FWFT") begin    
        // NIC
        //assign rd_en_int     = (~rd_empty_int & rd_empty) | (re & ~rd_empty_int);
        //assign rd_empty      = rd_empty_fwft;

        assign rd_en_int     = 1'b1;
        //assign rd_empty      = rd_empty_int;
        
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        init_set <= 1'b1;
        //    end
        //    else if (~init_set & rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //    else if (~rd_empty_int) begin
        //        init_set <= 1'b0;            
        //    end
        //    else if (rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //end
        // NIC 
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        rd_empty_fwft <= 1'b1;
        //    end
        //    else if (rd_en_int) begin
        //        rd_empty_fwft <= 1'b0;            
        //    end
        //    else if (re) begin
        //        rd_empty_fwft <= 1'b1;            
        //    end
        //end          
        
        //if (FAMILY == "TRION") begin
        if (OUTPUT_REG) begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid <= 1'b0;
                end
                else begin
                    rd_valid <= ~rd_empty;
                end
            end
            assign rd_vld = rd_valid;                
        end    
        else begin
            assign rd_vld = ~rd_empty;                
        end
        
        assign rd_empty = rd_empty_int;
    end
    else begin
        assign rd_en_int     = UNDERFLOW_PROTECT ? re & ~rd_empty_int : re;
        assign rd_empty      = rd_empty_int;
        
        if (OUTPUT_REG) begin
            reg rd_valid_r;
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid_r <= 'h0;
                    rd_valid   <= 'h0;
                end
                else begin
                    {rd_valid,rd_valid_r} <= {rd_valid_r,rd_en_int};
                end
            end
            assign rd_vld = rd_valid;            
        end
        else begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid  <= 'h0;
                end
                else begin
                    rd_valid <= rd_en_int;
                end
            end
            assign rd_vld = rd_valid;
        end
    end
    
    if (ALMOST_FLAG) begin
        assign wr_almost_full   = wr_datacount >= WR_DEPTH-1;
        assign rd_almost_empty  = rd_datacount <= 'd1;
    end
    else begin
        assign wr_almost_full   = 1'b0;
        assign rd_almost_empty  = 1'b0;        
    end
    
    if (PROGRAMMABLE_FULL == "STATIC_SINGLE") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_datacount >= PROG_FULL_ASSERT;
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end  
    end
    else if (PROGRAMMABLE_FULL == "STATIC_DUAL") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_prog_full_int ? wr_datacount >= PROG_FULL_NEGATE : wr_datacount >= PROG_FULL_ASSERT;   

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end              
    end
    else begin
        assign wr_prog_full  = 1'b0; 
    end
        
    if (PROGRAMMABLE_EMPTY == "STATIC_SINGLE") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty  = rd_datacount <= PROG_EMPTY_ASSERT;
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end
    end
    else if (PROGRAMMABLE_EMPTY == "STATIC_DUAL") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty = rd_prog_empty_int ? (rd_datacount <= PROG_EMPTY_NEGATE) : (rd_datacount <= PROG_EMPTY_ASSERT);
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end  
    end
    else begin
        assign rd_prog_empty  = 1'b0; 
    end
    
    if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_ack <= 1'b0;
            end
            else begin
                // NIC
                //wr_ack <= wr_en_int & ~wr_overflow;
                wr_ack <= OVERFLOW_PROTECT ? wr_en_int & ~wr_overflow : wr_en_int;
            end
        end
    end

    if (OVERFLOW_PROTECT) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else if (we && wr_full) begin
                wr_overflow <= 1'b1;
            end
            else begin
                wr_overflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else begin
                wr_overflow <= we && wr_full ? 1'b1 : wr_overflow;
            end
        end
    end

    if (UNDERFLOW_PROTECT) begin     
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else if (re && rd_empty) begin
                rd_underflow <= 1'b1;
            end
            else begin
                rd_underflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else begin
                rd_underflow <= re && rd_empty ? 1'b1 : rd_underflow;
            end
        end
    end
    
    localparam RATIO_WIDTH = (RADDR_WIDTH >= WADDR_WIDTH)? RADDR_WIDTH - WADDR_WIDTH : WADDR_WIDTH - RADDR_WIDTH;
    
    if (ASYM_WIDTH_RATIO < 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:RATIO_WIDTH]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:RATIO_WIDTH];
        assign wr_datacount_int = waddr_cntr - (raddr_int/RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int*RAM_MUX_RATIO)-raddr_cntr;
    end
    // NIC
    else if (ASYM_WIDTH_RATIO == 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:0]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - raddr_int;
        assign rd_datacount_int = waddr_int - raddr_cntr;
    end
    else begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:RATIO_WIDTH] == raddr_int[RADDR_WIDTH-1:0]);
        // NIC
        //assign rd_empty_int     = (waddr_int- raddr_cntr*RAM_MUX_RATIO) < RAM_MUX_RATIO;
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:RATIO_WIDTH] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - (raddr_int*RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int/RAM_MUX_RATIO)-raddr_cntr;
    end
endgenerate

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr   <= 'h0;
    end
    else if (wr_en_int) begin
        waddr_cntr   <= waddr_cntr + 1'b1;
    end
end

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr_r  <= 'h0;
    end
    else begin
        waddr_cntr_r  <= waddr_cntr;
    end
end

always @ (posedge rclk or posedge rd_rst) begin
    if (rd_rst) begin
        raddr_cntr   <= 'h0;
    end
    // NIC
    //else if (rd_en_int) begin
    else begin
        //raddr_cntr   <= raddr_cntr + 1'b1;
        //raddr_cntr   <= raddr_cntr + (re & ~rd_empty_int);
        raddr_cntr   <= ram_raddr;
    end
end
// NIC
assign ram_raddr = raddr_cntr + (UNDERFLOW_PROTECT ? re & ~rd_empty_int : re);


generate
    if (SYNC_CLK) begin : sync_clk
       if (MODE == "FWFT") begin
          assign waddr_int = waddr_cntr_r;
          assign raddr_int        = raddr_cntr;
       end
       else begin
          assign waddr_int        = waddr_cntr;
          assign raddr_int        = raddr_cntr;
       end
    end
    else begin : async_clk
        reg  [RADDR_WIDTH:0] raddr_cntr_gry_r;
        reg  [WADDR_WIDTH:0] waddr_cntr_gry_r;
        
        wire [RADDR_WIDTH:0] raddr_cntr_gry;
        wire [RADDR_WIDTH:0] raddr_cntr_gry_sync;
        wire [RADDR_WIDTH:0] raddr_cntr_sync_g2b;
        wire [WADDR_WIDTH:0] waddr_cntr_gry;
        wire [WADDR_WIDTH:0] waddr_cntr_gry_sync;
        wire [WADDR_WIDTH:0] waddr_cntr_sync_g2b;

        if (PIPELINE_REG) begin
            reg  [RADDR_WIDTH:0] raddr_cntr_sync_g2b_r;    
            reg  [WADDR_WIDTH:0] waddr_cntr_sync_g2b_r;        
        
            assign waddr_int        = waddr_cntr_sync_g2b_r;
            assign raddr_int        = raddr_cntr_sync_g2b_r;
            
            always @ (posedge wclk or posedge wr_rst) begin
                if (wr_rst) begin
                    raddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    raddr_cntr_sync_g2b_r <= raddr_cntr_sync_g2b;
                end
            end
            
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    waddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    waddr_cntr_sync_g2b_r <= waddr_cntr_sync_g2b;
                end
            end
        end
        else begin
            assign waddr_int        = waddr_cntr_sync_g2b;
            assign raddr_int        = raddr_cntr_sync_g2b;        
        end
      
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                raddr_cntr_gry_r <= 'h0;
            end
            else begin
                raddr_cntr_gry_r <= raddr_cntr_gry;
            end
        end
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_bin2gray    (.bin_i(raddr_cntr), .gray_o(raddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (RADDR_WIDTH+1), .ACTIVE_LOW(0)) xrd2wr_addr_sync   (.clk(wclk), .reset_n(wr_rst), .d_i(raddr_cntr_gry_r), .d_o(raddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_gray2bin    (.gray_i(raddr_cntr_gry_sync), .bin_o(raddr_cntr_sync_g2b));
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                waddr_cntr_gry_r <= 'h0;
            end
            else begin
                waddr_cntr_gry_r <= waddr_cntr_gry;
            end
        end        
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_bin2gray  (.bin_i(waddr_cntr), .gray_o(waddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (WADDR_WIDTH+1), .ACTIVE_LOW(0)) wr2rd_addr_sync (.clk(rclk), .reset_n(rd_rst), .d_i(waddr_cntr_gry_r), .d_o(waddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_gray2bin  (.gray_i(waddr_cntr_gry_sync), .bin_o(waddr_cntr_sync_g2b));        

    end
endgenerate
endmodule



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_resetsync) #(
    parameter ASYNC_STAGE = 2,
    parameter ACTIVE_LOW = 1
) (
    input  wire             clk,
    input  wire             reset,
    output wire             d_o
);


generate
   if (ACTIVE_LOW == 1) begin: active_low
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (1),
         .RST_VALUE  (0)
      ) efx_resetsync_active_low (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b1),
         .d_o     (d_o)
      );
   end
   else begin: active_high
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (0),
         .RST_VALUE  (1)
      ) efx_resetsync_active_high (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b0),
         .d_o     (d_o)
      );
   end
endgenerate

endmodule



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_asyncreg) #(
    parameter ASYNC_STAGE = 2,
    parameter WIDTH = 4,
    parameter ACTIVE_LOW = 1, // 0 - Active high reset, 1 - Active low reset
    parameter RST_VALUE = 0 
) (
    input  wire             clk,
    input  wire             reset_n,
    input  wire [WIDTH-1:0] d_i,
    output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] async_reg[ASYNC_STAGE-1:0];

assign d_o = async_reg[ASYNC_STAGE-1];

genvar i;
generate
   if (ACTIVE_LOW == 1) begin: active_low
      if (RST_VALUE == 0) begin: low_reset_0
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: low_reset_1
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
   else begin: active_high
      if (RST_VALUE == 0) begin: high_reset_0
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: high_reset_1
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
endgenerate

endmodule

`undef IP_UUID
`undef IP_NAME_CONCAT
`undef IP_MODULE_NAME
