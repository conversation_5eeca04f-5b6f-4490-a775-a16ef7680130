{"args": ["-o", "afifo_w32r8_reshape", "--base_path", "E:\\fpga\\yilingsi\\A2020_demo\\Ti60F225_A2020_LVDS_Display_30FPS\\Ti60F225_AD2020_LVDS_Display_30FPS\\ip", "--vlnv", {"vendor": "efinixinc.com", "library": "memory", "name": "efx_fifo_top", "version": "8.0"}], "conf": {"SYNC_CLK": "0", "SYNC_STAGE": "2", "DEPTH_2": "12", "DATA_WIDTH": "32", "MODE": "\"FWFT\"", "OUTPUT_REG": "0", "PROG_FULL_ASSERT": "4", "PROGRAMMABLE_FULL": "\"NONE\"", "PFN_INTERNAL": "3", "PEA_INTERNAL": "0", "PEN_INTERNAL": "0", "PROGRAMMABLE_EMPTY": "\"NONE\"", "OPTIONAL_FLAGS": "0", "PIPELINE_REG": "1", "ASYM_WIDTH_RATIO": "2", "BYPASS_RESET_SYNC": "0", "ENDIANESS": "1", "RAM_STYLE": "\"block_ram\"", "OVERFLOW_PROTECT": "0", "UNDERFLOW_PROTECT": "0"}, "output": {"external_source_source": ["afifo_w32r8_reshape\\afifo_w32r8_reshape_tmpl.sv", "afifo_w32r8_reshape\\afifo_w32r8_reshape_tmpl.vhd", "afifo_w32r8_reshape\\afifo_w32r8_reshape.sv", "afifo_w32r8_reshape\\afifo_w32r8_reshape_define.svh"], "external_example_example": ["afifo_w32r8_reshape\\T20F256_devkit\\fifo_demo_top.v", "afifo_w32r8_reshape\\T20F256_devkit\\fifo_demo_T20.sdc", "afifo_w32r8_reshape\\T20F256_devkit\\efx_symmetric_width_fifo_top.sv", "afifo_w32r8_reshape\\T20F256_devkit\\afifo_w32r8_reshape.sv", "afifo_w32r8_reshape\\T20F256_devkit\\afifo_w32r8_reshape_define.svh", "afifo_w32r8_reshape\\T20F256_devkit\\fifo_demo.peri.xml", "afifo_w32r8_reshape\\T20F256_devkit\\fifo_demo.xml"], "external_example_2": ["afifo_w32r8_reshape\\Ti60F225_devkit\\fifo_demo_top.v", "afifo_w32r8_reshape\\Ti60F225_devkit\\fifo_demo_Ti60.sdc", "afifo_w32r8_reshape\\Ti60F225_devkit\\efx_symmetric_width_fifo_top.sv", "afifo_w32r8_reshape\\Ti60F225_devkit\\afifo_w32r8_reshape.sv", "afifo_w32r8_reshape\\Ti60F225_devkit\\afifo_w32r8_reshape_define.svh", "afifo_w32r8_reshape\\Ti60F225_devkit\\fifo_demo.peri.xml", "afifo_w32r8_reshape\\Ti60F225_devkit\\fifo_demo.xml"], "external_testbench_testbench": ["afifo_w32r8_reshape\\Testbench\\fifo_tb.sv", "afifo_w32r8_reshape\\Testbench\\xrun.sh", "afifo_w32r8_reshape\\Testbench\\msim.sh", "afifo_w32r8_reshape\\Testbench\\flist", "afifo_w32r8_reshape\\Testbench\\modelsim.do", "afifo_w32r8_reshape\\Testbench\\afifo_w32r8_reshape.sv", "afifo_w32r8_reshape\\Testbench\\afifo_w32r8_reshape_define.svh"]}, "ooc_synthesis": {}, "sw_version": "2025.1.110.2.15", "generated_date": "2025-10-06T09:30:11.659804+00:00"}