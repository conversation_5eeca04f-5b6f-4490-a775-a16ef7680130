
# Efinity Pinout Report
# Version: 2025.1.110.2.15
# Date: 2025-07-22 18:24

# Copyright (C) 2013 - 2025 Efinix Inc. All rights reserved.

# Device: Ti60F225
# Package status: final
# Project: Ti60_Demo
# Configuration mode: active (x4)

Pin Number,Pin Name,Signal Name,Direction,Bank,I/O Bank Voltage,I/O Standard,Pull Type,Function
A1,GND,,Power,,,,,Ground
A2,GPIOL_N_18,lcd_tp_rst_o.N,Output,1B,1.8 V,1.8 V LVCMOS,,User IO/PLL_CLKIN
A3,GPIOL_03,cmos_vsync,Input,TL,3.3 V,3.3 V LVCMOS,,User IO
A4,GPIOL_09,cmos_sclk,Output,TL,3.3 V,3.3 V LVCMOS,,User IO
A5,GPIOT_P_03,hdmi_txd2.P,Output,2A,1.8 V,,,User IO/MIPI_GCLK/MIPI_RCLK
A6,GPIOT_N_03,hdmi_txd2.N,Output,2A,1.8 V,,,User IO/MIPI_GCLK/MIPI_RCLK
A7,GPIOT_N_06,dsi_pwm_o.N,Output,2A,1.8 V,1.8 V LVCMOS,,User IO
A8,GPIOT_N_07_CLK4_N,,,2A,1.8 V,,,User IO/GCLK/RCLK
A9,GPIOT_N_11,,,2B,1.8 V,,,User IO
A10,GPIOT_P_14,lvds_txd3.P,Output,2B,1.8 V,,,User IO/MIPI_GCLK
A11,GPIOT_P_17_PLLIN1,hdmi_txd0.P,Output,2B,1.8 V,,,User IO/PLL_CLKIN
A12,GPIOR_13,cmos_sdat,Bidirectional,TR,3.3 V,3.3 V LVCMOS,,User IO
A13,GPIOR_12,cmos_data[7],Input,TR,3.3 V,3.3 V LVCMOS,,User IO
A14,GPIOR_19,cmos_data[3],Input,TR,3.3 V,3.3 V LVCMOS,,User IO
A15,GND,,Power,,,,,Ground
B1,GPIOL_P_17_EXTFB,lcd_tp_scl_io.P,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO/PLL_EXTFB
B2,GPIOL_P_18_PLLIN0,lcd_tp_int_io.P,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO/PLL_CLKIN
B3,GPIOL_04,cmos_ctl2,Output,TL,3.3 V,3.3 V LVCMOS,,User IO
B4,VCCIO33_TL,,Power,TL,3.3 V,,,VCCIO Power
B5,GPIOL_10,cmos_data[4],Input,TL,3.3 V,3.3 V LVCMOS,,User IO
B6,GPIOT_P_01,lvds_txc.P,Output,2A,1.8 V,,,User IO
B7,GPIOT_P_06,csi_sda_io.P,Bidirectional,2A,1.8 V,1.8 V LVCMOS,,User IO
B8,GPIOT_P_07_CLK4_P,cmos_pclk.P,Input,2A,1.8 V,1.8 V LVCMOS,,User IO/GCLK/RCLK
B9,GPIOT_P_11,,,2B,1.8 V,,,User IO
B10,GPIOT_N_14,lvds_txd3.N,Output,2B,1.8 V,,,User IO/MIPI_GCLK
B11,GPIOT_N_17,hdmi_txd0.N,Output,2B,1.8 V,,,User IO/PLL_CLKIN
B12,GPIOR_15,cmos_ctl1,Input,TR,3.3 V,3.3 V LVCMOS,,User IO
B13,VCCIO33_TR,,Power,TR,3.3 V,,,VCCIO Power
B14,GPIOR_18,cmos_data[5],Input,TR,3.3 V,3.3 V LVCMOS,,User IO
B15,REF_RES_3A,,Input,3A,1.2 V,,,
C1,GPIOL_N_16,lcd_pwm_o.N,Output,1B,1.8 V,1.8 V LVCMOS,,User IO/MIPI_GCLK/MIPI_RCLK
C2,GPIOL_N_17,lcd_tp_sda_io.N,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO/PLL_EXTFB
C3,GPIOL_06,cmos_data[2],Input,TL,3.3 V,3.3 V LVCMOS,,User IO
C4,GPIOL_07,cmos_data[6],Input,TL,3.3 V,3.3 V LVCMOS,,User IO
C5,GPIOL_11_PLLIN2,clk_24m,Input,TL,3.3 V,3.3 V LVCMOS,,User IO/PLL_CLKIN
C6,GPIOT_N_01,lvds_txc.N,Output,2A,1.8 V,,,User IO
C7,VCCIO2A,,Power,2A,1.8 V,,,VCCIO Power
C8,GPIOT_P_10_CLK7_P,csi_ctl0_io.P,Bidirectional,2B,1.8 V,1.8 V LVCMOS,,User IO/GCLK/RCLK
C9,GPIOT_N_10_CLK7_N,csi_ctl1_io.N,Bidirectional,2B,1.8 V,1.8 V LVCMOS,,User IO/GCLK/RCLK
C10,GPIOT_P_13,lvds_txd0.P,Output,2B,1.8 V,,,User IO/VREF
C11,VCCIO2B,,Power,2B,1.8 V,,,VCCIO Power
C12,GPIOR_16,cmos_xclk,Output,TR,3.3 V,3.3 V LVCMOS,,User IO
C13,GPIOR_P_17,csi_rxc.P,Input,3A,1.2 V,,,User IO/MIPI_GCLK/MIPI_RCLK
C14,GPIOR_P_19_PLLIN0,csi_rxd1.P,Input,3A,1.2 V,,,User IO/PLL_CLKIN
C15,GPIOR_N_19,csi_rxd1.N,Input,3A,1.2 V,,,User IO/PLL_CLKIN
D1,GPIOL_P_16,lcd_blen_o.P,Output,1B,1.8 V,1.8 V LVCMOS,,User IO/MIPI_GCLK/MIPI_RCLK
D2,VCCIO1B,,Power,1B,1.8 V,,,VCCIO Power
D3,GND,,Power,,,,,Ground
D4,VCCAUX,,Power,4B,1.5 V,,,
D5,GPIOT_N_00,hdmi_txd1.N,Output,2A,1.8 V,,,User IO/PLL_CLKIN
D6,VCCIO2A,,Power,2A,1.8 V,,,VCCIO Power
D7,GPIOT_N_05,lvds_txd2.N,Output,2A,1.8 V,,,User IO
D8,GPIOT_N_09_CLK6_N,dsi_resetn_o.N,Output,2B,1.8 V,1.8 V LVCMOS,,User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK
D9,VCCIO2B,,Power,2B,1.8 V,,,VCCIO Power
D10,GPIOT_N_13,lvds_txd0.N,Output,2B,1.8 V,,,User IO
D11,REF_RES_2B,,Input,2B,1.8 V,,,
D12,GPIOR_20,cmos_href,Input,TR,3.3 V,3.3 V LVCMOS,,User IO
D13,GPIOR_N_17,csi_rxc.N,Input,3A,1.2 V,,,User IO/MIPI_GCLK/MIPI_RCLK
D14,GPIOR_N_18,csi_rxd0.N,Input,3A,1.2 V,,,User IO
D15,GPIOR_P_18,csi_rxd0.P,Input,3A,1.2 V,,,User IO
E1,GPIOL_P_15_NSTATUS,lcd_r7_0_io[0].P,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO/VREF/Configuration
E2,GPIOL_N_15_TEST_N,lcd_r7_0_io[1].N,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO/Configuration
E3,GPIOL_N_13_CBSEL1,lcd_g7_0_io[7].N,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO
E4,REF_RES_1B,,Input,1B,1.8 V,,,
E5,VCCA_TL,,Power,,,,,0.95 V PLL Power
E6,GPIOT_P_00_PLLIN1,hdmi_txd1.P,Output,2A,1.8 V,,,User IO/PLL_CLKIN/VREF
E7,GPIOT_P_05,lvds_txd2.P,Output,2A,1.8 V,,,User IO
E8,GPIOT_P_09_CLK6_P,csi_scl_io.P,Bidirectional,2B,1.8 V,1.8 V LVCMOS,,User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK
E9,GPIOT_N_12,lvds_txd1.N,Output,2B,1.8 V,,,User IO
E10,GPIOT_P_16_EXTFB,uart_tx_o.P,Output,2B,1.8 V,1.8 V LVCMOS,,User IO/PLL_EXTFB
E11,VCCAUX,,Power,4B,1.5 V,,,
E12,GPIOR_N_15,csi_rxd2.N,Input,3A,1.2 V,,,User IO
E13,VCC,,Power,,,,,0.95 V VCC Power
E14,GPIOR_N_16,csi_rxd3.N,Input,3A,1.2 V,,,User IO
E15,GPIOR_P_16,csi_rxd3.P,Input,3A,1.2 V,,,User IO
F1,GPIOL_P_14,lcd_r7_0_io[2].P,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO
F2,GPIOL_N_14,lcd_r7_0_io[3].N,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO
F3,GPIOL_P_13_CBSEL0,lcd_g7_0_io[6].P,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO
F4,VCCIO1B,,Power,1B,1.8 V,,,VCCIO Power
F5,GPIOL_N_11,lcd_g7_0_io[3].N,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO
F6,REF_RES_2A,,Input,2A,1.8 V,,,
F7,GPIOT_P_04,hdmi_txc.P,Output,2A,1.8 V,,,User IO
F8,GPIOT_N_04,hdmi_txc.N,Output,2A,1.8 V,,,User IO
F9,GPIOT_P_12,lvds_txd1.P,Output,2B,1.8 V,,,User IO
F10,GPIOT_N_16,uart_rx_i.N,Input,2B,1.8 V,1.8 V LVCMOS,weak pullup,User IO/PLL_EXTFB
F11,GPIOR_N_14,dsi_txd2.N,Output,3A,1.2 V,,,User IO
F12,GPIOR_P_15,csi_rxd2.P,Input,3A,1.2 V,,,User IO
F13,GPIOR_N_12,dsi_txc.N,Output,3A,1.2 V,,,User IO
F14,VCCIO3A,,Power,3A,1.2 V,,,VCCIO Power
F15,GPIOR_N_13,dsi_txd1.N,Output,3A,1.2 V,,,User IO
G1,GPIOL_N_09_CLK2_N,lcd_r7_0_io[5].N,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK
G2,GPIOL_N_10_CLK3_N,lcd_g7_0_io[5].N,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO/GCLK/RCLK
G3,GPIOL_P_12,lcd_b7_0_io[0].P,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO
G4,GPIOL_N_12,lcd_b7_0_io[1].N,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO
G5,GPIOL_P_11,lcd_g7_0_io[2].P,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO
G6,VQPS_GND,,Power,,,,,
G7,VCC,,Power,,,,,0.95 V VCC Power
G8,GND,,Power,,,,,Ground
G9,VCC,,Power,,,,,0.95 V VCC Power
G10,VCCA_TR,,Power,,,,,0.95 V PLL Power
G11,GPIOR_P_14,dsi_txd2.P,Output,3A,1.2 V,,,User IO
G12,VCCIO3A,,Power,3A,1.2 V,,,VCCIO Power
G13,GPIOR_P_12,dsi_txc.P,Output,3A,1.2 V,,,User IO
G14,GPIOR_N_11_CLK8_N,dsi_txd0.N,Output,3A,1.2 V,,,User IO/GCLK/RCLK
G15,GPIOR_P_13,dsi_txd1.P,Output,3A,1.2 V,,,User IO/VREF
H1,GPIOL_P_09_CLK2_P,lcd_r7_0_io[4].P,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK
H2,GPIOL_P_10_CLK3_P,lcd_g7_0_io[4].P,Bidirectional,1B,1.8 V,1.8 V LVCMOS,,User IO/GCLK/RCLK/Configuration
H3,GPIOL_N_08_CLK1_N,lcd_b7_0_io[7].N,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO/GCLK/RCLK/Configuration
H4,GPIOL_N_04_CDI3,lcd_b7_0_io[3].N,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO/Configuration
H5,GPIOL_P_04_CDI2,lcd_b7_0_io[2].P,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO/VREF/Configuration
H6,VCC,,Power,,,,,0.95 V VCC Power
H7,GND,,Power,,,,,Ground
H8,VCC,,Power,,,,,0.95 V VCC Power
H9,GND,,Power,,,,,Ground
H10,GPIOR_P_08_CLK11_P,addr[10].P,Output,3B,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
H11,GPIOR_N_08_CLK11_N,reset.N,Output,3B,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
H12,GPIOR_N_10_CLK9_N,dsi_txd3.N,Output,3A,1.2 V,,,User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK
H13,GPIOR_P_10_CLK9_P,dsi_txd3.P,Output,3A,1.2 V,,,User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK
H14,GPIOR_P_11_CLK8_P,dsi_txd0.P,Output,3A,1.2 V,,,User IO/GCLK/RCLK
H15,GPIOR_N_09_CLK10_N,clk_n.N,Output,3B,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
J1,GPIOL_N_07_CLK0_N,lcd_r7_0_io[7].N,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO/GCLK/RCLK
J2,GPIOL_P_08_CLK1_P,lcd_b7_0_io[6].P,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO/GCLK/RCLK
J3,GPIOL_N_05,lcd_b7_0_io[5].N,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO
J4,VCCIO1A,,Power,1A,1.8 V,,,VCCIO Power
J5,CRESET_N,,Input,1A,1.8 V,,,Configuration
J6,GND,,Power,,,,,Ground
J7,VCC,,Power,,,,,0.95 V VCC Power
J8,GND,,Power,,,,,Ground
J9,VCC,,Power,,,,,0.95 V VCC Power
J10,GPIOR_N_03_CDI27,addr[11].N,Output,3B,1.5 V,1.5 V SSTL,,User IO
J11,VCCIO3B,,Power,3B,1.5 V,,,VCCIO Power
J12,GPIOR_N_04_CDI29,addr[13].N,Output,3B,1.5 V,1.5 V SSTL,,User IO
J13,GPIOR_N_06_CDI21,addr[7].N,Output,3B,1.5 V,1.5 V SSTL,,User IO
J14,GPIOR_N_07,addr[9].N,Output,3B,1.5 V,1.5 V SSTL,,User IO
J15,GPIOR_P_09_CLK10_P,clk_p.P,Output,3B,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
K1,GPIOL_P_07_CLK0_P,lcd_r7_0_io[6].P,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO/GCLK/RCLK
K2,GPIOL_N_06,lcd_vs_o.N,Output,1A,1.8 V,1.8 V LVCMOS,,User IO
K3,GPIOL_P_06,lcd_hs_o.P,Output,1A,1.8 V,1.8 V LVCMOS,,User IO
K4,GPIOL_P_05,lcd_b7_0_io[4].P,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO
K5,REF_RES_1A,,Input,1A,1.8 V,,,
K6,GPIOB_N_04,addr[3].N,Output,4B,1.5 V,1.5 V SSTL,,User IO
K7,GPIOB_P_09_CLK13_P,verf0.P,Input,4A,1.5 V,1.5 V SSTL,,User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK/VREF/Configuration
K8,GPIOB_P_11_CDI10,addr[5].P,Output,4A,1.5 V,1.5 V SSTL,,User IO
K9,GPIOB_N_11_CDI11,addr[15].N,Output,4A,1.5 V,1.5 V SSTL,,User IO
K10,GPIOR_P_03_CDI26,addr[6].P,Output,3B,1.5 V,1.5 V SSTL,,User IO
K11,GPIOR_N_00_CDI22,addr[8].N,Output,3B,1.5 V,1.5 V SSTL,,User IO/PLL_CLKIN
K12,GPIOR_P_04_CDI28,addr[2].P,Output,3B,1.5 V,1.5 V SSTL,,User IO
K13,GPIOR_P_06_CDI20,addr[14].P,Output,3B,1.5 V,1.5 V SSTL,,User IO
K14,GPIOR_P_07,ba[2].P,Output,3B,1.5 V,1.5 V SSTL,,User IO
K15,GPIOR_N_05_CDI31,addr[0].N,Output,3B,1.5 V,1.5 V SSTL,,User IO
L1,GPIOL_N_03_CDI1,lcd_g7_0_io[1].N,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO/Configuration
L2,VCCIO1A,,Power,1A,1.8 V,,,VCCIO Power
L3,CDONE,,Bidirectional,1A,1.8 V,,,Configuration
L4,VCCAUX,,Power,4B,1.5 V,,,
L5,VCCA_BL,,Power,,,,,0.95 V PLL Power
L6,GPIOB_P_04_SSU_N,vref1.P,Input,4B,1.5 V,1.5 V SSTL,,User IO/VREF/Configuration
L7,GPIOB_N_06_CDI9,ba[0].N,Output,4B,1.5 V,1.5 V SSTL,,User IO
L8,GPIOB_N_09_CLK13_N,cke.N,Output,4A,1.5 V,1.5 V SSTL,,User IO/GCLK/MIPI_GCLK/RCLK/MIPI_RCLK
L9,GPIOB_N_10_CLK12_N,ba[1].N,Output,4A,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
L10,VCCA_BR,,Power,,,,,0.95 V PLL Power
L11,GPIOR_P_00_PLLIN0,addr[4].P,Output,3B,1.5 V,1.5 V SSTL,,User IO/PLL_CLKIN
L12,GPIOR_P_01_EXTFB,addr[12].P,Output,3B,1.5 V,1.5 V SSTL,,User IO/PLL_EXTFB
L13,GPIOR_N_01_CDI23,addr[1].N,Output,3B,1.5 V,1.5 V SSTL,,User IO/PLL_EXTFB
L14,VCCIO3B,,Power,3B,1.5 V,,,VCCIO Power
L15,GPIOR_P_05_CDI30,we.P,Output,3B,1.5 V,1.5 V SSTL,,User IO/VREF
M1,GPIOL_P_03_CDI0,lcd_g7_0_io[0].P,Bidirectional,1A,1.8 V,1.8 V LVCMOS,,User IO/Configuration
M2,GPIOL_N_02_CSO,lcd_pclk_o.N,Output,1A,1.8 V,1.8 V LVCMOS,,User IO/MIPI_GCLK/MIPI_RCLK/Configuration
M3,VCC,,Power,,,,,0.95 V VCC Power
M4,TDO,,Output,BL,3.3 V,,,Configuration
M5,REF_RES_4B,,Input,4B,1.5 V,,,
M6,GPIOB_N_03_CDI7,dq[9].N,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO/MIPI_GCLK/MIPI_RCLK
M7,GPIOB_P_06_CDI8,odt.P,Output,4B,1.5 V,1.5 V SSTL,,User IO
M8,GPIOB_N_08_CLK14_N,cs.N,Output,4B,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
M9,GPIOB_P_10_CLK12_P,dm[0].P,Output,4A,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
M10,GPIOB_N_13_CDI15,dq[1].N,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO
M11,VCCAUX,,Power,4B,1.5 V,,,
M12,REF_RES_3B,,Input,3B,1.5 V,,,
M13,GND,,Power,,,,,Ground
M14,GPIOR_P_02_CDI24,ras.P,Output,3B,1.5 V,1.5 V SSTL,,User IO/MIPI_GCLK/MIPI_RCLK
M15,GPIOR_N_02_CDI25,cas.N,Output,3B,1.5 V,1.5 V SSTL,,User IO/MIPI_GCLK/MIPI_RCLK
N1,GPIOL_N_01_CCK,spi_sck_o.N,Output,1A,1.8 V,1.8 V LVCMOS,,User IO/Configuration
N2,GPIOL_P_02_CSI,lcd_de_o.P,Output,1A,1.8 V,1.8 V LVCMOS,,User IO/MIPI_GCLK/MIPI_RCLK/Configuration
N3,TMS,,Input,BL,3.3 V,,,Configuration
N4,TCK,,Input,BL,3.3 V,,,Configuration
N5,VCCIO4B,,Power,4B,1.5 V,,,VCCIO Power
N6,GPIOB_P_03_CDI6,dq[13].P,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO/MIPI_GCLK/MIPI_RCLK
N7,VCCIO4B,,Power,4B,1.5 V,,,VCCIO Power
N8,GPIOB_P_08_CLK14_P,dq[11].P,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
N9,VCCIO4A,,Power,4A,1.5 V,,,VCCIO Power
N10,GPIOB_P_13_CDI14,dq[5].P,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO
N11,VCCIO4A,,Power,4A,1.5 V,,,VCCIO Power
N12,REF_RES_4A,,Input,4A,1.5 V,,,
N13,GPIOR_24,led_o[4],Output,BR,3.3 V,3.3 V LVCMOS,,User IO
N14,GPIOR_21,led_o[3],Output,BR,3.3 V,3.3 V LVCMOS,,User IO
N15,VCCIO33_BR,,Power,BR,3.3 V,,,VCCIO Power
P1,GPIOL_P_01_SSL_N,spi_ssn_o.P,Output,1A,1.8 V,1.8 V LVCMOS,,User IO/Configuration
P2,GPIOL_P_00_PLLIN0,,,1A,1.8 V,,,User IO/PLL_CLKIN
P3,TDI,,Input,BL,3.3 V,,,Configuration
P4,VCCIO33_BL,,Power,BL,3.3 V,,,VCCIO Power
P5,GPIOB_N_00,dq[15].N,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO/PLL_CLKIN
P6,GPIOB_N_01,dq[12].N,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO/PLL_EXTFB
P7,GPIOB_N_02_CDI5,dqs_n[1].N,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO
P8,GPIOB_N_07_CLK15_N,dq[8].N,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
P9,GPIOB_N_12_CDI13,dq[3].N,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO
P10,GPIOB_N_14_CDI17,dq[6].N,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO/MIPI_GCLK
P11,GPIOB_P_17_PLLIN1,dq[7].P,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO/PLL_CLKIN
P12,GPIOB_N_17,dq[4].N,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO/PLL_CLKIN
P13,GPIOR_27,led_o[5],Output,BR,3.3 V,3.3 V LVCMOS,,User IO
P14,GPIOR_22,led_o[1],Output,BR,3.3 V,3.3 V LVCMOS,,User IO
P15,GPIOR_25,led_o[0],Output,BR,3.3 V,3.3 V LVCMOS,,User IO
R1,GND,,Power,,,,,Ground
R2,GPIOL_N_00,cmos_ctl3.N,Output,1A,1.8 V,1.8 V LVCMOS,,User IO/PLL_CLKIN
R3,GPIOL_01,cmos_data[0],Input,BL,3.3 V,3.3 V LVCMOS,,User IO
R4,GPIOL_02,cmos_data[1],Input,BL,3.3 V,3.3 V LVCMOS,,User IO
R5,GPIOB_P_00_PLLIN1,dq[14].P,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO/PLL_CLKIN
R6,GPIOB_P_01_EXTFB,dq[10].P,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO/PLL_EXTFB
R7,GPIOB_P_02_CDI4,dqs[1].P,Bidirectional,4B,1.5 V,1.5 V SSTL,,User IO
R8,GPIOB_P_07_CLK15_P,dm[1].P,Output,4B,1.5 V,1.5 V SSTL,,User IO/GCLK/RCLK
R9,GPIOB_P_12_CDI12,dq[0].P,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO
R10,GPIOB_P_14_CDI16,dq[2].P,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO/MIPI_GCLK
R11,GPIOB_N_15_CDI19,dqs_n[0].N,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO
R12,GPIOB_P_15_CDI18,dqs[0].P,Bidirectional,4A,1.5 V,1.5 V SSTL,,User IO
R13,GPIOR_29_PLLIN2,clk_25m,Input,BR,3.3 V,3.3 V LVCMOS,,User IO/PLL_CLKIN
R14,GPIOR_28,led_o[2],Output,BR,3.3 V,3.3 V LVCMOS,,User IO
R15,GND,,Power,,,,,Ground
