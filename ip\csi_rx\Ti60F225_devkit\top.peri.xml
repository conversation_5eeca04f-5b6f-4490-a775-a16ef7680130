<?xml version="1.0" encoding="UTF-8"?>
<efxpt:design_db name="top" device_def="Ti60F225" version="2025.M.121" db_version="********" last_change_date="Sun May 11 00:19:53 2025" xmlns:efxpt="http://www.efinixinc.com/peri_design_db" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.efinixinc.com/peri_design_db peri_design_db.xsd ">
    <efxpt:device_info>
        <efxpt:iobank_info>
            <efxpt:iobank name="1A" iostd="1.8 V LVCMOS" is_dyn_voltage="false" mode_sel_name="1A_MODE_SEL"/>
            <efxpt:iobank name="1B" iostd="1.8 V LVCMOS" is_dyn_voltage="false" mode_sel_name="1B_MODE_SEL"/>
            <efxpt:iobank name="2A" iostd="1.8 V LVCMOS" is_dyn_voltage="false" mode_sel_name="2A_MODE_SEL"/>
            <efxpt:iobank name="2B" iostd="1.8 V LVCMOS" is_dyn_voltage="false" mode_sel_name="2B_MODE_SEL"/>
            <efxpt:iobank name="3A" iostd="1.2 V LVCMOS" is_dyn_voltage="false" mode_sel_name="3A_MODE_SEL"/>
            <efxpt:iobank name="3B" iostd="1.2 V LVCMOS" is_dyn_voltage="false" mode_sel_name="3B_MODE_SEL"/>
            <efxpt:iobank name="4A" iostd="1.2 V LVCMOS" is_dyn_voltage="false" mode_sel_name="4A_MODE_SEL"/>
            <efxpt:iobank name="4B" iostd="1.2 V LVCMOS" is_dyn_voltage="false" mode_sel_name="4B_MODE_SEL"/>
            <efxpt:iobank name="BL" iostd="3.3 V LVCMOS" is_dyn_voltage="false" mode_sel_name="BL_MODE_SEL"/>
            <efxpt:iobank name="BR" iostd="3.3 V LVCMOS" is_dyn_voltage="false" mode_sel_name="BR_MODE_SEL"/>
            <efxpt:iobank name="TL" iostd="3.3 V LVCMOS" is_dyn_voltage="false" mode_sel_name="TL_MODE_SEL"/>
            <efxpt:iobank name="TR" iostd="3.3 V LVCMOS" is_dyn_voltage="false" mode_sel_name="TR_MODE_SEL"/>
        </efxpt:iobank_info>
        <efxpt:ctrl_info>
            <efxpt:ctrl name="cfg" ctrl_def="CONFIG_CTRL0" clock_name="" is_clk_invert="false" cbsel_bus_name="cfg_CBSEL" config_ctrl_name="cfg_CONFIG" ena_capture_name="cfg_ENA" error_status_name="cfg_ERROR" um_signal_status_name="cfg_USR_STATUS" is_remote_update_enable="false" is_user_mode_enable="false"/>
        </efxpt:ctrl_info>
        <efxpt:seu_info>
            <efxpt:seu name="seu" block_def="CONFIG_SEU0" mode="auto" ena_detect="false" wait_interval="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="seu_START" type_name="START" is_bus="false"/>
                    <efxpt:pin name="seu_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="seu_INJECT_ERROR" type_name="INJECT_ERROR" is_bus="false"/>
                    <efxpt:pin name="seu_CONFIG" type_name="CONFIG" is_bus="false"/>
                    <efxpt:pin name="seu_ERROR" type_name="ERROR" is_bus="false"/>
                    <efxpt:pin name="seu_DONE" type_name="DONE" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:seu>
        </efxpt:seu_info>
        <efxpt:clkmux_info>
            <efxpt:clkmux name="CLKMUX_B" block_def="CLKMUX_B" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
            <efxpt:clkmux name="CLKMUX_L" block_def="CLKMUX_L" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
            <efxpt:clkmux name="CLKMUX_R" block_def="CLKMUX_R" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
            <efxpt:clkmux name="CLKMUX_T" block_def="CLKMUX_T" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
        </efxpt:clkmux_info>
    </efxpt:device_info>
    <efxpt:gpio_info>
        <efxpt:comp_gpio name="gpio_inst1" gpio_def="GPIOL_P_18" mode="input" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="pll_clkin" name_ddio_lo="" conn_type="pll_clkin" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="gpio_inst1_PULL_UP_ENA" dyn_delay_en_name="gpio_inst1_DLY_ENA" dyn_delay_reset_name="gpio_inst1_DLY_RST" dyn_delay_ctrl_name="gpio_inst1_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="gpio_inst6" gpio_def="GPIOR_P_06" mode="input" bus_name="" io_standard="1.2 V LVCMOS">
            <efxpt:input_config name="reset_n" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="gpio_inst6_PULL_UP_ENA" dyn_delay_en_name="gpio_inst6_DLY_ENA" dyn_delay_reset_name="gpio_inst6_DLY_RST" dyn_delay_ctrl_name="gpio_inst6_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="i_inject_err_n" gpio_def="GPIOR_N_06" mode="input" bus_name="" io_standard="1.2 V LVCMOS">
            <efxpt:input_config name="i_inject_err_n" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="i_inject_err_n_PULL_UP_ENA" dyn_delay_en_name="i_inject_err_n_DLY_ENA" dyn_delay_reset_name="i_inject_err_n_DLY_RST" dyn_delay_ctrl_name="i_inject_err_n_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_D16_blue" gpio_def="GPIOR_P_07" mode="output" bus_name="" io_standard="1.2 V LVCMOS">
            <efxpt:output_config name="led[0]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="2" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_D16_green" gpio_def="GPIOR_P_08" mode="output" bus_name="" io_standard="1.2 V LVCMOS">
            <efxpt:output_config name="led[1]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="2" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_D16_red" gpio_def="GPIOR_P_09" mode="output" bus_name="" io_standard="1.2 V LVCMOS">
            <efxpt:output_config name="" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="__gnd__" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_D17_blue" gpio_def="GPIOR_N_07" mode="output" bus_name="" io_standard="1.2 V LVCMOS">
            <efxpt:output_config name="led[2]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="2" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_D17_green" gpio_def="GPIOR_N_08" mode="output" bus_name="" io_standard="1.2 V LVCMOS">
            <efxpt:output_config name="led[3]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="2" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_D17_red" gpio_def="GPIOR_N_09" mode="output" bus_name="" io_standard="1.2 V LVCMOS">
            <efxpt:output_config name="led[4]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="2" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:global_unused_config state="input with weak pullup"/>
    </efxpt:gpio_info>
    <efxpt:pll_info>
        <efxpt:pll name="pll_inst1" pll_def="PLL_TL0" ref_clock_name="" ref_clock_freq="25.0000" multiplier="4" pre_divider="1" post_divider="1" reset_name="pll_rstn" locked_name="i_pll1_locked" is_ipfrz="false" is_bypass_lock="true">
            <efxpt:adv_prop ref_clock_mode="external" ref_clock1_name="" ext_ref_clock_id="2" clksel_name="" feedback_clock_name="mipi_clk" feedback_mode="core"/>
            <efxpt:gen_pin>
                <efxpt:pin name="" type_name="DESKEWED" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT_ENA" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT" is_bus="true"/>
                <efxpt:pin name="" type_name="SHIFT_SEL" is_bus="true"/>
            </efxpt:gen_pin>
            <efxpt:comp_output_clock name="mipi_dphy_tx_SLOWCLK" number="0" out_divider="16" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="mipi_dphy_tx_FASTCLK_C" number="1" out_divider="4" is_dyn_phase="false" phase_setting="4" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="mipi_dphy_tx_FASTCLK_D" number="2" out_divider="4" is_dyn_phase="false" phase_setting="2" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="mipi_clk" number="3" out_divider="30" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_prop/>
        </efxpt:pll>
        <efxpt:pll name="pll_inst2" pll_def="PLL_BR0" ref_clock_name="mipi_clk" ref_clock_freq="100.0000" multiplier="1" pre_divider="1" post_divider="2" reset_name="" locked_name="i_pll2_locked" is_ipfrz="false" is_bypass_lock="true">
            <efxpt:adv_prop ref_clock_mode="core" ref_clock1_name="" ext_ref_clock_id="2" clksel_name="" feedback_clock_name="CLKOUT0" feedback_mode="local"/>
            <efxpt:gen_pin>
                <efxpt:pin name="" type_name="DESKEWED" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT_ENA" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT" is_bus="true"/>
                <efxpt:pin name="" type_name="SHIFT_SEL" is_bus="true"/>
            </efxpt:gen_pin>
            <efxpt:comp_output_clock name="CLKOUT0" number="0" out_divider="25" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="pixel_clk" number="1" out_divider="20" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_prop/>
        </efxpt:pll>
    </efxpt:pll_info>
    <efxpt:osc_info/>
    <efxpt:lvds_info/>
    <efxpt:jtag_info/>
    <efxpt:mipi_dphy_info>
        <efxpt:mipi_dphy name="mipi_dphy_tx_clk" block_def="GPIOB_PN_03" ops_type="tx">
            <efxpt:tx_info mode="clock lane" is_reversible="false" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_tx_clk_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_HS_enable_C" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_clk_HS_OUT" type_name="HS_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_FASTCLK_D" type_name="SLOWCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_FASTCLK_C" type_name="FASTCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_clk_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_clk_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_clk_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_clk_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_clk_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_clk_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="mipi_dphy_rx_clk" block_def="GPIOB_PN_14" ops_type="rx">
            <efxpt:rx_info mode="clock lane" is_reversible="false" delay="0" delay_mode="static" is_fifo="false" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_rx_clk_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_HS_IN" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_FIFO_EMPTY" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_FIFO_RD" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_HS_TERM" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_HS_ENA" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_clk_CLKOUT" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="mipi_dphy_tx_data0" block_def="GPIOB_PN_00" ops_type="tx">
            <efxpt:tx_info mode="data lane" is_reversible="false" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_tx_data0_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data0_HS_OE" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data0_HS_OUT" type_name="HS_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_SLOWCLK" type_name="SLOWCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_FASTCLK_D" type_name="FASTCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data0_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data0_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data0_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data0_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data0_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data0_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="mipi_dphy_tx_data1" block_def="GPIOB_PN_01" ops_type="tx">
            <efxpt:tx_info mode="data lane" is_reversible="false" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_tx_data1_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data1_HS_OE" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data1_HS_OUT" type_name="HS_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_SLOWCLK" type_name="SLOWCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_FASTCLK_D" type_name="FASTCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data1_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data1_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data1_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data1_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data1_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data1_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="mipi_dphy_tx_data2" block_def="GPIOB_PN_02" ops_type="tx">
            <efxpt:tx_info mode="data lane" is_reversible="false" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_tx_data2_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data2_HS_OE" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data2_HS_OUT" type_name="HS_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_SLOWCLK" type_name="SLOWCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_FASTCLK_D" type_name="FASTCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data2_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data2_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data2_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data2_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data2_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data2_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="mipi_dphy_tx_data3" block_def="GPIOB_PN_04" ops_type="tx">
            <efxpt:tx_info mode="data lane" is_reversible="false" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_tx_data3_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data3_HS_OE" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data3_HS_OUT" type_name="HS_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_SLOWCLK" type_name="SLOWCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_FASTCLK_D" type_name="FASTCLK" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data3_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data3_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data3_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data3_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data3_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_tx_data3_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="mipi_dphy_rx_data2" block_def="GPIOB_PN_15" ops_type="rx">
            <efxpt:rx_info mode="data lane" is_reversible="false" delay="20" delay_mode="static" is_fifo="true" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_rx_data2_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_HS_IN" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_FIFO_EMPTY" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_FIFO_RD" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_HS_TERM" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_HS_ENA" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data2_CLKOUT" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="mipi_dphy_rx_data3" block_def="GPIOB_PN_17" ops_type="rx">
            <efxpt:rx_info mode="data lane" is_reversible="false" delay="20" delay_mode="static" is_fifo="true" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_rx_data3_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_HS_IN" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_FIFO_EMPTY" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_FIFO_RD" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_HS_TERM" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_HS_ENA" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data3_CLKOUT" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="mipi_dphy_rx_data0" block_def="GPIOB_PN_12" ops_type="rx">
            <efxpt:rx_info mode="data lane" is_reversible="false" delay="20" delay_mode="static" is_fifo="true" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_rx_data0_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_HS_IN" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_FIFO_EMPTY" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_FIFO_RD" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_HS_TERM" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_HS_ENA" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data0_CLKOUT" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="mipi_dphy_rx_data1" block_def="GPIOB_PN_13" ops_type="rx">
            <efxpt:rx_info mode="data lane" is_reversible="false" delay="20" delay_mode="static" is_fifo="true" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="mipi_dphy_rx_data1_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_HS_IN" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_FIFO_EMPTY" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_FIFO_RD" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_HS_TERM" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_HS_ENA" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="mipi_dphy_rx_data1_CLKOUT" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
    </efxpt:mipi_dphy_info>
</efxpt:design_db>
