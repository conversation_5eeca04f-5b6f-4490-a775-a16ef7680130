<?xml version="1.0" encoding="UTF-8"?>
<efxpt:design_db name="divider" device_def="Ti60F225" location="" version="2021.1.165.2.19" db_version="********" last_change_date="Thu Dec  2 10:26:29 2021" xmlns:efxpt="http://www.efinixinc.com/peri_design_db" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.efinixinc.com/peri_design_db peri_design_db.xsd ">
    <efxpt:device_info>
        <efxpt:iobank_info>
            <efxpt:iobank name="1A" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="1B" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="2A" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="2B" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="3A" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="3B" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="4A" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="4B" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="BL" iostd="3.3 V LVCMOS"/>
            <efxpt:iobank name="BR" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="TL" iostd="1.8 V LVCMOS"/>
            <efxpt:iobank name="TR" iostd="1.8 V LVCMOS"/>
        </efxpt:iobank_info>
        <efxpt:ctrl_info>
            <efxpt:ctrl name="cfg" ctrl_def="CONFIG_CTRL0" clock_name="" is_clk_invert="false" cbsel_bus_name="cfg_CBSEL" config_ctrl_name="cfg_CONFIG" ena_capture_name="cfg_ENA" error_status_name="cfg_ERROR" um_signal_status_name="cfg_USR_STATUS" is_remote_update_enable="false" is_user_mode_enable="false"/>
        </efxpt:ctrl_info>
        <efxpt:seu_info>
            <efxpt:seu name="seu" block_def="CONFIG_SEU0" mode="auto" ena_detect="false" wait_interval="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="seu_START" type_name="START" is_bus="false"/>
                    <efxpt:pin name="seu_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="seu_INJECT_ERROR" type_name="INJECT_ERROR" is_bus="false"/>
                    <efxpt:pin name="seu_CONFIG" type_name="CONFIG" is_bus="false"/>
                    <efxpt:pin name="seu_ERROR" type_name="ERROR" is_bus="false"/>
                    <efxpt:pin name="seu_DONE" type_name="DONE" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:seu>
        </efxpt:seu_info>
        <efxpt:clkmux_info>
            <efxpt:clkmux name="CLKMUX_B" block_def="CLKMUX_B" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
            <efxpt:clkmux name="CLKMUX_L" block_def="CLKMUX_L" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
            <efxpt:clkmux name="CLKMUX_R" block_def="CLKMUX_R" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
            <efxpt:clkmux name="CLKMUX_T" block_def="CLKMUX_T" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
        </efxpt:clkmux_info>
    </efxpt:device_info>
    <efxpt:gpio_info>
        <efxpt:comp_gpio name="clken" gpio_def="GPIOR_N_06" mode="input" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="clken" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="clken_PULL_UP_ENA" dyn_delay_en_name="clken_DLY_ENA" dyn_delay_reset_name="clken_DLY_RST" dyn_delay_ctrl_name="clken_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="fail_led" gpio_def="GPIOR_N_07" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="fail_led" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="pass_led" gpio_def="GPIOR_P_07" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="pass_led" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="pllin" gpio_def="GPIOL_P_18" mode="input" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="pllin" name_ddio_lo="" conn_type="pll_clkin" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="pllin_PULL_UP_ENA" dyn_delay_en_name="pllin_DLY_ENA" dyn_delay_reset_name="pllin_DLY_RST" dyn_delay_ctrl_name="pllin_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="reset" gpio_def="GPIOR_P_06" mode="input" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="reset" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="weak pullup" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="reset_PULL_UP_ENA" dyn_delay_en_name="reset_DLY_ENA" dyn_delay_reset_name="reset_DLY_RST" dyn_delay_ctrl_name="reset_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:global_unused_config state="input with weak pullup"/>
    </efxpt:gpio_info>
    <efxpt:pll_info>
        <efxpt:pll name="pll_inst1" pll_def="PLL_TL0" ref_clock_name="" ref_clock_freq="25.00" multiplier="4" pre_divider="1" post_divider="1" reset_name="" locked_name="" is_ipfrz="false" is_bypass_lock="true">
            <efxpt:adv_prop ref_clock_mode="external" ref_clock1_name="" ext_ref_clock_id="2" clksel_name="" feedback_clock_name="clk" feedback_mode="local"/>
            <efxpt:gen_pin>
                <efxpt:pin name="" type_name="DESKEWED" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT_ENA" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT" is_bus="true"/>
                <efxpt:pin name="" type_name="SHIFT_SEL" is_bus="true"/>
            </efxpt:gen_pin>
            <efxpt:comp_output_clock name="clk" number="0" out_divider="27" is_dyn_phase="false" phase_setting="0">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_prop is_outclk_inverted="false"/>
        </efxpt:pll>
    </efxpt:pll_info>
    <efxpt:osc_info/>
    <efxpt:lvds_info/>
    <efxpt:jtag_info/>
    <efxpt:mipi_dphy_info/>
</efxpt:design_db>
