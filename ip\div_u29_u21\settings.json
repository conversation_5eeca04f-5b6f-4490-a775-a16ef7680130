{"args": ["-o", "div_u29_u21", "--base_path", "E:\\fpga\\yilingsi\\A2020_demo\\Ti60F225_A2020_LVDS_Display_30FPS\\Ti60F225_AD2020_LVDS_Display_30FPS\\ip", "--vlnv", {"vendor": "efinixinc.com", "library": "arithmetic", "name": "efx_divider", "version": "5.3"}], "conf": {"NREPRESENTATION": "\"UNSIGNED\"", "WIDTHN": "29", "WIDTHD": "21", "DREPRESENTATION": "\"UNSIGNED\"", "PIPELINE": "1'b1", "LATENCY": "23"}, "output": {"external_source_source": ["div_u29_u21\\div_u29_u21_define.vh", "div_u29_u21\\div_u29_u21.v", "div_u29_u21\\div_u29_u21_tmpl.v", "div_u29_u21\\div_u29_u21_tmpl.vhd"], "external_example_example": ["div_u29_u21\\T20F256_devkit\\demo_divider.v", "div_u29_u21\\T20F256_devkit\\example_divider_T20.sdc", "div_u29_u21\\T20F256_devkit\\div_u29_u21.v", "div_u29_u21\\T20F256_devkit\\div_u29_u21_define.vh", "div_u29_u21\\T20F256_devkit\\divider.peri.xml", "div_u29_u21\\T20F256_devkit\\divider.xml"], "external_example_2": ["div_u29_u21\\Ti60F225_devkit\\demo_divider.v", "div_u29_u21\\Ti60F225_devkit\\example_divider_Ti60.sdc", "div_u29_u21\\Ti60F225_devkit\\div_u29_u21.v", "div_u29_u21\\Ti60F225_devkit\\div_u29_u21_define.vh", "div_u29_u21\\Ti60F225_devkit\\divider.peri.xml", "div_u29_u21\\Ti60F225_devkit\\divider.xml"], "external_testbench_testbench": ["div_u29_u21\\Testbench\\tb_divider.v", "div_u29_u21\\Testbench\\demo_divider.v", "div_u29_u21\\Testbench\\modelsim.do", "div_u29_u21\\Testbench\\div_u29_u21.v", "div_u29_u21\\Testbench\\div_u29_u21_define.vh"]}, "ooc_synthesis": {}, "sw_version": "2025.1.110.2.15", "generated_date": "2025-10-06T09:30:22.882237+00:00"}