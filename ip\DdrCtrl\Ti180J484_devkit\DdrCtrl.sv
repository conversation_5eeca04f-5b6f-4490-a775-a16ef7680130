// =============================================================================
// Generated by efx_ipmgr
// Version: 2025.**********
// IP Version: 5.14
// =============================================================================

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2025 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//                                                                       
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//                                                                       
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.                                                
//
////////////////////////////////////////////////////////////////////////////////

`define IP_UUID _157b779458a24b49aa47770753590abe
`define IP_NAME_CONCAT(a,b) a``b
`define IP_MODULE_NAME(name) `IP_NAME_CONCAT(name,`IP_UUID)
module DdrCtrl
(
    input clk,
    input core_clk,
    input twd_clk,
    input tdqss_clk,
    input tac_clk,
    input reset_n,
    output reset,
    output cs,
    output ras,
    output cas,
    output we,
    output cke,
    output [15:0] addr,
    output [2:0] ba,
    output odt,
    output [2:0] shift,
    output [4:0] shift_sel,
    output shift_ena,
    input cal_ena,
    output cal_done,
    output cal_pass,
    output [2:0] cal_shift_val,
    output [1:0] o_dm_hi,
    output [1:0] o_dm_lo,
    input [1:0] i_dqs_hi,
    input [1:0] i_dqs_lo,
    input [1:0] i_dqs_n_hi,
    input [1:0] i_dqs_n_lo,
    output [1:0] o_dqs_hi,
    output [1:0] o_dqs_lo,
    output [1:0] o_dqs_n_hi,
    output [1:0] o_dqs_n_lo,
    output [1:0] o_dqs_oe,
    output [1:0] o_dqs_n_oe,
    input [15:0] i_dq_hi,
    input [15:0] i_dq_lo,
    output [15:0] o_dq_hi,
    output [15:0] o_dq_lo,
    output [15:0] o_dq_oe,
    input [7:0] axi_aid,
    input [31:0] axi_aaddr,
    input [7:0] axi_alen,
    input [2:0] axi_asize,
    input [1:0] axi_aburst,
    input [1:0] axi_alock,
    input axi_avalid,
    output axi_aready,
    input axi_atype,
    input [7:0] axi_wid,
    input [127:0] axi_wdata,
    input [15:0] axi_wstrb,
    input axi_wlast,
    input axi_wvalid,
    output axi_wready,
    output [7:0] axi_rid,
    output [127:0] axi_rdata,
    output axi_rlast,
    output axi_rvalid,
    input axi_rready,
    output [1:0] axi_rresp,
    output [7:0] axi_bid,
    output [1:0] axi_bresp,
    output axi_bvalid,
    input axi_bready,
    output [7:0] cal_fail_log
);
`IP_MODULE_NAME(efx_ddr3_soft_controller)u_efx_ddr3_soft_controller
(
    .clk ( clk ),
    .core_clk ( core_clk ),
    .twd_clk ( twd_clk ),
    .tdqss_clk ( tdqss_clk ),
    .tac_clk ( tac_clk ),
    .reset_n ( reset_n ),
    .reset ( reset ),
    .cs ( cs ),
    .ras ( ras ),
    .cas ( cas ),
    .we ( we ),
    .cke ( cke ),
    .addr ( addr ),
    .ba ( ba ),
    .odt ( odt ),
    .shift ( shift ),
    .shift_sel ( shift_sel ),
    .shift_ena ( shift_ena ),
    .cal_ena ( cal_ena ),
    .cal_done ( cal_done ),
    .cal_pass ( cal_pass ),
    .cal_shift_val ( cal_shift_val ),
    .o_dm_hi ( o_dm_hi ),
    .o_dm_lo ( o_dm_lo ),
    .i_dqs_hi ( i_dqs_hi ),
    .i_dqs_lo ( i_dqs_lo ),
    .i_dqs_n_hi ( i_dqs_n_hi ),
    .i_dqs_n_lo ( i_dqs_n_lo ),
    .o_dqs_hi ( o_dqs_hi ),
    .o_dqs_lo ( o_dqs_lo ),
    .o_dqs_n_hi ( o_dqs_n_hi ),
    .o_dqs_n_lo ( o_dqs_n_lo ),
    .o_dqs_oe ( o_dqs_oe ),
    .o_dqs_n_oe ( o_dqs_n_oe ),
    .i_dq_hi ( i_dq_hi ),
    .i_dq_lo ( i_dq_lo ),
    .o_dq_hi ( o_dq_hi ),
    .o_dq_lo ( o_dq_lo ),
    .o_dq_oe ( o_dq_oe ),
    .axi_aid ( axi_aid ),
    .axi_aaddr ( axi_aaddr ),
    .axi_alen ( axi_alen ),
    .axi_asize ( axi_asize ),
    .axi_aburst ( axi_aburst ),
    .axi_alock ( axi_alock ),
    .axi_avalid ( axi_avalid ),
    .axi_aready ( axi_aready ),
    .axi_atype ( axi_atype ),
    .axi_wid ( axi_wid ),
    .axi_wdata ( axi_wdata ),
    .axi_wstrb ( axi_wstrb ),
    .axi_wlast ( axi_wlast ),
    .axi_wvalid ( axi_wvalid ),
    .axi_wready ( axi_wready ),
    .axi_rid ( axi_rid ),
    .axi_rdata ( axi_rdata ),
    .axi_rlast ( axi_rlast ),
    .axi_rvalid ( axi_rvalid ),
    .axi_rready ( axi_rready ),
    .axi_rresp ( axi_rresp ),
    .axi_bid ( axi_bid ),
    .axi_bresp ( axi_bresp ),
    .axi_bvalid ( axi_bvalid ),
    .axi_bready ( axi_bready ),
    .cal_fail_log ( cal_fail_log )
);
endmodule

//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
gorerY3uRzyhSBvOLA9r4GZrccR7nqHMlAhk3BRMEyl0goj6MKaFQOpchdOyNhwq
TZe25MmUFBbKWuPVQxu9wOUEVIft6gbtzRNK6C9L6+hypfJASs7r5B45CK5eQDPd
hEyTFOvzoowic98wi9epx0UBJKArCgLlnBEYdny3x+Wa3vZI/KBWxXe4rkIxftZ6
Se9ufaII2x293AcUSw28NVNFXQ3ioEADrLC26pcOE08VmHdOsXXYHKAbd36MIazG
kjAHyEkUViLEYZexaWXKAkppN8Gn0Dxt/N/ArsNXRLcWhCiF7jjeU0z3xMn/pwiB
mmGn5PNrkCkQqsKq/39Y/w==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 672 )
`pragma protect data_block
Gh/wt8CFSyKAUbv2rxDPqKJp5BE0Om5S5HYCy6u/1xA6+KM7A6+LDEwRL5UKFxFi
q3JLDUeeoXFef4CII3XV70KSHWwNJU/ewxiD3lIBhfu4br3CXObvhf1ST6Za645h
ghjIR5yjJdOzNJFuMQwmfaTFWA24JqOi3VnswRoxSzmSOfZhVfWOKmyJnbDbpt+e
HVeYDHG9y7KMCFkYPnhH/KoirLvNVrpyL0AivfVdLcIiBLbPSpf9Jvtpxid8yIiB
L+PP58kpDjUvd1H8KDfAidaLS2DL1RTuJaPHV9XevTDaoqeKjn7E+R8oRBxNuGIG
+aX4+5vvALWk1hfzOrWm4O7KjKGDLQK03GR3X1pkxWXHTAm2P9drpqd14RvKf+7o
EpQWrhhiCu4IyZArHCEd9p8nK3xYgV95Rxbcjet1SryAOsUSVFJUU8Ggm4cgcyV/
0ffnUM4IDR52l/DOmOJrL2bBSLj+4ujNZ+Ri0uhmLCfyDkSkhQ/9ODpfdoaTjKxE
1CiFbL/Pbn35rpF0unCGQ6aiKKOx3RQqwXs/rfRjinOwusG+YWPBxXoBrckB/EZM
KosWd9a0ujv6Fnq16xZRuTVeyd/6vUojginVONJQ7yHLl08UPBinhG/Q85mV3UKC
5xc4RtHFr3NlFgTPJ7AJmzjZGQaOMWPxUwDBrKWh0NkRIsMKiNdS0/cp/9iVQIOM
yrlVYNCM2kTroGLSDqpnZgcli4P+4yULnvahVWUMqDDQHilGiUw4CrRKFdRp41g8
ahoZsuWJfcR+UkYdX0y5iShlur/MQR2Pzw7FhKAdSbz3y9738Dalydc9xx1xzog7
ZSSgMCFXYVczClZOLgc09OaYj0CGpKYVsHJyDa9cXzJXJ9rTg4zAPk58B3QMX7Ki
`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
NhaNrvRh3LUJS8KdMO99fpLx+pwEbiYFeqW7cNueec8xT6xjmPXJQbBWXV+mc1tl
svGR51/S+cCLMpJ/x/ReNXXzqcFW+rZHYZtIqOPFikKORR+bXBeq4wxya/ntNBvr
61tcn8SxHmygshJ/pXdEZJ1ti2T5GxT/kNZuibD7phwF3I3QV2U60xFGNILGkv86
aMombTZky0QFN7yXuVJGdjhdnOxwe+xDeBw7fXu6RzcsxSijpuPIT6EzqSDRhUov
W95EXM3+JLcHXHH+rcdP+WwSVg0HkNgDITesPqG5fZOIPzbHjyS1W1Uh1xCcZRBD
SAWvlxakzSrhn70yynNwvA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 672 )
`pragma protect data_block
GxEthXjSOMd+ccGXcVvYHsYGXaf3QiZPQlSZ2jE6UAwYwmXUr7dsVKg+E7deY0o8
/VAQ0WS76MSSi3cK4qNlRK+5NnG59YccYmMp+1AasBMyCo2cl0XF9Gz8WIGO/WoS
orf7p0i0/tZwHxB33sLVZuTmqKYp4rwG3ihUxuRA6eqyF+YeOnXHEydsPTEqqzBf
KjkG2WwPOyFN7usY+2hhD5kmPEqt0XRna36oSi1AfGQ7xHDDsnS/aX6XN1oxObH7
Fe7qANjBLUFbMe7c5Zu5LUeX5Kxv1jhGNOvmHAO1f21KpwT4Nhw3jAJ/5zB1xBuX
DfoY9Ye2uEbRwO8hIxvus2aZyJgjkmPyN0dMFEcH4Om/YFkbYIWypclMBPSJF4Ht
joXHis7dBIh1OySE5XlL6zlu5y6PtTVPJ5agjQXJXoOik1oeWpMv4mJ33WLcNvN5
JOKO1i6KBywNe0Z1Fuf/HQwyjNK76WALOw37UY69VjkrCxWKSF1vj4IRnICYB1E+
ix77yD1eYM2MBRjZ48xsyE2J5WF9fA0mY1VJb33c+qKN5be06rn6DF6LSiTeKemq
vRImbHjei8D7RNltPHAq+p4akbb5XVgripoRQchQyc/jnUR2Wc0Qr03IYB3Eh+tY
zQvdw/BNWpdwrZJMX7Ly/r+/G/VLNUduXn+auyHpJITsIhzuSzV/wRCwUlfJKglS
UlpwnoMw5tMyUXfdOFOZzQ5Wo+C3Wkf+xeSG8+jzmYcF36aJzDAAIjrRasg4dDOF
b2anjuUvLvyCUBvMTPyYbpc7iNNn++31EtM2m9Ben8S8JI7/nchkpJYanxZUIiqY
NirCxkZgUZ9TMaHUwI5pqHazsNn+0RgQnDM0021+0eU9VIFfOtkjrFwQmEzs+WTh
`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
MLcpZzGGIMsrt5b+6wkmU6weFIz1qRKpcLOSGuzX9KraW9fTISTqgh4Ahv5piRBe
hR4ySfL1bma0VsbNDBe4emrvJSwfeWsy7zIBjQ1ZJndbIYBXyKqrqXhhRiT0ICh9
JA6c+P0US6mbfaa43/uA/mOPpQwrzfTWxSyuFm+zsvul455q58mKuWBRAuTRhgCf
BAhncIYCvaYdKaHLjZUm1+WL0OlMfqIRWNtHiM8eWVp5M3lAVJtGS3EcWISEfN98
QB2ZYgzwq0WmDqsUoI5QVCqzrBToTgnshjf038Nf1zHIrW83MDIGWLVpsV0ihveR
+itlZsT0isEAcBpZNpqe9g==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 14448 )
`pragma protect data_block
TEgkXQqq9V0wAsaZFrC55NARC4dq7pSYFxxmYhIGQ1YRdn1zAmK9/r++Mvm+001/
99/Gy27GUSVUPrRMxWahl+o0jMsmDknmj1GbpiQUA7xSXwZSRdMk7Bx7DQNrzzVd
ixbyZPyLibAQXkgwOi/wDukbJpx2rbZHyYlhD3kUCSTbEb+64RcvWF0Ccm9r3JOY
dikNTTbUQTqyjWIGvAlMpgXDtBrBb6ho8o3CSlZmYiAT9Xk30QeMcylyn1yqvVj3
1egurQRyTeovSoGFM7CblFJLN+HS54oYQ8PrbpDN/Z8In/h0ftd/vseR+IjONVoO
kGI7uUYyiHm+X38DSptP3lcxKXyBoIDglW+qZxut8cLPYTLimsdoKj4miKLCJqy5
HlJoxZcyXL/pciRWiJkgK3cMbxr421q51N48abpkwoyFg8LeyQgcolx4TKZKDu5z
sSQq8lAHr9wHNlPBUQAPuwE2Niiw4gUJatWUs5VIsNDHhd5A1IeGK9PQO/NV9p5X
stR7yArhZ+pueniox2jT7F5jRmLe764lhJNoyo9hni14Khti/87HF1Lo02j+24H6
XyuAs9wnlISDQjs61IOnAtpF9y5SAI35QOJ1GGGP+OqT8qJrJTPF/OJaLwnf00An
ghuhvW4VKy+Dd7wRXJYDRvfZStSM+RFkvp8J9Pph8pI8ZmdULcKUcvyY6z7pCBwR
LmiGmYfAB9pcAXQTlnClTHDUGta8rL3KtMfEZeFv6bSN3rBEH9QEAw/qingp24oe
/Rgg8muk/geDxEwURNK7IM0jS1ZSNJ/p82d2CEUjPsiSQjBZ+aV0evzTPcIgpifR
riUkLM28fz7YpUCDVA4+KXsN4svUQu4/UD9f62teEgCUw/y7wKgb6w3VPth5YPB3
W5GJgrikAUEGO/iRK/5n1kWRgECleKX6ccXzqGUDH6QAJxPjbWxhMktQXTi904hS
ob2TUxA6kSE52pV7bWphgPCrclKZ/p4BQeQ9fBSxIh/8lZe8tH+eXqX2vIASOd5M
IV7tIsZZek4TSqyfdcZgzxiTqhNOZQAx/DhXG3Jk3W2H+OEEy4fui7+JtaIbXbqK
NVP3h0Gs2ujyNrqIuvRMAkTlYG9Sjgzb92NZoqj3wSa4CRJ0panATTKD/b4QVntE
pCtV0PNdHCYjRknsvKhxVlZnku6Xy4+IXEwISo3GVT+e659flsWZHKjRmBH/2cW2
Gnr7GhKdo0pYo/V1VO0U6CMpNS0VsoR1F161m8crig7SnvANiLruPt9wTmhHVGdl
0w2rTmGulxJ7JkixXs0NjFcLgCoD71T1J53M1teCTQQVtP8Se6bqJ0tMompvHYgS
0f5ujIcj3w6cMnAm45LW9vTPccKv+mAS4xs24PrR+tdP27bDv01xyHwEnoSEik8G
6LZO14tMD49QuXjHUQfDNUN6eINr2CYzjwHcjiVLNBSd5DzjvCaseIE20qSLIzkP
h9J8vFkkqB2u6+O4bQZ3SaN5gmcG94A9hb9xFu8P8UZGluDMgRsBWgPWCvbpE4QI
DxQIW+3fSZwW2HpeKDdQrPdX3+yz92n8YEESP903jBlJuDQ1+wwRIk6hGKC+8RXb
D3f0OQdv3hhqtxI2wEdX1g8nA+TWvRJmZNkKtyuL8fChLbvzAcXYMhiJlrh5s1wZ
ASs264T/gvglmhaKnwD0cYUFPdNf3MDkzUC/BtKdWpiNwPkQAY+FMNknReDCk/PM
MV3bbKd8lqbVnWEkvuuysInSH7oP8VW0JvLmpKrnuNSlvwAArIRIrXk0odKQj7FU
aeeYRvSNjjjj/hwvbpEn8mr1w5kyyzeNSAEXM5es6EM6rBVfVkt4XoGYPhoHHH90
sp45haXxq2rCbAt/9AlE+GSvZhMZcMMAPwf6VkkrlGLJ4SM9HYDY78QjemDUcd7r
Xc9bWpzuPlLsnzkFhwqSt1httbxoZiCjDy8WgyAr7vr4oeF8bX7TKBzILTc/PJGQ
PqC6AqnwVQ5k/RO+FPc7Vu8ozUZLglsM1dBHFwIAr52bITonDzdUJl7McRXp7GT4
WnhSU9aN6acngOWApgRD8sPWbF4Il+OOc/U3Xwq1eU1be/SkE4U6/OWAKIuDtb+x
czqKLNnMDcd9vpFAqi6J0C0xy7pwdNar8/ojxOJM0vjzbMYmkz8fm9NAHUpIKPG9
giYfPL0eKJcB/lyPgiFmYbpf3nvu7KhEz0YQ0nXdGjqoplJxQmeYJCOZq3GWpIf3
RVV8G7krvtR+kR3de3uzmZiog5tuyPgneE5p9sx0wlO178drZIxG8qeSEDLq6rgn
CKPnUu4tecagVnBoVoRsMQEUm6fWK3AsVVqeuvvkJwy6xBvk1z+QOGotw4vbh+Q0
fWG9FfPParf5O5dv03B9H33e0Gp651htRp8eav/sejXo//mAkYXz7swSBDeNDgfX
RP2VwizM1OybvWUSKN8lzZzxCeEqCM2QLUOE0sZTjxj8KBnaUwwUC0RhIdLq2FLo
HthSFNWaD2Y5m48TcdJ68oZF9pMQQnrXP3Hryg6wza1XfIBS4ICUHza1sIxLaocH
qkvyEipsHTHa/i1kum8C700uTizOYYHC3mFrZDyEj7r9cX5VrVBaKMcl2Uw/6GGa
GdmqgBDPWLLChZHrGT5tX8rOvbHa2d0Q9an0uhvfJ16HMAj8JDz2J/2oy27P4r4C
HtCkMSZvMiWOyJNTLoOubM0+1FuBS98RtYS56Vtrqa5ZGmkR8QaJ1IJCw43/HwMv
9qB+YhB6GJxyXnbGxmn3sipT8gDHtGXPQ3IWg0TJYLqQEcsmWjG81T1DmMxz6EHZ
UM8ckSjl/3tM5RJLdUHQqhYm9BE+wcjSs4DZVtdAeDmkqpvqoP4Lw2t7kn2Jo3G3
ztUkpbctJbjLYFTa4Uiv7dQWAqTKyc115Q0oeSWbDJXuAL0xLaqT/V/5pXgAfbcX
+You3x6a+DahrSTH6o0vv/4vnXwC9CmN7YvJgduF9MdvlOAfFP9AIyF7Wa59ExZ9
Eim1lqt8egZzzEmJ2CPiU1oGVCU9NMKH3whNcXCCYLlTYWpgM4PL/6f5Ghvm0tPC
a3el+I0ukb9ruR7ocZxPoLiiYWtfhgBYwoSrNeB1ZUngsrb4HAgqvtZzeTN8zezx
6OcD6qTaDYXTGrFJQ6QyLag0aXIfa++ZWhdOx5ZR5+i3fmyQ2syb7ouib+mvQEgw
G+4ob4rwICm9Jc8BaC01P0iHUnmv04HL9QD+skCqdJMI80LuEgHNA6hBNYgSjr8z
Nb5viIjAyvv2qBzCKjNs/bGX37+X2MhVnLJ+HkApyi4mtCZ/Gvu6uj9iTy22oP0K
0lOlzH0WllzuWfARHLZk4kvleFMTdMy5+7MnuVzr5vp9B6ESifm4a+rxjJhbc55h
Ph8YpwVXU967B3vcxHsBX/1OiE11Rw0CfvAmpaD1ZTg03pv6VzmxZCCTfLQYFmhG
3rRWnrJebUIBAJqysUR2YSn4FrpahIHf9m3QpJERP/OLvoCb0wghyQKjvxHHwDxd
CWLpzm4HYMF6yOOmA81KIBuV3ALKcrBE+eskYkXHpF6f5KqGY+nlImKBWQZABLRu
aK30G7fJWdE3q3T/1LmSFh4oazd+oH5lIQIDAgYu3r6hT46nvQ3HKpvtPZw2rlvX
y0qwhtvl+Rz+TXu9ok+c0OsO1u4dJRgHzA0psOMN3+aeGsSFUtz28Mto+Jen38qT
Xfj4SiuBB2EynNqxiaxQXPNbNhjW/tYmah2hv8lnSKXZqULdrBKXEs47oP9U+7Pb
mIpda2sol32/IEJeuHWv3GtMAp89IYDcN+pxIxGMB0fMtTQrP3OTJuLDuJjXBS+R
HE5yIdUqWSxJjTx6hgkA/VWgGabkr1JnSdRrGoXKknv6w2QgFIdbau5CiACFUBCk
zpDlyhRP1LJVqS32w8mVsFlXgoT1ND+KpZOGoyinmzy/flgS+fKFckaEJ68b7lsQ
DZvGsVIp8BDIpce8FZeDy/PLRbPXjvrtQF9ZII9tHDfZfOiMTL/HZSWKaqOCoMWA
biEgsjU4EtOTbhGmT78d9s1IyVOxyJGQbM+NNJsw4e+o8O6yjp4DA6jCtI1we6bk
nYLJX0Szj7r95eTneovNIYcJjeNFwNQRWm26QNddLV7738mLeFt9rcPn/HEHyGzE
UUmjUR1Y67QESNluuUuTZ6BfHDokbZDytXvjRoYcMYjTE0PSBGdgB9aadY6CTd2E
Tqhn1jvTT6gF0CqJNPhN2HPOxKelgX+X9nHQMphx0/sMG4Rv74IZgYIREQPSy5nc
/acvszeJ2LcsNuAOduCBG2vtYBMW+oHLA3EnA6yUdW2hWdBc8RnusG0jdAUYTWzH
226CnQZLoaGq1nD5Iw8jcAuo9irHF+l14Sna8UENQmVCl0KyTIG/MQTS9GRM7mJw
f+a6eotyRrgJs/qMS42EKMJM7kd49ipeVTzSzRQCAMUnDzeQiydODBOPGnrB8p1g
PY2RA3xkKGGPeaTF7utPrIj2OcfDVG6POKhsO4taladCpGwEhcNltOrqA2gtkmKo
8+wi0UFFK2n/oY1zDGef9CyvlMgLDRpyNXPicMZ90Ynjx6LfGO7K+BLyGgCmD04K
6COmQZtAoCt2SPgg8t9rDiAQEc3vaYdHUdV3hF9ReWzA5MvJl1IuWLw0u+/X7yFy
G0ecQpBaZrNdLCKVN0YO9VNIC8PWnydfVqpeNA9wY9BFlmeQluJMggHpH+KCRpWs
revpesIKDEA+d4Z2vHGRobYK9vTz7t+8GBz2mtSvw2RJdFPrD9BXj3oqdDWwGjux
ZjqKGNUB0Ynya5NpEKHQJ/X30owl/RMg8VDXWKg1Wnfk81QCJ3khtCkpvgrhlt8f
d9nBlrEP+tSed6jPca8pX0O1X3CJ1nupjxiZIVW83r4BZQUBOeVABsFqQRsEzObQ
p03Xx+yuJ1XWO8zV5OJwS3ohdm8imRX+T1RrTInUkovn3Yi6C5Pt9+UYoN7Mzs1i
aS9vlNPxPJI4bOoNrJQ58DPdED3ZzaNGlUA0XAUNDnDdYt/IPCkvO6cUOxLwaTDc
G1WrFBeYjT2fDdRL5vdJ74gG70T9FKd3MrdGoha3bJogAXjnoxz9BnE2F19tqP98
qCKjpH14VIerR6M0bfmDH5iEVwRs6Nfgs7UOPfj+MZ6gzoBr4+SpTwwufNO6O+7K
7woiF4QLxw0PZZzTU5EBjj74fR2Sqo6ru6vpkevJdgz93r7diNmJ7zkoGDG8gtl0
Ka3ZHbvjZmfUs1+P3un7EJltag0g6CZxP6Wh3QUizQCqesLvZ5Nw3NNAD5ruA5Pp
FCZyvIV1geklzXhee8/XyW85RRhT1wJy8+5t0COEfhjvYf+XgLkBQAlz4BlykHCi
H+PpomW308dE8QvyMLQ5gAUoHrdVoqR2Eglj9wnSjLk4kKLYxQM4RAl5pXPK7XA8
AxDzBbA3gnv3l3u/35pbXjdS+1t6b+UN+ECKm3bxhanIFH5XmFog7pPIiO0DW6bC
FstGBOAuplvbnE7JvF5BvqK3Sfbxuego/eu8fNk3wKgHmzpNzA6rrrfFpVPU7Q6O
TD9xk8KysWVTkxkFcfqMQ8NKjzG8uoRoANDwmiFteZ8Dn2nN2vvynuRY1EBhXLdI
N+rHLB9OcZqdayGCACG+Uzhx6p/d95ko6tnKcEUH4uNVf0S7Cie89rfakR6DezcI
+Slte89szl+UUNBFV++OwppuqyLCo6mEdZghQMNFBobZnfoRLFecBznAA4sk4BzG
bvMNsP77zLkEbprbtgLxjLrtVStMfbNGTLq/Wdc3OpFzARnP4/93FQULyj0ZSqAN
3HI9QuNfptlF5RbS4MBDDRC28BZfKS/dNOkvyzOqWzzWjgCYDki2dt9v8gPBufHW
gaXJ96wV8NGmmMLADq9qQMT15ZEoRHRonJJJe+ILTAvJSCga0sFfJbypUdqy+wSf
OvEGNVBCveWfwUzjhhTqV9c4Ihgm3l4Ag1hhcK60MwAgoTkegdmamZ3R3rUeEmwA
7PnaSwtahxi7DUanec6M1ouNsc4KC147O8ooZnB+ARepXnicmU4Lc8/3E2gGs7ZV
8McSqyNir7Rk+9cd7k7xzoJ9YQgyE5S2SnASHDUXMbSFdtaX4uE8o2olmECZJwgl
jC920HJZ0M0B05avvEJI/ashpPin3iQep4xcgJGYUHhLWxIJzhNQ7UuoAgA6+7pL
kISN9JbR3K0AeL3FPxIIltttcneuBlQZeOCGOBCC2aMoe+CWIwxi40bSMvLgySZX
rDDgs3PKhifmvLSdgTxsv+ljL3oCe52OclmKbXl1nA9FMqHgErkBE4pjfJBUR3ys
8PJd1+a+ptWi6e/qPM5e6ZixT/gODXoCJ4vkc5uWoEYqwACdonqR4oVP4namrl0n
bHGM31jeRbUm1oaPbvxFehNapiB6SevUJfC/pGUZLSmZtopKH016t7QgtGLVRxra
XKf+UnfvVVNRL8SvcrGHFdbHxjvIQNONe0KYEL6Ri9t64hgvKc1wjusRkPn1vwv+
U+VgLTCGvgkcBwCnaqFZ01LcGW7WH4Qz98fA4+krC4NSvrHeyhStzOeWwh+oG2a+
8OHCCeRJgMEQimF75EgaeP3zo5AGtptObg6AycVNW1bFIQAkUPOb77G1j3u6oI4+
hE3AbxwtQbXtVr+aZNfBd1ryFqdcRGne2yZ4jtPXRU6NPqcokqo44Nelu07DgYOk
FYAq7laAkcs03L9IPCmwcTMTNq1EZqVrwUEGrUTCWq1OER2Znz6LXhmVc1a84LiG
09EvJ6SrQeLcIUK1184ldHf0GftTPMNnRp55zUHcNKEmyDDbbJX1omUlp6T+DdKb
4lkLWnTBQ7smMxOS5xSkqgW3oxgsp9UKr71X180sbxaS0llCaZbWmBEQCdQYvGRo
Zwc7VhUqTbYboCZoo96jZY+44dK1T8H2WCJKp3FwxB48kEf1gVeb0h4qLPo+6Fyw
xam5OjLMCgX16D4t3ZsksylS2Aw1fhicrJjNB+b4FonD/c2aBGKoTEpg17CkeIOm
nmKJ2gKI08kehE/BJWurDDCsgq81G/OblwPS36Bamh9CqLdtzuvnqy0VHWfV1EWI
AiposuikOeMtZZBJf8NvjEdSeMvMGU4HDFAv8y7dkmkN9hvLB1MWuYH5GiSkULLa
N7X1dKNiCbiKReoNcfkHT9Ron93FSfGWTXUjMh1wbemPo2ZwNMxiWrf8VqIfhXfs
LlKZzIr3yQ+OLWvBH+agK4MuyYS7810IjGKFFvsAcjlQucVIRKMs2MQcXE657onn
sNBcXHZsA4UcNDPsm9M5PM6XfgL946pCepW5/K3MxOvCoLhTB5Vr3tyhWSoWl4ne
goFvyBqqyUdrxo46bEVLW44iKTzyR3pct9ERvxAOgGD5c0KKIgjXDJKS9NoenJDj
AcwMwibanaM8l9r41XTyNNJaQnNeJfW5qYxMXxhfRJwa2mNyXlZ1nbm77x7ZJUF+
NtEHeAzqRyUmzqpXLXF4T76zwZX9KkyQZ6+30Xts9ksBhwJMhGkffzXfZpWwvdxh
PY1NS2iQdn5eN5rriPPecr73EvAOI22TsaR063ORisqkdpC+F270hUvP23UpKkcD
oMql9hsrZYr8MnJSQw3II/jJ6Wnjcr8WPw4Xc0eK1vqqi2tx+qzyHBy8Gi8hGKWK
ymQu1F9dDHp/bw28bHaZTCyPnlTY/gpFta6XCEGj+DYElPIj1SDOjAc1RxVDjXfa
OZRS5bJb4Foo3+AyQb28sdTilobDBDwXXQ3yiWdGbviL7SPIHMKIT5zdfbA0v0KQ
+cxKJQpsvxV1ky0FDl5vXeEmSnteQApajH96JCiJ7ztWrTMN44dweyfPcIrSd45S
CwGZ8XMoENFZ5UsYsFH8WqnPgrwjMi2qFSJeOb7bBTPmD3Ywr54LyVYy5DG7Y9RJ
5/CU4SKUYbIAEt1ThLQH2btR7Huj1I49LgROjuQZfU3K07+Ng30VytDH5YV44DjR
AQI6qkLKTnLAN8X7Xw0tSp5GzzcmRmg4+JmzKh3gttXu0SlSF0e0XzL3HvYtkFVM
9WO8OYZRjIEkp1dVXcuYDaCoYgVujqnfUxaSwP+s98Cvr0o3OOMXVn/ElRhSptMR
0+2TYEz0eOdq0n/SJc/SvpX+oTKQB+ByE3zOMCSh/HBa1Hc4R4flqFpAhgxv7nv9
dhX7wcxiLBIvCtoRU0FZ3P0X2LiMYvbFhhPnDUb9epmLwY5pZW/2uQOAsrdn8rmC
u84kkhO9hwW5JcYBX5l0YyuBATenY0RAvqjI3+3zKPH3jIWYsdUpczDFtVE//Gm9
ATd+OT8jsxRBTAXyoT0JG+IpzxI1c7DiFpZe1VjmPYmWVbBZZ3h0IPOPHu4/4XVV
EsXE41BX3bWZRMNNTGoqdRDqfZz/0jfXmJXocyyxb2RCyB0ZzNtgD+GjFey7LPGj
W4A5ryA59f2GkmVpRzEk8LXdC/dIQJdpzsKkvNeoQmAn3DJoACFLNV6U8Btw7wTq
CGpSC9zXJrSAP6Gc+6nZ2UQPnvX/HPHTmq4MFtByqgY2WXzKrXq2BuA40V8AeI6K
xSFQI4H/f/wiPooF+PF/W/7S60qc7VaDfQ/c4vH7FHXnWki5+1rV8LFX1OcAcmHi
YmhzaUglhwoAJmQT63dWH2/burqVRKlVDlUJUjQtpTi+q0WX8OjhGFCYIEjfRjD9
3n9p0oM165UE5qaUJ04AWzwM73WiY1Mukb6GyqfE+J4PtLBNbUVPKkQpDZxoyvIa
mwubRURJueG3okpG0y8TKuRfaBCXLUAvmHP6qecwEEGOsa8cjpaBKodKVQ5lxskS
NZkJjQFHtktkLS6PBpVy1gmdoCDbnTkiNxn8blTZ7rWrW+ePHBQngwbebViNkSAO
8G7Vi9fCpbCuuutJ+BUwKoNdGNDDorpklBairKQZFjZ0Xjj60SvszNYIhFxBCUb1
jlvSL1mbyaR1RLT0zvrikZiOZNKvuS5XLSv8SCelOJfRZQeJGK6DW7Wq6+nANYWY
VdQunb1NtH/cl4FqYcTMER4XbOzLZL2yv0das3engaIam+k5xQuGGFvTuz3T85wh
SB6mb3Y2MwzWoxrMC6SvkZvwqEBfguM0u8UPnx8xVwXRUd5IsuYfMjmiNePJQS31
8cpgz7hM0HDnXHmZ3dcCpbXSuyQ0ZXtHo4TTaIQpKe7FWgOF5FnRXW29wMVJ6BB2
FBbJ6ZPjYGJuZKOgg0YNmsanBRPy57vs8aSJ4qUerSRgKP5DGU3Ci4CDsvQXcU+9
dn9znyDE8AlWYVRyAwurFFvIsSMI4qmhwAtZEmMBY839SpEHRYWX/Vj87ixA6DcZ
xwPwr0rvKUoH87CW1/9gG4B9Mqd+hUPEkeqD6DeNKQ/2r9uWUdvFpIfnEMdOntDI
Uvl2tgH0tjFo9y+H7D4vauWxdMjkLM/dDkO3gI3SgBZByGHa+PPfxv2iInO2FRKj
4yrGPOSZ3XWh02wlSaFsN0ubdaf8bKfyhyNZ0NiZon12th6KemCh8aM50GgD0ofg
2iU84rbWHOR7eEY3qkSD4AoHDm7WloLNBew8lNKHQnrPBHIl+fKa/rFTHCV9z+uI
k9z5aL9ipWEIse84zBp9R3f7pgCfonMrspmRTln7woYlvMyebG3j9Zx6XyziuTKM
hoCnnoP8CRkgMMmrfJETyu/JmGhZdGvyReUAytF4AzlTY+hVZExFCS+CD1e7oNuv
0U/G8S2WVCvzMToP7s53ELXD/DKbKeyYtRccDlUDUDjLp/Ut7f+QRPaUBMNIh8Kx
EsrD+FpiYb/jcV35ud2AvtbLdJpezGtxgPoazZTZem4NI5GxOvkPX/XzzahPr+Wx
4oYwrtK2hCp+SoEJeo8WsjWWqx6qipuk1DHduPkEPphiBgUY4rEC9EsktQRnLpkV
RzWkxv5DMWD6YQXmidSn14xHloNfvoDEdG4KDkMysLoWEHRFerTGXdHFHIEC7Nlk
bk3vo+b6glOmwCJMwFxVscinqNEFvtr611wG8NeMKCyafrRBGbwlVQjOitTMPCFO
xDsLKpuhuuN/MWVlk/DZ8P/YOJmXvHTl/XrAjbvfKng8XTmqIM0uDn2Hy007Zu9G
TIeuFX94iITASEL6g2+YqtDKZui+IO6iHJCiRlhNfwGvYJCbOlzT6kqn/GSYkOyl
jHeStTy3mCGRhe288QlXGCT+rOt4AIFIs/pmr7UhmdQOfG1Z+qsdHrRtqjm3T2dZ
1OkZrWqwqS6i+BA2aeHyCoI1lQSFXDiLgUrR3ShK8zTVIT+wTAriACYqLPyiJFsP
4Wk6kBtPN3YTSkcCh/S6cHvjdjtfL9LuKtba1xAngtE7fbbxxLZBYqqzw19UcgqV
o9zTZrtuyZL6jtJARErhH+DWQsVOD59BwO3/ZhSOnODF816dTDcLqmkTWPHQOOVV
JlxUBSEvuz1qnRPOf+kEH9wswV6zJrsg8//XlIzuA+vJykzJIorAkwNMCHcZA+mH
vf8M0FLd0QoNh29euES8pAbbGG2joWOrPosPtprcj6fn7dNrSvb8vRPdktdhIi0e
Qjnu7ck+Sqoy2GCX8hPJ5nJ59TCrm/cGeIDtXTx+xrla1MYaAY697mD6BZsc6cne
7VuQssdR2PagNV0ed8vZMUqVqcZQLO6sljOdXkRhML3npQxVKtLt1HtH0tssNPbn
16RDZzzla+yWZSGhN2cJ9H2BnKaBto5zXCvyU0gule20cF5VcpjRm8vjvHYTO7ie
wg9aYgzu8yuFsYUukx4b840d7UzdKMfN4l9qrDJHnSVSEQoM08i83jQLc6kbN907
y7YjgfPeg6deZtir2thQD5VBmOFnpwXE1m7C7rgDW2sfFbzDYDMEIj4XeF1AfuNm
sX2kiSO6sUiDiGphbVYgVbzRoaA7UtB/v/dbXE3kOnbbGgLKbBNdyPgjDSTC3pQA
CebjqXZdpxIJmPimw5iuVzEl5DjSngMFq1FENeTtOsQiAbaUvasufGNuB7dmDiNF
+FY0NmYINE1p1IlbTmriyhrryt3MradJM5/WfcdKPHObvmqHEwob2WUWVYpSdo3w
mimzzA8hZ0MDaovjsX+SEnBpnLY9ilkB/G5epg4p2n3ENvjpftyMe7FKomPjPQva
3EOW9Cjs1058b1RHUV76fTTMUnLzNhFKOfg14HYAgIXDTvT0aKNjJ79ZETF4WkDS
KstJioPlpFb9GBnnP3YGpWqv1OmzCDSDxBL7Wu8AFa16g6952CtRB4pmqATqy5ID
gCIBWf4yxa8AaLyG81Y6e+D0Q+WukEP8BljT+IFPL5aKC9iQZH9WAO2J1ICb91zn
g7IOQ1FnCvGw0/voNyfNw+Onr2b//1r0DH4mu1A5y/i+G34W8orGbv9AoG65zTyH
7IKf5DJuR+Pe0gN0rTraULBbX3S88zCvgtXwQRuo1oSz+2+/lD54IdL/iXqgpueN
zHMO9PhGbOR2HgOdxe35qJmKjxXXLPdSEMmoBh26HJ1mr/KzE20pxmhFwIh/O4UH
fGqVjgOCsTrry4AtMgtv/kYJcziBdFP1ipIFaWH1QfvqwEFzgkMitqCg9oudKaJh
zp2pPTVGOOLJmv8PyqYkrXDpp8No1Wz2Xe/p3TDYoIShTc815NyrHcjmgZFWjzx5
oX8wsAYQ6DniF4V67e4L306Syb38LQWI8C6E0I0C3m7uRCHUj3Kkk3MgioKcUYvs
9R3ewceJSyzTqh8sI0X1MuwYyF1RS+aHcb4T9mtVeVi5dFU81045JkjoI5yLaI2S
Cgyksj89MCNAdQ/r9MXf10nFyA7tPDNJfh/8lgSg1bzvjbkzOcAczsKNWTfbuMLc
owSyBpkjRr5Kav8J/bjGcXHSfWkfENMzyr43B4RIwAlW8nAGMMm2VATCY8yF5CG1
LuS15F/78cnrbHf4haQ1nw/IsX5dLuY49bPX6IYhNIa6uIryxsOz57xbBwXuYh5Z
vbvPJQc9hhaD/oh9Mzi9ihaWruwQ7hN/pS61NDxY+2zsJLn9+b5VBuNAaVQwCpg7
atG4VYbcZiOtlUczqv4UBo5EGqdYBtm4GtV1j6rNJH6NAmmNaQ0xYt7MPeE9Ptxh
DvC1XvJNLmYHhO/iKnKOZudA8sJoZPBaR9Ce3CF+hf7p786/2I6+wPWpBZATQN9d
UHaYHVWrMSEj7O5nYxG4pIJHrsJIjr4Y6Oo+ZuZWr1IVbEL79M3lxmRmD+7+/Ls4
wVjjZBaJ8ctYifOMciW2v0JU54WOoOAhBtgMUdiccl26Xd1OM0JKCm0o+X8VjN3a
Zt6Dacz0y75p8J08DiN9ThXdC+fODuJeAKRs5h/dc5rVB2HxT64WAXP0JLlYha3X
c41YeR4f/HydQywkwwciT3KeDlpTmoE72SbtwmeUbhb+rleFnSX0YTSDk9AU9rzB
kIX1LU8Nwjc4OiWwHP1TrF8IC/FUqemqgQVYcEJHslIZkOlRWBvViesHp0uFOJg5
XeGYPa4bWcz5MjIPPo7299v2acoJwaNmfX6nx79Fc6KaeC6CP7aQp8qtsgzm9yUC
Xoqbxxus+hWLfhF4BGJhF4Z7ZXyroGCFZbn1KpYtBjNJAYj7CpZeLzHD4BXDj08/
p+LtXgIZ7WCimGkFuaU4QZPcIgBL7msjadl91lK1XkEZUFzUf1uqlMg3wdyb9PiE
51KHGxUX9EhGmYjjLWZvlafK7N2mISxQH9KDfSoujBhDwohy1AzM0zMcjlbORHWy
JcvuzQOBjsSre30ynzNRxmTW6BBzh512CeeV8j5JrA2Qaqcw1dP3Wt9nM5FUknWO
cbtyOCBwIWLAwN+r8mEDXqVk516Gv+Lj9PbNmN8Vs0gaANdsTPSBPUtPl/l11+9F
qyIapnaqlTXmzg/bP+RHT+Yg5AJbzbnJcBhWvdiZ5eUghaG8XHF8znlKo4wlbhmH
TkbbLnxW1LG/SF75U4PpSn+n4JwOnxRI2ivAjjvlQo6t5Y3plSO4XqQzcDEe1/O6
yshjlXJRyB6kV11+QqcbdTYE9Lzw5VE+LOOM9w/uHib65ZBofuy5po9JM+Yo68c0
J40pFLMNfTWVAEb6fffizPvc2OXAapLCwcAzdgPwRqxJXBDFq8MzHWTJWHOFbwOa
vj5wz8jAAXCnk2E/5Q2DM9INVqNk5Un0U6d18TDeWTgo6AykF6RUHdC9Ruk4QHxu
UPpBzOUUs8+5Wngsbfh1dAaH8Y7Ii4RZZGTD8pvc/XpLE7J83vNrPxzTiNsB8CB/
0L6yH8BU+h8NaHBtYnsFt8Npu6qZ/xBN0wBrmMSfSmSPETTeouEh7QsvoApsVaSf
HPyvVdxbnwGwuZyfnrzm1nb+jZNJWnPzPZnqF2+jiTH4zb0wNsCdlw4Znom9GTuJ
NLAmjsLFHDUqihtRO7MCqrLblRcd6xYyWGojB2/9FdVy1rMXuVt2vwoPhfsUprts
iMMQgPVuV9HVXo0oyl4/PAwrolcYdz2C/kzoApQRQSaQR/2O/K0MbcwD0QEx+U/n
gLGlyIB3g7F8nyBrcIYKkDbMgD44Ot/We/Krh668gockFbaHoIh5KnKw2Cv9vvDi
z3RxR7ma+I9ZqsaVeDrkv+7bf11/io7wCDWTz1jmkcGZ9Lnzp0osSYBX8Z8HG4Vu
wK0PQLfsYpzkGylT0kAa9/YlkRuPS+Z6+Ym4SM1KsnJ6JX86BMrqiYeQbagmNK8z
RHsVpYLuvKinJRxxkE8l3F5jws06GrlykYGMTXfsudsbwmULLG7kWZePqN3KmgBR
E1XLqT9YTUDwr45YUIl09e3YdPFljEHC4tQuM/0P05nc76F73yD21A2OPAz2Wh80
OMeBZ/ufCDOSJQ8wC5ThA4fHrb6VBFr+ZqQ4OeacMER6izGyro1O6yHktrFynYbM
3PGgsSRsnSQ2VDbR66jnuQIUpjOR1LtTri5pFvU0z8LO7wTNje97lDOunakm6oVX
hX0bLpceFeNxiR8IdwAtpKCbE5kxEAbRsYKq3WXT75DKhDgnq+f3D6sQsRJeSV3R
35vQTx9blpDiS7EBvB4IQ+EdjPjPYKK3ovHFAmtszOXIATr3yG/r1leDBBBSanfS
GcQmr5xvzviXDKrgHTJsQi56ov2WT+52cJluI0x2vgNA9g8z5BH7PUNZ8cjCsVtS
Mxw3oGx7CERoGXFo2NMIpe1n4FZ539/GbFUYSb9s9uJQQCxbNPessMBJg4tKK9kc
tdBIpuTSoZEXXY7vaBGaUtxGvQmdbiwJGSkCURw0gA6PNFxktDOot1eF57yJv0UB
HwsUUl98uICp8ObdUYag8r4TJ6IHvDs0YJTEFntDHDDELN/RijQ1Njow4Dr7TXPf
rrD5OiNW/tohXUHnv7CFBOkYMRmhx8peEnGGhemD7nGoXQHL7UFKVM7UK7j2N2Ip
/XA6h4I5w0TwDtkW3JcxSedSMZBccApVdaKbUpQLDBDAEngUtUI44Ps3gKiUsMH1
lvxCL50KGvEUczsTtzVO4sLtxDz0NO80L65Fi0pFnw4VVW4FokxYdZPm25RI6RVS
KSDJKswVUHM+jHlqJ/Rl1IkXHNPoJb7GC/1Mi/2z3MilJP4dCIBjLaDTRhnV5o5U
e46PPeuBeBz19gg1hg6nyZ0sRXHxxO60ZOwXFKf6GHtuAkTv0SeXaEjs5I6HPzUO
m1Bupc66PAJd/PNPJNk7mkrXm6qhgnHvf7uOAWMy0TESaLrRnKyLadUvHcB3TEo7
NHjQ4KZkt8BbVbzGK75bhLcxkef5WE7LswKodppIFGlyuCdnI3xl/rVTW+rQr9Vd
ZJRASIHRb4KsB+ScQgA4x7Q5+DCnwPkAWZ1+H//df10bj6ZnyXlR7LmQYFPzQZf7
Of8p9fmSPpcQVO8EZarYR+QSc9kzxbIHbc1jWZ24itZvxMMi41izZ1eb2t/lj2jK
WPqo4sFGz8VzWFp9+9JPiLtQACtGWQzikq8k235bDUnQC/261agCdZreqNcYvJ2b
yNO1xeq7RhqhE59t3kHXQl2audSBrsqzStmlxsNv2/lNTqfAtyrIg6SDX/DP6Lks
U0TCirW3FZtcHo4SbWW7acvbusHwPBLXQJk7EHxn1UQWWtixg7OyLZBkQp16UrIv
1qXJutuv59tPpfDSa6Yifi//WkdbqijQufQj5AoH0u1m0ENMjcKF5JCQpmd0j+Dh
GqhXsdAIWn1HC6k993RHFV8vGsJhe0PxyNvgsg1FKq0qNYpZIREyRxna1MW0kBNZ
o5WbXrx6bUojr+6eQ5L27PT/AQcb08gVUVsMCagapgefQUAw154y/JMXpiHSQzhe
mL36Ovp+vah67v2V1ppcTRpyVCc2k6sbpNBbh/Z6RTyiTNd+YhL6Xwc/V4YuUKS3
GlfLswszv/PDvl9m/LTs1yfKZ5sKbPw79Cfwi5APr4SQcGWm4txxlpswJsGXVo6t
5OoExy6uuJclMZwSs58o0Jdxs9RbYUOQnsFnGlh/FpfYwnvgdxRidMSQzpzMh06+
PCXZEmhWVbCI8t5049Aryjst1OAIp1VrCJEVzK4sxx+v9oI90oR8ie26Xf7CDvcj
4vmP7PxGepPh140kzPO82rftCREzNfLmFy3VSDGFZ2ev10ba5kvE5fcXgDMfWZeW
gyNZsRZJcZ/kABEbWt2aRyBr9tReBqzYqWNfG2d/cF3/uejbO8/7ST3L2UP+Pgh8
DtKyqaUTANu8fweAVz48BbFRGdSJ8ga4mTDaQ5nlSbie0vAM7HCe6yH2RbIupkOV
L+uUBZFPVtlx7m8G7PFT3o8vkbavGYdn41xQt/UYrTmXAxFHMp5Fo6lMoeFhiVOO
Xk8stfyZ4I7l513lGChjf8OMUfHExU178QmfaILk6pPCfvKEUiUKaJCsJRf3XarP
iulOIsB7GtsCAsGkDQ8XThDwJX0ElvxF3M9J+005aYlTlF5AVxlqqzKQaVW+zORe
VbilZO+zxLH6RElc/n6iAUxfg2eYeocYp/Sk2JhXuuTL37xs8mVCA8fOqF3p8wZ/
ejFmjA7PBPDbDoSR3YCkch6rSJTCttDZmvB6uUW+rw+hXLiC4/ISuaeQbhgoMYBG
iCSHIMov6nIjhHz1y/ffUJBE0j6yhFrlZQmhngy6K1xmFKUr2tM1VpBVZ5D/21Pb
F1PNdKlEAOs7RvKTRY7aoOrbdzJ+6CZioaXr31c3ChEdq6tKqK2BeNexQ8daEb71
66LvtkM2IX2vS6KhZGcjWpeqnx3uUyZhVLcgVACpfnkM9RYcqriuLJDhNqT/aK+r
cclxaXD7YjwsJF3tDXfzsUKsIGjZ564IFMTt7VcMs30DOdIy8c1d4ULKvZ0cMHKd
G+0fFFcBpk6E1gEvkozpj/zfvT9ZwpTXGek5S7uaa4tGD96kW+ajhtQ86UqtLlEY
Nh4787nK7v7GqEJjSYwcL/AQTi3jxbiGyHFl8B2jenmbvsLlbad2xA25eMJqULi/
lxMj62s8R7KArey/ECLrbuR/PeF0ipHDFIgS76RWcax9MEVlCEXBb6+u270z1Z13
6nE+/nnuB+UoMT2/Yl3LHl5cH/qUyqC8vc2toWFDxyDXsSjQcUB7wb8HhLDaHU+z
jrc77GiAhUEwmYJNxRheJytds849Cx98hAE49LS9XG410Ln2Yt8WP8c8B0LaJTWo
t4jZygr9yiyi7hOVfNh+1ucBIdCZ9gFf8Q0pddmK1gbgXCiV0oAMgxnCHeZSwyGs
fX/3VXhM9BgALTlPXN/OEqjbWV5+wxHrcTywxUJgRgQxDtRdA3MmCry1n0j6IW9l
2w0SLx76dA+V9fWISkeh5M8xtkx4ER818jwwKLWW1TggNgq4BQmejmBPn/c2BjcX
ZbvWR+QJoQxPUplAG2f/V911UVUugfMHCgiur1872wEmk7WLOGlUoKqdBgICmmZF
8bIPbjcdOOq8XXEvfjxgG4oTYMiagZs3T/yGjwWOmCW6fTQxWggdCbTtBr95gPSE
3TSnEblP0L8DdmPmnPYHmLs5C2eKwURDigSio15gWsaNgGDZWi/f61LdHU36funj
+OrXOaG6zztK/PjMgkx3mJfMTf5vJS1LgwV1gCapA7QQHTuzcBbGWdtTZwPKBqq7
lCIXxbnmGW0NNiCKJlG9PPF0L9CorlJne0MP8Ca42wQ4vmg9S7BRpLpJgrvNDwM3
cNF5kMqtGp8aUK27F6ETjWlwNVAdhFCnq/LBlUzUgLaoqpgnE0G6gNtWTQXYCRrM
R4fKlwrlui8a6RY+WkgRrXWW7pMFjbsyO9IvbkR66bPfpzqKP5wnTfUzdbwFr0O6
spMsiX49WMU0Auym3xRNLhseb8uzlgbqqMOO5AmlxUtuArb2giVouwajdb4OOQ7H
3WZ/mp0uyIdTMCzwjC8f3Luv/2lzvvO8YkSWHj9cv602UBqEKVfHjp+kmxccTu9m
i5Plx7y8zJxsoNSWPbnHRXJOr4jgR/JiVvfQu9+b8YyrBzNbIbhb7lV7xZEguDUG
Th8bq2nG+Oadc3RtSTURfFgJZ6rMCRKu/zSxYcwa1mg7IvnxS1fiPCFFsHVZqN9Z
wXwjCDgAojGQcL8Ku+QOFAjlh/LDtP/CcTcQS3CmVhkUKQT9LIYd+QSTRn9WEjOa
P3/zc/XLG/5rVhaCefLq7XIrmZoxeik5sXiPlT0N0YrWji0dOa0fby1FCvV3R1P2
1Fy+QQldhm65JDeLvCTfHSozBoJUAPUptPTlVN/UBA70ecah5qOuFKGqfft1YaKF
pZSz12e1UAOCyw9Q1xlyMe1/4K13mj3qDTyzE76wdJXotkCU1/L8LlWPOtkmeD3s
8DrRH2kuNmISAbxcLfhU2c+Uq3mEr6w1SlJf437VCt76aWBzoidrOpV0FpftAMwX
XRq9xnEVjthNKErAV7M8raRvnmvhpokoi3ULJRtywFn6KhKgk8cr+cBTsbSDJ1oH
Bb2xFLLhBUHCb6oMPtSmESIKfWwG4ubxkZ/k01/cbSfBMNN5Za5hNZP90rwaXa4L
nnyR4ug+U0VLR5h1kh0+kKy37N8xyuR4tvozYAVDmgHYwaafZ3biaiReAj3jek+9
z3d+vchEH5b1LaJSZ5BaprUN5KbkO1yhEdyQLfemoY1kSARM/dDfROtwA09tOiWT
eb5yrmc65p16fx93DFdTU7uJQSFpRAK3PkesvXXPfAGusKuYRCaris1QykZTdYoq
fjQP8zDgwRDjQ2lhL6TiV1LCoPcrpy5/15w0LdWqgElLEbRBDOIcEAPC27mUgKTn
sYYrFuwLS+pj/SmdyRPBPcfyKL4/RXEXi9Cjq/nPVSLXWi6LsJBqrw+q+QmkbMc+
eLUemWx3TkLBbC12AB4ljLfa7uY4df5ZrQ0amvu5LFLqe0OBHMqb5ROKTm8LKVZg
imVaNP2K4d1HwP1T2SWFY/lIvYtesLaDxcNvOfhjeAHESeAbevKdn32BuOdhfYqS
9tPgXEJMsMiyqY3+sQcN8PEZKW6s/Lq/f/ZHfHWf+Dh//HSB8Ffj1k209XyF4Bx4
eDBP5ZNsDjEuY2NjInmxvVCjOZAart3HQukl/4PPOmiwIqzFq5jQ3oh9rNKtDCHg
ojsgQBt+QTEDavc0ti8/epD0jI1AwP+3qM+j7L0Kf+S9TRY4RNMdRT0Tv7PcPPeo
weDqrvSeijsRMe4VgZ+XXLlMMxAYY6imAcLupwDLNi5tTvSW93ymoBGtkcllDhKZ
KRE6a6VFFMYJ1LvScgU8AwUEHB2cfBRv2SCP0lWeAtVOBrrtCuLrwkytTHNZOcj5
6cofgeDBmoeinm/8UFqZkKzjQuaA41LOsFwaJp0Bipm8dhQsJJhBcOaBoS9ZHDRd
6kVnH49MkZ4W5Ev1vqGXPH6Def3vGblsIhi4j65vxDV0VnB8hAbMSyycFzsi+8MT
YawhfJpwp8JDbe//Wh25kpeTGHA5uagCZntPI6fCl0x2hJX/9UA7wFxIpnvDZfmU
M7vGNiSd07MX/xUHWdFDxDZV4rbuZpH9eJ80r0qYi11Y00gbSJPXpEpdAC31LKHT
epgzSuBdSEm5JVsrDUAjvAzxv3ieZjmZTketItjvNYw4efU+3UGsL2Fv2HYDuVGA
KM54L4zNpYfQvrABSDc5HXnWkJg87+KxZ9kUjF36sNvr0bricSy3Zr/L49FCwvmF
9dbo6LTMES066L4NEa8qJjUzfbApMPPzSmh3IbS71SDFESyLKJgwPOl+pxhu1f+x
MWd3xn2uIKzWFn66WXlFQfjcIX/S02hJriVxzNxo/e4883j5/Gpclhy9b7n6xX/C
`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
KjrfCWYd3eK3cAqJ7oRj1KUGvVXjqYVz0dVoiGZBBknt288iuGCDuhGW9TjMYmO9
mbkBrBtmJwD2ObRm4t/kjMx1U/cRDVKQujP9rEmIolC9lPL23eFgc08cT3K1Q6gL
bXDsqklxDUlIdeI4CuDpWbweiPXbjdK6FPqAUVuJI1Qf+d46hX+pFh5ctXCZYRtJ
MnO02vbKo6nizQYTqDM9ncCZ6io7HiNW9VJcn/fNdksa77tqjWA4mn/s0+zaAJG3
TaGsg4ESDQq87meXM6JI5U4S6axBAsAy2uCt77dHFrnls+NFZ32x8zTeHhxulwlt
2G7+hQQf1fjQKzntc/l8lg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 40432 )
`pragma protect data_block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**************************+F39qGIxndQ3h0Z/6BvfiT+tg0+QqPW5Crb4LX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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
amdsD/IhaSMtlB+xkCVyrB75rJLFrL+1pO5yIZhtP4FdoV0t1GMU6R/yEawhX/o7
5Ml0APifcJtATed+XZe9+A94giKZovlmLL4HS1xpGZQ8+8scgtvcmwDxs/0VcnuR
4B9z6hC8MJOTA9FugkPTYz6BH4lgangZZxTD+QF494L56cHq3aJXG7Yqvs621iY1
Iaa2t9HlC67ySwb4E/j9lb8pNBnzUMwwR/BHgT0yErsyo4zYg61EfhOe25rzIZW3
huYLkTnPe/61bWdzSngpx7yYxQrLuA6Cl63A0JgJwwZrunDcROaR4k3d/3Z3cpmi
wfWvrw/mCSggNm2wW8z0/A==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 5424 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
ZBmIITDQ6WtQRkDIM/umAkkj2rW2+XzmZTFmD2Jn0IfXZKhJhCjDRsFp8KO/6kst
sAxJjwK5ngqfmieCO+AQl/nLAysaJUOJjo9gBPNC+tu23LGZzyPwmUxiqVIayfbp
m+0pYLI2ONJwxi7dqh0KPlMnHYG1qs3ZDvh8XsUjpgEPrNRIr2YyJHN2ALM0iRy0
A7oocRtkMJ13FrVcZwodPco/NpkuCNiEsDF3lO0gMj0kTNiAFKf2RgjBuUtCONFs
G/P+tniyXxOss5YUMJwRkXw/+nVefoGHiffioxZJcBQd96DNWQM/VcruQVn0qba6
8IP2D9iUS2nHZRJwcoUbwA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 1776 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
QPjGGGcMrBGoZgigSz2Po736lXCPM19htCNhRmfq/nNBaDgN6aMQcG4kt1VQxU4Z
lvKKbzDbXnoopcsP0IkRumjkli1fXNSLxqx+rh5JxNXIGx6wbsfF9Te451V/lZNL
UbFSSLhmrW/KS0pJarAIqHwPB1oHxpue3hGvIIXHu18qLb2W3fgo3a8UKlKINsi3
jhqF8qPgvygzUvO16OK7qRM+mcKQ0KqmcVsBtpnwZ/awbDzJTIRzqO1EIsWqI3V8
lw5VfK86QMhLgqtvxODSc8FL7Z5xy4XU8CKdysn2I7uiIITwdHhUQ2BtQTNRqnXK
jy6BYcNhqM4WmJ7W5S2zwA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 1760 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
kyxXpGJTe2bkVoJ/XBSn1z4w6MVMVXF2VA8BdmBCyKYixfOctteq6yUhLWRnxcer
XCiFWzOdBw7zxBiJSbA8jkfxP9CLRNA+RbWynOcr3npJrNReBOAD/adzzip/ZqqB
FrbRZdGZhHBzUVP+HP0u+oMjIUUCYBMPDj6fnLRs9zc+CzT3yqL3wf7f4ZEdltyD
4YMXHLScIZV575edIQ6/hmxKA70dmYbQe2BdfxWXAc5/96//v2AMlc+NQBQEVTvW
rhPC5f3b6+wE4n59hj2nltmL0RYUFtgOaH+s//dBnu31qZL1SEOCxrmTC8Sg0fRo
r+n6+WoBSoSjQFZHNkDSZw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 1600 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
jMBbZbnN0W0TPkvh/wFGFXj69Uasy36JCxBHnU+3OkYQO8JPci40Z0czuaPfXMvk
ku+/hQ5uR+Zgm6vnJ7CERm4IQB8Fr2N/knOyYsD7JW8wn7zWG6vF1/q0l3MpjToL
jqgrmL7aRQ242oqIPtSlnGctu43mbVb2TXsX9Grtx4QZPGSIdgpFluXHrk6Jkg2K
eZNo7h7ZbguwTdfKDPqbaE2/6WD8ICW/Zf21AdnB3MXyIIEbna+ZZTWEUBk1CrN9
i3Fbqrvb+SRtB5BfG6YdCDvNGezc6NHkaL443p2xmaI4KjyX4/4YkfDCwCn5PIEZ
DLBDNWIUWB8I2Tm5RINNrw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 1952 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
module `IP_MODULE_NAME(efx_ddr3_soft_controller)
(
clk,
core_clk,
twd_clk,
tdqss_clk,
tac_clk,
reset_n,
reset,
cs,
ras,
cas,
we,
cke,
addr,
ba,
odt,
o_dm_hi,
o_dm_lo,
i_dqs_hi,
i_dqs_lo,
i_dqs_n_hi,
i_dqs_n_lo,
o_dqs_hi,
o_dqs_lo,
o_dqs_n_hi,
o_dqs_n_lo,
o_dqs_oe,
o_dqs_n_oe,
i_dq_hi,
i_dq_lo,
o_dq_hi,
o_dq_lo,
o_dq_oe,
wr_busy,
wr_data,
wr_datamask,
wr_addr,
wr_en,
wr_addr_en,
wr_ack,
rd_busy,
rd_addr,
rd_addr_en,
rd_en,
rd_data,
rd_valid,
rd_ack,
shift,
shift_sel,
shift_ena,
cal_ena,
cal_done,
cal_pass,
cal_fail_log,
cal_shift_val,
read_back_en_pipe,
axi_aid,
axi_aaddr,
axi_alen,
axi_asize,
axi_aburst,
axi_alock,
axi_avalid,
axi_aready,
axi_atype,
axi_wid,
axi_wdata,
axi_wstrb,
axi_wlast,
axi_wvalid,
axi_wready,
axi_rid,
axi_rdata,
axi_rlast,
axi_rvalid,
axi_rready,
axi_rresp,
axi_bid,
axi_bvalid,
axi_bresp,
axi_bready
);
input clk;
input core_clk;
input twd_clk;
input tdqss_clk;
input tac_clk;
input reset_n;
output reset;
output cs;
output ras;
output cas;
output we;
output cke;
output [15:0]addr;
output [2:0]ba;
output odt;
output [`DRAM_GROUP-1'b1:0] o_dm_hi;
output [`DRAM_GROUP-1'b1:0] o_dm_lo;
input [`DRAM_GROUP-1'b1:0]i_dqs_hi;
input [`DRAM_GROUP-1'b1:0]i_dqs_lo;
input [`DRAM_GROUP-1'b1:0]i_dqs_n_hi;
input [`DRAM_GROUP-1'b1:0]i_dqs_n_lo;
output [`DRAM_GROUP-1'b1:0]o_dqs_hi;
output [`DRAM_GROUP-1'b1:0]o_dqs_lo;
output [`DRAM_GROUP-1'b1:0]o_dqs_n_hi;
output [`DRAM_GROUP-1'b1:0]o_dqs_n_lo;
output [`DRAM_GROUP-1'b1:0]o_dqs_oe;
output [`DRAM_GROUP-1'b1:0]o_dqs_n_oe;
input [`DRAM_WIDTH-1'b1:0] i_dq_hi;
input [`DRAM_WIDTH-1'b1:0] i_dq_lo;
output [`DRAM_WIDTH-1'b1:0] o_dq_hi;
output [`DRAM_WIDTH-1'b1:0] o_dq_lo;
output [`DRAM_WIDTH-1'b1:0] o_dq_oe;
output 						wr_busy;
input [`WFIFO_WIDTH-1'b1:0]	wr_data;
input [`DM_BIT_WIDTH-1'b1:0] wr_datamask;
input [31:0]				wr_addr;
input 						wr_en;
input						wr_addr_en;
output 						wr_ack;
output 						rd_busy;
input  [31:0] 				rd_addr;
input  				    rd_addr_en;
input  				    rd_en;
output [`WFIFO_WIDTH-1'b1:0]	rd_data;
output 						rd_valid;
output 						rd_ack;
output [2:0]shift;
output [4:0]shift_sel;
output shift_ena;
input cal_ena;
output cal_done;
output cal_pass;
output [7:0]cal_fail_log;
output [2:0]cal_shift_val;
output [3:0]read_back_en_pipe;
input wire [7:0]    axi_aid;
input wire [31:0]   axi_aaddr;
input wire [7:0]    axi_alen;
input wire [2:0]    axi_asize;
input wire [1:0]    axi_aburst;
input wire [1:0]    axi_alock;
input wire          axi_avalid;
output wire          axi_aready;
input wire          axi_atype;
input wire [7:0]    axi_wid;
input wire [`WFIFO_WIDTH-1:0]  axi_wdata;
input wire [`DM_BIT_WIDTH-1'b1:0]   axi_wstrb;
input wire          axi_wlast;
input wire          axi_wvalid;
output wire          axi_wready;
output wire [7:0]    axi_rid;
output wire [`WFIFO_WIDTH-1:0]  axi_rdata;
output wire          axi_rlast;
output wire          axi_rvalid;
input wire          axi_rready;
output wire [1:0]    axi_rresp;
output wire [7:0]    axi_bid;
output wire          axi_bvalid;
output wire [1:0]    axi_bresp;
input wire          axi_bready;
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
AQl/yrSDETA0ntT9bNaYs0RpOxxtkElvFwrbJafivp+6slXzdmQAz6uuiVzsvmIF
FuJ8kQbPny++hld/I01AVYhEPAxjkGxE0PXuEWLkMzzZ8ycu4HCW/DpqdaimgUIw
wr9NOW/Z+dbzXpTbARkYUEj/K6oeYfMxOhM55ciJWxs/P1g7+d7ta8Qm6lyrNo3N
9WZoNP/0kkt2EzVjUvZvXZI73w20f3I+u+v0yy4FKM96kb693BDFdcHC8tk4mBjp
NXM7+wGWZRMF3Ku0gWy0tW0ikuzQ7ZNwgP0vfQr0CcGDX8PsHUL5Rog3oxvNf9tQ
U7CY9OGBFzHKYqou58eGyA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 3344 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
HFlOxD+dXxaoGicD9eqC+rU3Gj6yHA0j7Yd8CRkLqRSibfpaOT6azvFsG8pDSWom
u+hsDo0Ursv2MiUUoHK5eU2HoxqDknhPmY/iEv/wE7hmzO7UPUWStnfaWq4biwUz
6C3bV2Fe9JpANsf7b/H2GS329un5clA8AynYJ2b/KvkJ+vlCN1UVE1LItIOER4JQ
ySrD5xGRW6tI5G1poiX9B2qgDAgF+M8q2G187cZcw8Yd7uRvj2Dq3sZkfVRB7ch1
S1yiakv8Hs8ZAjPpuq2L0FiSdV9v1macd2SIZ1SXZyKBRTR5xmbHx+XYA0T+ULdE
uDO/KPLiSkxrIFwomLZy1w==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 992 )
`pragma protect data_block
ln40565sgypENrxNU3y1cifLvfP739zaAdk92DKhDx8pVhFFUuvIT7biMX8URAaQ
/JbSk8yTWxoS6Zbh0vIMwKwAdVAqN3rQAGkTFC+MmM0ov9CUJWqQ+My+oo+KCNWK
ILb+XOPmpB6rzz0argmegz2t653kozgbYmCqDcz1oGb89+7m4/QUOVs+bANMAXNb
Lsx+JUoEIKSC6+vxOSSam9rqf1MMtJANr54TzYLPSUwtPZmCaog5aNKMMMw1DToh
RYkv5eaD9sTQ1piog/sCtaI75T3TT4FmrdFuLfRrA7f8gME38zMCqJIYjr6BGES5
oAvav/oSJrofH16lkdfLUZ2LX9u11vwydzASp1RV79eLztFQaWozz9ThmxAbjI4/
04cRxcRChErO0/7T4Nz83oeRoHpaKHWmb6ZZ4CZkKYetMBqJqNI3OWys3pLrQTKp
X1LtgSqKQFS3HJ7614ptqEB2JpPOef4eZpgOl/4vx9+uMmanqYwpdHmc15N4wvxH
mO/GaJ+CwzloKQX1xHZEvndjcUrte0Zer3B7SI498xCVEwhYAq7NTmZPfoi4nJVK
5llMRTwdFoUoJZiADygAAAuoye2FdqZnbTefUswVeLbCrYj56SFq3bECC9oiSXHA
WAiUtV1I0WSnlJdT4PZAW6eKhWIqV5J4RLhMGEBCvHCIWizi2OqpJn1w1e/v70JE
pj90EjDuSMp1RcScGvv6/+7omANyateVRrg7cZyNTPtKHX3WZ1lKVnJz93Q+4qSB
NfrG6uHbTchn9X5AS74WtnFWodfBNiqrmPWKszQACPb1f/7MdD5cgUGphiIszS8v
bHRdzkN4Nru1Tgm6jB4+VwVMzW3ZVwK02cpUbzgEzfcs+ne3ehK4bKh8ms2Tf2HG
rdctRMuhhqdy/b85l5PiIGJXGmf5mSUDVe64vGvqg1gYkKgwpvjdpaIzUm6ciTvn
cY5DlwA/Sga/9fVMLoE+0TWzKGH9guyMZ6htBldqGoqBHvrCxVyFwdJK3ICP57OC
8Ef7bLhfG38yS1xROCCcZY31vvcLqiJmeMu81UPxx4z3r/TnhTOyJ9PbY7OJwhV3
+lfOnm4giN9TAY3I6uOv4O7ucpMmzD8RTHvBZs9Ll2EfSfWiVo9LPJnmJFqniWTc
iKKx07aeGqXl+bKLRrk4UzMSMOHA27fl1jjr7DXOhcOcmMa3gdO0+BH0zaKgT0x+
ZNCvi3tFifWshwLKVwgF0QdJoVFtY1GPkc8JnbGURBfl/1RzuP/BXVHHaEeG04mI
g/Sm+L8Ih88ZZv4q8kvAvpiePThRsxszua0nAGTL6EU=
`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
SFEunvKNCjwvLn0k9p+CJ9aQ2f/z3z5ofteyQl0vRbZnwJ7dpOlVWZIfvh6lfEQA
6qekcGK3DPCTD+mLOdAIUxMAK404UU01gCmwSHcF/3MMuLOalOhG+6aCo9hEucmp
X13yVFVs0XQdHhjCJndxKYWZRMrwJ8P7UDNkzSw/oYdYlGCbpoFjVS2hdOh/J/9x
kOX6O/Sc+g/9K96FlzphSUOVvstgVahDlwbEYyzSjyfyWDlig29iJ6Oj54CRzDim
gJbWLsmVKX4XwW/GkDivzJ+GLnich6cfw++ueeXs/yQoUvoIzUYBE+Qvg8ZlqRrC
ACmn+EzmIHOPpqyIx9c8Kw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 14832 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
XMbTw5GZpqlprBhpFFobnQc3gcCyZf8gGBQhEedIUgpgtkcv6mpujFTJzNaiV+vy
JpZlcC0k2cQm6OaVX2p4++aWM8df/n23JOARVwBew1UmuXC43GppU2/WJjtKueHi
FDGUJSXJKRxCcoREAc8W3xuzIY0gVS/Xz40T+6Y9gs2ia60d2bX2H4skyI8Rmb1e
ItZNkVYrMQ3XzOPKS+4sioRUXne4mB1L2fbiahRcN/q9rBJFbtZCgxGEf5y4UEn2
9KsCuZ7pVohQ6Uj2jOKUu22hN9yqqhRXJxkmFwdlAEhWSbOxGNbJRsdF7gLq4iJc
HcMc7cSEGTiUwYoY6pqgRQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 4256 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
b8OfkeuoVrQLX+IO/QGEzfX+cXUfZS9fYH+hGC9kpcVEn1MZrwV2EwuInqNb8WxG
MJfR7qy+0lJWcT4154oq8RRLM9bEBMZZzUQYAVGAaC6nhfqOg3zUatBMijReQYBh
x/EIC15TgqBIjSbGEwvY2oUoiZFIt6UnpTeNRo+6Q13vZX+5NetPn65UObCXaZxi
ic9m/ly/F6r1k+VYdZtZ4Q+p/QXvsjFvMhaeITi0cqCsputB4O9+8kdfcZrJXZfw
JiH7yaOVwUpdIbuRIcirIvFnP93cPDQhczLqd0IBx8IumR+HE7nu+yZ9q8IbY5gk
ANwgLeOwo6LC+sVBCDATcg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 30496 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_top) # (
    parameter FAMILY             = "TRION",       // New Param
    parameter SYNC_CLK           = 0,
    parameter BYPASS_RESET_SYNC  = 0,             // New Param
    parameter SYNC_STAGE         = 2,             // New Param
    parameter MODE               = "STANDARD",
    parameter DEPTH              = 512,           // Reverted (Equivalent to WDATA_DEPTH) 
    parameter DATA_WIDTH         = 32,            // Reverted (Equivalent to WDATA_WIDTH)
    parameter PIPELINE_REG       = 1,             // Reverted (By default is ON)
    parameter OPTIONAL_FLAGS     = 1,             // Reverted
    parameter OUTPUT_REG         = 0,
    parameter PROGRAMMABLE_FULL  = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_FULL_ASSERT   = 27,
    parameter PROG_FULL_NEGATE   = 23,
    parameter PROGRAMMABLE_EMPTY = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_EMPTY_ASSERT  = 5,
    parameter PROG_EMPTY_NEGATE  = 7,
    parameter ALMOST_FLAG        = OPTIONAL_FLAGS,
    parameter HANDSHAKE_FLAG     = OPTIONAL_FLAGS,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter WADDR_WIDTH        = depth2width(DEPTH),
    parameter RDATA_WIDTH        = rdwidthcompute(ASYM_WIDTH_RATIO,DATA_WIDTH),
    parameter RD_DEPTH           = rddepthcompute(DEPTH,DATA_WIDTH,RDATA_WIDTH),
    parameter RADDR_WIDTH        = depth2width(RD_DEPTH),
    parameter ENDIANESS		      = 0,
    parameter OVERFLOW_PROTECT   = 1,
    parameter UNDERFLOW_PROTECT  = 1,
    parameter RAM_STYLE          = "block_ram"

)(
    input  wire                   a_rst_i,
    input  wire                   a_wr_rst_i,
    input  wire                   a_rd_rst_i,
    input  wire                   clk_i,
    input  wire                   wr_clk_i,
    input  wire                   rd_clk_i,
    input  wire                   wr_en_i,
    input  wire                   rd_en_i,
    input  wire [DATA_WIDTH-1:0]  wdata,
    output wire                   almost_full_o,
    output wire                   prog_full_o,
    output wire                   full_o,
    output wire                   overflow_o,
    output wire                   wr_ack_o,
    output wire [WADDR_WIDTH :0]  datacount_o,
    output wire [WADDR_WIDTH :0]  wr_datacount_o,
    output wire                   empty_o,
    output wire                   almost_empty_o,
    output wire                   prog_empty_o,
    output wire                   underflow_o,
    output wire                   rd_valid_o,
    output wire [RDATA_WIDTH-1:0] rdata,
    output wire [RADDR_WIDTH :0]  rd_datacount_o,
    output wire                   rst_busy
); 

localparam WR_DEPTH      = DEPTH;
localparam WDATA_WIDTH   = DATA_WIDTH;
localparam RAM_MUX_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? 32 :
                           (RDATA_WIDTH <= WDATA_WIDTH/16) ? 16 :
                           (RDATA_WIDTH <= WDATA_WIDTH/8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH/4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH/2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH)    ? 1  :
                           (RDATA_WIDTH <= WDATA_WIDTH*2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH*4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH*8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH*16) ? 16 : 32;           

wire                   wr_rst_int;
wire                   rd_rst_int;
wire                   wr_en_int;
wire                   rd_en_int;
wire [WADDR_WIDTH-1:0] waddr;
wire [RADDR_WIDTH-1:0] raddr;
wire                   wr_clk_int;
wire                   rd_clk_int;
wire [WADDR_WIDTH :0]  wr_datacount_int;
wire [RADDR_WIDTH :0]  rd_datacount_int;

generate
    if (ASYM_WIDTH_RATIO == 4) begin
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
            assign datacount_o    = wr_datacount_int;
            assign wr_datacount_o = {(WADDR_WIDTH+1){1'b0}};
            assign rd_datacount_o = {(RADDR_WIDTH+1){1'b0}};
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
            assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
            assign wr_datacount_o = wr_datacount_int;
            assign rd_datacount_o = rd_datacount_int;
        end
    end
    else begin
        assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
        assign wr_datacount_o = wr_datacount_int;
        assign rd_datacount_o = rd_datacount_int;
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
        end
    end    

    if (!SYNC_CLK) begin
        //(* async_reg = "true" *) reg [1:0] wr_rst;
        //(* async_reg = "true" *) reg [1:0] rd_rst;
        //
        //always @ (posedge wr_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        wr_rst <= 2'b11;
        //    else 
        //        wr_rst <= {wr_rst[0],1'b0};            
        //end
        //
        //always @ (posedge rd_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        rd_rst <= 2'b11;
        //    else 
        //        rd_rst <= {rd_rst[0],1'b0};            
        //end
        

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_wr_rst_i;
            assign rd_rst_int = a_rd_rst_i; 
            assign rst_busy   = 1'b0;
        end 
        else begin
            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_wr_rst (
               .clk   (wr_clk_int),
               .reset (a_rst_i),
               .d_o   (wr_rst_int)
            );

            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_rd_rst (
               .clk   (rd_clk_int),
               .reset (a_rst_i),
               .d_o   (rd_rst_int)
            );
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end

    end
    else begin
        //(* async_reg = "true" *) reg [1:0] a_rst;
        //
        //always @ (posedge clk_i or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        a_rst <= 2'b11;
        //    else 
        //        a_rst <= {a_rst[0],1'b0};            
        //end
        wire   a_rst;

        `IP_MODULE_NAME(efx_resetsync) #(
           .ACTIVE_LOW (0)
        ) efx_resetsync_a_rst (
           .clk   (clk_i),
           .reset (a_rst_i),
           .d_o   (a_rst)
        );

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_rst_i;
            assign rd_rst_int = a_rst_i;
            assign rst_busy   = 1'b0;
        end
        else begin
            assign wr_rst_int = a_rst;
            assign rd_rst_int = a_rst;   
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end 
endgenerate

`IP_MODULE_NAME(efx_fifo_ram) # (
    .FAMILY        (FAMILY),
    .WR_DEPTH      (WR_DEPTH),
    .RD_DEPTH      (RD_DEPTH),
    .WDATA_WIDTH   (WDATA_WIDTH),
    .RDATA_WIDTH   (RDATA_WIDTH),
    .WADDR_WIDTH   (WADDR_WIDTH),
    .RADDR_WIDTH   (RADDR_WIDTH),
    .OUTPUT_REG    (OUTPUT_REG),
    .RAM_MUX_RATIO (RAM_MUX_RATIO),
    .ENDIANESS	    (ENDIANESS),
    .RAM_STYLE     (RAM_STYLE)
) xefx_fifo_ram (
    .wdata (wdata), 
    .waddr (waddr),
    .raddr (raddr), 
    .we    (wr_en_int), 
    .re    (rd_en_int),
    .wclk  (wr_clk_int),
    .rclk  (rd_clk_int),
    .rdata (rdata)
);

`IP_MODULE_NAME(efx_fifo_ctl) # (
    .SYNC_CLK           (SYNC_CLK),
    .SYNC_STAGE         (SYNC_STAGE),
    .MODE               (MODE),
    .WR_DEPTH           (WR_DEPTH),
    .WADDR_WIDTH        (WADDR_WIDTH),
    .RADDR_WIDTH        (RADDR_WIDTH),
    .ASYM_WIDTH_RATIO   (ASYM_WIDTH_RATIO),
    .RAM_MUX_RATIO      (RAM_MUX_RATIO),
    .PIPELINE_REG       (PIPELINE_REG),
    .ALMOST_FLAG        (ALMOST_FLAG),
    .PROGRAMMABLE_FULL  (PROGRAMMABLE_FULL),
    .PROG_FULL_ASSERT   (PROG_FULL_ASSERT),
    .PROG_FULL_NEGATE   (PROG_FULL_NEGATE),
    .PROGRAMMABLE_EMPTY (PROGRAMMABLE_EMPTY),
    .PROG_EMPTY_ASSERT  (PROG_EMPTY_ASSERT),
    .PROG_EMPTY_NEGATE  (PROG_EMPTY_NEGATE),
    .OUTPUT_REG         (OUTPUT_REG),
    .HANDSHAKE_FLAG     (HANDSHAKE_FLAG),
    .OVERFLOW_PROTECT   (OVERFLOW_PROTECT),
    .UNDERFLOW_PROTECT  (UNDERFLOW_PROTECT)
) xefx_fifo_ctl (
    .wr_rst          (wr_rst_int),
    .rd_rst          (rd_rst_int),
    .wclk            (wr_clk_int),
    .rclk            (rd_clk_int),
    .we              (wr_en_i),
    .re              (rd_en_i),
    .wr_full         (full_o),
    .wr_ack          (wr_ack_o),
    .rd_empty        (empty_o),
    .wr_almost_full  (almost_full_o),
    .rd_almost_empty (almost_empty_o),
    .wr_prog_full    (prog_full_o),
    .rd_prog_empty   (prog_empty_o),
    .wr_en_int       (wr_en_int),
    .rd_en_int       (rd_en_int),
    .waddr           (waddr),
    .raddr           (raddr),
    .wr_datacount    (wr_datacount_int),
    .rd_datacount    (rd_datacount_int),
    .rd_vld          (rd_valid_o),
    .wr_overflow     (overflow_o),
    .rd_underflow    (underflow_o)
);

function integer depth2width;
input [31:0] depth;
begin : fnDepth2Width
    if (depth > 1) begin
        depth = depth - 1;
        for (depth2width=0; depth>0; depth2width = depth2width + 1)
            depth = depth>>1;
        end
    else
       depth2width = 0;
end
endfunction 

function integer width2depth;
input [31:0] width;
begin : fnWidth2Depth
    width2depth = width**2;
end
endfunction

function integer rdwidthcompute;
input [31:0] asym_option;
input [31:0] wr_width;
begin : RdWidthCompute
    rdwidthcompute = (asym_option==0)? wr_width/16 :
                     (asym_option==1)? wr_width/8  :
                     (asym_option==2)? wr_width/4  :
                     (asym_option==3)? wr_width/2  :
                     (asym_option==4)? wr_width/1  :
                     (asym_option==5)? wr_width*2  :
                     (asym_option==6)? wr_width*4  :
                     (asym_option==7)? wr_width*8  :
                     (asym_option==8)? wr_width*16 : wr_width/1;
end
endfunction

function integer rddepthcompute;
input [31:0] wr_depth;
input [31:0] wr_width;
input [31:0] rd_width;
begin : RdDepthCompute
    rddepthcompute = (wr_depth * wr_width) / rd_width;
end
endfunction

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ram) #(
    parameter FAMILY        = "TRION",
    parameter WR_DEPTH      = 512,
    parameter RD_DEPTH      = 512,
    parameter WDATA_WIDTH   = 8,
    parameter RDATA_WIDTH   = 8,
    parameter WADDR_WIDTH   = 9,
    parameter RADDR_WIDTH   = 9,
    parameter OUTPUT_REG    = 1,
    parameter RAM_MUX_RATIO = 4,
    parameter ENDIANESS 	 = 0, //0: Big endian (default)   1: Little endian 
    parameter RAM_STYLE     = "block_ram"
) (
    input  wire                     wclk,
    input  wire                     rclk,
    input  wire                     we,
    input  wire                     re,
    input  wire [(WDATA_WIDTH-1):0] wdata,
    input  wire [(WADDR_WIDTH-1):0] waddr,
    input  wire [(RADDR_WIDTH-1):0] raddr,
    output wire [(RDATA_WIDTH-1):0] rdata
);

localparam MEM_DEPTH         = (WR_DEPTH > RD_DEPTH) ? WR_DEPTH : RD_DEPTH;
localparam MEM_DATA_WIDTH    = (WDATA_WIDTH > RDATA_WIDTH) ? RDATA_WIDTH : WDATA_WIDTH;   
localparam LSB_WIDTH         = (WADDR_WIDTH > RADDR_WIDTH) ? (WADDR_WIDTH - RADDR_WIDTH) : (RADDR_WIDTH - WADDR_WIDTH);
localparam RDATA_WDATA_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? "ONE_THIRTYTWO" :
                               (RDATA_WIDTH <= WDATA_WIDTH/16) ? "ONE_SIXTEENTH" :
                               (RDATA_WIDTH <= WDATA_WIDTH/8)  ? "ONE_EIGHTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/4)  ? "ONE_FOURTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/2)  ? "ONE_HALF"      :
                               (RDATA_WIDTH <= WDATA_WIDTH)    ? "ONE"           :
                               (RDATA_WIDTH <= WDATA_WIDTH*2)  ? "TWO_TIMES"     :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "FOUR_TIMES"    :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "EIGHT_TIMES"   :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "SIXTEEN_TIMES" : "THIRTYTWO_TIMES";

(* syn_ramstyle = RAM_STYLE *) reg [MEM_DATA_WIDTH-1:0] ram[MEM_DEPTH-1:0];
reg [RDATA_WIDTH-1:0]    r_rdata_1P;
reg [RDATA_WIDTH-1:0]    r_rdata_2P;

wire                     re_int;

generate
    if (FAMILY == "TRION") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                end
                r_rdata_2P <= r_rdata_1P;
            end
        end
        
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            if (ENDIANESS == 0) begin 
            	integer i;
            	always @ (posedge wclk) begin
                	for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                    end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                	end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
        end
        
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  ||RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
            //integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end 
            end
            else begin //endianess == 1
            	always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end
            end
        end
        if (OUTPUT_REG) begin
            assign re_int = re;
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end
    end
    else begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                    r_rdata_2P <= r_rdata_1P;
                end
            end
        end 
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            //integer i;
            if (ENDIANESS == 0) begin
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
            
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
        end
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  || RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
        	//integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
            
            else begin //endianess ==1
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
        end
        // NIC
        //if (MODE == "STANDARD") begin     
        //    if (OUTPUT_REG) begin
        //        reg re_r;
        //        always @ (posedge rclk) begin
        //            re_r <= re;
        //        end
        //        assign re_int = re | re_r;        
        //        assign rdata  = r_rdata_2P;            
        //    end
        //    else begin
        //        assign re_int = re;
        //        assign rdata  = r_rdata_1P;
        //    end
        //end
        //else begin
        //    assign re_int = re;
        //    assign rdata  = r_rdata_1P;
        //end    
        if (OUTPUT_REG) begin
            reg re_r;
            always @ (posedge rclk) begin
                re_r <= re;
            end
            assign re_int = re | re_r;        
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end    
    end
endgenerate

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2021 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   gray2bin.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Gray to Binary Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_gray2bin)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] bin_o,
 // input
 input [WIDTH-1:0] gray_i);

//---------------------------------------------------------------------
// Recursive Module
// Description: reduction xor
generate
   if (WIDTH > 1) begin
      wire [1:0] bin_1;
      assign bin_1 = {gray_i[WIDTH-1], gray_i[WIDTH-1]^gray_i[WIDTH-2]};
      if (WIDTH == 2) begin
	 assign bin_o = bin_1;
      end
      else begin
	 assign bin_o[WIDTH-1] = bin_1[1];
	 `IP_MODULE_NAME(efx_fifo_gray2bin) #(.WIDTH(WIDTH-1)) u_gray2bin (.bin_o(bin_o[WIDTH-2:0]), .gray_i({bin_1[0], gray_i[WIDTH-3:0]}));
      end
   end
   else /* if (WIDTH == 1) */
     assign bin_o = gray_i;
endgenerate
   
endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   pipe_reg.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Parallel Pipelining Shift Register
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_datasync) #(
    parameter STAGE = 32,
    parameter WIDTH = 4
) (
 input  wire             clk_i,
 input  wire [WIDTH-1:0] d_i,
 output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] pipe_reg [STAGE-1:0];
integer i;

always @(posedge clk_i) begin
  for (i=STAGE-1; i>0; i = i - 1) begin
    pipe_reg[i] <= pipe_reg[i-1];
  end
  pipe_reg[0] <= d_i;
end
assign d_o = pipe_reg[STAGE-1];
   

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_resetsync) #(
    parameter ASYNC_STAGE = 2,
    parameter ACTIVE_LOW = 1
) (
    input  wire             clk,
    input  wire             reset,
    output wire             d_o
);


generate
   if (ACTIVE_LOW == 1) begin: active_low
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (1),
         .RST_VALUE  (0)
      ) efx_resetsync_active_low (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b1),
         .d_o     (d_o)
      );
   end
   else begin: active_high
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (0),
         .RST_VALUE  (1)
      ) efx_resetsync_active_high (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b0),
         .d_o     (d_o)
      );
   end
endgenerate

endmodule



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_asyncreg) #(
    parameter ASYNC_STAGE = 2,
    parameter WIDTH = 4,
    parameter ACTIVE_LOW = 1, // 0 - Active high reset, 1 - Active low reset
    parameter RST_VALUE = 0 
) (
    input  wire             clk,
    input  wire             reset_n,
    input  wire [WIDTH-1:0] d_i,
    output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] async_reg[ASYNC_STAGE-1:0];

assign d_o = async_reg[ASYNC_STAGE-1];

genvar i;
generate
   if (ACTIVE_LOW == 1) begin: active_low
      if (RST_VALUE == 0) begin: low_reset_0
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: low_reset_1
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
   else begin: active_high
      if (RST_VALUE == 0) begin: high_reset_0
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: high_reset_1
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
endgenerate

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ctl) # (
    parameter SYNC_CLK           = 1,
    parameter SYNC_STAGE         = 2,
    parameter MODE               = "STANDARD",
    parameter WR_DEPTH           = 512,
    parameter WADDR_WIDTH        = 9,
    parameter RADDR_WIDTH        = 9,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter RAM_MUX_RATIO      = 1,
    parameter PIPELINE_REG       = 1,
    parameter ALMOST_FLAG        = 1,
    parameter PROGRAMMABLE_FULL  = "NONE",
    parameter PROG_FULL_ASSERT   = 0,
    parameter PROG_FULL_NEGATE   = 0,
    parameter PROGRAMMABLE_EMPTY = "NONE",
    parameter PROG_EMPTY_ASSERT  = 0,
    parameter PROG_EMPTY_NEGATE  = 0,
    parameter OUTPUT_REG         = 0,
    parameter HANDSHAKE_FLAG     = 1, 
    parameter OVERFLOW_PROTECT   = 0,
    parameter UNDERFLOW_PROTECT  = 0
)(
    input   wire                   wr_rst,
    input   wire                   rd_rst,
    input   wire                   wclk,
    input   wire                   rclk,
    input   wire                   we,
    input   wire                   re,
    output  wire                   wr_full,
    output  reg                    wr_ack,
    output  wire                   wr_almost_full,
    output  wire                   rd_empty,
    output  wire                   rd_almost_empty,
    output  wire                   wr_prog_full,
    output  wire                   rd_prog_empty,
    output  wire                   wr_en_int,
    output  wire                   rd_en_int,
    output  wire [WADDR_WIDTH-1:0] waddr,
    output  wire [RADDR_WIDTH-1:0] raddr,
    output  wire [WADDR_WIDTH:0]   wr_datacount,
    output  wire [RADDR_WIDTH:0]   rd_datacount,
    output  wire                   rd_vld,
    output  reg                    wr_overflow,
    output  reg                    rd_underflow
);

reg  [WADDR_WIDTH:0] waddr_cntr;
reg  [WADDR_WIDTH:0] waddr_cntr_r;
reg  [RADDR_WIDTH:0] raddr_cntr;
reg                  rd_valid;

wire [WADDR_WIDTH:0] waddr_int;
wire [RADDR_WIDTH:0] raddr_int;
wire                 rd_empty_int;
wire [WADDR_WIDTH:0] wr_datacount_int;
wire [RADDR_WIDTH:0] rd_datacount_int;

assign waddr     = waddr_cntr[WADDR_WIDTH-1:0];
// NIC
wire [RADDR_WIDTH:0] ram_raddr;
assign raddr     = (MODE == "FWFT") ? ram_raddr[RADDR_WIDTH-1:0] : raddr_cntr[RADDR_WIDTH-1:0];
//assign raddr     = raddr_cntr[RADDR_WIDTH-1:0];
//assign wr_en_int = we & ~wr_full;
assign wr_en_int = OVERFLOW_PROTECT ? we & ~wr_full : we;

assign wr_datacount  = wr_datacount_int;
assign rd_datacount  = ASYM_WIDTH_RATIO == 4 && SYNC_CLK ? wr_datacount_int : rd_datacount_int;


generate
    if (MODE == "FWFT") begin    
        // NIC
        //assign rd_en_int     = (~rd_empty_int & rd_empty) | (re & ~rd_empty_int);
        //assign rd_empty      = rd_empty_fwft;

        assign rd_en_int     = 1'b1;
        //assign rd_empty      = rd_empty_int;
        
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        init_set <= 1'b1;
        //    end
        //    else if (~init_set & rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //    else if (~rd_empty_int) begin
        //        init_set <= 1'b0;            
        //    end
        //    else if (rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //end
        // NIC 
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        rd_empty_fwft <= 1'b1;
        //    end
        //    else if (rd_en_int) begin
        //        rd_empty_fwft <= 1'b0;            
        //    end
        //    else if (re) begin
        //        rd_empty_fwft <= 1'b1;            
        //    end
        //end          
        
        //if (FAMILY == "TRION") begin
        if (OUTPUT_REG) begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid <= 1'b0;
                end
                else begin
                    rd_valid <= ~rd_empty;
                end
            end
            assign rd_vld = rd_valid;                
        end    
        else begin
            assign rd_vld = ~rd_empty;                
        end
        
        assign rd_empty = rd_empty_int;
    end
    else begin
        assign rd_en_int     = UNDERFLOW_PROTECT ? re & ~rd_empty_int : re;
        assign rd_empty      = rd_empty_int;
        
        if (OUTPUT_REG) begin
            reg rd_valid_r;
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid_r <= 'h0;
                    rd_valid   <= 'h0;
                end
                else begin
                    {rd_valid,rd_valid_r} <= {rd_valid_r,rd_en_int};
                end
            end
            assign rd_vld = rd_valid;            
        end
        else begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid  <= 'h0;
                end
                else begin
                    rd_valid <= rd_en_int;
                end
            end
            assign rd_vld = rd_valid;
        end
    end
    
    if (ALMOST_FLAG) begin
        assign wr_almost_full   = wr_datacount >= WR_DEPTH-1;
        assign rd_almost_empty  = rd_datacount <= 'd1;
    end
    else begin
        assign wr_almost_full   = 1'b0;
        assign rd_almost_empty  = 1'b0;        
    end
    
    if (PROGRAMMABLE_FULL == "STATIC_SINGLE") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_datacount >= PROG_FULL_ASSERT;
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end  
    end
    else if (PROGRAMMABLE_FULL == "STATIC_DUAL") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_prog_full_int ? wr_datacount >= PROG_FULL_NEGATE : wr_datacount >= PROG_FULL_ASSERT;   

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end              
    end
    else begin
        assign wr_prog_full  = 1'b0; 
    end
        
    if (PROGRAMMABLE_EMPTY == "STATIC_SINGLE") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty  = rd_datacount <= PROG_EMPTY_ASSERT;
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end
    end
    else if (PROGRAMMABLE_EMPTY == "STATIC_DUAL") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty = rd_prog_empty_int ? (rd_datacount <= PROG_EMPTY_NEGATE) : (rd_datacount <= PROG_EMPTY_ASSERT);
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end  
    end
    else begin
        assign rd_prog_empty  = 1'b0; 
    end
    
    if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_ack <= 1'b0;
            end
            else begin
                // NIC
                //wr_ack <= wr_en_int & ~wr_overflow;
                wr_ack <= OVERFLOW_PROTECT ? wr_en_int & ~wr_overflow : wr_en_int;
            end
        end
    end

    if (OVERFLOW_PROTECT) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else if (we && wr_full) begin
                wr_overflow <= 1'b1;
            end
            else begin
                wr_overflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else begin
                wr_overflow <= we && wr_full ? 1'b1 : wr_overflow;
            end
        end
    end

    if (UNDERFLOW_PROTECT) begin     
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else if (re && rd_empty) begin
                rd_underflow <= 1'b1;
            end
            else begin
                rd_underflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else begin
                rd_underflow <= re && rd_empty ? 1'b1 : rd_underflow;
            end
        end
    end
    
    localparam RATIO_WIDTH = (RADDR_WIDTH >= WADDR_WIDTH)? RADDR_WIDTH - WADDR_WIDTH : WADDR_WIDTH - RADDR_WIDTH;
    
    if (ASYM_WIDTH_RATIO < 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:RATIO_WIDTH]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:RATIO_WIDTH];
        assign wr_datacount_int = waddr_cntr - (raddr_int/RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int*RAM_MUX_RATIO)-raddr_cntr;
    end
    // NIC
    else if (ASYM_WIDTH_RATIO == 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:0]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - raddr_int;
        assign rd_datacount_int = waddr_int - raddr_cntr;
    end
    else begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:RATIO_WIDTH] == raddr_int[RADDR_WIDTH-1:0]);
        // NIC
        //assign rd_empty_int     = (waddr_int- raddr_cntr*RAM_MUX_RATIO) < RAM_MUX_RATIO;
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:RATIO_WIDTH] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - (raddr_int*RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int/RAM_MUX_RATIO)-raddr_cntr;
    end
endgenerate

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr   <= 'h0;
    end
    else if (wr_en_int) begin
        waddr_cntr   <= waddr_cntr + 1'b1;
    end
end

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr_r  <= 'h0;
    end
    else begin
        waddr_cntr_r  <= waddr_cntr;
    end
end

always @ (posedge rclk or posedge rd_rst) begin
    if (rd_rst) begin
        raddr_cntr   <= 'h0;
    end
    // NIC
    //else if (rd_en_int) begin
    else begin
        //raddr_cntr   <= raddr_cntr + 1'b1;
        //raddr_cntr   <= raddr_cntr + (re & ~rd_empty_int);
        raddr_cntr   <= ram_raddr;
    end
end
// NIC
assign ram_raddr = raddr_cntr + (UNDERFLOW_PROTECT ? re & ~rd_empty_int : re);


generate
    if (SYNC_CLK) begin : sync_clk
       if (MODE == "FWFT") begin
          assign waddr_int = waddr_cntr_r;
          assign raddr_int        = raddr_cntr;
       end
       else begin
          assign waddr_int        = waddr_cntr;
          assign raddr_int        = raddr_cntr;
       end
    end
    else begin : async_clk
        reg  [RADDR_WIDTH:0] raddr_cntr_gry_r;
        reg  [WADDR_WIDTH:0] waddr_cntr_gry_r;
        
        wire [RADDR_WIDTH:0] raddr_cntr_gry;
        wire [RADDR_WIDTH:0] raddr_cntr_gry_sync;
        wire [RADDR_WIDTH:0] raddr_cntr_sync_g2b;
        wire [WADDR_WIDTH:0] waddr_cntr_gry;
        wire [WADDR_WIDTH:0] waddr_cntr_gry_sync;
        wire [WADDR_WIDTH:0] waddr_cntr_sync_g2b;

        if (PIPELINE_REG) begin
            reg  [RADDR_WIDTH:0] raddr_cntr_sync_g2b_r;    
            reg  [WADDR_WIDTH:0] waddr_cntr_sync_g2b_r;        
        
            assign waddr_int        = waddr_cntr_sync_g2b_r;
            assign raddr_int        = raddr_cntr_sync_g2b_r;
            
            always @ (posedge wclk or posedge wr_rst) begin
                if (wr_rst) begin
                    raddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    raddr_cntr_sync_g2b_r <= raddr_cntr_sync_g2b;
                end
            end
            
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    waddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    waddr_cntr_sync_g2b_r <= waddr_cntr_sync_g2b;
                end
            end
        end
        else begin
            assign waddr_int        = waddr_cntr_sync_g2b;
            assign raddr_int        = raddr_cntr_sync_g2b;        
        end
      
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                raddr_cntr_gry_r <= 'h0;
            end
            else begin
                raddr_cntr_gry_r <= raddr_cntr_gry;
            end
        end
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_bin2gray    (.bin_i(raddr_cntr), .gray_o(raddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (RADDR_WIDTH+1), .ACTIVE_LOW(0)) xrd2wr_addr_sync   (.clk(wclk), .reset_n(wr_rst), .d_i(raddr_cntr_gry_r), .d_o(raddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_gray2bin    (.gray_i(raddr_cntr_gry_sync), .bin_o(raddr_cntr_sync_g2b));
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                waddr_cntr_gry_r <= 'h0;
            end
            else begin
                waddr_cntr_gry_r <= waddr_cntr_gry;
            end
        end        
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_bin2gray  (.bin_i(waddr_cntr), .gray_o(waddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (WADDR_WIDTH+1), .ACTIVE_LOW(0)) wr2rd_addr_sync (.clk(rclk), .reset_n(rd_rst), .d_i(waddr_cntr_gry_r), .d_o(waddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_gray2bin  (.gray_i(waddr_cntr_gry_sync), .bin_o(waddr_cntr_sync_g2b));        

    end
endgenerate
endmodule


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   bin2gray.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Binary to Gray Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_bin2gray)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] gray_o,
 // input
 input [WIDTH-1:0] bin_i
 );

//---------------------------------------------------------------------
// Function :   bit_xor
// Description: reduction xor
function bit_xor (
  input [31:0] nex_bit,
  input [31:0] curr_bit,
  input [WIDTH-1:0] xor_in);
  begin : fn_bit_xor
    bit_xor = xor_in[nex_bit] ^ xor_in[curr_bit];
  end
endfunction

// Convert Binary to Gray, bit by bit
generate 
begin
  genvar bit_idx;
  for(bit_idx=0; bit_idx<WIDTH-1; bit_idx=bit_idx+1) begin : gBinBits
    assign gray_o[bit_idx] = bit_xor(bit_idx+1, bit_idx, bin_i);
  end
  assign   gray_o[WIDTH-1] = bin_i[WIDTH-1];
end
endgenerate

endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
BdBMMrQSsgVCZyb9c09rDIFEXYxAR7+LLz6oosxtZCEdgSIOEyOtytHcRkp0xw6r
ufJycf0V2cXq9z5w/EeHIenbJZXf1cl0H97Vds9qL4O949BkDAfGhQNGqzH75uN7
eWpR8psQXk+qNTJYleYsBlxoIGy++mauag8B1oUi/SZns9yfatAJNoQpvBhOT10d
zSsBMpKtwDhWhwuQwrYA2hfoMl2NOFGhF8gju9VP28ahgnGABDqCiOAWQRcvb29G
SMRQA7NQHtcfPHB1pIdFbwkdKnj607Lx8AA70RdnZJIjoyn/JuZPEI1iTjpJX+NO
3p7tYxnaegM5wgwkIFmBOA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 1152 )
`pragma protect data_block
CeS4zhF2GjdFiXaGXJZ2OcFdVq0EotZ60f4h8xl0n78GO/jJrvXiat85ecmYI/RA
sCVmjHBwEf0KiL2sxbemLt7CDPYHeHLYmzMFt+rz/i7GExm39W02grzCEP0FwjrM
1Wy7Lu9+qymEkqjOHEmcLztqGSk28t5sToKrcyTYQv/F6ECmQr6S9fH8XOjCdz1+
C1NUt6dYzPhEWvpSQinm5zl6QFcnD97cybavOU6FCuZmXMR3Dd2Wwi/N1dsSVaZ6
ZhWMSb3OzfqtQzW5Q/XM4qsPz414/FBST6CWltYERsyx/zLR1dGlYgnnCglHfuKP
yGNJ790XHZd4v9xxVJ9Em1/LXmYVoSPoGw0v+9TSRaW0LB6+YXZ7r1zHnXRRyK+X
7JQwUG8ijRVszupNeH5bvJptZ+3XdLORNd9EabqLPzJSdIuo4HAlZNcBW0fH2RAa
v2Y+4zX9S9yxHUm41KLCQQgWuxYfzC6CgugPMXGqg6F2duLi4A1mJQAu27xj1Wcv
Z6idRRnEshXzWTIqRi+fZa9dKGOJMUgzWyjvIAK6RZTqOBAoweVS3MxJ1EOc8VSB
uvghMQPtc1RDz6MfGdm/d5U8PQjqCBi3OvtWA/vKsnNr1o6A9ijZKf6uwkmhiYtd
O2TSstz/26bA0DUYaAA0Iq1AF4rUhkAjM6ASEc8+Nim/EaQ0Bbo7K2qJOwllZTVU
2iASaGqsOU8vMj6JYGC7UMr4chwnx2cLoVtdVCA0/TGv4l/RHvXKihDttW9kQ4b8
Q4wwFKt8An7VW7jbF65bfIhhXGSFlpMP3PFr8i7ZkzWv3hr5XE/V9x0/2fN9lMYT
RovLjke2uV3UCXnh8TIFMOESalE7BnRpWbJxBUrZnMWMlWna6l2LZFquam4tgKeh
GycBauujrFfoo9e4dak5qnAx6qCuJ0pJHqt6lZ6QGKHPPFKhrz+RkCZIGRTFOFOM
ZyjWJ5I9LB5qe9w+K/YIwLcedcwJi3uoO1mikMv/TkCcb/sF2TsBh9zrZ31lbVcc
3A76tDkPbH0vzVibgD82bGXWDen9WaDkp22xitTim0qVfya3oKJGWsUtY6ZWDa2Z
6zYnOTatDhRGslok6hPEgfNIfjIAHKQ0uGQ/SdLtfXJowRixJXN5wrwUtT9w3jXa
lIxxmGrFHcgaAvp/MVIega+VmmF00VCAc4gz8gUnu5aZTw12wZ213NmGYYaK1e4u
rtARqAXQ+QQy11PS+KDH2Ah3Hg1AfHnfTEWOoWYpSlWAQvYRgRt01IejiNtlZ2c7
JzACqL3WzOnO/WuNWLfRcXFwVTI/ZL01ZMK1fBpgxrlndyUdOfZ5ESFx4hXg3IH6
wKEvm8sfdyT6Y6b/VZWNCzyrnn0nqq1X/29eG0KpoK3cQ2lnKhX53qHGWuH4kjr+
vxTirn4nqm1oLaT+vCqV6okOWvRchI3alyW6JiqvsOEey1gAvyKLxUyABVwVoB0H
yPYsjqjcOC4oBoWfXKyVMXHir4ET4OPNrWv6PSWGUWr0XF6e7Woi5XbZtZ5RbHCy
`pragma protect end_protected

//pragma protect end

`undef IP_UUID
`undef IP_NAME_CONCAT
`undef IP_MODULE_NAME
