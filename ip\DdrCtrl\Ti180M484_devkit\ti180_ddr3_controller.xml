<?xml version="1.0" encoding="UTF-8"?>
<efx:project name="ti180_ddr3_controller" description="" last_change_date="Wed January 4 2023 14:32:14" location="" sw_version="2022.2.322" last_run_state="pass" last_run_tool="efx_pgm" last_run_flow="bitstream" config_result_in_sync="true" design_ood="sync" place_ood="sync" route_ood="sync" xmlns:efx="http://www.efinixinc.com/enf_proj" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.efinixinc.com/enf_proj enf_proj.xsd">
    <efx:device_info>
        <efx:family name="Titanium"/>
        <efx:device name="Ti180M484"/>
        <efx:timing_model name="C4"/>
    </efx:device_info>
    <efx:design_info def_veri_version="verilog_2k" def_vhdl_version="vhdl_2008">
        <efx:top_module name="example_top"/>
        <efx:design_file name="example_top.v" version="default" library="default"/>
        <efx:design_file name="DdrCtrl.v" version="default" library="default"/>
        <efx:design_file name="memory_checker_axi.v" version="default" library="default"/>
        <efx:top_vhdl_arch name=""/>
    </efx:design_info>
    <efx:constraint_info>
        <efx:sdc_file name="constraint.sdc"/>
        <efx:inter_file name=""/>
    </efx:constraint_info>
    <efx:sim_info/>
    <efx:misc_info>
        <efx:misc_file name="ddr3_controller.bin"/>
        <efx:misc_file name="ddr3_controller.vh"/>
    </efx:misc_info>
    <efx:ip_info/>
    <efx:synthesis tool_name="efx_map">
        <efx:param name="work_dir" value="work_syn" value_type="e_string"/>
        <efx:param name="write_efx_verilog" value="on" value_type="e_bool"/>
        <efx:param name="mode" value="speed" value_type="e_option"/>
        <efx:param name="max_ram" value="-1" value_type="e_integer"/>
        <efx:param name="max_mult" value="-1" value_type="e_integer"/>
        <efx:param name="infer-clk-enable" value="3" value_type="e_option"/>
        <efx:param name="infer-sync-set-reset" value="1" value_type="e_option"/>
        <efx:param name="min-sr-fanout" value="0" value_type="e_integer"/>
        <efx:param name="fanout-limit" value="0" value_type="e_integer"/>
        <efx:param name="bram_output_regs_packing" value="1" value_type="e_option"/>
        <efx:param name="retiming" value="1" value_type="e_option"/>
        <efx:param name="seq_opt" value="1" value_type="e_option"/>
        <efx:param name="blast_const_operand_adders" value="1" value_type="e_option"/>
        <efx:param name="operator-sharing" value="0" value_type="e_option"/>
        <efx:param name="optimize-adder-tree" value="0" value_type="e_option"/>
        <efx:param name="seq-opt-sync-only" value="0" value_type="e_option"/>
        <efx:param name="blackbox-error" value="1" value_type="e_option"/>
        <efx:param name="allow-const-ram-index" value="0" value_type="e_option"/>
        <efx:param name="dsp-mac-packing" value="1" value_type="e_option"/>
        <efx:param name="dsp-output-regs-packing" value="1" value_type="e_option"/>
        <efx:param name="dsp-input-regs-packing" value="1" value_type="e_option"/>
        <efx:param name="pack-luts-to-comb4" value="0" value_type="e_option"/>
        <efx:param name="hdl-compile-unit" value="1" value_type="e_option"/>
        <efx:param name="create-onehot-fsms" value="0" value_type="e_option"/>
        <efx:param name="min-ce-fanout" value="0" value_type="e_integer"/>
    </efx:synthesis>
    <efx:place_and_route tool_name="efx_pnr">
        <efx:param name="work_dir" value="work_pnr" value_type="e_string"/>
        <efx:param name="verbose" value="off" value_type="e_bool"/>
        <efx:param name="load_delaym" value="on" value_type="e_bool"/>
        <efx:param name="optimization_level" value="TIMING_2" value_type="e_option"/>
        <efx:param name="seed" value="444" value_type="e_integer"/>
        <efx:param name="placer_effort_level" value="2" value_type="e_option"/>
        <efx:param name="max_threads" value="2" value_type="e_integer"/>
    </efx:place_and_route>
    <efx:bitstream_generation tool_name="efx_pgm">
        <efx:param name="mode" value="active" value_type="e_option"/>
        <efx:param name="width" value="1" value_type="e_option"/>
        <efx:param name="enable_roms" value="on" value_type="e_option"/>
        <efx:param name="spi_low_power_mode" value="on" value_type="e_bool"/>
        <efx:param name="io_weak_pullup" value="on" value_type="e_bool"/>
        <efx:param name="oscillator_clock_divider" value="DIV8" value_type="e_option"/>
        <efx:param name="bitstream_compression" value="on" value_type="e_bool"/>
        <efx:param name="enable_external_master_clock" value="off" value_type="e_bool"/>
        <efx:param name="active_capture_clk_edge" value="negedge" value_type="e_option"/>
        <efx:param name="jtag_usercode" value="0xFFFFFFFF" value_type="e_string"/>
        <efx:param name="release_tri_then_reset" value="on" value_type="e_bool"/>
        <efx:param name="four_byte_addressing" value="off" value_type="e_bool"/>
        <efx:param name="generate_bit" value="on" value_type="e_bool"/>
        <efx:param name="generate_bitbin" value="off" value_type="e_bool"/>
        <efx:param name="generate_hex" value="on" value_type="e_bool"/>
        <efx:param name="generate_hexbin" value="off" value_type="e_bool"/>
        <efx:param name="cold_boot" value="off" value_type="e_bool"/>
        <efx:param name="cascade" value="off" value_type="e_option"/>
    </efx:bitstream_generation>
    <efx:debugger>
        <efx:param name="work_dir" value="work_dbg" value_type="e_string"/>
        <efx:param name="auto_instantiation" value="off" value_type="e_bool"/>
        <efx:param name="profile" value="NONE" value_type="e_string"/>
    </efx:debugger>
    <efx:security>
        <efx:param name="randomize_iv_value" value="on" value_type="e_bool"/>
        <efx:param name="iv_value" value="" value_type="e_string"/>
        <efx:param name="enable_bitstream_encrypt" value="off" value_type="e_bool"/>
        <efx:param name="enable_bitstream_auth" value="off" value_type="e_bool"/>
        <efx:param name="encryption_key_file" value="NONE" value_type="e_string"/>
        <efx:param name="auth_key_file" value="NONE" value_type="e_string"/>
    </efx:security>
</efx:project>
