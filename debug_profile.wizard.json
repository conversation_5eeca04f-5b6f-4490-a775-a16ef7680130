{"debug_cores": [{"name": "la0", "type": "la", "uuid": "904f5abac45e40469644ac8a7f4be1d0", "trigin_en": false, "trigout_en": false, "auto_inserted": true, "capture_control": false, "data_depth": 1024, "input_pipeline": 1, "probes": [{"name": "data_typer", "width": 8, "probe_type": 1}]}], "connections": [{"command": "add_ports", "id": 1, "args": {"netlist": "example_top", "ports": [{"name": "jtag_inst1_CAPTURE", "dir": "in", "width": 1}, {"name": "jtag_inst1_DRCK", "dir": "in", "width": 1}, {"name": "jtag_inst1_RESET", "dir": "in", "width": 1}, {"name": "jtag_inst1_RUNTEST", "dir": "in", "width": 1}, {"name": "jtag_inst1_SEL", "dir": "in", "width": 1}, {"name": "jtag_inst1_SHIFT", "dir": "in", "width": 1}, {"name": "jtag_inst1_TCK", "dir": "in", "width": 1}, {"name": "jtag_inst1_TDI", "dir": "in", "width": 1}, {"name": "jtag_inst1_TMS", "dir": "in", "width": 1}, {"name": "jtag_inst1_UPDATE", "dir": "in", "width": 1}, {"name": "jtag_inst1_TDO", "dir": "out", "width": 1}]}}, {"command": "instantiate", "netlist": "edb_top", "id": 2, "instance": "edb_top_inst"}, {"command": "connect", "id": 3, "args": {"instance": "edb_top_inst", "ports": [{"name": "bscan_CAPTURE", "net": "jtag_inst1_CAPTURE"}, {"name": "bscan_DRCK", "net": "jtag_inst1_DRCK"}, {"name": "bscan_RESET", "net": "jtag_inst1_RESET"}, {"name": "bscan_RUNTEST", "net": "jtag_inst1_RUNTEST"}, {"name": "bscan_SEL", "net": "jtag_inst1_SEL"}, {"name": "bscan_SHIFT", "net": "jtag_inst1_SHIFT"}, {"name": "bscan_TCK", "net": "jtag_inst1_TCK"}, {"name": "bscan_TDI", "net": "jtag_inst1_TDI"}, {"name": "bscan_TMS", "net": "jtag_inst1_TMS"}, {"name": "bscan_UPDATE", "net": "jtag_inst1_UPDATE"}, {"name": "bscan_TDO", "net": "jtag_inst1_TDO"}, {"name": "la0_clk", "net": "csi_rxc_i", "path": []}, {"name": "la0_probe0[0]", "net": "data_typer[0]", "path": []}, {"name": "la0_probe0[1]", "net": "data_typer[1]", "path": []}, {"name": "la0_probe0[2]", "net": "data_typer[2]", "path": []}, {"name": "la0_probe0[3]", "net": "data_typer[3]", "path": []}, {"name": "la0_probe0[4]", "net": "data_typer[4]", "path": []}, {"name": "la0_probe0[5]", "net": "data_typer[5]", "path": []}, {"name": "la0_probe0[6]", "net": "data_typer[6]", "path": []}, {"name": "la0_probe0[7]", "net": "data_typer[7]", "path": []}]}}], "vdbs": [{"file": "debug_top.post.vdb", "instance": "edb_top_inst"}], "session": {"wizard": {"data_depth": 1024, "capture_control": false, "selected_nets": [{"name": "data_typer", "width": 8, "clk_domain": "csi_rxc_i", "selected_probe_type": "DATA AND TRIGGER", "child": [], "path": [], "net_idx_left": 7, "net_idx_right": 0}], "top_module": "example_top", "db_checksum": "ba5fce12098a2c03e7bae2e9a172d1842464edfca8e284870b3519e987537970", "src": "elaborate", "jtag_user": "USER1", "input_pipeline": 1}}}