INFO     : Efinix FPGA Synthesis.

INFO     : Version: 2025.1.110.2.15

INFO     : Compiled: Jun 21 2025.

INFO     : 

INFO     : Copyright (C) 2013 - 2025 Efinix, Inc. All rights reserved.



INFO     : Reading project database "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.xml"

INFO     : Analyzing Verilog file 'C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v' [VERI-1482]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(8): compiling module 'EFX_IBUF' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(16): compiling module 'EFX_OBUF' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(23): compiling module 'EFX_IO_BUF' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(33): compiling module 'EFX_CLKOUT' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(41): compiling module 'EFX_IREG' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(51): compiling module 'EFX_OREG' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(60): compiling module 'EFX_IOREG' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(75): compiling module 'EFX_IDDIO' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(87): compiling module 'EFX_ODDIO' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(99): compiling module 'EFX_GPIO_V1' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(124): compiling module 'EFX_GPIO_V2' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(149): compiling module 'EFX_GPIO_V3' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(186): compiling module 'EFX_PLL_V1' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(205): compiling module 'EFX_PLL_V2' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(231): compiling module 'EFX_PLL_V3' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(279): compiling module 'EFX_OSC_V1' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(285): compiling module 'EFX_OSC_V3' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(294): compiling module 'EFX_FPLL_V1' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(348): compiling module 'EFX_JTAG_V1' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(357): compiling module 'EFX_JTAG_CTRL' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(364): compiling module 'EFX_LVDS_RX_V1' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(381): compiling module 'EFX_LVDS_TX_V1' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(399): compiling module 'EFX_LVDS_RX_V2' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(426): compiling module 'EFX_LVDS_TX_V2' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v(446): compiling module 'EFX_LVDS_BIDIR_V2' [VERI-1018]

INFO     : Reading project database "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.xml"

INFO     : ***** Beginning Analysis ... *****

INFO     : default VHDL library search path is now "C:/Efinity/2025.1/sim_models/vhdl/packages/vhdl_2008" [VHDL-1504]

INFO     : Analyzing Verilog file 'C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v' [VERI-1482]

INFO     : Analyzing Verilog file 'C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v' [VERI-1482]

INFO     : Analyzing Verilog file 'C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v' [VERI-1482]

INFO     : Analyzing Verilog file 'C:/Efinity/2025.1/sim_models/maplib/efinix_peri_lib.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v' [VERI-1482]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(673): undeclared symbol 'w_csi_rx_clk', assumed default net type 'wire' [VERI-2561]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(674): undeclared symbol 'config_data', assumed default net type 'wire' [VERI-2561]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(675): undeclared symbol 'config_data_valid', assumed default net type 'wire' [VERI-2561]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\ddr_rw_ctrl.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\i2c_timing_ctrl_16bit.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\CMOS_Capture_RAW_Gray.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\I2C_MT9M001_Gray_Config.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_para.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_driver.v' [VERI-1482]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_driver.v(37): analyzing included file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src/lcd_para.v' [VERI-1328]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_driver.v(37): back to file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_driver.v' [VERI-2320]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\hdmi_ip\hdmi_tx_ip.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\hdmi_ip\encode.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\hdmi_ip\serdes_4b_10to1.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\I2C_SC130GS_12801024_4Lanes_Config.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\PWMLite.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_display.v' [VERI-1482]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_display.v(48): analyzing included file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src/lcd_para.v' [VERI-1328]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_display.v(48): back to file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_display.v' [VERI-2320]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\dsi\dsi_init.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\Sensor_Image_XYCrop.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\i2c_timing_ctrl_reg16_dat16.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\I2C_AR0135_1280720_Config.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\AXI4_AWARMux.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lvds\LCDDual2LVDS.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\hdmi_ip\tmds_channel.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\hdmi_ip\rgb2dvi.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\VIP_RAW8_RGB888.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\Line_Shift_RAM_8Bit.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\VIP_Matrix_Generate_3X3_8Bit.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\BoundCrop\FrameBoundCrop.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\I2C_AD2020_1280960_FPS60_1Lane_Config.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\i2c_timing_ctrl_reg16_dat8_wronly.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\common_uese\delay_reg.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\common_uese\Row_Line_Counter.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO\W0_FIFO.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO\R0_FIFO.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/dsi_tx\dsi_tx.sv' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v' [VERI-1482]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(266): analyzing included file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl/ddr3_controller.vh' [VERI-1328]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(266): back to file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v' [VERI-2320]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(568): undeclared symbol '**', assumed default net type '**' [VERI-2561]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(568): undeclared symbol '**', assumed default net type '**' [VERI-2561]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(573): analyzing included file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl/ddr3_controller.vh' [VERI-1328]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(573): back to file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v' [VERI-2320]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1567): analyzing included file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl/ddr3_controller.vh' [VERI-1328]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1567): back to file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v' [VERI-2320]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1821): analyzing included file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl/ddr3_controller.vh' [VERI-1328]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1821): back to file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v' [VERI-2320]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2124): analyzing included file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl/ddr3_controller.vh' [VERI-1328]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2124): back to file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v' [VERI-2320]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2574): analyzing included file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl/ddr3_controller.vh' [VERI-1328]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2574): back to file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v' [VERI-2320]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_8\W0_FIFO_8.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_64\W0_FIFO_64.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/FIFO_W48R24\FIFO_W48R24.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_8\R0_FIFO_8.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_16\R0_FIFO_16.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/div_u39_u31\div_u39_u31.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W8R8_D2048_YUV\W8R8_D2048_YUV.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/div_u27_u21\div_u27_u21.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/div_u26_u10\div_u26_u10.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/div_u29_u21\div_u29_u21.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v' [VERI-1482]

INFO     : Analyzing Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v' [VERI-1482]

INFO     : Analysis took 0.385413 seconds.

INFO     : 	Analysis took 0.375 seconds (approximately) in total CPU time.

INFO     : Analysis virtual memory usage: begin = 53.444 MB, end = 66.844 MB, delta = 13.4 MB

INFO     : 	Analysis peak virtual memory usage = 66.844 MB

INFO     : Analysis resident set memory usage: begin = 55.82 MB, end = 72.064 MB, delta = 16.244 MB

INFO     : 	Analysis peak resident set memory usage = 72.068 MB

INFO     : ***** Ending Analysis ... *****

INFO     : ***** Beginning Elaboration ... *****

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(598): port 'cal_fail_log' remains unconnected for this instance [VERI-1927]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1250): port 'axi_awid' remains unconnected for this instance [VERI-1927]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(6): compiling module 'example_top' [VERI-1018]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(738): using initial value of 'r_axi_addr' since it is never assigned [VERI-1220]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(739): using initial value of 'r_axi_wdata' since it is never assigned [VERI-1220]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(741): using initial value of 'r_axi_sel' since it is never assigned [VERI-1220]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(429): expression size 32 truncated to fit in target size 1 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(182): port 'wr_busy' remains unconnected for this instance [VERI-1927]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(182): port 'wr_data' is not connected on this instance [VERI-2435]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(49): compiling module 'DdrCtrl' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1822): compiling module 'efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2454): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2569): compiling module '**' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2569): expression size ** truncated to fit in target size ** [VERI-1209]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(884): compiling module 'EFX_SRL8' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2454): actual bit length ** differs from formal bit length *** for port '**' [VERI-1330]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2569): compiling module '**' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2569): expression size ** truncated to fit in target size ** [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2569): compiling module '**' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2569): expression size ** truncated to fit in target size ** [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2454): actual bit length ** differs from formal bit length *** for port '**' [VERI-1330]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2873): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1626): compiling module '**' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(568): port '**' remains unconnected for this instance [VERI-1927]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(568): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1749): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1816): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1749): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): extracting RAM for identifier '**' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(261): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(221): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): extracting RAM for identifier '**' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): extracting RAM for identifier '**' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2894): compiling module 'efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3186): compiling module 'efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3223): extracting RAM for identifier 'ram' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3672): compiling module 'efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33(SYNC_CLK=0,PROGRAMMABLE_FULL="STATIC_DUAL",PROG_FULL_ASSERT=27,PROG_FULL_NEGATE=23,PROGRAMMABLE_EMPTY="STATIC_DUAL",PROG_EMPTY_ASSERT=5,PROG_EMPTY_NEGATE=7)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3948): expression size 32 truncated to fit in target size 10 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3949): expression size 32 truncated to fit in target size 10 [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(4068): compiling module 'efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3593): compiling module 'efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3508): compiling module 'efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3508): compiling module 'efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3508): compiling module 'efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3508): compiling module 'efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3508): compiling module 'efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3508): compiling module 'efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3508): compiling module 'efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3508): compiling module 'efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(3508): compiling module 'efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(568): actual bit length ** differs from formal bit length *** for port '**' [VERI-1330]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): extracting RAM for identifier '**' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1562): extracting RAM for identifier '**' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1423): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2119): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(2119): extracting RAM for identifier '**' [VERI-2571]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1423): expression size ** truncated to fit in target size ** [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(568): net '**' does not have a driver [VDB-1002]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(568): input port '**' remains unconnected for this instance [VDB-1053]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(568): input port '**' is not connected on this instance [VDB-1013]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1749): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1816): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(1689): compiling module '**' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v(116): input port 'wr_data[127]' is not connected on this instance [VDB-1013]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(565): actual bit length 4 differs from formal bit length 8 for port 'axi_aid' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(572): actual bit length 4 differs from formal bit length 8 for port 'axi_wid' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(581): actual bit length 4 differs from formal bit length 8 for port 'axi_bid' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(587): actual bit length 4 differs from formal bit length 8 for port 'axi_rid' [VERI-1330]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\AXI4_AWARMux.v(11): compiling module 'AXI4_AWARMux(AID_LEN=4)' [VERI-1018]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\AXI4_AWARMux.v(90): expression size 2 truncated to fit in target size 1 [VERI-1209]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\i2c_timing_ctrl_16bit.v(410): compiling module 'i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(102): port 'a_wr_rst_i' remains unconnected for this instance [VERI-1927]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(49): compiling module 'afifo_w24d16_r24d16' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(302): compiling module 'efx_fifo_top_97de8c1d535c46caaff94d8f5a5d7caa(FAMILY="TITANIUM",MODE="FWFT",DEPTH=16,DATA_WIDTH=24,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=13,PROG_FULL_NEGATE=13,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=0,OVERFLOW_PROTECT=0,UNDERFLOW_PROTECT=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(1381): compiling module 'efx_resetsync_97de8c1d535c46caaff94d8f5a5d7caa(ACTIVE_LOW=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(1426): compiling module 'efx_asyncreg_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=1,ACTIVE_LOW=0,RST_VALUE=1)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(622): compiling module 'efx_fifo_ram_97de8c1d535c46caaff94d8f5a5d7caa(FAMILY="TITANIUM",WR_DEPTH=16,RD_DEPTH=16,WDATA_WIDTH=24,RDATA_WIDTH=24,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(659): extracting RAM for identifier 'ram' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(953): compiling module 'efx_fifo_ctl_97de8c1d535c46caaff94d8f5a5d7caa(SYNC_CLK=0,MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=13,PROG_FULL_NEGATE=13,HANDSHAKE_FLAG=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(213): compiling module 'efx_fifo_bin2gray_97de8c1d535c46caaff94d8f5a5d7caa' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(1426): compiling module 'efx_asyncreg_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=5,ACTIVE_LOW=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(125): compiling module 'efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(125): compiling module 'efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=4)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(125): compiling module 'efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=3)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(125): compiling module 'efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=2)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v(88): input port 'a_wr_rst_i' is not connected on this instance [VDB-1013]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(667): actual bit length 8 differs from formal bit length 9 for port 'i2c_config_index' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(669): actual bit length 8 differs from formal bit length 9 for port 'i2c_config_size' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(674): actual bit length 1 differs from formal bit length 24 for port 'config_data' [VERI-1330]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\cmos_i2c\I2C_AD2020_1280960_FPS60_1Lane_Config.v(18): compiling module 'I2C_AD2020_1280960_FPS60_1Lane_Config' [VERI-1018]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(683): actual bit length 8 differs from formal bit length 9 for port 'LUT_INDEX' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(685): actual bit length 8 differs from formal bit length 9 for port 'LUT_SIZE' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(695): expression size 3 truncated to fit in target size 2 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(696): expression size 5 truncated to fit in target size 4 [VERI-1209]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\PWMLite.v(10): compiling module 'PWMLite' [VERI-1018]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\PWMLite.v(22): expression size 8 truncated to fit in target size 7 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(843): expression size 3 truncated to fit in target size 2 [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(49): compiling module 'csi_rx' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(5439): port '**' is not connected on this instance [VERI-2435]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(5136): compiling module 'efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480(tINIT_NS=1000,NUM_DATA_LANE=1,HS_BYTECLK_MHZ=187,CLOCK_FREQ_MHZ=50,DPHY_CLOCK_MODE="Discontinuous",PIXEL_FIFO_DEPTH=1024,ENABLE_USER_DESKEWCAL=1'b0,ENABLE_VCX=1'b0,PACK_TYPE=15)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(5544): compiling module 'efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6496): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6292): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(5987): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6337): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(4539): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6506): compiling module 'efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(5130): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(3784): compiling module '**' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(3784): expression size ** truncated to fit in target size ** [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(4011): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(1013): compiling module '**' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(1013): assignment and/or driver for '**' inside static function '**' is not preserved [VERI-2457]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(5538): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6590): compiling module 'efx_fifo_top_e7fb069599c849bca10d5e09e6d98480(SYNC_CLK=1,MODE="FWFT",DEPTH=16,DATA_WIDTH=8,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6882): compiling module 'efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6919): extracting RAM for identifier 'ram' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7439): compiling module 'efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7715): expression size 32 truncated to fit in target size 5 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7716): expression size 32 truncated to fit in target size 5 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(5538): input port '**' is not connected on this instance [VDB-1013]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(936): port '**' remains unconnected for this instance [VERI-1927]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(936): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6506): compiling module 'efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6506): compiling module 'efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(936): expression size ** truncated to fit in target size ** [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(2253): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(2385): compiling module '**' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(2385): expression size ** truncated to fit in target size ** [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(2545): compiling module '**' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(2545): expression size ** truncated to fit in target size ** [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(2624): compiling module '**' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6590): compiling module 'efx_fifo_top_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",DEPTH=1024,DATA_WIDTH=64,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=1024,PROG_FULL_NEGATE=1024,PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=2)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6882): compiling module 'efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(6919): extracting RAM for identifier 'ram' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7439): compiling module 'efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(SYNC_CLK=0,MODE="FWFT",WR_DEPTH=1024,WADDR_WIDTH=10,RADDR_WIDTH=10,ALMOST_FLAG=0,PROG_FULL_ASSERT=1024,PROG_FULL_NEGATE=1024,PROGRAMMABLE_EMPTY="STATIC_DUAL",PROG_EMPTY_NEGATE=2,HANDSHAKE_FLAG=0)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7715): expression size 32 truncated to fit in target size 11 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7716): expression size 32 truncated to fit in target size 11 [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7835): compiling module 'efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7289): compiling module 'efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(7204): compiling module 'efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(936): input port '**' is not connected on this instance [VDB-1013]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(5439): net '**' does not have a driver [VDB-1002]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv(5439): input port '**' remains unconnected for this instance [VDB-1053]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(867): actual bit length 4 differs from formal bit length 1 for port 'Rx_LP_D_P' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(868): actual bit length 4 differs from formal bit length 1 for port 'Rx_LP_D_N' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(878): actual bit length 4 differs from formal bit length 1 for port 'Rx_HS_enable_D' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(879): actual bit length 4 differs from formal bit length 1 for port 'LVDS_termen_D' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(880): actual bit length 4 differs from formal bit length 1 for port 'fifo_rd_enable' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(881): actual bit length 4 differs from formal bit length 1 for port 'fifo_rd_empty' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(884): actual bit length 32 differs from formal bit length 1 for port 'u_dly_enable_D' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(909): actual bit length 8 differs from formal bit length 6 for port 'datatype' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(934): actual bit length 8 differs from formal bit length 6 for port 'axi_awaddr' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(943): actual bit length 32 differs from formal bit length 1 for port 'axi_bready' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(945): actual bit length 8 differs from formal bit length 6 for port 'axi_araddr' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(949): actual bit length 32 differs from formal bit length 1 for port 'axi_rready' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(965): expression size 3 truncated to fit in target size 2 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(966): expression size 7 truncated to fit in target size 6 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1019): expression size 12 truncated to fit in target size 11 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(102): port 'a_wr_rst_i' remains unconnected for this instance [VERI-1927]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(49): compiling module 'afifo_w32r8_reshape' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(302): compiling module 'efx_fifo_top_9f54396145b74975ae3dbe247cfd03f0(FAMILY="TITANIUM",MODE="FWFT",DEPTH=4096,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=4,PROG_FULL_NEGATE=4,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=0,ASYM_WIDTH_RATIO=2,ENDIANESS=1,OVERFLOW_PROTECT=0,UNDERFLOW_PROTECT=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(1381): compiling module 'efx_resetsync_9f54396145b74975ae3dbe247cfd03f0(ACTIVE_LOW=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(1426): compiling module 'efx_asyncreg_9f54396145b74975ae3dbe247cfd03f0(WIDTH=1,ACTIVE_LOW=0,RST_VALUE=1)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(622): compiling module 'efx_fifo_ram_9f54396145b74975ae3dbe247cfd03f0(FAMILY="TITANIUM",WR_DEPTH=4096,RD_DEPTH=16384,WDATA_WIDTH=32,WADDR_WIDTH=12,RADDR_WIDTH=14,OUTPUT_REG=0,ENDIANESS=1)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(659): extracting RAM for identifier 'ram' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(953): compiling module 'efx_fifo_ctl_9f54396145b74975ae3dbe247cfd03f0(SYNC_CLK=0,MODE="FWFT",WR_DEPTH=4096,WADDR_WIDTH=12,RADDR_WIDTH=14,ASYM_WIDTH_RATIO=2,RAM_MUX_RATIO=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=4,PROG_FULL_NEGATE=4,HANDSHAKE_FLAG=0)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(1241): expression size 32 truncated to fit in target size 13 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(1242): expression size 32 truncated to fit in target size 15 [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(213): compiling module 'efx_fifo_bin2gray_9f54396145b74975ae3dbe247cfd03f0(WIDTH=15)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(1426): compiling module 'efx_asyncreg_9f54396145b74975ae3dbe247cfd03f0(WIDTH=15,ACTIVE_LOW=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=15)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=14)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=13)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=12)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=11)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=9)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=8)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=7)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=6)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=4)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=3)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(125): compiling module 'efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=2)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(213): compiling module 'efx_fifo_bin2gray_9f54396145b74975ae3dbe247cfd03f0(WIDTH=13)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(1426): compiling module 'efx_asyncreg_9f54396145b74975ae3dbe247cfd03f0(WIDTH=13,ACTIVE_LOW=0)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v(88): input port 'a_wr_rst_i' is not connected on this instance [VDB-1013]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1042): actual bit length 11 differs from formal bit length 15 for port 'rd_datacount_o' [VERI-1330]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\common_uese\delay_reg.v(31): compiling module 'delay_reg(delay_level=1280)' [VERI-1018]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\Sensor_Image_XYCrop.v(32): compiling module 'Sensor_Image_XYCrop(IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=1024,IMAGE_YSIZE_TARGET=600,PIXEL_DATA_WIDTH=8)' [VERI-1018]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1132): expression size 64 truncated to fit in target size 8 [VERI-1209]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\VIP_RAW8_RGB888.v(31): compiling module 'VIP_RAW8_RGB888(IMG_HDISP=1024,IMG_VDISP=600)' [VERI-1018]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\VIP_Matrix_Generate_3X3_8Bit.v(35): compiling module 'VIP_Matrix_Generate_3X3_8Bit(IMG_HDISP=14'b010000000000,IMG_VDISP=14'b01001011000)' [VERI-1018]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\Line_Shift_RAM_8Bit.v(2): compiling module 'Line_Shift_RAM_8Bit(DATA_DEPTH=14'b010000000000)' [VERI-1018]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\Line_Shift_RAM_8Bit.v(64): extracting RAM for identifier 'r_ram' [VERI-2571]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\Line_Shift_RAM_8Bit.v(2): compiling module 'Line_Shift_RAM_8Bit(DATA_DEPTH=14'b010000000000,DELAY_NUM=1)' [VERI-1018]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\Line_Shift_RAM_8Bit.v(64): extracting RAM for identifier 'r_ram' [VERI-2571]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v(2): compiling module 'axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=2457600,C_W_WIDTH=32,C_R_WIDTH=32)' [VERI-1018]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v(91): expression size 32 truncated to fit in target size 16 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v(113): expression size 3 truncated to fit in target size 2 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v(114): expression size 3 truncated to fit in target size 2 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v(196): expression size 3 truncated to fit in target size 2 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v(238): expression size 2 truncated to fit in target size 1 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v(257): expression size 23 truncated to fit in target size 22 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(99): port 'a_wr_rst_i' remains unconnected for this instance [VERI-1927]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(49): compiling module 'W0_FIFO_32' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(380): compiling module 'efx_fifo_top_9133b310c90946e99246ad457ba54d5f(SYNC_STAGE=3,MODE="FWFT",DEPTH=2048,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=1536,PROG_FULL_NEGATE=1536,PROGRAMMABLE_EMPTY="STATIC_SINGLE",PROG_EMPTY_ASSERT=127,PROG_EMPTY_NEGATE=127,ASYM_WIDTH_RATIO=6)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(672): compiling module 'efx_fifo_ram_9133b310c90946e99246ad457ba54d5f(MODE="FWFT",WR_DEPTH=2048,WDATA_WIDTH=32,RDATA_WIDTH=128,WADDR_WIDTH=11,OUTPUT_REG=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(709): extracting RAM for identifier 'ram' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(990): compiling module 'efx_fifo_ctl_9133b310c90946e99246ad457ba54d5f(SYNC_CLK=0,SYNC_STAGE=3,MODE="FWFT",WR_DEPTH=2048,WADDR_WIDTH=11,ASYM_WIDTH_RATIO=6,RAM_MUX_RATIO=4,ALMOST_FLAG=0,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=1536,PROG_FULL_NEGATE=1536,PROGRAMMABLE_EMPTY="STATIC_SINGLE",PROG_EMPTY_ASSERT=127,PROG_EMPTY_NEGATE=127,HANDSHAKE_FLAG=0)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(1266): expression size 32 truncated to fit in target size 12 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(1267): expression size 32 truncated to fit in target size 10 [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(291): compiling module 'efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(120): compiling module 'efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=9)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=8)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=7)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=6)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=4)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=3)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=2)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(291): compiling module 'efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=12)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(120): compiling module 'efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=12)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=12)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(203): compiling module 'efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=11)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(94): actual bit length 11 differs from formal bit length 12 for port 'wr_datacount_o' [VERI-1330]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(97): actual bit length 9 differs from formal bit length 10 for port 'rd_datacount_o' [VERI-1330]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v(84): input port 'a_wr_rst_i' is not connected on this instance [VDB-1013]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(116): port 'a_wr_rst_i' remains unconnected for this instance [VERI-1927]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(49): compiling module 'R0_FIFO_32' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(316): compiling module 'efx_fifo_top_acec3874261e4a788712c2a22eb49f4c(FAMILY="TITANIUM",SYNC_STAGE=3,MODE="FWFT",DATA_WIDTH=128,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=256,PROG_FULL_NEGATE=256,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=2,PROG_EMPTY_NEGATE=3,ASYM_WIDTH_RATIO=2)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(1395): compiling module 'efx_resetsync_acec3874261e4a788712c2a22eb49f4c(ACTIVE_LOW=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(1440): compiling module 'efx_asyncreg_acec3874261e4a788712c2a22eb49f4c(WIDTH=1,ACTIVE_LOW=0,RST_VALUE=1)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(636): compiling module 'efx_fifo_ram_acec3874261e4a788712c2a22eb49f4c(FAMILY="TITANIUM",RD_DEPTH=2048,WDATA_WIDTH=128,RDATA_WIDTH=32,RADDR_WIDTH=11,OUTPUT_REG=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(673): extracting RAM for identifier 'ram' [VERI-2571]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(967): compiling module 'efx_fifo_ctl_acec3874261e4a788712c2a22eb49f4c(SYNC_CLK=0,SYNC_STAGE=3,MODE="FWFT",RADDR_WIDTH=11,ASYM_WIDTH_RATIO=2,RAM_MUX_RATIO=4,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=256,PROG_FULL_NEGATE=256,PROG_EMPTY_ASSERT=2,PROG_EMPTY_NEGATE=3,OVERFLOW_PROTECT=1,UNDERFLOW_PROTECT=1)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(1255): expression size 32 truncated to fit in target size 10 [VERI-1209]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(1256): expression size 32 truncated to fit in target size 12 [VERI-1209]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(227): compiling module 'efx_fifo_bin2gray_acec3874261e4a788712c2a22eb49f4c(WIDTH=12)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(1440): compiling module 'efx_asyncreg_acec3874261e4a788712c2a22eb49f4c(ASYNC_STAGE=3,WIDTH=12,ACTIVE_LOW=0)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=12)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=11)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=9)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=8)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=7)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=6)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=4)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=3)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(139): compiling module 'efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=2)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(227): compiling module 'efx_fifo_bin2gray_acec3874261e4a788712c2a22eb49f4c(WIDTH=10)' [VERI-1018]

INFO     : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(1440): compiling module 'efx_asyncreg_acec3874261e4a788712c2a22eb49f4c(ASYNC_STAGE=3,WIDTH=10,ACTIVE_LOW=0)' [VERI-1018]

WARNING  : D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v(95): input port 'a_wr_rst_i' is not connected on this instance [VDB-1013]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v(464): expression size 17 truncated to fit in target size 16 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\axi\axi4_ctrl.v(574): expression size 23 truncated to fit in target size 22 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1222): actual bit length 32 differs from formal bit length 4 for port 'axi_bid' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1223): actual bit length 32 differs from formal bit length 2 for port 'axi_bresp' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1224): actual bit length 32 differs from formal bit length 1 for port 'axi_bvalid' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1234): actual bit length 32 differs from formal bit length 2 for port 'axi_rresp' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1249): actual bit length 8 differs from formal bit length 32 for port 'tp_o' [VERI-1330]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(1251): expression size 8 truncated to fit in target size 4 [VERI-1209]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_driver.v(39): compiling module 'lcd_driver' [VERI-1018]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\lcd_driver.v(145): expression size 14 truncated to fit in target size 12 [VERI-1209]

INFO     : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\BoundCrop\FrameBoundCrop.v(6): compiling module 'FrameBoundCrop(SKIP_ROWS=2,SKIP_COLS=2,TOTAL_ROWS=600,TOTAL_COLS=1024)' [VERI-1018]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\BoundCrop\FrameBoundCrop.v(44): expression size 3 truncated to fit in target size 2 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\BoundCrop\FrameBoundCrop.v(45): expression size 3 truncated to fit in target size 2 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\BoundCrop\FrameBoundCrop.v(46): expression size 3 truncated to fit in target size 2 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\BoundCrop\FrameBoundCrop.v(53): expression size 13 truncated to fit in target size 12 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\BoundCrop\FrameBoundCrop.v(61): expression size 13 truncated to fit in target size 12 [VERI-1209]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(414): net 'w_dev_index_o[7]' does not have a driver [VDB-1002]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(415): net 'w_dev_cmd_o[7]' does not have a driver [VDB-1002]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(416): net 'w_dev_wdata_o[31]' does not have a driver [VDB-1002]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(417): net 'w_dev_wvalid_o' does not have a driver [VDB-1002]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(421): net 'w_spi_ssn_o' does not have a driver [VDB-1002]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(422): net 'w_spi_data_o[3]' does not have a driver [VDB-1002]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(439): net 'w_lcd_b_o[7]' does not have a driver [VDB-1002]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(674): net 'config_data' does not have a driver [VDB-1002]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(675): net 'config_data_valid' does not have a driver [VDB-1002]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(853): input port 'Rx_HS_D_4[7]' remains unconnected for this instance [VDB-1053]

INFO     : Elaboration took 0.20963 seconds.

INFO     : 	Elaboration took 0.1875 seconds (approximately) in total CPU time.

INFO     : Elaboration virtual memory usage: begin = 66.844 MB, end = 121.264 MB, delta = 54.42 MB

INFO     : 	Elaboration peak virtual memory usage = 121.264 MB

INFO     : Elaboration resident set memory usage: begin = 72.084 MB, end = 127.908 MB, delta = 55.824 MB

INFO     : 	Elaboration peak resident set memory usage = 127.912 MB

INFO     : ***** Ending Elaboration ... *****

INFO     : ... Setting Synthesis Option: mode=speed

INFO     : ***** Beginning Reading Mapping Library ... *****

INFO     : Analyzing Verilog file 'C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v' [VERI-1482]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(8): compiling module 'EFX_ADD' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(21): compiling module 'EFX_FF' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(38): compiling module 'EFX_COMB4' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(48): compiling module 'EFX_GBUFCE' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(57): compiling module 'EFX_LUT4' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(65): compiling module 'EFX_MULT' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(100): compiling module 'EFX_DSP48' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(156): compiling module 'EFX_DSP24' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(209): compiling module 'EFX_DSP12' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(262): compiling module 'EFX_RAM_4K' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(322): compiling module 'EFX_RAM_5K' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(394): compiling module 'EFX_DPRAM_5K' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(499): compiling module 'RAMB5' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(561): compiling module 'EFX_RAM_10K' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(653): compiling module 'EFX_RAM10' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(754): compiling module 'EFX_DPRAM10' [VERI-1018]

INFO     : C:/Efinity/2025.1/sim_models/maplib/efinix_maplib.v(884): compiling module 'EFX_SRL8' [VERI-1018]

INFO     : Reading Mapping Library took 0.0143297 seconds.

INFO     : 	Reading Mapping Library took 0.015625 seconds (approximately) in total CPU time.

INFO     : Reading Mapping Library virtual memory usage: begin = 160.764 MB, end = 160.764 MB, delta = 0 MB

INFO     : 	Reading Mapping Library peak virtual memory usage = 160.764 MB

INFO     : Reading Mapping Library resident set memory usage: begin = 167.176 MB, end = 167.18 MB, delta = 0.004 MB

INFO     : 	Reading Mapping Library peak resident set memory usage = 167.184 MB

INFO     : ***** Ending Reading Mapping Library ... *****

INFO     : ... Pre-synthesis checks begin

WARNING  : The primary output port 'csi_ctl0_o' wire 'csi_ctl0_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'csi_ctl1_o' wire 'csi_ctl1_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_rst_o' wire 'dsi_txc_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_lp_p_oe' wire 'dsi_txc_lp_p_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_lp_p_o' wire 'dsi_txc_lp_p_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_lp_n_oe' wire 'dsi_txc_lp_n_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_lp_n_o' wire 'dsi_txc_lp_n_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_hs_oe' wire 'dsi_txc_hs_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_hs_o[7]' wire 'dsi_txc_hs_o[7]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_hs_o[6]' wire 'dsi_txc_hs_o[6]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_hs_o[5]' wire 'dsi_txc_hs_o[5]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_hs_o[4]' wire 'dsi_txc_hs_o[4]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_hs_o[3]' wire 'dsi_txc_hs_o[3]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_hs_o[2]' wire 'dsi_txc_hs_o[2]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_hs_o[1]' wire 'dsi_txc_hs_o[1]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txc_hs_o[0]' wire 'dsi_txc_hs_o[0]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_rst_o' wire 'dsi_txd0_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_hs_oe' wire 'dsi_txd0_hs_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_hs_o[7]' wire 'dsi_txd0_hs_o[7]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_hs_o[6]' wire 'dsi_txd0_hs_o[6]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_hs_o[5]' wire 'dsi_txd0_hs_o[5]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_hs_o[4]' wire 'dsi_txd0_hs_o[4]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_hs_o[3]' wire 'dsi_txd0_hs_o[3]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_hs_o[2]' wire 'dsi_txd0_hs_o[2]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_hs_o[1]' wire 'dsi_txd0_hs_o[1]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_hs_o[0]' wire 'dsi_txd0_hs_o[0]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_lp_p_oe' wire 'dsi_txd0_lp_p_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_lp_p_o' wire 'dsi_txd0_lp_p_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_lp_n_oe' wire 'dsi_txd0_lp_n_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd0_lp_n_o' wire 'dsi_txd0_lp_n_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_rst_o' wire 'dsi_txd1_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_lp_p_oe' wire 'dsi_txd1_lp_p_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_lp_p_o' wire 'dsi_txd1_lp_p_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_lp_n_oe' wire 'dsi_txd1_lp_n_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_lp_n_o' wire 'dsi_txd1_lp_n_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_hs_oe' wire 'dsi_txd1_hs_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_hs_o[7]' wire 'dsi_txd1_hs_o[7]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_hs_o[6]' wire 'dsi_txd1_hs_o[6]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_hs_o[5]' wire 'dsi_txd1_hs_o[5]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_hs_o[4]' wire 'dsi_txd1_hs_o[4]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_hs_o[3]' wire 'dsi_txd1_hs_o[3]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_hs_o[2]' wire 'dsi_txd1_hs_o[2]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_hs_o[1]' wire 'dsi_txd1_hs_o[1]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd1_hs_o[0]' wire 'dsi_txd1_hs_o[0]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_rst_o' wire 'dsi_txd2_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_lp_p_oe' wire 'dsi_txd2_lp_p_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_lp_p_o' wire 'dsi_txd2_lp_p_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_lp_n_oe' wire 'dsi_txd2_lp_n_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_lp_n_o' wire 'dsi_txd2_lp_n_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_hs_oe' wire 'dsi_txd2_hs_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_hs_o[7]' wire 'dsi_txd2_hs_o[7]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_hs_o[6]' wire 'dsi_txd2_hs_o[6]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_hs_o[5]' wire 'dsi_txd2_hs_o[5]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_hs_o[4]' wire 'dsi_txd2_hs_o[4]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_hs_o[3]' wire 'dsi_txd2_hs_o[3]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_hs_o[2]' wire 'dsi_txd2_hs_o[2]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_hs_o[1]' wire 'dsi_txd2_hs_o[1]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd2_hs_o[0]' wire 'dsi_txd2_hs_o[0]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_rst_o' wire 'dsi_txd3_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_lp_p_oe' wire 'dsi_txd3_lp_p_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_lp_p_o' wire 'dsi_txd3_lp_p_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_lp_n_oe' wire 'dsi_txd3_lp_n_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_lp_n_o' wire 'dsi_txd3_lp_n_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_hs_oe' wire 'dsi_txd3_hs_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_hs_o[7]' wire 'dsi_txd3_hs_o[7]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_hs_o[6]' wire 'dsi_txd3_hs_o[6]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_hs_o[5]' wire 'dsi_txd3_hs_o[5]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_hs_o[4]' wire 'dsi_txd3_hs_o[4]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_hs_o[3]' wire 'dsi_txd3_hs_o[3]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_hs_o[2]' wire 'dsi_txd3_hs_o[2]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_hs_o[1]' wire 'dsi_txd3_hs_o[1]' is not driven. [EFX-0256]

WARNING  : The primary output port 'dsi_txd3_hs_o[0]' wire 'dsi_txd3_hs_o[0]' is not driven. [EFX-0256]

WARNING  : The primary output port 'uart_tx_o' wire 'uart_tx_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'cmos_sclk' wire 'cmos_sclk' is not driven. [EFX-0256]

WARNING  : The primary output port 'cmos_sdat_OUT' wire 'cmos_sdat_OUT' is not driven. [EFX-0256]

WARNING  : The primary output port 'cmos_sdat_OE' wire 'cmos_sdat_OE' is not driven. [EFX-0256]

WARNING  : The primary output port 'cmos_ctl2' wire 'cmos_ctl2' is not driven. [EFX-0256]

WARNING  : The primary output port 'cmos_ctl3' wire 'cmos_ctl3' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_oe' wire 'hdmi_txc_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_oe' wire 'hdmi_txd0_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_oe' wire 'hdmi_txd1_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_oe' wire 'hdmi_txd2_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_rst_o' wire 'hdmi_txc_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_rst_o' wire 'hdmi_txd0_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_rst_o' wire 'hdmi_txd1_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_rst_o' wire 'hdmi_txd2_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[9]' wire 'hdmi_txc_o[9]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[8]' wire 'hdmi_txc_o[8]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[7]' wire 'hdmi_txc_o[7]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[6]' wire 'hdmi_txc_o[6]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[5]' wire 'hdmi_txc_o[5]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[4]' wire 'hdmi_txc_o[4]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[3]' wire 'hdmi_txc_o[3]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[2]' wire 'hdmi_txc_o[2]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[1]' wire 'hdmi_txc_o[1]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txc_o[0]' wire 'hdmi_txc_o[0]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[9]' wire 'hdmi_txd0_o[9]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[8]' wire 'hdmi_txd0_o[8]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[7]' wire 'hdmi_txd0_o[7]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[6]' wire 'hdmi_txd0_o[6]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[5]' wire 'hdmi_txd0_o[5]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[4]' wire 'hdmi_txd0_o[4]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[3]' wire 'hdmi_txd0_o[3]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[2]' wire 'hdmi_txd0_o[2]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[1]' wire 'hdmi_txd0_o[1]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd0_o[0]' wire 'hdmi_txd0_o[0]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[9]' wire 'hdmi_txd1_o[9]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[8]' wire 'hdmi_txd1_o[8]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[7]' wire 'hdmi_txd1_o[7]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[6]' wire 'hdmi_txd1_o[6]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[5]' wire 'hdmi_txd1_o[5]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[4]' wire 'hdmi_txd1_o[4]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[3]' wire 'hdmi_txd1_o[3]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[2]' wire 'hdmi_txd1_o[2]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[1]' wire 'hdmi_txd1_o[1]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd1_o[0]' wire 'hdmi_txd1_o[0]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[9]' wire 'hdmi_txd2_o[9]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[8]' wire 'hdmi_txd2_o[8]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[7]' wire 'hdmi_txd2_o[7]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[6]' wire 'hdmi_txd2_o[6]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[5]' wire 'hdmi_txd2_o[5]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[4]' wire 'hdmi_txd2_o[4]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[3]' wire 'hdmi_txd2_o[3]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[2]' wire 'hdmi_txd2_o[2]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[1]' wire 'hdmi_txd2_o[1]' is not driven. [EFX-0256]

WARNING  : The primary output port 'hdmi_txd2_o[0]' wire 'hdmi_txd2_o[0]' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_tp_sda_o' wire 'lcd_tp_sda_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_tp_sda_oe' wire 'lcd_tp_sda_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_tp_scl_o' wire 'lcd_tp_scl_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_tp_scl_oe' wire 'lcd_tp_scl_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_tp_int_o' wire 'lcd_tp_int_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_tp_int_oe' wire 'lcd_tp_int_oe' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_tp_rst_o' wire 'lcd_tp_rst_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_pwm_o' wire 'lcd_pwm_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_blen_o' wire 'lcd_blen_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_vs_o' wire 'lcd_vs_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_hs_o' wire 'lcd_hs_o' is not driven. [EFX-0256]

WARNING  : The primary output port 'lcd_de_o' wire 'lcd_de_o' is not driven. [EFX-0256]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][7]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][6]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][5]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][4]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][3]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][2]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][1]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][0]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Out[7]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Out[6]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Out[5]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Out[4]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Out[3]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Out[2]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Out[1]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Out[0]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][31]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][30]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][29]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : Removing redundant signal : Shift_Q7_Out[4][28]. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:2569) [EFX-0200]

WARNING  : (The above message was generated too many times, subsequent similar messages will be muted.) [EFX-0200]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:1562) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:1562) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:1562) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:3223) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:1562) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:1562) [EFX-0677]

INFO     : Module Instance 'map_ram' input pin 'wclk' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[0]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[1]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[2]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[3]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[4]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[5]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[6]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[7]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[8]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[9]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[10]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[11]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[12]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[13]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[14]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[15]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[16]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[17]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[18]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[19]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[20]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[21]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[22]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[23]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[24]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[25]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[26]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[27]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[28]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[29]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[30]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[31]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[32]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[33]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[34]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[35]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[36]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[37]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[38]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[39]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[40]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[41]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[42]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[43]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[44]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[45]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[46]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[47]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[48]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[49]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[50]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[51]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[52]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[53]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[54]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[55]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[56]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[57]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[58]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[59]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[60]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[61]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[62]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[63]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[64]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[65]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[66]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[67]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[68]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[69]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[70]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'wdata[71]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'waddr[0]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'waddr[1]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'waddr[2]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'waddr[3]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'waddr[4]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'waddr[5]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'waddr[6]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'waddr[7]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'waddr[8]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 'we' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'map_ram' input pin 're' is tied to constant (=1). [EFX-0266]

INFO     : Module Instance 'fifo_rdpush' input pin 'io_push_payload[0]' is tied to constant (=1). [EFX-0266]

INFO     : Module Instance 'fifo_rdpush' input pin 'io_pop_ready' is tied to constant (=1). [EFX-0266]

WARNING  : Re-wiring to GND non-driven net 'fifo_rd.a_rst_i'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:568) [EFX-0201]

WARNING  : Re-wiring to GND non-driven net 'fifo_rd.clk_i'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:568) [EFX-0201]

INFO     : Module Instance 'fifo_ack' input pin 'io_pop_ready' is tied to constant (=1). [EFX-0266]

WARNING  : Re-wiring to GND non-driven net 'w_ar_fifo_rd_vaild'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:568) [EFX-0201]

WARNING  : Re-wiring to GND non-driven net 'w_aw_fifo_rd_vaild'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:568) [EFX-0201]

INFO     : Module Instance 'mux0' input pin 'b_rd_en' is tied to constant (=1). [EFX-0266]

INFO     : Module Instance 'phase_u0' input pin 'pll_sel[0]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'phase_u0' input pin 'pll_sel[1]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'phase_u0' input pin 'pll_sel[2]' is tied to constant (=1). [EFX-0266]

INFO     : Module Instance 'phase_u0' input pin 'pll_sel[3]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'phase_u0' input pin 'pll_sel[4]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'inst_arburst_fifo' input pin 'I_Wr_Data[2]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'inst_arburst_fifo' input pin 'I_Wr_Data[3]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'inst_arburst_fifo' input pin 'I_Wr_Data[4]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'inst_arburst_fifo' input pin 'I_Wr_Data[5]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'inst_arburst_fifo' input pin 'I_Wr_Data[6]' is tied to constant (=0). [EFX-0266]

INFO     : Module Instance 'inst_arburst_fifo' input pin 'I_Wr_Data[7]' is tied to constant (=0). [EFX-0266]

INFO     : (The above message was generated too many times, subsequent similar messages will be muted.) [EFX-0266]

INFO     : Module Instance 'u_wr_addr_fifo' input pin 'I_Wr_Data[42]' is tied to constant (=0). [EFX-0266]

WARNING  : Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[127]'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:116) [EFX-0201]

WARNING  : Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[126]'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:116) [EFX-0201]

WARNING  : Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[125]'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:116) [EFX-0201]

WARNING  : Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[124]'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:116) [EFX-0201]

WARNING  : Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[123]'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:116) [EFX-0201]

WARNING  : Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[122]'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/DdrCtrl\DdrCtrl.v:116) [EFX-0201]

WARNING  : (The above message was generated too many times, subsequent similar messages will be muted.) [EFX-0201]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v:659) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv:6919) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/csi_rx\csi_rx.sv:6919) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w32r8_reshape\afifo_w32r8_reshape.v:659) [EFX-0677]

INFO     : ... Memory 'r_ram' is not initialized, assuming empty initial contents. (D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\Line_Shift_RAM_8Bit.v:64) [EFX-0677]

INFO     : ... Memory 'r_ram' is not initialized, assuming empty initial contents. (D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\src\isp\Bayer2RGB\Line_Shift_RAM_8Bit.v:64) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/W0_FIFO_32\W0_FIFO_32.v:709) [EFX-0677]

INFO     : ... Memory 'ram' is not initialized, assuming empty initial contents. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/R0_FIFO_32\R0_FIFO_32.v:673) [EFX-0677]

INFO     : ... Pre-synthesis checks end (Real time : 0s)

INFO     : ... NameSpace init begin

INFO     : ... NameSpace init end (Real time : 0s)

INFO     : ... Mapping design "example_top"

INFO     : ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)" begin

INFO     : ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_DEPTH=32)" begin

INFO     : ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_DEPTH=32)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=48,DATA_DEPTH=32)" begin

INFO     : ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=48,DATA_DEPTH=32)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "native_mux_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "native_mux_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)" begin

INFO     : ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=4,CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" begin

INFO     : ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=4,CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" begin

INFO     : ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "buffercc_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "buffercc_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "buffercc_1_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "buffercc_1_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)" begin

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=144)" begin

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=144)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=1)" begin

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33(SYNC_CLK=0,PROGRAMMABLE_FULL="STATIC_DUAL",PROG_FULL_ASSERT=27,PROG_FULL_NEGATE=23,PROGRAMMABLE_EMPTY="STATIC_DUAL",PROG_EMPTY_ASSERT=5,PROG_EMPTY_NEGATE=7)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33(SYNC_CLK=0,PROGRAMMABLE_FULL="STATIC_DUAL",PROG_FULL_ASSERT=27,PROG_FULL_NEGATE=23,PROGRAMMABLE_EMPTY="STATIC_DUAL",PROG_EMPTY_ASSERT=5,PROG_EMPTY_NEGATE=7)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=2)" begin

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=52)" begin

INFO     : ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=52)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "user_dual_port_ram_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=72,OUTPUT_REG="FALSE",RAM_INIT_FILE="ddr3_controller.bin")" begin

INFO     : ... Hierarchical pre-synthesis "user_dual_port_ram_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=72,OUTPUT_REG="FALSE",RAM_INIT_FILE="ddr3_controller.bin")" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "ddr_3_controller_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "ddr_3_controller_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "controller_top_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "controller_top_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)" begin

INFO     : ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=2,CYCLE=5,IN_RST_ACTIVE=2'b0,OUT_RST_ACTIVE=2'b0)" begin

INFO     : ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=2,CYCLE=5,IN_RST_ACTIVE=2'b0,OUT_RST_ACTIVE=2'b0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "phase_shift_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "phase_shift_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "top_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "top_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_ddr3_axi_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "efx_ddr3_axi_5ee7df9aa375494db25875c4929bcb33" end (Real time : 1s)

INFO     : ... Hierarchical pre-synthesis "efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33" begin

INFO     : ... Hierarchical pre-synthesis "efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "DdrCtrl" begin

INFO     : ... Hierarchical pre-synthesis "DdrCtrl" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "AXI4_AWARMux(AID_LEN=4)" begin

INFO     : ... Hierarchical pre-synthesis "AXI4_AWARMux(AID_LEN=4)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=1,ACTIVE_LOW=0,RST_VALUE=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=1,ACTIVE_LOW=0,RST_VALUE=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_resetsync_97de8c1d535c46caaff94d8f5a5d7caa(ACTIVE_LOW=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_resetsync_97de8c1d535c46caaff94d8f5a5d7caa(ACTIVE_LOW=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_97de8c1d535c46caaff94d8f5a5d7caa(FAMILY="TITANIUM",WR_DEPTH=16,RD_DEPTH=16,WDATA_WIDTH=24,RDATA_WIDTH=24,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_97de8c1d535c46caaff94d8f5a5d7caa(FAMILY="TITANIUM",WR_DEPTH=16,RD_DEPTH=16,WDATA_WIDTH=24,RDATA_WIDTH=24,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_97de8c1d535c46caaff94d8f5a5d7caa" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_97de8c1d535c46caaff94d8f5a5d7caa" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=5,ACTIVE_LOW=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=5,ACTIVE_LOW=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=2)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=3)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=3)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=4)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa(WIDTH=4)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_97de8c1d535c46caaff94d8f5a5d7caa" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_97de8c1d535c46caaff94d8f5a5d7caa(SYNC_CLK=0,MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=13,PROG_FULL_NEGATE=13,HANDSHAKE_FLAG=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_97de8c1d535c46caaff94d8f5a5d7caa(SYNC_CLK=0,MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=13,PROG_FULL_NEGATE=13,HANDSHAKE_FLAG=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_97de8c1d535c46caaff94d8f5a5d7caa(FAMILY="TITANIUM",MODE="FWFT",DEPTH=16,DATA_WIDTH=24,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=13,PROG_FULL_NEGATE=13,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=0,OVERFLOW_PROTECT=0,UNDERFLOW_PROTECT=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_97de8c1d535c46caaff94d8f5a5d7caa(FAMILY="TITANIUM",MODE="FWFT",DEPTH=16,DATA_WIDTH=24,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=13,PROG_FULL_NEGATE=13,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=0,OVERFLOW_PROTECT=0,UNDERFLOW_PROTECT=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "afifo_w24d16_r24d16" begin

INFO     : ... Hierarchical pre-synthesis "afifo_w24d16_r24d16" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)" begin

INFO     : ... Hierarchical pre-synthesis "i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "I2C_AD2020_1280960_FPS60_1Lane_Config" begin

INFO     : ... Hierarchical pre-synthesis "I2C_AD2020_1280960_FPS60_1Lane_Config" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "PWMLite" begin

INFO     : ... Hierarchical pre-synthesis "PWMLite" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "dphy_rx_clane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" begin

INFO     : ... Hierarchical pre-synthesis "dphy_rx_clane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "dphy_rx_dlane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" begin

INFO     : ... Hierarchical pre-synthesis "dphy_rx_dlane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "dphy_rx_dword_align_e7fb069599c849bca10d5e09e6d98480" begin

INFO     : ... Hierarchical pre-synthesis "dphy_rx_dword_align_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "dphy_rx_deskew_cal_e7fb069599c849bca10d5e09e6d98480" begin

INFO     : ... Hierarchical pre-synthesis "dphy_rx_deskew_cal_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")" begin

INFO     : ... Hierarchical pre-synthesis "efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_rx_csr_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_rx_csr_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_ecc_check_e7fb069599c849bca10d5e09e6d98480" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_ecc_check_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_crc16_d8_gen_e7fb069599c849bca10d5e09e6d98480" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_crc16_d8_gen_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depacketizer_e7fb069599c849bca10d5e09e6d98480(ENABLE_VCX=1'b0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depacketizer_e7fb069599c849bca10d5e09e6d98480(ENABLE_VCX=1'b0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480(SYNC_CLK=1,MODE="FWFT",DEPTH=16,DATA_WIDTH=8,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480(SYNC_CLK=1,MODE="FWFT",DEPTH=16,DATA_WIDTH=8,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_lane_aligner_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_lane_aligner_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depack40bit_e7fb069599c849bca10d5e09e6d98480" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depack40bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depack48bit_e7fb069599c849bca10d5e09e6d98480" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depack48bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depack56bit_e7fb069599c849bca10d5e09e6d98480" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depack56bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depack64bit_e7fb069599c849bca10d5e09e6d98480" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_depack64bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(SYNC_CLK=0,MODE="FWFT",WR_DEPTH=1024,WADDR_WIDTH=10,RADDR_WIDTH=10,ALMOST_FLAG=0,PROG_FULL_ASSERT=1024,PROG_FULL_NEGATE=1024,PROGRAMMABLE_EMPTY="STATIC_DUAL",PROG_EMPTY_NEGATE=2,HANDSHAKE_FLAG=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(SYNC_CLK=0,MODE="FWFT",WR_DEPTH=1024,WADDR_WIDTH=10,RADDR_WIDTH=10,ALMOST_FLAG=0,PROG_FULL_ASSERT=1024,PROG_FULL_NEGATE=1024,PROGRAMMABLE_EMPTY="STATIC_DUAL",PROG_EMPTY_NEGATE=2,HANDSHAKE_FLAG=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",DEPTH=1024,DATA_WIDTH=64,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=1024,PROG_FULL_NEGATE=1024,PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=2)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",DEPTH=1024,DATA_WIDTH=64,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=1024,PROG_FULL_NEGATE=1024,PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_byte2pixel_e7fb069599c849bca10d5e09e6d98480(PIXEL_FIFO_DEPTH=1024,ENABLE_VCX=1'b0,PACK_TYPE=15)" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_byte2pixel_e7fb069599c849bca10d5e09e6d98480(PIXEL_FIFO_DEPTH=1024,ENABLE_VCX=1'b0,PACK_TYPE=15)" end (Real time : 1s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_rx_top_e7fb069599c849bca10d5e09e6d98480(HS_DATA_WIDTH=8,tINIT_NS=1000,CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,PACK_TYPE=15,ENABLE_VCX=1'b0,PIXEL_FIFO_DEPTH=1024)" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_rx_top_e7fb069599c849bca10d5e09e6d98480(HS_DATA_WIDTH=8,tINIT_NS=1000,CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,PACK_TYPE=15,ENABLE_VCX=1'b0,PIXEL_FIFO_DEPTH=1024)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480(tINIT_NS=1000,NUM_DATA_LANE=1,HS_BYTECLK_MHZ=187,CLOCK_FREQ_MHZ=50,DPHY_CLOCK_MODE="Discontinuous",PIXEL_FIFO_DEPTH=1024,ENABLE_USER_DESKEWCAL=1'b0,ENABLE_VCX=1'b0,PACK_TYPE=15)" begin

INFO     : ... Hierarchical pre-synthesis "efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480(tINIT_NS=1000,NUM_DATA_LANE=1,HS_BYTECLK_MHZ=187,CLOCK_FREQ_MHZ=50,DPHY_CLOCK_MODE="Discontinuous",PIXEL_FIFO_DEPTH=1024,ENABLE_USER_DESKEWCAL=1'b0,ENABLE_VCX=1'b0,PACK_TYPE=15)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "csi_rx" begin

INFO     : ... Hierarchical pre-synthesis "csi_rx" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_9f54396145b74975ae3dbe247cfd03f0(WIDTH=1,ACTIVE_LOW=0,RST_VALUE=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_9f54396145b74975ae3dbe247cfd03f0(WIDTH=1,ACTIVE_LOW=0,RST_VALUE=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_resetsync_9f54396145b74975ae3dbe247cfd03f0(ACTIVE_LOW=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_resetsync_9f54396145b74975ae3dbe247cfd03f0(ACTIVE_LOW=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_9f54396145b74975ae3dbe247cfd03f0(FAMILY="TITANIUM",WR_DEPTH=4096,RD_DEPTH=16384,WDATA_WIDTH=32,WADDR_WIDTH=12,RADDR_WIDTH=14,OUTPUT_REG=0,ENDIANESS=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_9f54396145b74975ae3dbe247cfd03f0(FAMILY="TITANIUM",WR_DEPTH=4096,RD_DEPTH=16384,WDATA_WIDTH=32,WADDR_WIDTH=12,RADDR_WIDTH=14,OUTPUT_REG=0,ENDIANESS=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9f54396145b74975ae3dbe247cfd03f0(WIDTH=15)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9f54396145b74975ae3dbe247cfd03f0(WIDTH=15)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_9f54396145b74975ae3dbe247cfd03f0(WIDTH=15,ACTIVE_LOW=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_9f54396145b74975ae3dbe247cfd03f0(WIDTH=15,ACTIVE_LOW=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=2)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=3)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=3)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=4)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=4)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=6)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=6)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=7)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=7)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=8)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=8)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=9)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=9)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=11)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=11)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=12)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=12)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=13)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=13)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=14)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=14)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=15)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9f54396145b74975ae3dbe247cfd03f0(WIDTH=15)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9f54396145b74975ae3dbe247cfd03f0(WIDTH=13)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9f54396145b74975ae3dbe247cfd03f0(WIDTH=13)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_9f54396145b74975ae3dbe247cfd03f0(WIDTH=13,ACTIVE_LOW=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_9f54396145b74975ae3dbe247cfd03f0(WIDTH=13,ACTIVE_LOW=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_9f54396145b74975ae3dbe247cfd03f0(SYNC_CLK=0,MODE="FWFT",WR_DEPTH=4096,WADDR_WIDTH=12,RADDR_WIDTH=14,ASYM_WIDTH_RATIO=2,RAM_MUX_RATIO=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=4,PROG_FULL_NEGATE=4,HANDSHAKE_FLAG=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_9f54396145b74975ae3dbe247cfd03f0(SYNC_CLK=0,MODE="FWFT",WR_DEPTH=4096,WADDR_WIDTH=12,RADDR_WIDTH=14,ASYM_WIDTH_RATIO=2,RAM_MUX_RATIO=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=4,PROG_FULL_NEGATE=4,HANDSHAKE_FLAG=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_9f54396145b74975ae3dbe247cfd03f0(FAMILY="TITANIUM",MODE="FWFT",DEPTH=4096,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=4,PROG_FULL_NEGATE=4,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=0,ASYM_WIDTH_RATIO=2,ENDIANESS=1,OVERFLOW_PROTECT=0,UNDERFLOW_PROTECT=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_9f54396145b74975ae3dbe247cfd03f0(FAMILY="TITANIUM",MODE="FWFT",DEPTH=4096,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="NONE",PROG_FULL_ASSERT=4,PROG_FULL_NEGATE=4,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=0,PROG_EMPTY_NEGATE=0,ASYM_WIDTH_RATIO=2,ENDIANESS=1,OVERFLOW_PROTECT=0,UNDERFLOW_PROTECT=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "afifo_w32r8_reshape" begin

INFO     : ... Hierarchical pre-synthesis "afifo_w32r8_reshape" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "delay_reg(delay_level=1280)" begin

INFO     : ... Hierarchical pre-synthesis "delay_reg(delay_level=1280)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "Sensor_Image_XYCrop(IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=1024,IMAGE_YSIZE_TARGET=600,PIXEL_DATA_WIDTH=8)" begin

INFO     : ... Hierarchical pre-synthesis "Sensor_Image_XYCrop(IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=1024,IMAGE_YSIZE_TARGET=600,PIXEL_DATA_WIDTH=8)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "Line_Shift_RAM_8Bit(DATA_DEPTH=14'b010000000000)" begin

INFO     : ... Hierarchical pre-synthesis "Line_Shift_RAM_8Bit(DATA_DEPTH=14'b010000000000)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "Line_Shift_RAM_8Bit(DATA_DEPTH=14'b010000000000,DELAY_NUM=1)" begin

INFO     : ... Hierarchical pre-synthesis "Line_Shift_RAM_8Bit(DATA_DEPTH=14'b010000000000,DELAY_NUM=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "VIP_Matrix_Generate_3X3_8Bit(IMG_HDISP=14'b010000000000,IMG_VDISP=14'b01001011000)" begin

INFO     : ... Hierarchical pre-synthesis "VIP_Matrix_Generate_3X3_8Bit(IMG_HDISP=14'b010000000000,IMG_VDISP=14'b01001011000)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "VIP_RAW8_RGB888(IMG_HDISP=1024,IMG_VDISP=600)" begin

INFO     : ... Hierarchical pre-synthesis "VIP_RAW8_RGB888(IMG_HDISP=1024,IMG_VDISP=600)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_9133b310c90946e99246ad457ba54d5f(MODE="FWFT",WR_DEPTH=2048,WDATA_WIDTH=32,RDATA_WIDTH=128,WADDR_WIDTH=11,OUTPUT_REG=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_9133b310c90946e99246ad457ba54d5f(MODE="FWFT",WR_DEPTH=2048,WDATA_WIDTH=32,RDATA_WIDTH=128,WADDR_WIDTH=11,OUTPUT_REG=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=2)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=3)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=3)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=4)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=4)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=6)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=6)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=7)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=7)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=8)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=8)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=9)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=9)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=12)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=12)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=12)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=12)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=11)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=11)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=12)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=12)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_9133b310c90946e99246ad457ba54d5f(SYNC_CLK=0,SYNC_STAGE=3,MODE="FWFT",WR_DEPTH=2048,WADDR_WIDTH=11,ASYM_WIDTH_RATIO=6,RAM_MUX_RATIO=4,ALMOST_FLAG=0,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=1536,PROG_FULL_NEGATE=1536,PROGRAMMABLE_EMPTY="STATIC_SINGLE",PROG_EMPTY_ASSERT=127,PROG_EMPTY_NEGATE=127,HANDSHAKE_FLAG=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_9133b310c90946e99246ad457ba54d5f(SYNC_CLK=0,SYNC_STAGE=3,MODE="FWFT",WR_DEPTH=2048,WADDR_WIDTH=11,ASYM_WIDTH_RATIO=6,RAM_MUX_RATIO=4,ALMOST_FLAG=0,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=1536,PROG_FULL_NEGATE=1536,PROGRAMMABLE_EMPTY="STATIC_SINGLE",PROG_EMPTY_ASSERT=127,PROG_EMPTY_NEGATE=127,HANDSHAKE_FLAG=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_9133b310c90946e99246ad457ba54d5f(SYNC_STAGE=3,MODE="FWFT",DEPTH=2048,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=1536,PROG_FULL_NEGATE=1536,PROGRAMMABLE_EMPTY="STATIC_SINGLE",PROG_EMPTY_ASSERT=127,PROG_EMPTY_NEGATE=127,ASYM_WIDTH_RATIO=6)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_9133b310c90946e99246ad457ba54d5f(SYNC_STAGE=3,MODE="FWFT",DEPTH=2048,OPTIONAL_FLAGS=0,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=1536,PROG_FULL_NEGATE=1536,PROGRAMMABLE_EMPTY="STATIC_SINGLE",PROG_EMPTY_ASSERT=127,PROG_EMPTY_NEGATE=127,ASYM_WIDTH_RATIO=6)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "W0_FIFO_32" begin

INFO     : ... Hierarchical pre-synthesis "W0_FIFO_32" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_acec3874261e4a788712c2a22eb49f4c(WIDTH=1,ACTIVE_LOW=0,RST_VALUE=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_acec3874261e4a788712c2a22eb49f4c(WIDTH=1,ACTIVE_LOW=0,RST_VALUE=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_resetsync_acec3874261e4a788712c2a22eb49f4c(ACTIVE_LOW=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_resetsync_acec3874261e4a788712c2a22eb49f4c(ACTIVE_LOW=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_acec3874261e4a788712c2a22eb49f4c(FAMILY="TITANIUM",RD_DEPTH=2048,WDATA_WIDTH=128,RDATA_WIDTH=32,RADDR_WIDTH=11,OUTPUT_REG=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ram_acec3874261e4a788712c2a22eb49f4c(FAMILY="TITANIUM",RD_DEPTH=2048,WDATA_WIDTH=128,RDATA_WIDTH=32,RADDR_WIDTH=11,OUTPUT_REG=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_acec3874261e4a788712c2a22eb49f4c(WIDTH=12)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_acec3874261e4a788712c2a22eb49f4c(WIDTH=12)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_acec3874261e4a788712c2a22eb49f4c(ASYNC_STAGE=3,WIDTH=12,ACTIVE_LOW=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_acec3874261e4a788712c2a22eb49f4c(ASYNC_STAGE=3,WIDTH=12,ACTIVE_LOW=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=2)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=3)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=3)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=4)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=4)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=6)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=6)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=7)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=7)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=8)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=8)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=9)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=9)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=11)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=11)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=12)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_gray2bin_acec3874261e4a788712c2a22eb49f4c(WIDTH=12)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_acec3874261e4a788712c2a22eb49f4c(WIDTH=10)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_bin2gray_acec3874261e4a788712c2a22eb49f4c(WIDTH=10)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_acec3874261e4a788712c2a22eb49f4c(ASYNC_STAGE=3,WIDTH=10,ACTIVE_LOW=0)" begin

INFO     : ... Hierarchical pre-synthesis "efx_asyncreg_acec3874261e4a788712c2a22eb49f4c(ASYNC_STAGE=3,WIDTH=10,ACTIVE_LOW=0)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_acec3874261e4a788712c2a22eb49f4c(SYNC_CLK=0,SYNC_STAGE=3,MODE="FWFT",RADDR_WIDTH=11,ASYM_WIDTH_RATIO=2,RAM_MUX_RATIO=4,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=256,PROG_FULL_NEGATE=256,PROG_EMPTY_ASSERT=2,PROG_EMPTY_NEGATE=3,OVERFLOW_PROTECT=1,UNDERFLOW_PROTECT=1)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_ctl_acec3874261e4a788712c2a22eb49f4c(SYNC_CLK=0,SYNC_STAGE=3,MODE="FWFT",RADDR_WIDTH=11,ASYM_WIDTH_RATIO=2,RAM_MUX_RATIO=4,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=256,PROG_FULL_NEGATE=256,PROG_EMPTY_ASSERT=2,PROG_EMPTY_NEGATE=3,OVERFLOW_PROTECT=1,UNDERFLOW_PROTECT=1)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_acec3874261e4a788712c2a22eb49f4c(FAMILY="TITANIUM",SYNC_STAGE=3,MODE="FWFT",DATA_WIDTH=128,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=256,PROG_FULL_NEGATE=256,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=2,PROG_EMPTY_NEGATE=3,ASYM_WIDTH_RATIO=2)" begin

INFO     : ... Hierarchical pre-synthesis "efx_fifo_top_acec3874261e4a788712c2a22eb49f4c(FAMILY="TITANIUM",SYNC_STAGE=3,MODE="FWFT",DATA_WIDTH=128,PROGRAMMABLE_FULL="STATIC_SINGLE",PROG_FULL_ASSERT=256,PROG_FULL_NEGATE=256,PROGRAMMABLE_EMPTY="NONE",PROG_EMPTY_ASSERT=2,PROG_EMPTY_NEGATE=3,ASYM_WIDTH_RATIO=2)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "R0_FIFO_32" begin

INFO     : ... Hierarchical pre-synthesis "R0_FIFO_32" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=2457600,C_W_WIDTH=32,C_R_WIDTH=32)" begin

INFO     : ... Hierarchical pre-synthesis "axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=2457600,C_W_WIDTH=32,C_R_WIDTH=32)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "lcd_driver" begin

INFO     : ... Hierarchical pre-synthesis "lcd_driver" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "FrameBoundCrop(SKIP_ROWS=2,SKIP_COLS=2,TOTAL_ROWS=600,TOTAL_COLS=1024)" begin

INFO     : ... Hierarchical pre-synthesis "FrameBoundCrop(SKIP_ROWS=2,SKIP_COLS=2,TOTAL_ROWS=600,TOTAL_COLS=1024)" end (Real time : 0s)

INFO     : ... Hierarchical pre-synthesis "example_top" begin

INFO     : ... Clock Enable Synthesis Performed on 1050 flops.

INFO     : ... Hierarchical pre-synthesis "example_top" end (Real time : 3s)

INFO     : ... Flat optimizations begin

WARNING  : Optimizing into logic zero initialized read-only memory block 'u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ram/ram__D$2'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v:659) [EFX-0656]

WARNING  : Optimizing into logic zero initialized read-only memory block 'u_i2c_timing_ctrl_16bit/u_afifo_w24d16_r24d16/u_efx_fifo_top/xefx_fifo_ram/ram__D$1'. (D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display\ip/afifo_w24d16_r24d16\afifo_w24d16_r24d16.v:659) [EFX-0656]

INFO     : ... Flat optimizations end (Real time : 0s)

INFO     : ... Flat synthesis begin

INFO     : ... Flat synthesis end (Real time : 0s)

INFO     : ... Flat optimizations begin

INFO     : ... Flat optimizations end (Real time : 0s)

INFO     : ... Check and break combinational loops begin

INFO     : ... Check and break combinational loops end (Real time : 0s)

INFO     : ... Setup for logic synthesis begin

INFO     : ... Setup for logic synthesis end (Real time : 1s)

INFO     : ... LUT mapping begin

INFO     : ... LS, strategy: 3, nd: 5044, ed: 16155, lv: 7, pw: 11184.55

INFO     : ... LUT mapping end (Real time : 8s)

INFO     : ... Post-synthesis Verific netlist creation begin

INFO     : ... Post-synthesis Verific netlist creation end (Real time : 0s)

INFO     : ***** Beginning VDB Netlist Checker ... *****

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(11): Input/inout port clk_24m is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(12): Input/inout port clk_25m is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(21): Input/inout port clk_pixel_2x is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(22): Input/inout port clk_pixel_10x is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(30): Input/inout port dsi_refclk_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(31): Input/inout port dsi_byteclk_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(32): Input/inout port dsi_serclk_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(33): Input/inout port dsi_txcclk_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(61): Input/inout port clk_lvds_7x is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(62): Input/inout port clk_27m is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(63): Input/inout port clk_54m is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(97): Input/inout port i_dqs_n_hi[1] is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(98): Input/inout port i_dqs_n_lo[1] is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(121): Input/inout port csi_ctl0_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(125): Input/inout port csi_ctl1_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(129): Input/inout port csi_scl_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(133): Input/inout port csi_sda_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(156): Input/inout port csi_rxd1_lp_n_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(157): Input/inout port csi_rxd1_lp_p_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(158): Input/inout port csi_rxd1_hs_i[7] is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(165): Input/inout port csi_rxd2_lp_p_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(166): Input/inout port csi_rxd2_lp_n_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(167): Input/inout port csi_rxd2_hs_i[7] is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(174): Input/inout port csi_rxd3_lp_p_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(175): Input/inout port csi_rxd3_lp_n_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(176): Input/inout port csi_rxd3_hs_i[7] is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(235): Input/inout port dsi_txd0_lp_p_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(236): Input/inout port dsi_txd0_lp_n_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(237): Input/inout port dsi_txd1_lp_p_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(238): Input/inout port dsi_txd1_lp_n_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(239): Input/inout port dsi_txd2_lp_p_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(240): Input/inout port dsi_txd2_lp_n_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(241): Input/inout port dsi_txd3_lp_p_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(242): Input/inout port dsi_txd3_lp_n_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(247): Input/inout port uart_rx_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(257): Input/inout port cmos_sdat_IN is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(262): Input/inout port cmos_pclk is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(263): Input/inout port cmos_vsync is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(264): Input/inout port cmos_href is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(265): Input/inout port cmos_data[7] is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(266): Input/inout port cmos_ctl1 is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(316): Input/inout port lcd_tp_sda_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(320): Input/inout port lcd_tp_scl_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(324): Input/inout port lcd_tp_int_i is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(344): Input/inout port lcd_b7_0_i[7] is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(345): Input/inout port lcd_g7_0_i[7] is unconnected and will be removed. [VDB-8003]

WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(346): Input/inout port lcd_r7_0_i[7] is unconnected and will be removed. [VDB-8003]

WARNING  : CE port of EFX_DSP48 instance mult_184 is permanently disabled [VDB-8002]

WARNING  : Found 99 warnings in the post-synthesis netlist.

INFO     : VDB Netlist Checker took 0.0177965 seconds.

INFO     : 	VDB Netlist Checker took 0.03125 seconds (approximately) in total CPU time.

INFO     : VDB Netlist Checker virtual memory usage: begin = 240.808 MB, end = 240.888 MB, delta = 0.08 MB

INFO     : 	VDB Netlist Checker peak virtual memory usage = 241.528 MB

INFO     : VDB Netlist Checker resident set memory usage: begin = 252.524 MB, end = 252.696 MB, delta = 0.172 MB

INFO     : 	VDB Netlist Checker peak resident set memory usage = 252.7 MB

INFO     : ***** Ending VDB Netlist Checker ... *****

INFO     : Resource Summary 

INFO     : =============================== 

INFO     : EFX_ADD         : 	1067

INFO     : EFX_LUT4        : 	5017

INFO     : EFX_DSP48       : 	1

INFO     : EFX_FF          : 	5224

INFO     : EFX_SRL8        : 	722

INFO     : EFX_RAM10       : 	69

INFO     : =============================== 

INFO     : ... Post-Map Verilog Writer begin

INFO     : Writing netlist 'example_top' to Verilog file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.map.v' [VDB-1030]

INFO     : ... Post-Map Verilog Writer end (Real time : 0s)

INFO     : The entire flow of EFX_MAP took 16.6787 seconds.

INFO     : 	The entire flow of EFX_MAP took 21.7031 seconds (approximately) in total CPU time.

INFO     : The entire flow of EFX_MAP virtual memory usage: begin = 17.864 MB, end = 212.844 MB, delta = 194.98 MB

INFO     : 	The entire flow of EFX_MAP peak virtual memory usage = 278.98 MB

INFO     : The entire flow of EFX_MAP resident set memory usage: begin = 15.132 MB, end = 219 MB, delta = 203.868 MB

INFO     : 	The entire flow of EFX_MAP peak resident set memory usage = 284.784 MB

