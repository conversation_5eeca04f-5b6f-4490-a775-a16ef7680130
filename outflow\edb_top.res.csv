sep=\t
Module Resource Usage Distribution Estimates
Generated at: Mar 20, 2025 16:36:21

Note: some resources maybe grouped under different hierarchy due to optimization and LUT mapping

Module	         FFs	        SRLs	        ADDs	        LUTs	    COMB4s	      RAMs	 DSP/MULTs
edb_top:edb_top                                             	     715(0)	       8(0)	      86(0)	     843(0)	     0(0)	     1(0)	     0(0)	
 +la0:edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b10010000010011110101101010111010110001000101111001000000010001101001011001000100101011001000101001111111010010111110000111010000,PROBE0_WIDTH=8,PROBE0_TYPE=1)	   629(464)	       8(0)	     86(39)	   819(598)	     0(0)	     1(0)	     0(0)	
  +axi_crc_i:edb_adbg_crc32                                 	     32(32)	       0(0)	       0(0)	     54(54)	     0(0)	     0(0)	     0(0)	
  +GEN_PROBE[0].genblk7.genblk3.trigger_cu:compare_unit(WIDTH=11'b01000,PIPE=1)	     19(19)	       0(0)	       0(0)	     26(26)	     0(0)	     0(0)	     0(0)	
  +trigger_tu:trigger_unit(WIDTH=32'b01,PIPE=1)             	       1(1)	       0(0)	       0(0)	       3(3)	     0(0)	     0(0)	     0(0)	
  +la_biu_inst:la_biu(CAPTURE_WIDTH=32'b01000)              	    113(38)	       8(0)	      47(0)	    138(56)	     0(0)	     1(0)	     0(0)	
   +fifo_with_read_inst:fifo_with_read(DATA_WIDTH=32'b01001)	     75(55)	       8(8)	     47(47)	     82(52)	     0(0)	     1(0)	     0(0)	
    +transcode_write_addr:fifo_address_trancode_unit        	     10(10)	       0(0)	       0(0)	     10(10)	     0(0)	     0(0)	     0(0)	
    +transcode_read_addr:fifo_address_trancode_unit         	     10(10)	       0(0)	       0(0)	     20(20)	     0(0)	     0(0)	     0(0)	
    +simple_dual_port_ram_inst:edb_simple_dual_port_ram(DATA_WIDTH=32'b01001,ADDR_WIDTH=10,RAM_INIT_FILE="")	       0(0)	       0(0)	       0(0)	       0(0)	     0(0)	     1(1)	     0(0)	
 +debug_hub_inst:debug_hub                                  	     86(86)	       0(0)	       0(0)	     24(24)	     0(0)	     0(0)	     0(0)	
