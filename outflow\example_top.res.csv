sep=\t
Module Resource Usage Distribution Estimates
Generated at: Mar 20, 2025 16:36:32

Note: some resources maybe grouped under different hierarchy due to optimization and LUT mapping

Module	         FFs	        SRLs	        ADDs	        LUTs	    COMB4s	      RAMs	 DSP/MULTs
example_top:example_top                                     	   4313(81)	     586(0)	     950(6)	   5638(15)	     0(0)	    52(0)	     0(0)	
 +ddr3_ctl_axi:DdrCtrl                                      	    2162(0)	     514(0)	     472(0)	    2484(0)	     0(0)	    27(0)	     0(0)	
  +u_efx_ddr3_soft_controller:efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33	    2162(0)	     514(0)	     472(0)	    2484(0)	     0(0)	    27(0)	     0(0)	
   +genblk1.inst_ddr3_axi:efx_ddr3_axi_5ee7df9aa375494db25875c4929bcb33	  2162(321)	     514(0)	   472(128)	  2484(253)	     0(0)	    27(0)	     0(0)	
    +inst_arburst_fifo:Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)	       7(7)	       8(8)	     12(12)	     24(24)	     0(0)	     0(0)	     0(0)	
    +u_rd_addr_fifo:Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_DEPTH=32)	       7(7)	   116(116)	     12(12)	     74(74)	     0(0)	     0(0)	     0(0)	
    +u_wr_addr_fifo:Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=48,DATA_DEPTH=32)	       7(7)	   124(124)	     12(12)	     78(78)	     0(0)	     0(0)	     0(0)	
    +inst_awid_fifo:Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)	       7(7)	       0(0)	     12(12)	     12(12)	     0(0)	     0(0)	     0(0)	
    +inst_arid_fifo:Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)	       7(7)	       0(0)	     12(12)	     12(12)	     0(0)	     0(0)	     0(0)	
    +inst_respon_fifo:Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)	       7(7)	       0(0)	     12(12)	     13(13)	     0(0)	     0(0)	     0(0)	
    +inst_rd_brust_fifo:Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)	       7(7)	     32(32)	     12(12)	     32(32)	     0(0)	     0(0)	     0(0)	
    +inst_rd_last_fifo:Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)	       7(7)	     32(32)	     12(12)	     33(33)	     0(0)	     0(0)	     0(0)	
    +inst_respon_len_fifo:Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)	       7(7)	     32(32)	     12(12)	     39(39)	     0(0)	     0(0)	     0(0)	
    +inst_efx_ddr3:top_5ee7df9aa375494db25875c4929bcb33     	  1778(188)	     170(0)	    236(64)	  1914(598)	     0(0)	    27(0)	     0(0)	
     +top_mc:controller_top_5ee7df9aa375494db25875c4929bcb33	   1578(27)	     168(0)	     172(0)	   1304(26)	     0(0)	    27(0)	     0(0)	
      +rst_ctrl0:reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=4,CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)	      16(0)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
       +genblk1[0].genblk1.genblk1.inst_sysclk_rstn:reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)	       4(4)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
       +genblk1[1].genblk1.genblk1.inst_sysclk_rstn:reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)	       4(4)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
       +genblk1[2].genblk1.genblk1.inst_sysclk_rstn:reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)	       4(4)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
       +genblk1[3].genblk1.genblk1.inst_sysclk_rstn:reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)	       4(4)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
      +rst_ctrl1:reset_ctrl_5ee7df9aa375494db25875c4929bcb33(CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)	       4(0)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
       +genblk1[0].genblk1.genblk1.inst_sysclk_rstn:reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)	       4(4)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
      +fifo_ar:streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)	     54(26)	       0(0)	       8(8)	     61(48)	     0(0)	     2(2)	     0(0)	
       +popToPushGray_buffercc:buffercc_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       8(8)	     0(0)	     0(0)	     0(0)	
       +pushToPopGray_buffercc:buffercc_1_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       5(5)	     0(0)	     0(0)	     0(0)	
      +fifo_aw:streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)	     54(26)	       0(0)	       8(8)	     65(49)	     0(0)	     2(2)	     0(0)	
       +popToPushGray_buffercc:buffercc_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       8(8)	     0(0)	     0(0)	     0(0)	
       +pushToPopGray_buffercc:buffercc_1_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       8(8)	     0(0)	     0(0)	     0(0)	
      +fifo_wr:streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=144)	     54(26)	       0(0)	       8(8)	     52(40)	     0(0)	     8(8)	     0(0)	
       +popToPushGray_buffercc:buffercc_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       8(8)	     0(0)	     0(0)	     0(0)	
       +pushToPopGray_buffercc:buffercc_1_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       4(4)	     0(0)	     0(0)	     0(0)	
      +fifo_rdpush:streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=1)	     54(26)	       0(0)	       8(8)	     44(31)	     0(0)	     0(0)	     0(0)	
       +popToPushGray_buffercc:buffercc_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       4(4)	     0(0)	     0(0)	     0(0)	
       +pushToPopGray_buffercc:buffercc_1_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       9(9)	     0(0)	     0(0)	     0(0)	
      +fifo_rd:efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)	      61(0)	      22(2)	      37(0)	      80(0)	     0(0)	     7(0)	     0(0)	
       +xefx_fifo_ram:efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)	       0(0)	       0(0)	       0(0)	     30(30)	     0(0)	     7(7)	     0(0)	
       +xefx_fifo_ctl:efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33_renamed_due_excessive_length_1	     61(61)	      20(0)	     37(37)	     50(14)	     0(0)	     0(0)	     0(0)	
        +genblk7.xrd2wr_bin2gray:efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(9)	     0(0)	     0(0)	     0(0)	
        +genblk7.xrd2wr_addr_sync:efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)	       0(0)	     10(10)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
        +genblk7.xrd2wr_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(1)	     0(0)	     0(0)	     0(0)	
         +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)	       0(0)	       0(0)	       0(0)	       8(1)	     0(0)	     0(0)	     0(0)	
          +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)	       0(0)	       0(0)	       0(0)	       7(1)	     0(0)	     0(0)	     0(0)	
           +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)	       0(0)	       0(0)	       0(0)	       6(1)	     0(0)	     0(0)	     0(0)	
            +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)	       0(0)	       0(0)	       0(0)	       5(1)	     0(0)	     0(0)	     0(0)	
             +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33	       0(0)	       0(0)	       0(0)	       4(1)	     0(0)	     0(0)	     0(0)	
              +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)	       0(0)	       0(0)	       0(0)	       3(1)	     0(0)	     0(0)	     0(0)	
               +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)	       0(0)	       0(0)	       0(0)	       2(1)	     0(0)	     0(0)	     0(0)	
                +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)	       0(0)	       0(0)	       0(0)	       1(1)	     0(0)	     0(0)	     0(0)	
        +genblk7.wr2rd_bin2gray:efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(9)	     0(0)	     0(0)	     0(0)	
        +genblk7.wr2rd_addr_sync:efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)	       0(0)	     10(10)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
        +genblk7.wr2rd_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(1)	     0(0)	     0(0)	     0(0)	
         +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)	       0(0)	       0(0)	       0(0)	       8(1)	     0(0)	     0(0)	     0(0)	
          +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)	       0(0)	       0(0)	       0(0)	       7(1)	     0(0)	     0(0)	     0(0)	
           +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)	       0(0)	       0(0)	       0(0)	       6(1)	     0(0)	     0(0)	     0(0)	
            +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)	       0(0)	       0(0)	       0(0)	       5(1)	     0(0)	     0(0)	     0(0)	
             +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33	       0(0)	       0(0)	       0(0)	       4(1)	     0(0)	     0(0)	     0(0)	
              +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)	       0(0)	       0(0)	       0(0)	       3(1)	     0(0)	     0(0)	     0(0)	
               +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)	       0(0)	       0(0)	       0(0)	       2(1)	     0(0)	     0(0)	     0(0)	
                +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)	       0(0)	       0(0)	       0(0)	       1(1)	     0(0)	     0(0)	     0(0)	
      +fifo_ack:streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=2)	     54(26)	       0(0)	       0(0)	     47(40)	     0(0)	     1(1)	     0(0)	
       +popToPushGray_buffercc:buffercc_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       4(4)	     0(0)	     0(0)	     0(0)	
       +pushToPopGray_buffercc:buffercc_1_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       3(3)	     0(0)	     0(0)	     0(0)	
      +ac_fifo:streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=52)	     54(26)	       0(0)	       0(0)	     45(39)	     0(0)	     3(3)	     0(0)	
       +popToPushGray_buffercc:buffercc_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       3(3)	     0(0)	     0(0)	     0(0)	
       +pushToPopGray_buffercc:buffercc_1_5ee7df9aa375494db25875c4929bcb33	     14(14)	       0(0)	       0(0)	       3(3)	     0(0)	     0(0)	     0(0)	
      +controller_main:ddr_3_controller_5ee7df9aa375494db25875c4929bcb33	 1146(1146)	   146(146)	   103(103)	   884(880)	     0(0)	     4(0)	     0(0)	
       +map_ram:user_dual_port_ram_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=72,OUTPUT_REG="FALSE",RAM_INIT_FILE="ddr3_controller.bin")	       0(0)	       0(0)	       0(0)	       4(4)	     0(0)	     4(4)	     0(0)	
     +rst_top0:reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=2,CYCLE=5,IN_RST_ACTIVE=2'b0,OUT_RST_ACTIVE=2'b0)	       2(0)	       2(0)	       0(0)	       1(0)	     0(0)	     0(0)	     0(0)	
      +genblk1[0].genblk1.genblk1.inst_sysclk_rstn:reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)	       1(1)	       1(1)	       0(0)	       1(1)	     0(0)	     0(0)	     0(0)	
      +genblk1[1].genblk1.genblk1.inst_sysclk_rstn:reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)	       1(1)	       1(1)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +phase_u0:phase_shift_5ee7df9aa375494db25875c4929bcb33 	     10(10)	       0(0)	       0(0)	     11(11)	     0(0)	     0(0)	     0(0)	
 +axi4_awar_mux:AXI4_AWARMux(AID_LEN=4)                     	       6(6)	       0(0)	       0(0)	     40(40)	     0(0)	     0(0)	     0(0)	
 +u_i2c_timing_ctrl_16bit:i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)	     63(63)	       0(0)	     41(41)	   118(118)	     0(0)	     0(0)	     0(0)	
 +dsi_pwm:PWMLite                                           	       8(8)	       0(0)	       6(6)	       2(2)	     0(0)	     0(0)	     0(0)	
 +mipi_rx_0:csi_rx                                          	     771(0)	      22(0)	     155(0)	    1695(0)	     0(0)	     8(0)	     0(0)	
  +u_efx_csi2_rx:efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_2	     771(0)	      22(0)	     155(0)	    1695(0)	     0(0)	     8(0)	     0(0)	
   +dphy_rx_inst:efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")	     132(0)	       0(0)	      21(0)	     221(0)	     0(0)	     0(0)	     0(0)	
    +rx_clane_fsm_inst0:dphy_rx_clane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)	     18(18)	       0(0)	       9(9)	     33(33)	     0(0)	     0(0)	     0(0)	
    +genblk3[0].rx_dlane_fsm_inst:dphy_rx_dlane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)	     33(33)	       0(0)	     12(12)	     93(93)	     0(0)	     0(0)	     0(0)	
    +genblk3[0].rx_dword_align_inst:dphy_rx_dword_align_e7fb069599c849bca10d5e09e6d98480	     81(81)	       0(0)	       0(0)	     95(95)	     0(0)	     0(0)	     0(0)	
   +csi2_rx_top_inst:efx_csi2_rx_top_e7fb069599c849bca10d5e09e6d98480(HS_DATA_WIDTH=8,tINIT_NS=1000,CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,PACK_TYPE=15,ENABLE_VCX=1'b0,PIXEL_FIFO_DEPTH=1024)	    639(27)	      22(0)	    134(17)	    1474(9)	     0(0)	     8(0)	     0(0)	
    +syncreg_1:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
    +syncreg_2:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
    +depacketizer_inst:efx_csi2_depacketizer_e7fb069599c849bca10d5e09e6d98480(ENABLE_VCX=1'b0)	   167(142)	       0(0)	       0(0)	   327(252)	     0(0)	     0(0)	     0(0)	
     +genblk4.ecc_check_inst:efx_csi2_ecc_check_e7fb069599c849bca10d5e09e6d98480	     25(25)	       0(0)	       0(0)	     75(75)	     0(0)	     0(0)	     0(0)	
    +genblk2.lane_aligner_inst:efx_csi2_lane_aligner_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)	      19(1)	       0(0)	       7(0)	      23(2)	     0(0)	     1(0)	     0(0)	
     +genblk1[0].u1_inst:efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_3	      18(2)	       0(0)	       7(0)	      21(0)	     0(0)	     1(0)	     0(0)	
      +xefx_fifo_ram:efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)	       0(0)	       0(0)	       0(0)	       0(0)	     0(0)	     1(1)	     0(0)	
      +xefx_fifo_ctl:efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)	     16(16)	       0(0)	       7(7)	     21(21)	     0(0)	     0(0)	     0(0)	
    +genblk3.byte2pixel_inst:efx_csi2_byte2pixel_e7fb069599c849bca10d5e09e6d98480(PIXEL_FIFO_DEPTH=1024,ENABLE_VCX=1'b0,PACK_TYPE=15)	    422(45)	      22(0)	    110(65)	  1115(179)	     0(0)	     7(0)	     0(0)	
     +syncreg_0:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_1:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_2:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_3:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_4:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_5:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_6:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_7:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_8:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_9:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_10:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_11:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_12:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_13:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_14:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_15:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_16:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_17:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_18:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_20:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)	       4(4)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_23:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_24:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_25:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +syncreg_26:efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)	       2(2)	       0(0)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +genblk2.depack40bit_inst:efx_csi2_depack40bit_e7fb069599c849bca10d5e09e6d98480	     53(53)	       0(0)	       0(0)	   205(205)	     0(0)	     0(0)	     0(0)	
     +genblk3.depack48bit_inst:efx_csi2_depack48bit_e7fb069599c849bca10d5e09e6d98480	     71(71)	       0(0)	       0(0)	   210(210)	     0(0)	     0(0)	     0(0)	
     +genblk4.depack56bit_inst:efx_csi2_depack56bit_e7fb069599c849bca10d5e09e6d98480	     87(87)	       0(0)	       0(0)	   281(281)	     0(0)	     0(0)	     0(0)	
     +genblk5.depack64bit_inst:efx_csi2_depack64bit_e7fb069599c849bca10d5e09e6d98480	     33(33)	       0(0)	       0(0)	     64(64)	     0(0)	     0(0)	     0(0)	
     +u1_inst:efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_4	      83(4)	      22(0)	      45(0)	     176(0)	     0(0)	     7(0)	     0(0)	
      +xefx_fifo_ram:efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)	       0(0)	       0(0)	       0(0)	   105(105)	     0(0)	     7(7)	     0(0)	
      +xefx_fifo_ctl:efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_5	     79(79)	      22(0)	     45(45)	     71(31)	     0(0)	     0(0)	     0(0)	
       +genblk7.xrd2wr_bin2gray:efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)	       0(0)	       0(0)	       0(0)	     10(10)	     0(0)	     0(0)	     0(0)	
       +genblk7.xrd2wr_addr_sync:efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)	       0(0)	     11(11)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
       +genblk7.xrd2wr_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)	       0(0)	       0(0)	       0(0)	      10(1)	     0(0)	     0(0)	     0(0)	
        +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(1)	     0(0)	     0(0)	     0(0)	
         +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)	       0(0)	       0(0)	       0(0)	       8(1)	     0(0)	     0(0)	     0(0)	
          +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)	       0(0)	       0(0)	       0(0)	       7(1)	     0(0)	     0(0)	     0(0)	
           +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)	       0(0)	       0(0)	       0(0)	       6(1)	     0(0)	     0(0)	     0(0)	
            +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)	       0(0)	       0(0)	       0(0)	       5(1)	     0(0)	     0(0)	     0(0)	
             +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480	       0(0)	       0(0)	       0(0)	       4(1)	     0(0)	     0(0)	     0(0)	
              +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)	       0(0)	       0(0)	       0(0)	       3(1)	     0(0)	     0(0)	     0(0)	
               +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)	       0(0)	       0(0)	       0(0)	       2(1)	     0(0)	     0(0)	     0(0)	
                +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)	       0(0)	       0(0)	       0(0)	       1(1)	     0(0)	     0(0)	     0(0)	
       +genblk7.wr2rd_bin2gray:efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)	       0(0)	       0(0)	       0(0)	     10(10)	     0(0)	     0(0)	     0(0)	
       +genblk7.wr2rd_addr_sync:efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)	       0(0)	     11(11)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
       +genblk7.wr2rd_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)	       0(0)	       0(0)	       0(0)	      10(1)	     0(0)	     0(0)	     0(0)	
        +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(1)	     0(0)	     0(0)	     0(0)	
         +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)	       0(0)	       0(0)	       0(0)	       8(1)	     0(0)	     0(0)	     0(0)	
          +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)	       0(0)	       0(0)	       0(0)	       7(1)	     0(0)	     0(0)	     0(0)	
           +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)	       0(0)	       0(0)	       0(0)	       6(1)	     0(0)	     0(0)	     0(0)	
            +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)	       0(0)	       0(0)	       0(0)	       5(1)	     0(0)	     0(0)	     0(0)	
             +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480	       0(0)	       0(0)	       0(0)	       4(1)	     0(0)	     0(0)	     0(0)	
              +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)	       0(0)	       0(0)	       0(0)	       3(1)	     0(0)	     0(0)	     0(0)	
               +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)	       0(0)	       0(0)	       0(0)	       2(1)	     0(0)	     0(0)	     0(0)	
                +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)	       0(0)	       0(0)	       0(0)	       1(1)	     0(0)	     0(0)	     0(0)	
 +u_Sensor_Image_XYCrop:Sensor_Image_XYCrop(IMAGE_HSIZE_SOURCE=160,IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=160,IMAGE_YSIZE_TARGET=720)	     59(59)	       0(0)	     23(23)	     32(32)	     0(0)	     0(0)	     0(0)	
 +u_axi4_ctrl:axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=921600,C_W_WIDTH=32)	   367(198)	      42(2)	    100(24)	    205(80)	     0(0)	    16(0)	     0(0)	
  +Gen_FIFO_W32.u_W0_FIFO_32:W0_FIFO_32                     	      90(0)	      20(0)	      43(0)	      63(0)	     0(0)	     8(0)	     0(0)	
   +u_efx_fifo_top:efx_fifo_top_9133b310c90946e99246ad457ba54d5f_renamed_due_excessive_length_6	      90(4)	      20(0)	      43(0)	      63(0)	     0(0)	     8(0)	     0(0)	
    +xefx_fifo_ram:efx_fifo_ram_9133b310c90946e99246ad457ba54d5f(MODE="FWFT",WR_DEPTH=2048,WDATA_WIDTH=32,RDATA_WIDTH=128,WADDR_WIDTH=11,OUTPUT_REG=0)	     13(13)	       0(0)	       0(0)	       1(1)	     0(0)	     8(8)	     0(0)	
    +xefx_fifo_ctl:efx_fifo_ctl_9133b310c90946e99246ad457ba54d5f_renamed_due_excessive_length_7	     73(73)	      20(0)	     43(43)	     62(26)	     0(0)	     0(0)	     0(0)	
     +genblk7.xrd2wr_bin2gray:efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(9)	     0(0)	     0(0)	     0(0)	
     +genblk7.xrd2wr_addr_sync:efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=10)	       0(0)	     10(10)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +genblk7.xrd2wr_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(1)	     0(0)	     0(0)	     0(0)	
      +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=9)	       0(0)	       0(0)	       0(0)	       8(1)	     0(0)	     0(0)	     0(0)	
       +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=8)	       0(0)	       0(0)	       0(0)	       7(1)	     0(0)	     0(0)	     0(0)	
        +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=7)	       0(0)	       0(0)	       0(0)	       6(1)	     0(0)	     0(0)	     0(0)	
         +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=6)	       0(0)	       0(0)	       0(0)	       5(1)	     0(0)	     0(0)	     0(0)	
          +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f	       0(0)	       0(0)	       0(0)	       4(1)	     0(0)	     0(0)	     0(0)	
           +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=4)	       0(0)	       0(0)	       0(0)	       3(1)	     0(0)	     0(0)	     0(0)	
            +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=3)	       0(0)	       0(0)	       0(0)	       2(1)	     0(0)	     0(0)	     0(0)	
             +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=2)	       0(0)	       0(0)	       0(0)	       1(1)	     0(0)	     0(0)	     0(0)	
     +genblk7.wr2rd_bin2gray:efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=12)	       0(0)	       0(0)	       0(0)	       9(9)	     0(0)	     0(0)	     0(0)	
     +genblk7.wr2rd_addr_sync:efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=12)	       0(0)	     10(10)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +genblk7.wr2rd_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=12)	       0(0)	       0(0)	       0(0)	       9(1)	     0(0)	     0(0)	     0(0)	
      +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=11)	       0(0)	       0(0)	       0(0)	       8(1)	     0(0)	     0(0)	     0(0)	
       +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=10)	       0(0)	       0(0)	       0(0)	       7(1)	     0(0)	     0(0)	     0(0)	
        +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=9)	       0(0)	       0(0)	       0(0)	       6(1)	     0(0)	     0(0)	     0(0)	
         +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=8)	       0(0)	       0(0)	       0(0)	       5(1)	     0(0)	     0(0)	     0(0)	
          +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=7)	       0(0)	       0(0)	       0(0)	       4(1)	     0(0)	     0(0)	     0(0)	
           +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=6)	       0(0)	       0(0)	       0(0)	       3(1)	     0(0)	     0(0)	     0(0)	
            +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f	       0(0)	       0(0)	       0(0)	       2(1)	     0(0)	     0(0)	     0(0)	
             +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=4)	       0(0)	       0(0)	       0(0)	       1(1)	     0(0)	     0(0)	     0(0)	
  +Gen_RFIFO_8.u_R0_FIFO_8:R0_FIFO_8                        	      79(0)	      20(0)	      33(0)	      62(0)	     0(0)	     8(0)	     0(0)	
   +u_efx_fifo_top:efx_fifo_top_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_8	      79(4)	      20(0)	      33(0)	      62(0)	     0(0)	     8(0)	     0(0)	
    +xefx_fifo_ram:efx_fifo_ram_8ff540a3c0cf4ec6ad5f65a53419c763(FAMILY="TITANIUM",MODE="FWFT",RD_DEPTH=8192,WDATA_WIDTH=128,RADDR_WIDTH=13,OUTPUT_REG=0,RAM_MUX_RATIO=16)	       0(0)	       0(0)	       0(0)	       0(0)	     0(0)	     8(8)	     0(0)	
    +xefx_fifo_ctl:efx_fifo_ctl_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_9	     75(75)	      20(0)	     33(33)	     62(26)	     0(0)	     0(0)	     0(0)	
     +genblk7.xrd2wr_bin2gray:efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)	       0(0)	       0(0)	       0(0)	       9(9)	     0(0)	     0(0)	     0(0)	
     +genblk7.xrd2wr_addr_sync:efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=14)	       0(0)	     10(10)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +genblk7.xrd2wr_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)	       0(0)	       0(0)	       0(0)	       9(1)	     0(0)	     0(0)	     0(0)	
      +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=13)	       0(0)	       0(0)	       0(0)	       8(1)	     0(0)	     0(0)	     0(0)	
       +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=12)	       0(0)	       0(0)	       0(0)	       7(1)	     0(0)	     0(0)	     0(0)	
        +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=11)	       0(0)	       0(0)	       0(0)	       6(1)	     0(0)	     0(0)	     0(0)	
         +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)	       0(0)	       0(0)	       0(0)	       5(1)	     0(0)	     0(0)	     0(0)	
          +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=9)	       0(0)	       0(0)	       0(0)	       4(1)	     0(0)	     0(0)	     0(0)	
           +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=8)	       0(0)	       0(0)	       0(0)	       3(1)	     0(0)	     0(0)	     0(0)	
            +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=7)	       0(0)	       0(0)	       0(0)	       2(1)	     0(0)	     0(0)	     0(0)	
             +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=6)	       0(0)	       0(0)	       0(0)	       1(1)	     0(0)	     0(0)	     0(0)	
     +genblk7.wr2rd_bin2gray:efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(9)	     0(0)	     0(0)	     0(0)	
     +genblk7.wr2rd_addr_sync:efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=10)	       0(0)	     10(10)	       0(0)	       0(0)	     0(0)	     0(0)	     0(0)	
     +genblk7.wr2rd_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)	       0(0)	       0(0)	       0(0)	       9(1)	     0(0)	     0(0)	     0(0)	
      +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=9)	       0(0)	       0(0)	       0(0)	       8(1)	     0(0)	     0(0)	     0(0)	
       +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=8)	       0(0)	       0(0)	       0(0)	       7(1)	     0(0)	     0(0)	     0(0)	
        +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=7)	       0(0)	       0(0)	       0(0)	       6(1)	     0(0)	     0(0)	     0(0)	
         +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=6)	       0(0)	       0(0)	       0(0)	       5(1)	     0(0)	     0(0)	     0(0)	
          +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763	       0(0)	       0(0)	       0(0)	       4(1)	     0(0)	     0(0)	     0(0)	
           +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=4)	       0(0)	       0(0)	       0(0)	       3(1)	     0(0)	     0(0)	     0(0)	
            +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=3)	       0(0)	       0(0)	       0(0)	       2(1)	     0(0)	     0(0)	     0(0)	
             +genblk1.genblk1.u_gray2bin:efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=2)	       0(0)	       0(0)	       0(0)	       1(1)	     0(0)	     0(0)	     0(0)	
 +u_lcd_driver:lcd_driver                                   	     38(38)	       0(0)	     24(24)	   104(104)	     0(0)	     0(0)	     0(0)	
 +u_rgb2dvi:rgb2dvi(ENABLE_OSERDES=0)                       	      44(0)	       0(0)	      37(0)	     100(0)	     0(0)	     0(0)	     0(0)	
  +enc_0:tmds_channel                                       	     15(15)	       0(0)	       9(9)	     19(19)	     0(0)	     0(0)	     0(0)	
  +enc_1:tmds_channel                                       	     15(15)	       0(0)	       5(5)	     18(18)	     0(0)	     0(0)	     0(0)	
  +enc_2:tmds_channel                                       	     14(14)	       0(0)	     23(23)	     63(63)	     0(0)	     0(0)	     0(0)	
 +edb_top_inst:edb_top                                      	   714(714)	       8(8)	     86(86)	   843(843)	     0(0)	     1(1)	     0(0)	
