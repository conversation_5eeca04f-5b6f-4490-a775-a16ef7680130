// =============================================================================
// Generated by efx_ipmgr
// Version: 2025.**********
// IP Version: 5.12
// =============================================================================

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2025 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//                                                                       
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//                                                                       
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.                                                
//
////////////////////////////////////////////////////////////////////////////////

`define IP_UUID _449714fdae91449eb701c8d91e94b26b
`define IP_NAME_CONCAT(a,b) a``b
`define IP_MODULE_NAME(name) `IP_NAME_CONCAT(name,`IP_UUID)
module csi_rx
(
    input reset_n,
    input clk,
    input reset_byte_HS_n,
    input clk_byte_HS,
    input reset_pixel_n,
    input clk_pixel,
    input Rx_LP_CLK_P,
    input Rx_LP_CLK_N,
    output Rx_HS_enable_C,
    output LVDS_termen_C,
    input [0:0] Rx_LP_D_P,
    input [0:0] Rx_LP_D_N,
    input [7:0] Rx_HS_D_0,
    input [7:0] Rx_HS_D_1,
    input [7:0] Rx_HS_D_2,
    input [7:0] Rx_HS_D_3,
    input [7:0] Rx_HS_D_4,
    input [7:0] Rx_HS_D_5,
    input [7:0] Rx_HS_D_6,
    input [7:0] Rx_HS_D_7,
    output [0:0] Rx_HS_enable_D,
    output [0:0] LVDS_termen_D,
    output [0:0] fifo_rd_enable,
    input [0:0] fifo_rd_empty,
    output [0:0] DLY_enable_D,
    output [0:0] DLY_inc_D,
    input [0:0] u_dly_enable_D,
    output vsync_vc1,
    output vsync_vc15,
    output vsync_vc12,
    output vsync_vc9,
    output vsync_vc7,
    output vsync_vc14,
    output vsync_vc13,
    output vsync_vc11,
    output vsync_vc10,
    output vsync_vc8,
    output vsync_vc6,
    output vsync_vc4,
    output vsync_vc0,
    output vsync_vc5,
    output irq,
    output pixel_data_valid,
    output [63:0] pixel_data,
    output [3:0] pixel_per_clk,
    output [5:0] datatype,
    output [15:0] shortpkt_data_field,
    output [15:0] word_count,
    output [1:0] vcx,
    output [1:0] vc,
    output hsync_vc3,
    output hsync_vc2,
    output hsync_vc8,
    output hsync_vc12,
    output hsync_vc7,
    output hsync_vc10,
    output hsync_vc1,
    output hsync_vc0,
    output hsync_vc13,
    output hsync_vc4,
    output hsync_vc11,
    output hsync_vc6,
    output hsync_vc9,
    output hsync_vc15,
    output hsync_vc14,
    output hsync_vc5,
    input axi_rready,
    output axi_rvalid,
    output [31:0] axi_rdata,
    output axi_arready,
    input axi_arvalid,
    input [5:0] axi_araddr,
    input axi_bready,
    output axi_bvalid,
    output axi_wready,
    input axi_wvalid,
    input [31:0] axi_wdata,
    output vsync_vc3,
    output vsync_vc2,
    output axi_awready,
    input [0:0] u_dly_inc_D,
    input axi_clk,
    input axi_reset_n,
    input [5:0] axi_awaddr,
    input axi_awvalid
);
`IP_MODULE_NAME(efx_csi2_rx)
#(
    .PACK_TYPE (15),
    .tLPX_NS (50),
    .tINIT_NS (1000),
    .tCLK_TERM_EN_NS (38),
    .tD_TERM_EN_NS (35),
    .tHS_SETTLE_NS (85),
    .tHS_PREPARE_ZERO_NS (145),
    .NUM_DATA_LANE (1),
    .ASYNC_STAGE (2),
    .HS_BYTECLK_MHZ (187),
    .CLOCK_FREQ_MHZ (50),
    .DPHY_CLOCK_MODE ("Discontinuous"),
    .PIXEL_FIFO_DEPTH (1024),
    .AREGISTER (8),
    .ENABLE_USER_DESKEWCAL (1'b0),
    .FRAME_MODE ("GENERIC"),
    .ENABLE_VCX (1'b0)
)
u_efx_csi2_rx
(
    .reset_n ( reset_n ),
    .clk ( clk ),
    .reset_byte_HS_n ( reset_byte_HS_n ),
    .clk_byte_HS ( clk_byte_HS ),
    .reset_pixel_n ( reset_pixel_n ),
    .clk_pixel ( clk_pixel ),
    .Rx_LP_CLK_P ( Rx_LP_CLK_P ),
    .Rx_LP_CLK_N ( Rx_LP_CLK_N ),
    .Rx_HS_enable_C ( Rx_HS_enable_C ),
    .LVDS_termen_C ( LVDS_termen_C ),
    .Rx_LP_D_P ( Rx_LP_D_P ),
    .Rx_LP_D_N ( Rx_LP_D_N ),
    .Rx_HS_D_0 ( Rx_HS_D_0 ),
    .Rx_HS_D_1 ( Rx_HS_D_1 ),
    .Rx_HS_D_2 ( Rx_HS_D_2 ),
    .Rx_HS_D_3 ( Rx_HS_D_3 ),
    .Rx_HS_D_4 ( Rx_HS_D_4 ),
    .Rx_HS_D_5 ( Rx_HS_D_5 ),
    .Rx_HS_D_6 ( Rx_HS_D_6 ),
    .Rx_HS_D_7 ( Rx_HS_D_7 ),
    .Rx_HS_enable_D ( Rx_HS_enable_D ),
    .LVDS_termen_D ( LVDS_termen_D ),
    .fifo_rd_enable ( fifo_rd_enable ),
    .fifo_rd_empty ( fifo_rd_empty ),
    .DLY_enable_D ( DLY_enable_D ),
    .DLY_inc_D ( DLY_inc_D ),
    .u_dly_enable_D ( u_dly_enable_D ),
    .vsync_vc1 ( vsync_vc1 ),
    .vsync_vc15 ( vsync_vc15 ),
    .vsync_vc12 ( vsync_vc12 ),
    .vsync_vc9 ( vsync_vc9 ),
    .vsync_vc7 ( vsync_vc7 ),
    .vsync_vc14 ( vsync_vc14 ),
    .vsync_vc13 ( vsync_vc13 ),
    .vsync_vc11 ( vsync_vc11 ),
    .vsync_vc10 ( vsync_vc10 ),
    .vsync_vc8 ( vsync_vc8 ),
    .vsync_vc6 ( vsync_vc6 ),
    .vsync_vc4 ( vsync_vc4 ),
    .vsync_vc0 ( vsync_vc0 ),
    .vsync_vc5 ( vsync_vc5 ),
    .irq ( irq ),
    .pixel_data_valid ( pixel_data_valid ),
    .pixel_data ( pixel_data ),
    .pixel_per_clk ( pixel_per_clk ),
    .datatype ( datatype ),
    .shortpkt_data_field ( shortpkt_data_field ),
    .word_count ( word_count ),
    .vcx ( vcx ),
    .vc ( vc ),
    .hsync_vc3 ( hsync_vc3 ),
    .hsync_vc2 ( hsync_vc2 ),
    .hsync_vc8 ( hsync_vc8 ),
    .hsync_vc12 ( hsync_vc12 ),
    .hsync_vc7 ( hsync_vc7 ),
    .hsync_vc10 ( hsync_vc10 ),
    .hsync_vc1 ( hsync_vc1 ),
    .hsync_vc0 ( hsync_vc0 ),
    .hsync_vc13 ( hsync_vc13 ),
    .hsync_vc4 ( hsync_vc4 ),
    .hsync_vc11 ( hsync_vc11 ),
    .hsync_vc6 ( hsync_vc6 ),
    .hsync_vc9 ( hsync_vc9 ),
    .hsync_vc15 ( hsync_vc15 ),
    .hsync_vc14 ( hsync_vc14 ),
    .hsync_vc5 ( hsync_vc5 ),
    .axi_rready ( axi_rready ),
    .axi_rvalid ( axi_rvalid ),
    .axi_rdata ( axi_rdata ),
    .axi_arready ( axi_arready ),
    .axi_arvalid ( axi_arvalid ),
    .axi_araddr ( axi_araddr ),
    .axi_bready ( axi_bready ),
    .axi_bvalid ( axi_bvalid ),
    .axi_wready ( axi_wready ),
    .axi_wvalid ( axi_wvalid ),
    .axi_wdata ( axi_wdata ),
    .vsync_vc3 ( vsync_vc3 ),
    .vsync_vc2 ( vsync_vc2 ),
    .axi_awready ( axi_awready ),
    .u_dly_inc_D ( u_dly_inc_D ),
    .axi_clk ( axi_clk ),
    .axi_reset_n ( axi_reset_n ),
    .axi_awaddr ( axi_awaddr ),
    .axi_awvalid ( axi_awvalid )
);
endmodule


// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_asyncreg) #(
    parameter ASYNC_STAGE = 2,
    parameter WIDTH = 4,
    parameter ACTIVE_LOW = 1, // 0 - Active high reset, 1 - Active low reset
    parameter RST_VALUE = 0 
) (
    input  wire             clk,
    input  wire             reset_n,
    input  wire [WIDTH-1:0] d_i,
    output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] async_reg[ASYNC_STAGE-1:0];

assign d_o = async_reg[ASYNC_STAGE-1];

genvar i;
generate
   if (ACTIVE_LOW == 1) begin: active_low
      if (RST_VALUE == 0) begin: low_reset_0
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: low_reset_1
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
   else begin: active_high
      if (RST_VALUE == 0) begin: high_reset_0
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: high_reset_1
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
endgenerate

endmodule


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#Vz~<DZ!udx*EeR-5\XexBJz'HIW<'_3dPlO7"]W+7mRz>VmW}OsV[EZR>2DWl5U;7oG#?lB
    qB2]+>YT}aap<+1$'Q,AmOoXA31,['>Kj^-1V-]3]_7[{jxmOG?vm5v#3V3ZrR_Ee<'A+jj?r?TR
    #A]>^IX<\j[/:[lBGK_;Jr#Rr?z5lJIireIjiwUer]]Qkd^pZm\jZJK]VVas+r[2'wW$7D*mCiY~
    ,^|[@7AL1A+U=p'D.QOX]v=3wa7B@P[O^#|JQ#sIjsJ_>EuJ{^{YO!BckX^eR'?XJ-*}D^-A%;IJ
    $@siu}_ixU^Zl<lX=XU22=IH+}z]~!XK;rBae1D?+OI{}OKV~jl'{z;Yi#>B'Y>zJs+x}}AQK1*e
    uUoVEv7H]BTK*T>pejBj!vA.kvae7$GT~xx={Re$@o$I#a+k2s+_,~7{.\9[IWKD1pOQZhL9N6I5
    AmPe57zkxHU}Okr^vpJ'vi,Lb^#KQ]YrYP=$+I7Ua^BVH}rxprY>[]H'roo!A=ixTr<{G2^kD^sx
    K-1\?X7jZO=i3lT_Uz0>eY+QCAl&XsEiB];~=il{X'@V,AR{]xJ<UYx\j*!JlpeZTGw7G#9S-o!I
    y@<eEC7#B7EW\C>=Bda[rl5HZW)6z}sCOx[_UR1u=_GK$Z7@}c7B;Br$3BXeu$enTWk5v<6UG*iV
    ,KTZ={{'>!>++<jMv{'Q?C'EE)+'Z7<<lo1u^2$!,~$R'G{Ykri7n]asx5'Zp]q,n=G(CVaTk{H?
    Hs]jYYxV['\r\u+TXED+@EXvKI'HECHIG!eXt=er>B7+$H6vWpW^ET3uH}ps|xC~]]]5p+o$BrY'
    m-oJu'QQZslC^=O<5^+UBWq$G'I[$UO2Yjun'O*fkV'53>3B*z~UnGT{m<K?ZvGZI7Uz5w'V1C#p
    F[-sz*elV#7p=,!O?$?]]]sG]_^R*X*!A[nVD!rVvBi<{[ou7p{+R{7<7\$YZGU=3=?'#Ck[ZQQ-
    j}vrvxHau\_[T_pUW,i'=JUGTyQX>Gu[3T^{}$or;ua1o17_IO\"o+GDxJ@\#R$$T1<n$2-E1WRK
    Ys-,Z,s]5ICz:nRu2B;U3R*$!e]1};G!aB+Bs;^J!]A<EXAK#U-\l\a7]D'G;_6$\w~1mD@nve#,
    ,zGD{+vo-[@o[IRmjJEjRU{=@B~'-ZuBa**pK-=B_{@ozrp;6#nROU>s<]nWs}7Di>'i>QnUj7uK
    ?V5AU_DxEOD$sla<}az>Jg~'T;~'?p=?;~K1kK*\?<6C$ipggD<WTZoBxOvT,=#Il}#DpiwAn5Y@
    DsuoX<'OXo[prEQE#'C51neEiRM'j]J=Rr<?XD2y@<_G*;<O(>CjJ|u^D\z~-<bLK<Xk@[EW?_ED
    Tl},]@G3{H^o]sJxb,!YRB\2~1=7?*-5=kX]TvwRCZDopIH-a7~nHQw!_=pa{wz@<Y*{r2>7UYvC
    ^jAr@1QR[o[<}d[oKDr<zU3pHknn-?[^5kDpn^Q7Bz2^u*]GKwis@],[=_vKjRwX>rZv7IZ5*+,7
    5Xwazz2l;Je2pZju5pR\ulVuB?8T[TCm<VOn{+H_{jOmv,ejK'un_BlFlC=;@'o'VBnTh*5Tp'U,
    QE<[HQ@;[BG#QAlz!}G3Jq2r_omnJwbgrk*5vaCkBz[aZH$rx3xkB1Qm,'E_FSE7HR^{=2xaHA;1
    x+'Rh1r!uG!-YHIeTM0A>GT=X^iOmEUH[\n<V~#g'D{CL.E$ou>AE-!GT~rwRxh*u2lp1!BW'iDW
    G{<C'nw;xIEW5C!DDYmr7x{Rm~]h,JE[?v+D7HHvOuZ1I{^uN}wTYFn[}oy-$<>2aKDB]3Rek\Gl
    W~kC-IBr}#w},<G>j*},{!uoaJD<s[3BZ1+k__AviDp<UI$K1w@ld=Rja,JAn~H>_kECnm1msJE2
    rcKYVBp[G[-_D+'D_H_A,E~n7D"$XnlKXzOhctWYT=75ZCWzo{>xWBq?RX+[7TKo2^moWp;~Ri[!
    IuI^,-shF?UWJ[kje{V?z-Xmj?_CwsJ5-\!<]5IJe#7JlDQ>X#CuRmG}{u{Z->w$XK<\U-Tr}2as
    ~[^UrCxp[~$Y,-'s_*@@eI?-ah\iX{D}ZTW{v!T[^nl+Yx'}pWn>$uBCo<se->Q[oVCDB{E3Uxo+
    ]-0[w;vDY~a;>@C;R?XseAa,*m'{C@w#To2}i7I7{2;I@WuaIr=%<Q7mrzj-qJXW-]!l$58!jxG\
    3]+J\lZrE?3B7^Cj}{vxe\=UT3pY;ElpU[_E{YT6'^BK'ZT7^z3nO;D!>h8Gbs{]r_!xZOl3KI!U
    72aw'Y'}$BQsT1[Yx3oZ^><+!sAE{?}ZT-UY=8EjDux_<+9fo_-'>^]^YCH1qSU^SOMpW[Bx-T_H
    Cox{QE*,#eoz55uCg3rBj'e5*J{}*[Q$J}o{Ik^luuaeT'_1D<]iRxo5!Qz1QLx_Kvu}e?GSQ+3'
    E<IO1m3O~^VJ!HoEl5<Ar@2jRU<_E#G?Wv${-H<1a,,2bj-_Tp_KpY65-_,#,3xf+$C?v]eD5D#@
    pdK=z~-v_A$_W@Yo]<\IH^Ri1mDaCzh_32x#p?D^JprWv>!&Sj5Dv1l!H)7VvWr;XG*TGG5uH];s
    $Q{U2;EGn[n'X*dVll{~a$QTpX]GngYuu5Zj5~xQ]aH,r2@_JE^Wl}IUQCG'[iYD\n#BT!-7}=u<
    *aiUTnC_=mYT~VTAaCrG!Tr5E3xQvvo3^QXBv17?Hut3-!pBnD_;=>aH5!v7=$Bv}HxBCgMmxQ[|
    l_WRAU3<Kv'\Kp_oT-*@deBl>~1~XOo[EG^@ZG\rjox1@j]XCy'Aas[ju,=R^$lpaQ7jKWjQ{v+<
    @Ee0A6DWDT<w+o~,AYJo+Cr^Io==<}[TH;(kp~H4j[[,Gl#k(A^e29_2K-s;!^c,e1ua>IBK1k^O
    ljQNpm*~B5oplRIAe<UJ|V@z-*}up^{^KrSUTmpIB#sx~-2)'r#lk=_@'Isre[!ApV\H2Rz*eC@V
    SsQ}G'uRoQAGuIEK*biX=Wp.DWx2]wI\'U_1\-_'L6WorU^i-5HQ!7,\DC%Yx3>*[k*^I,1CkAs'
    [*rnl_x@Y[C'GWU\xE$_'+lU\*z(;CYC0x7mlwX'E~-_3ZpXWp*nB-CnQp_pXDz>}IxWj*eU}5CK
    wJQ]T;$Z-Txo!I,uY<{2U2ri>R1RDUlX^7Zv>ezr\\E]32I[rZ,^~}Z=E1O7@7sTH)JIt-HpGg?a
    zZ7kZRs?m~27sHJV#v~A_JMi1aWsXY]%U}+OBs]k3*!5nlnz*n*!}]rD"i[$Q(*-OoGn@KqQ?aKw
    N#}YQ3<Kp%15mZ-I2va}1nRYs{+Ie{E{>]^,H\Uv^eFW}3~kLeYzBTe;^KUe}-U2Kjs<=zACC;Qe
    On^rZr>+5{rZuDQwDl2>U$,TEFpHvTK}uni-u1&2+HD:f{_C~Tpp?~V#Js7w?{1J=,@*w:Ym=~]o
    VUG5-!}>Ev-R{=%CEol'GYA^r*@/J{~^Y+*Q3nHBOU!X5K[U>Yv5B3T22DU<xiToznQT6*jR_3pr
    s2\J^[m~HZH[@s2'G]2ACuGzk3Ywe$an+qWGw<yCC?24Di_oAOWWC#}#]kmC(+Y=EJ7$5*+-zlKE
    TKpzO?BwTonJp:2E<OL?Ten2jY'$YAO1GHDx*~Bz-WRX[1Esr{<;v$<ZaXYr1li5}m*Hpzza1JCI
    {ODWjKO!]k?DjH_kL}7$Wa<!*D>QCGAnoBW'#wBm_"rEsnaR;DXXQp:z'f1OX;_B-Z!'an}eHzrs
    3x+[\uJBK2OK{'#U!Od#x]IA<U2W7n@1{zZ#XY]zQo@jzpO?Q#]\],>*}Xnuj\jR__Xz2Gl2eA#a
    z\O*:eiGW'X_-o-vlQ*GX=VEGwURG>5~=a=}WlR1zK*D*;YTv3<aC\=WkmYYj4X-'soj]TlH}KJj
    35=RTGI11z%m&n5n+AG+?S/j<$zUv\'X*2\Q^?'u,}vI>R*OiX<1+WeoxKk|x!nY1wXDS3,w5'1=
    Q2{UWu^z5Tt,D,IjD}$_gZ'=#&$RwYw$A?^I]s!=@[l[El}gBJ]*iI]QCWYE,@mp]-FulVvvGp},
    wxu_j@pJAYk'lQ#e8[vvrJnJaIC$sk,kB<|1RX]ur<-g7BjT^#~pw'?n7uK{lnmlT+21Ujp2uUBV
    BV[K#$+aG_m=Rr~'~U@C#v2{1ns@~x<]zoUK?^J^f#\o\BE<BvW_jRaDlBWx>Y#j-'Irpwe]ww$p
    O_aza*l'wQu_s7?eUj+Kl4llEWWw\o6Evl;w11*{T5i*-w$I?[@S\sj$7sk7;>*aal~e@jRCApA[
    Y_Upvme7Qk,2?zD*-sO}lA[aXVTkUpiQp8Rk>[C}j^+r\{>Tj1xG>x0-snmsAnR?n5}[>-H-DzEu
    H<zzx$UiR+Zjn\w,WZO^Q$X0=@v5G1#<{^QOh#<s]Vx~j-DJGGU'Dj[pW7+W[6;aZ*wwm'W+-^f_
    'np5A@oSRIr~Je\B\@-m1G*I{<vQ;52C^~+GHRA<=_sJS,[RYQ?-[5rTv's<_=D>B_GXT?v*x\~[
    @v~_DN\<n*SE{W<zCrRF7DnEHOkIR9aw*k3AJ{Rlz^6VieJs?^]g^VA]\7Z?L2+*IO>$_n1plke*
    ~\R,GL1@*W5|=l2!<\EDK5CYO]Xr[/lj}'*RwHDC-_m{R,BE]u<j!xaC32C_]R>nl;?j5-\{mRYQ
    aQlUT-,,{G/gTA$HkIKu|owrX2E}RClI2qEV_uv75O8Y]a;$=W+*.wU[YB?{@H}B?)BJU'[aaTsU
    K1s>ZnC@[@TwDYl^i-#vvCRZ<E#-17VEOWhowZ@i}DlpvUZ$=_>5x!mo!Aaa'EAU]m5o\X<X+JHa
    ,-$T$V,.@Vs1'QYOUD_@'p=_0ApeoU}^jK<Ge(waHnL'Iu<CuCQs@C^-vU7^>V5pD5#Nu[a~h];v
    mekDv!r3,Ykw{V]'@KoseGQ'C,RrK<UCIEYa#nzn@C2U+Vur,/Govp_~HQ=wD7yRl;n9[V@pc*Q3
    ]kw=3G{ap>=YkOn]'#AsEI$>IOvo\sKJ=kz\u_Jn{D>YR^r=Z:V*5J+^@]s7#Cril;RB@UHh4lz1
    @HI5@?^ivOwrC+Xln@OWxe@Ix2*<x^!=el2wnlOs,2+TKTwsne~rsnG'I-77;jzsewG;K]UC\7<>
    ?7I-p]+BDWE=mjO?={DU7nVXv\9P-Ia-Gs$C\_J+wo7>n>Y+6zozv?Dp3{,$5{_2*zn;>m+GR]}7
    1Q5n5(Hj!+6\*!$,nYsIQ1sKnOGQUTn;X>\5Yn_.O^QKVxv3r'o+Rv\U$j-@Zn,W_H{p~_EnJx<-
    p?rRcdR?E~Qvmm+7O_O|w7n=)9V{*nq91]{QilYJOz;pl@Rpa+G<-1^{IUXG[9D'GlvCjZA]\l-5
    ;pQ]K'_Q;U*BR5n=VT{e,ErD+1IeOKIj!C^;1AeQCVwHxO\u'?IVX_Q;},EYDl1HnnHV+OdKw=rA
    Ge5{j1Z^$p?1=uD1i@mj>s;5J>YWR+D}Q$]w{H]ZDZ3sH,]k5HGT[^Z@>v\I5DT9hM$Tx@qwY<w3
    wwAG_okBTE;01w+p|bKRJOY$ow'pD;>5lwr~$CqJ[~B7ja$aH>7eD$2ov{YUzAJ[IxpkpVGiE@l\
    {jQ5[[E~+\Tj=uXjTBp0}o1BjC#m\u~no]7Q3jBu:.-GC;nE~#GD-1aj]1rs\[x<V\8#[C^6[no?
    RDko?}Z@[vA++wT!;^XEws$;U7Za&^-vlC*^!\A;v?>IiLvWXl[$\Q?-*HT[RxowoWS2]Ru|<Y+'
    n\+,D\#3vX^sH}pIG+wZH'=DqJDxEN-nr~Vjv}]5a_ZBVlklH5<elR]\urm'J@WTHe?{I@pfR=57
    AU',K1KvBD_[IoB5AUsQUtY{'1_Y]-q]DsXB7i;G{[CrUIka]]HVaIuEm@v;Cr7W>=@'~+xbJrU-
    *Da{Un-nWEC^^#x~oAAeYU7l]Do']M_\DU0-R->d+Uj!-7<*no~~1EvvwXG<V_QY\WEKOp>$UDnT
    Y2-kovw$2GY-&*2Vw;63eK2+YrjioK]W^{@It~opGk$sR?TQm2UmH9[,<7URYk0VaQWzJ*@+C]=@
    <-OMCH=_^ZO+2o2?E_iD+$kzy7Q}@,'AuOs\$?Top#w\T$>5^.=Hj-7D~$6$}CYw{p+_=-_-GaA,
    X^zpEDTVKX3k_uuw}Z+XpiuZ153GAJVzW1,~,ZT}^>~0S->G'_'D3$o1U[eBvr3@j}~U~\+Emx5x
    kjED,5zH1z]Q';\CZOVuWnUE3G[$[<B@ReU$23pw?R$E}rGRGv^pXTTwslYuRk^n[E3H=+lB-oHK
    z:VW5uARvX']kn_vs!_>l[Ow-Jyo@w>+s$3m[rV>$X]mD!}ED\u!XQWR+2mgv{UD7W72Q=THDiuZ
    mzoH%XQ3?'}-R{{_lzu,2pwG!mIzm~U*W31r^o.>IRQ-*=wl;J@!|>xuX!A}u]_~l--]ls!zInlY
    *_#nu~5Axi[]GxGTO3*e;v;!v'J'pKCi=h->*^'w[2O[5\En@=?R'{]L$w\CsanRxWR^2EY!V1'x
    Rps*1npKi+I*?a5K^}V]~Y2]dAo{KY]Ik2_N17<;2[uY+Xl@9n{woQf]}{H]E]n^-ROre'JKnCx!
    jlCLIm@3Xrz,\>}C^:i5A^zYz=Dw*W_O{p5al'{xUA3Xw>sw3*_[ArgnC\#e-AA%KUX[5IYzzsqD
    TlIyk1<jQ7*je-5u1Jp]ko+K'/~Y['?r++C,B!S~\Qnkw*-?A_=]}ns>AEn@O'@x^Xn3DGWnDJ,Z
    E!7nn*wDQDz]rm~$~IvjKu<be_RJCox@-OQwe=o55!;-Oe3D<+pZEsGZXIkH0A$OiUjl~M,Y2]';
    H?}n~EV~RCQnu=iRjo,+DT}GBaVY{r/\_5nu<-1M}2lCAoWkBRC>R=*WrBmQo]iI!e}w''VlF=zh
    J$j,W-]1+<p\[zujC<mC_KvR/v~z#;{wKm}-^@jGBY,IpXsio>D2aj>n>j1C@\3j[XspnE\[n1O2
    +<I1j0vZ5n_~D<|!$-Rz^3*z\HDUo*!Qv!@!,@jKX<O2Cekm+\nm-!_R+xXjDBpKwQEYeQIY1u@I
    er=_Y^o6#-13}iA,w5<>O*a{@]OA_Ra'[zWzTwBKPZXYK_AK3xGIQj$-3'*1!J^ea'3m*1K__WI@
    mr#5s5{J~exx@Y>Q+\pm\DxT'?L=K}~+XGx?'DH)IpCwwX]l}3UzlExCY\v'6Tz$=t}<QR{__l#a
    HE#Ix}}Tsp1aCQ/>zJu}x<KW^~v^U*IjHpBYOUsx3DOwEeRl}~!~-!T<Q@W~_Q*jK=mpo~v~pp<6
    ^Dm<DG5K~AeZ@<'*1(l$BRL<7xx?zk']me{\-UemEAOAG1p{Q90#^vWZBU[KeXu7$lm4'~E<&rHe
    *vOiGIajApAU=[@J$MIX<V$H{Dw]OnanrkcA,Ru}]Y1-wEX\vaYJn$s9IK[@Q{<[(,rH\}2Y1Tr^
    u!-@JoO6]Dj[Gu2-~TsDYC+Ti-173CxT.,uu;HN|6Ek@ZrlbiBv<|L>=zXTB~ZYAjOBRro{Q'<}1
    Dvi]HkI'{#9A<@I_jkk,_srjIE,&QGWUm[\O^\1u@Y_\ox227m_U[?2~D_ZGPS#a7!l3vR~*VaIQ
    TwIwTWR!\pR<uB8O@>;b8de\,Uz3Y7jZ$>E+z=r3X-P!R=?rDn{JBxXGpU+#17G#vJ5=KQ}es7]e
    =]USOVJ1}TaTxkHn,mVv[)xA1V'?e7$7jzOQ{@7^BI!rW[3YwaiEm}Qk5~VaB}FYio5~]A{H}W^n
    =UXH_mXJEROOusDzm3+Zn\5,CKmWx;Ysis$iQvU--,3ECCYs}u?zDXJAw\@j5H]'EKOEH*H31eo7
    ?5^I=iJ(_Qrs#+$-OD,GTA$pwaW^k+mpGJa@nAGnks[UD5]ujz^$(D#GsePXGH*TOviCXQ]BYHvD
    =eonVazsT}v$12CE>$rlp}27<E}sawIxVnO:Y+<a]W@l5kxp,}DAIO@XrkB5KGru;][H<s$m8$mB
    _^OI;RwJ,Yr[C2L:n'i@YYRZuA$DmeVInQRRj+A]i_[I6H{~lQ^]<*O*GQeG!CKV$xC[*||$-JmN
    U]G<\<DVRrQ]'Q[2CKekg>>Z'Z^<-)CQ5J=?TH-^OB_iI[mw^zHHK!,aH!Rr^rR~<n3nH7a$>*]*
    #TH-73KV,$-TZ$^OT+=ppEP$@lW{,kR!Gz>{eC@'_i>5wa^q#[XsXCHr5AT+3z<RWRkErjalykj%
    UQREvQlI$_k<is'_GV_%4E?<OlroJL[vu>Q7?=1Gk1",HxUoi=2O;+1QzW}7^RK![To=3siQZLEe
    '{I<CoH[2\Rvw_u_x'IG\]sKDBWV5=@HK<X\C\1rvwA<;,}<~]I^C~LK77]Q3wZU51!na{wX7Jp1
    _V#:QmEZiO5$h?UWC7ua]1mvT%2HDHwj#uP*k2?z7nE3e^23$e'7ep+Za>?oQ<e}E{Cp~s{>TjW'
    ;Y1D5!sjXWn;TVk3v@B=<VXHVw;EIAXiI?2Q-Jv@$e[xpKT-[@E!s[mu*xrz712nCm$]O<#a,\fi
    D<jZo7Vf9Jn>*1X;U6cmXE2V-!l+]@;~>,wun$VK*[mZTU>OAe#;ss7ir$]j$jm~'<aIv$T^]wa4
    H[Wj$CuVYo^sWeo^\HRl%a]HzpJ+eOwB},VjnIQHs15r$5[w,[\$}?^7YVo+Kae1=0<Gb}'kCilk
    ])q};U;s227lH}TVjrKTBa_n>$+hOr\j9w<$Q-=Y$$e}{Y:\lEDXx=H$>\i7J'j'={AiA\j+$B[T
    w{Ha'>zBIO?&m_BsBo>+c4rOwr2UsADXO*MfC+u?uDYYvrE<7';eK*vIE$k+K$Gixs*lUI,;]!CU
    1e5K;^-X8#GCK\=?VaVr'7eoA2I;npJz^-*'vYIxn3VUueeZ}i\V5FHps\j,E_wVZpunri*sI-D~
    [TEim{=kOJ(O2<!ro<A^i>'\oiR{-IYIT}_uXBW]TUpzJC!ReKBGlH*za->+p$G?I~5{{J3^a;n^
    }BsJ5CaA__pE3xjH1v<W+EB4wVX5\w2BI]2vQW><hi\-zkQkXnBE}a*QTGs]<8~|[4i7Iwk7iu!H
    RnQv]]srEvh{C$u:Dn31-Xap{$]W$q=?&u\ITHr;mTQJpoCeWa,V}H}X_kQZocS/'in{QA$]}Yx7
    2,U~ewwKJEkEG,p?^2A;_1IlrS^Yr77juK5|*wDZ~nGvi5xE?GI-\[A^B>5<ksBO@o{wGl}m_<w_
    5pXE*Ts@*,^UD};{o}#Jn=-#mO@Q'<{$eljEDWW*~AX{iDB;*~C]{VBvX++XGkYm!=*p]=}v|zmX
    ^oEwmt
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#o4*A[-jGK+e_VJQvo\lkr>zl\]I?l]W}i7lO7i=iD*'3ZOjx7rGmVGx,A'K,jO1?!G7WoT3
    U}vL-nXuz3C[EfkQ'Be-e]iXa[zx^QI}VCX$sD72[lNW'#Zz,C!q$H7pz{{7?r$plrXQ9aDj^FQT
    {TP\<]s1mDTP!1Ao\V+K-,]7+l@A,*aDw7?}Yu\K;<,?5,3aGMHU;<x!<v>{Z]XnA#3^ijt~v7l}
    >@WL,z_>v5k?1EI^>=<H,X1pl-GW>GYiG~<Hx^+2qBC=DGjN\Y^oIO_C1D\JxH*vTQvH_^Q5HjZB
    Ea-vB5[7I#nxrvZ[;[n\EaXCGKp1YDBD@,j7rz3ZNomO\QGr$13RHc6&#E]n[!UuqvIDT;_ZEtq;
    =pnon+]h^~<}2=j<;w55GnCv<U=a7s'H?{Ju;=CJmU{ZuBIB?a=BiGVs_IR}x>@Uh^R<BQY[ix!o
    UUzV56l@j]P^;-QrDw*X1]J$ZjO]<mp4'\]'xk]D7mo[pYGpivBrm7~sG+AsGT!A13zBCCi!VRrE
    Czjlrl7sCTJ7RAHvYi}j#[AY][3XselZqOH1Kbo}DkCl5K~,+T'KT;o7"B\Duk5me~CKU@ox\wY~
    B/J^3;<j5>iAn2jWX[+V-nF_E-$iTj!+7#\X,[#?hovs-I[C?bKnJmH__+YlQ_\}G$<jrB?Ij<E'
    ^I,isXCjX!?E{_[3V!{*U]SlWHT=D{$xUH[T'R6ECx?=jjW+HQuS*'$@lC^HYp\pn<^XJV!?\,}e
    =UJ~+'3^Rj1*uHA[kT[kC~EJO[+pW.e1k!v{3#TY{C=Ju=Z{3_J&8VI}5q'V#?4ME'1W]Ir{yw{_
    U!IH]Q11wyql#1^Gx!mH'1D7>A'[!mQ83s*2,Ej}v#eoz@C~-aY^G^nRezXD$'$YI#ssWDo^MX7J
    J_}X>*>a2j#~!CX*iU<Qu]}$~Q-]5u{<OX5w-Y]BW,oK=[mz7RWVm^Il+elBk1s<[}ebIUu3w[vG
    7C_neop[g?5X!I#jC^Co<5'~aOINKz^-9EGj<=Gm~wO#nBn2RqU_2Je3D_7RvziDUIp~5r#z\[]G
    lzxoi--C{\juavgsB-a|E;-RG?GrHB_5GZQZI$pYV}]#=R?J?jT;p~oD'{I]7Jw[6=j2Q,2v[T5R
    'H7w?J{K7Ex2]@+_G0kT@AlD2v]WlXsprnjG.{e?KRnw7N-$]Q{}k}VC]Z>Li}$oaU$GD\HHIZD{
    1Yv[,3mCe_n5C5TJ=XRKNuX_*z{BGBmB+):lm2T}u*AinKe(v=+z-oliDm]!1Y~AE[
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#_s'a?_{7}ZZHX<Y#+^sUb=G;ZC\>sdTS*;Jw7Z[{'Ao!Q,z7k-<^_=+$y]s^;G~]a|0*pwE
    snn?pHQzvjk}]irz!w$wIi@DFzx^QI}VCX$sD72[lNW'#Zz,C!q$H<pz{{!Tr$pYmsQ9aDj^FBmj
    TP\a]R1;]w7i3[kswr3\B2'r2k$w=RHV=io<Q{\Vju2Gf[W<?:ERzv<G'i^zpR]U\{s?o2?}=YaU
    C7'~T!Ark[&1,C^!$Ym1%lY}K{C5VEOmmjlKrdsanWcYaK3,me2^o$jH<5$a*>#x[\w;}p]:,7Rs
    I{TXW+Y3ZxA#@{A~B~G,51=\-z22fo!$~{7I[MIT;nEwo$c-a}CQx$'dQ2pev\ACV3I5I$$eaYYD
    GOZQV;<Vv;*!,D-eH_s]WxZBE_G#B!2x-=T=fTH+~G<O2~YjkRmZv3B^plHZ?-wm!{eVT7EQEXOR
    i!n!Qvn5_IkH$_~Ex._Cvp1e[=i'uovj+p1<lQQVD1eR-AeD[K[KxQoX^Oa[Tx4|OoXxx2A@Qa!Y
    g1W=~E*BwlEj1>jnC5$r;]{QJB>ZD*;uYGR7@}QwC=mlA/GC>T~e{lCivv_CRj^9?ooozoRz|V2K
    !RR<ROVQxJU}TB*Asne}A-ET,k^>uxHn,YI7=CeZzynx#j-GWuE*k1~U''^u~aiaX!\=@Va$YvB{
    _w$Vn~|.G>X7[-^73=je[3-x~xZBv_v=1zs+vZx^ewD]D?B*a{C[~[ko7E2?1HRip=TEapjrU{_j
    IC-@v5'@}JKAxM\u]2EA\\yWU3['s<~U-jJh3X!D"r]K*i1DVI@-5Do{==mp@h-C=]!eQ5#*\seK
    TT%^.|Xza?X5(^;{xz_-vz7V]G#1Z]v#k1*<pZYP1eHpTpvjR2m=1^_+_vsH\W>KVo~7.[!{I];I
    C=5@s,_~Xlx<I;HjkEkm*qlkwvQW^ICjKvp'C@>nQz__kDE5G$W1~e]\;VlZ!@V}p]~n{Yn[?Wr[
    \x+xH<P!wYHQ@j]9OAupHIEu5kw3BRCa^b)P5OOpy^5nRo_vVQ+GYz!T'XVD2!XC,I?{RHa=^O3_
    X51,',xZ{CsV,Dm3e7x2Cx@<I5o{2waD;coB-+r[iZ:v3nlvOzm=XG2Qu2^L.GZBG(#+X$5GX~Q>
    n\}5Z;m51jmHJ^p.Fs3XC@T<}UeZG]@<?-_;K[sA~WT;5>1DmTrQCVwI5x?53T[5!zm[}}Tjr$?K
    w^~}jAC^ZRiX$=!'*'C3?(2'szAX~Ug*A1K{]pE=QVY|<'rJA>pv@RV$\r+Ze7kno~<GRR_xWDnI
    wTGHQ!lA$zT13EBIm{771k<BuEnR7^>R*}+a;j~$TlB?K_HDB<3pjGZEci$![G?\YVA-Z@_lX#HJ
    wpu~sTXjTUlm_qa*'@*\s=*<=^=-{#Uw]KpeIXGAE#E1Tk${u\{wG7]]U3Rk@BS,=o;]]{sS_l[W
    1sjW\iKV*B=Wz]p~sAXYRYovg12'mqvQ?xj{UA->}^9Ru*!B5}AI5zon=@lwDorZ[nVC#1Jx5DHV
    ?T[N\\<E<puV5DvUxT}A{BUJm\~,@[VBnUxXOs-s*v77xJ\Y#s!oy]ATwi+zB\2DXDCo!O1#UI=p
    ;px7wB~Gw-IiWr'wO1GW<^Hzx]X<WAqKe;^$7]EkUj#pj5J=?xo*+XYE'Dr{Cx_Vuu;6pe;wIEpQ
    H]D#~TV[}#u?4YHworAI}1{p@Y!z1Ion],u7I'BRC3BOXl,_mi$@#^mD@e?R_R~<l_1+W!D|"BRG
    ~<11J.eC*x15Z=DXQ7GJpz?r1,~C^$@+T1h6LO5>JoOnnUX+WIYVooXUzAQxE$!,Y-I,@\!@K3sj
    -w\zaa<@TURVsu$m<ssAC3]3wI}Oz7Ep;e<DeVl'<RenV>,V2+*{!_o]_U*33A[u\pg>Q*viH$\x
    Oe5HCY7kE;AxjiD)TGJUz[n]m-G['mj\]*W_d7=?}+S8_e?v7_!R
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#Y>VQ^D>3xEYJO1'^>rX[S=G;ZC\>s@7p[*;Jw7Z[r?Ao!Q,z7k-<^_=+$yuB^;CW@V[!<IF
    wD@sqC|z3C[EfkQ'Be-e]iXa[zx^QI}VCX$sD72[lNW'#Zz,C!q$H7pz{{,azT\Q_+$wt-=][^;{
    Am^[!RRu$7\$?[viro*ipsY?Y!H=_lO35vO*XQQ{?Zs*k>OU{lvGs{lA*5,?[(@DpJT,!#W]<}+D
    _HH{]JY*=!'^D,eU2^GKIYdIuQ2J$]2[;5{>_ZGD3OB(Dw2~\v,rnwen%^a1Oa^~OHD!xBmQ<\oe
    32*~YBr<m3VKW[+IauXKYi<HOWB3AizJj@OK{m'H;?DRJBJz*+Umje],XRL;sm{OzBl1,z^IpQQ<
    j+kx?>Gj1w*p}I>pBxwo-Q~R#o~,i*KY[eU/1YpOCu'oXC<\1?p3w1+Wu]ix}2Gjt[oG+O.u'_ZK
    _!aB$KKvB3n,#KThD<<l7~'pL<,=?_nUB![;jD~Ia&V!RHUn;Z-[uZfr>TRs$n#A5]@kpVGol#]k
    'A*t;'X1pCbiz;pl-w\xKn,$?pXsk7@G7psI?FQzmC'r[wHn1xv3@j,AKk-YGK]^Z2[ZK3;Op@-1
    X;rw$*PwYmvW-m>3Q5zCoQXw<!U'~Km$vBTH{s}eI!=sEQ2|E\n=5TH1!]@1QRJ^]R{?YD_$(#*o
    UFp\v-Cav*z=T@ar-VUBaTrK]k]G7+{o{HBpsJ%%r^\W@*xDW-[D^<X7rYxv7@vpQ5\G$>w?sr+,
    a}GHwDn}Hp+K6=1-AY!1l\r1s+CRi7=!IA7?'''+o=$_E1<[l9={-Y7$X55}[!A[V@iIH7d~U'na
    sO{n7m-J1nua$5IJ1{lAa;w_Ewzqka!z2qpe<k7vzuRu'we-1e5rK#wO$vIkOWVe?T}3z[R$Y3O>
    x<Vu{OIIQuGRxo<_UH^v!58<{[OIlslUo@~d#x>![zH1mUQ_(uXJ~#wu>kOna=oGr_2;OZ]H]/}#
    oZ5a7!F71esKUC_,zkr=zexRCC{Lf^jKHs;I]sm7>oU2_E\*=ji-]9V*JE^u;Dq}\A<g}R3Z=7ok
    RXQT'@-C7OKJj!p?rmwpB@A<iX@'WaBZ?Tu3i\*aUR~UFY.*\~_OK<2*#@s:5k'_1<<KVu@Km>RO
    r^Z>YTCiUaj-wUAoiOu!1J}?XHQ$']~?)H7{WI~2IesTw=mIzIKz#F/1xE2,Z53^BQ{%42}_]"a{
    jsHHv~zI$]@-+rL\iaGR2Z~2'_l<\1s!1X2RI.fv?$?-\XKK7AnZw^3B2eOq[_5eo_V!DOGHX*2v
    sjn^G~!>|\^Jj%xm}~Gx*!B_R3oZD7IaQWY7l2[;VkCD?',uG1is{xvw->GnoJNap#A=A{ae>B}w
    Uz@XsTKU\*T{Qp]!rE=3<Ore0RQOW$I@<kUlj?=![-A!2O$owqmr@agC;e}OTE]F~C+?!anaZ1Cx
    1ana3[Tn$!=\^\+]zrlWOJX[2=oV[DRoH7#=*zYouRQ\lrx@9=v@A+a>I^W,VVQmB0rY,-h?V,Jn
    AY$r+}>@wKAHAe{KVXxZ*$kO1o[}l12@T5{(}(^i\AAxl<]kuBU7{Gvi^$]2Z$wY[3?lkD;_>#/>
    A^<Ia<=[KR-6<}CvoJZ[G;A,sRo5K}{@Y@5s7\O7>+1,kzwv7rEC$Hj^>$,C12@GR@7wwQZ;*GIe
    |rlwQ;GZ}:R,wpBDK<u[Q5j<sY<,Ensr@s^32HBUr!_@luC]>Z2\nBs>T\MTw}x5awp{}xO;E,K3
    ^xOu><Ca=Amp@HG21_Y}YTj7-1}$p}r^7v;I*sR^>r+wR7C2v$Ag7!U'Tlir=BmxM-zllyC_*U-}
    ?#v^vu_3-XCrk~*?rRzjj@[Gm5TC$<lFr3Qo7k>QxG*Xj'$esExT>AYn7=#a~AxI!>zJR@*ex@Dn
    mQVB2Apnu><lI-D[L@UG]OV{1Em;u(V[a{}muY4,}rR_$X,gSJT'ngCk<o]EomlRTwO5En^{Xkrj
    Y_B_BV3n<ka{KC>}{]/lZQKVil3:>[a>x+1$w,zOVHBBe?;=^WzmGV$?p,@\2=pC;l]rm5Tk%Zp{
    1M1m}mtr$QXM/oZ}Ci\_Db!<D*A}[Gx?oxw^;$cI!DTp?W3[z,v1REko*jZl+Xa,w5!DI<=[I_'w
    =;7_je>vj+oHVw+$T^v]^-BXIXY5X>'[_@E\iw]E*,w~{]n>YGo^s\<Q{R37}><]QVK1EXR2lX#[
    Ke$#op#YXps+DaEw_7j~B<pvnRm}w{'T*A$s!_~\UUsvC7AA'W~[m$@eiJ=D>Q1>E{5=T+nC-Bp$
    2]R-]Tw2Y+3A\Cx[sXsux~A3T;;7QVB^^1aCxA,s_VH8]2BY/~*[TBT@+@aC\QOl@Eu3aYrXH']T
    pq,<sr\VaQr*+'HH>zV=VC8{VXork2vBsi'Z5+?[#>Xnw@'RVT$(DHROjrT}BQwr~IO-w]~E-{+B
    nAp'5Ko][YIAV3D3*7Up_vaR>rrw?Cq7r<sin}^GT+zX_G=sj]DYK3-}O_z-+pEVk\QT+Ax5jkl#
    [mXn-Xm_^>GRs;7QR;1z[*kGkJ{FipxW_vW1n[D!^1r[1{zz+BEw*T5u1$UDwB[H(=$<Ak[-#%Y7
    'kH[+Tc'}*2oBr,o,Y@1!,zI@];_B51[#!odzi1]n*Jn#n'o$m5UI\;2PIj#BK+RiV,J{1!\1k}E
    }R2B#A915Eo{+J<Y7e]i]le?1Do[Ko>IYIe2D-xY1$GovJECk]B~E[GCsE^s0-Xm<v'Vuv;RorYJ
    ~1}Q[1V\vVouAj*In$jB77Jx^=ZsG^\]GoI-m'mw'$1x[!{<rr!v_l]n~9ij?<XXODzROo]wACQ8
    9}_<D]*o>RmTQZje;$3O~$X\I6']j#[Kow*IGeg@p2CJ{u$>r$U!ToYzl'E_zU=eAop",C*ir,}B
    1kvjBQO~l'[m}+VH;53z^\{a>{mBVxKE5e*_UjwB1H'eWGU'Qp!C#]X}ez<wRTvr$I5<KAT+x'$}
    #xa-{QVvvW\G1^>]AeRo$K~xJx5-lIV+p';D-GCwNnECvEr\xpZ$!k<{[#j1RVTaKRi^Riw>+zmD
    lPH>VXseDiK^@Qv}515>VpjUlQHDoQv[<O=X}TD;Ga,ZalHn[l+5p<XxJ;rDXzipA}WjY~o3,xdC
    kjDl+W;_T-2_=7'\]Hzj3*C0li[^l{=X6I}RHapDn,*o\CWe@DUw]xlED[z*?)l^aA3-]w^WlBlZ
    w<E~~=5#$R~5u<*sOBkAv~|B?<srZC7^X-\BnE[1;\pQQ<<eWIn!T}W!,,mE-^[-=aD{<K[v#^Z7
    =$A7C+XUe<@x,_3a]<!WH$$2_!\92}QAo@eEme?5VwJa8Dw{n{EH$1AQQT>}n[nDAQl1['CjmHsQ
    atWrlC{}Q;,+CZvw7]]!Kv'TXB2VI$Z'eam5mem*^B>,kQppxRHnJeH-mQvrVB7{=ue|&U<[eaUX
    {]Av!7m*5#Cr!D$Q2/=+1}7A1UBxHwZ*K},!wni[*z[lYOEx,WQJ!WYDTx8{qAzKKqu*ilo{u@L=
    piYmA\@QHp;s7vCj^IpedIrv-.1}To-jQm}uWAkUuAGC5^X&s77!,tk_vwOJ2Q2{^<!X,53'@a~{
    }'Zr?D#7r>B+u',AW{ajZ1e_TlHXT7I5$pj'@CspR-<}*o>s#Th'TJj7OAHo$GIorE2,5u+(-E2e
    h;vK!vKQ@l=RJBOD>5(zI3HB^^*~7$\kRN'MFoWX;]^2mZ=+_!j%E~x2w*ml\[
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#g5{~aM_a!uqBA<K7\QeB{AR\l]H=je1=?o\'F"5\;}m=z+VHWr;BV[9BoA{\RIo}xl;=u2J
    %<RA@@D=pgaap<+1$'Q,AmOoXA31,['>Kj^-1V-]3]_7[{jx;]}jpm_Vxi]}?_G,'>?rTvwt*?ZJ
    E_w?Y,AIxQepzzozkjH@k,1Txk3$Z$>*[!~o^z*rsmDZTeQG)rxnz']l}YsUZv~s*zzE~flz>sL+
    QnWnsrousY'Vr#BG1r[X\QTPmour[k1RrVKoU{<AK'T\\@{GnA1rpV*Ki17H]+uCI^{!bA*5~Ql-
    @C<o#HsTD,oWu3$vZ\qeD+_}OQ$IOHeR!Oko*Isj#DHa}!T>DRCrT5kVrkJnrpj[D!Y=5WBlW\a7
    UVex^A2WvBuZ[j]*v#*]$wZ=n^@BQ-}x~oUB$+5xiB{1{K#a]!3,[!j*{e-AAW+^+sisu,]<BpGA
    a;;25jxjWm{;DC;YnzzleCW^j]pjWWGYW+U$D{7,=lTB7u?C]i@Kok$a+Q@rQ,l'i,$w-akRm+E.
    +5-xzH1u;5}5=uRuh"3zG=-oEJ*EZWEizC\Wo1H]YkEQvi>a[\e;TU#vK!@EBV%eB&lI~v.][U]7
    =*T;wzRW5pE*-7avwl=Dn@V='><xI\lzHC,$_pj'DnD-AjiX1->;UA?7OZ~RQ,[O=Aeq-U>u^>sR
    A5TE?UuV;DH?re?I#$anWvip[-KBUw~?>oRwr#\ooAIH$B]r5Iup{z5!nj<-k[2}Xr<nxl2@,x{B
    A{p1,;z[lVsOoQK^qez2Bl];C{RT~Zn;pw<Ue^>}\*oT'lJUYv3\$6w{!>cf5vmCj1UBp3]OGx+l
    5GHG1+\mQ{vKo}R$TrZ;#5*~Z]Tm6Em@CQVXxuX~vO(SQXjzr2nVCHv@|'DGCZAG31>>K\EB+'mR
    K3sa~.Dl_@C5BQw^#ai=1C$p1]uYB_YQrDHea_z^nV>${}>l!p$]+$^I<w,VrDm><+GBr++oBl'G
    T$]z3sphl;_3'EY!A^[B*wu,T}\U3V'55kI]!xWIl!A;)DA;,bsY;GG<j;r{v]TU.jbBT}-#>E}r
    JH<--mk!1Oo=GZeK[1\|xKZe.7ZYmTxX15A2TI+=rlnO;cI{B5-O~+Uek[l2wKRjQuOR-<J{}TH]
    {BpsO1_Y>[-Iwjq0.-7&Wj-p%b!o!K[75-\\Ok?=Gm<EZ_\rKou{ouysn]Z-7CU*?_=h[Vxw#>uu
    7~Yip13z7k!;!_G$p'X?nxlHe$Jz$>CD>YE-z-TGF(uoT!MsH<;qv=G!=r=JuVwObno{=aoR[jk+
    ?+zDw}zsD>$;zDHWV)WDiY[j{[<=RQGp_IV_<^wwO1eX7Z,,,U8BprC-X7k2B5n7]pZexruI$7u]
    oX3BvRUD*_7mI!}V5{1:'~DD;T51V{<H_@+k[AHQR-oD[<{x*?Jk_w7OHB']!+$T]2}]]vzXqE8z
    Dj#YHmYT7EUrO-<3A+U^aUa*pEOkwQY!BI*0x1A?o\ekGVR>IY=o[<Xj,vRupz7~A*=R~s1]JD-J
    IDYRGzkQWD+#]oBe[_jJP*v~_k{{-g2D3~I1esEBA]7wzBG]5v}kn-hT>B[GXCpBkpi3>o\$=UpO
    ^O+Ar~oaD,'*+s';BQ?vUOvk$-a_js}@wzmtaw,WInn2IQ!I@lKm;'?YMu$$}sG<vkXY1V$x7iTD
    nsC;?3v@\Tz*A5$smO;*k}\^XBD7m^Hj]JQxHCQDE2Y?^[H{o$;v+Jpz]jwoQ5zY^G#xQ,'oVl_X
    ZO$J$wo,D^r[-Rz{l5{r^G['JFk=}7p][i*vi<XO7W&O{R5x]]w5\1l7wA,/T1^;DaD;boy:vv+R
    eiAT>5UEsD>u-'2vGkJU81BAp.p@[ZMxe?*"nBK_h;\2-A}[=F3E*Gm,]{/i\QBRYv5A-KEAO[ur
    _j5B'WT[T*{j{5jeE7<'T'wxYJzO<D]lJGD#+-KR#BU1Eik>R_CAzGWY2D>4>\<,T<CH]Ek-'@H$
    _-2'lXK]1p-r<[]_!-K;!vrn5oJp!7a_wa3YG{KBT+Gno\lj<,_s\'{BRzsoH\-K=v,AQ==@l=ko
    =!XVwe_D3p+we{~^Xr;2U,];8QK\-[=$~{\Z^j''AQ3RnY[\-aOJHGCWDI;EEYW^I2BYHZYxXy|o
    ^KoTXnjcs2$;vTY,Xne74Y#1$vGI{:R+X^LaQu1RD]#_w\C,-a[Y2uJ]_uEQam~DEn21G>TruwI*
    W~o)OBQmsAa]x@OV/CsW+}B\rQ+}*=xUK'wDOG;uWqC^?OkUW$3V#w!a72lIDXy_{2$w_ae[(Kj#
    a6z\'KC>7!W7{A6i_?1#I7~Q_pexrV@GU{'!VnwHEVTvY2<Emjo=li^zke}OG+jYpT5N3>}wv$#!
    J^$Op~T<^-1[?VQ$JDEA][A!lnAKz\m\!R-G(FE;V]W'X]oJ\v-OH7R;Z*"m=k'GG_H},j}HGl1d
    X']$<-I*e!sBE3aEZX$<v_WrxupEVQ!?C'Dxev~p\@>uzunWmzK}KawKi'-]3A>W*'ao's^H1VB2
    -o[3jnH5>n$us7IT81ZeYuVsW5;m#O7__<eUp%p}_nfCHzxA5j1'W-O/*<]k~QXr,#OV#5\a$5w#
    WTO3}1I7dna$,R~xW2A2QY,veT5BQ#[B3[<u5!suUp;^Qw<I;ulxC3B2XOVV{uBTk'AR'2BeO];3
    xCmnQRXl@4j=WlvsK5-VE@7;BD'7A[sU*$o'Z#C_H\,z~TRMWG\jZ=1Z+nA?U<w>v]HTR]-''a5^
    A+<uN#5VQeAw\^KE3ZH='8w1T{~T<$B\Q5I{n^Dj1GOA>_2=Tp4O2^2mI!<i-H<FQvk3l,I-Y>lE
    UY1v<$T2\$2s+\C;}QQ_*7mo={B>jQB@,m7ksR[\_J[sg>>*Zv['-nR}!v;wnlY]RInXZCs>1BQZ
    3Q\I*3-]*b*eJ\^~p2r3lDxO@_ETDZ\;J7"T<j,D@*X84[mz2nET~*@<1^_+]KA+ws$H{#Ep=1KW
    !ouYW!VT5,>!Jx0VCo{V0x$[VXG<vXs$VEC]\aVpK0mxUHExT5nX@I^O[QNnGl;z]IrO+oC=xeKG
    E;^YvQQ^x2upIrns[**ZDqVe,[jwwI|Uw}5o6CA1C1T^mIHo^'<OmCap~,RNpW~m[,n=_5<ns-vO
    e>V-I\7Ye3CAp!'a\7^Xr]Q{pH_n\A@~7}eHIxaG$}~@E;'^;TEXZ\{Kx_,;~{V}?DRjru{Gm<v2
    _K5<zH3_2}Rlp5z@?RVs1+l>y{tg{[HKw${V^C?_VXl;s-w_eeY${{{>SCiwecO^A!FC+uYLzxer
    +}QK'Zz+R}2ev+\auR=l?wHa{}W+mE^mwr*iO>]?L-Bj'$5CuUpYpX[=;z?WKsU_zYZp<2^Wx7T]
    mvk2<}^TVCUA7[QVm;[leHG2>\+=;v;j=g'3<^\27^,B,jQT+,v;XCNXr7eZAlob>1@+X$vO@s$,
    z+CUn'AXQ>2Gmnn}Q3-I7I[Z'@x+L2<lkz2VU1RBwY-rX>^<J]!~AyFxTrnn<C;pIavuj,XG[<Uq
    /7\QupGvUGBo7k1{Iv@]@)t*1iY[$iz'oWjz_$,ka]$HImp1nno~]]Xo-ll8W*l~He-H)J[BC^#\
    DOsHXjDVX7#IQ%!wT$j1paOR-@jGsXkr$uI[X[QzJOQR>*J\BuerVVpnW{TR=5P_rlvj7k{,-eB7
    ealWR\,B_]mp5=}@<C=1uK=*xpTWoi[7OW?Z$j[^[kwJXm?#waxTGU2lBu]Hx1=Y[YH7m7U%-p?Q
    iG3vCjw5DG,+\TCrxaGIQs'\3TCH2U{#s7pX;AJIxX=2Z]Ea.TzX<R1+p8p3}Q^j#Q>j5JyMK1G~
    to]^,,^JEzapWl-2lmTpl6E>>nKXX[;,AY[Q'udV[@Bv<WR@_e5ETCJf~'HDv?XD1}-wEa<uW<7$
    I5A}~vIC2]?eBr~Hj"q5W^wmzJ12=\Jl[]OmnuKTAppJEVxT<><1uYG1C-1Ho+R;{3le'5sGwY!_
    \AmaV><1al-7GT*<5ex\Ql]h<=G$BC=VZY#w,1Rj{['vB7<#~Y\R{sjC_,3_"WCT@E~j#7omaW\E
    lE{EveR5T#eO?V[okXGV^FzJG\[E,?~Gsw'~]oOvwwnHxDeZTV*2jll>2jF$O=]=ODY^7nR@ll=D
    1i'+v!s1&W$]vp_\B>Uu2deY~jIk}ni{nXD'#CCCT-E+DQ3G@2AvzU1UW5Y;Y;V$;o7wnEG7r$\U
    A#uT__KC=Vl~^*;[o@kp2Cg9\1XXOTYOl'V7^KGw-n[wC3\u7BZoXOvXH5_ER#;}z!Za[AzeZ}$W
    +]zzH=}xeV2@dQ_n~0kU;{ROH}vBi$(Y7e2r,C3;,CaVGk2mBi_]-,K~Du<z,>{I={@@z3{IOI!*
    2QlICueTTKXBwZ}Y}Go/EQ#~x7!\CGeER#Ts\AlQIj~~xWwp#'TCH1'{lF3'_*<VT*r\i,;5l1x=
    J@erlXO2JV!Q3RIY*-[s[k!YmDso$R!X7GkXT'6'olxP1>$T$D-u]BO7&Z5m=@+@@6*V#3$*kl@*
    a*AO+Y5z-*+lxTX-J'Un-;l+O#$QYU^1~{ZtuAo*Y$3Tl5swio*^=<*{kGued0xnl$7OCsGY>uIu
    *!vsn-IXz#JEk';AA]-1QUO#5XvVa*vv$OFO_17Dpo>>VR~ZSon+}<{laJpv$v;5o,v<nmXP>\EE
    \+~?w{ZvvTRpr-*u=3pY"X=k<tqIiA+u=2<x}p$un=-^;ZTvwEWzuH!2UW;mG@ZH62hp;A'aR<sV
    7BjbiO<n=]<zoHQ~Ez5-jR!E$n\B9UCx3As=QR2=Gj2,ZOWD${1jRz+2KHj^2ID-vGsDZl@<1g>O
    m{*2IX#pkWp$xeG]\]7Wp+ZaQsZ>$W~R>^{=IYDmR@Vam[72I~mBpj2UrHDpe'rWEG$#;jA[3RvA
    \Ozk[uE3mu%BVDJ=A;K_@p\h,TaX|5RDDE5vwAUXe7i{'K'in_zal9b/I]7^41<A~]BjR#+WwAU5
    pZz-#,n~V>rX5?CBn_}EAwp2e7*W>\'ACvkH,8b17E3uVHG'Ia5[W]]51W1Vj@]C-rpQmX@o#$^g
    HB*,V}al6d'YXevW*A,{=XpBpT$z<1@7$X8*!X},aV;wzn3rO\1$]E7h%H*J{gUXAXG,~p=TjH'>
    +3n$,x!p5p3__36wEk+Z\=[~7]RJ]!#V3_*+X~nJ-V'l'5HM^?IQYSF-{l]%@G,=#BZwG;Yr-677
    7Oa{Amv-uVrJW,E@zx-A]7K+^7!$j~G_IW}VG-jum!I3<w}#R=s3YD%Krl}qG!pEQAKI]_ICNMKw
    n3rk}-Ov2C^AsJrUe@I\n@VC5\H]}Z*,p^zD\7E]~E<*rDeHw!B>~@ks^@%m-u?6D!=_T_D{R$lE
    ss{X#jozE_#2o!;AKjpx%1HEv^=55*[KjkBa!+j>}V7T-x}*voTQTHv]5eE?sizu{lz^jOWv[A]X
    o!ax~uls7zn\[r;Q-OE[n2DBI1ZCDRs;$@Y=DG?T,k{w\Iao53A_U~X_X^-U5K\}3Z6DUHD$,E_V
    HYo6yo2x~'@+mp3\oH_nIC}']7o~Vt'e;RO_pHUE#GRO_Ga,r5x$B~RZo'(+Q{1x1[mGuAR$nlil
    ;+w}'Z$3\KvVQ\\-j1lQZ;^9pp*}V1x'z=J-QuA>>GejF,pKx?*Cl,i_XRlH3=izTnlW^kH]s\vk
    Cb;,<l,21nz>[IwpKG#w^X4bC!@=*!{-:p$_C7]KV}o#CVD~zw^XB<>aosB1jp'3AEC5X[jYT^=+
    ;^a<r{lVv\w1Wo+_JQB2~J$\iu$CrJRI>U[o}@B*noj;v(v73Q?]lHCJ<aIr,,K1's2B[{fkD[xD
    K-xT}<O0O/+_s'%CHH_r}is<Qw?Dl}]-BA**#~DsH!Kv?Gi0y]1DIxr_@RGYJ\5IwDT7Bl_WK-DX
    +9~tX]]EYRu@vwCu\eI3nRxB[+*HaA_;!Ri=~,A=J>zC;pe>mnsRc@T$lz]7<UrJRDE+VHUmZ_v~
    TUIpG~V<sRY!!rw>Kv!lCoU';7Q4'[AQI~,uX+xOVHp<Te+$V@jn/JsR}siBkkH^G+'j$mwW^bPh
    ?Ul2jA-wG?_Wnoekp5\Y;xj,oCu];eKkgXU^I'p\,1KXW^O^5?>,,JO,[.qO,VIW9D_*n#aw}KCE
    J'%i>Y7QC2,BKX_'G>pi+aHo+uv2'YI#Q~3e=AE|3AKs3CZ+53@j'G$>>zIkHn@nA{zG'2!#sJ=<
    ^kCBX^+vvZZuI,ECp1{eBR_,.5es^u^!ey@H7XW=UE6sCn;;r*@DE{UvWH]/QBk'"xH'x=;3{H}}
    [Y,K<aIzT$=Jefzn*nvWs-oBsWz1up{n22q$2uQC@Joe1?YvRm2*!r+l?OY'5Tlg^-<^ok7@WDX3
    Y7YZrowUERVu=8G$r,'jHjXw12,TBk7o>HrWGs7Hj@v<OpO}-\1vI].ExXX$U~rpkG=IG*_mOOG?
    <Tszl'ZO!>Z;oA7|Vn2>O1wUvk$aW+{X2Xx;Ou{OV2'Z]r-rm_s~^1kvl[km,X<-Jr*3K+}D,HT$
    C\G#=A25O;ZY*GJ-uAjVIo^YeD;r1Kpa2X;,w[n{_UZB-O}?#wExG+oj_j*W#COIpM;^i{AwlQh@
    QlJ~Xlm%7wXRBI?-VXrJmU_#y^@E_7'omP^vA~]U[rvpB[lOpjaE?7r_!CI]nk*7E\$k^*O-*z3\
    RR\nEO^tK<R{V]=HKoXT[%XX$?cIB_UJ[pDRi~^YJ@?JGlp7z<TOI3R^+V3+ET^R'TXs3KD.9!lm
    !{'H,=k5V=2=U!U{!lXmze;wrBoawDE*~Fm$Qe5_UGe8\wn#993-KxUeG'>Q#T'Q]Rxx^RYW<'v_
    x@vm3YEz^<WXA#37$?Rx,K?]}G@,gK=_?IGjl*Xnva1[I3^D74_zz@=w^EQAzI5_{-c\2viIo#{z
    =Jz>[nam=jkl'#2O-zI@T=pcOx-3u5>nOvX{Yq<e_H>pcE3J{9s?a=#Hl#iRX7TYOiR3{@7m!_o}
    5RXw[HEalR=D,xw}\1s=>}s6OjkO7ZwH~-s]mHGrd=@BZTrkK[IJ}.eB^]?+;X>,-H][JB-H*uD-
    pJ^$x2&vf%}sJWY[mBE,wE'ZQsoO7u@T!*;IW~BZZ!>X'Z-17oL#XG'*_O]~1nj~,O*]&F}H$B9*
    [B99KU-2x,TnHU5ioaAV7,m3?Be5s<IY;jU@o{jo^+7!K]7]QwX_AI_Ju>>2y3QT[l<mA~7DK7Vv
    ^7,uw_I]]ZRT->_1ms!3BB+mzAn-,B\\x~\zQs>VH$wWJU1^~;5<^qKOHG-IR^l'DsAUX[uz]<Hz
    RuaI;a8#\A,5H5uI@l@3-ae_!GiZX$7vj?{3G{z6BUvA-5\JTp_m(v@;x[]'<#x@D'<-jKIn,_{r
    JrOj+3-KwolHTjWYErnQCouIUIl*pZ[R*mAomCT\p5a,G}ZlXs;!{_jTW#\m@C!vz}-wZ;E]vA-T
    3^#7D{a=Baj1v;Y,i'#E]$~$kn$j];5Q~\E3AyCw>@};e#1;[os^mp{ej{]GU#IrU2rz__BT;B.'
    -A5,e'?77V]UA_[<DreQ5@<i+T,bxlZxA1Dld5,v@RCz\Yrm',GAu@DV?[;K*]{v_C\<GyV2DV^x
    vIzaEw,CUA#nuQuD!W#=Q$e-W[d3C=C}u<R3w1He3];A_@zh1H@ZuOZ+i\x-J{Q$'}[>G7Hw$Dom
    9@Css*lQz1W-V$sj]VoTWYep^!x$A[G*1*'}i55]<C3<*G<B5,TV{y'TU@eRvH=uo?*7}RD;!
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#b,xsV#p<><*~<i[}GZXIYKl<kY=G-%u&:"^"[up~N#R^,!Q;=v;~TZsp7CEO!lA3W\<2Z,*
    ZOcX7+2U{){Ck;G$k\s{ErsSKwJUz^;};<VnzxCXX{Dwo*ap0&HU;z#GwY'a_V$@BZI=k$C[C<v]
    CQG#Dmxi;kJRWma7C=wn*B_kl@EpH{!CGll95wszTr~Ri[l?7rDX2{'<RG{@UNjam{hr+sZr=lAl
    j\ol_TkxaJ[zn\_pkDKs[;ZQ}?pXjBXp[5X1*iC&>$+exGKBe?E$kwY=5IO~#$,\W{s<''R1w{Ih
    C<@$Usp\HoX}UOxlJ*A1tAx7U}mAO~+1}KXC3P{w1ep-@2,x-J?a}xQ1G2i}Z=3$xpZ$JI>$nZ]K
    A'GMaaovOoz[}<@G\Y_w[[AmCE\mG;52rIY5*!75Z_?CjOw>TG[sEgaA>[Zv2B"7zGXz_Y;b[[1e
    rj?!_EB{=pZC-{z*X<{Z'jX{+<7J]w-X-UUUTU[@Aj!^#{'sf~X3Hhbi5z@GpwGsoTm[}=D7xpum
    n-ekrsljH3~5BHe5_#-Y$Dzx!Q<;G}C.<>m*rD?*]m]l3{oD!EsB@'B~{}=,8C!7#Z->_FR?'+NJ
    r^Dsz*j6|~wAWN=koKE<1\Tj]z_z--^}-p,3Bl'm'JUYO?Dr,IvxGG[<r+tw{<ku*t[i}Z+Q?1o^
    iGYxl^CHlY=UArmapG~A$U}"Cx?Q"l][!8{XezGW$vs~I@=a~{xJ}x+wJ;C;Tsk$Ve}uX+>R]+|z
    5jYZRwYR!72x3z,[jV}XsTTT+=k{{{E?pl!BOT!b@Oo[n][3Y~7a?li}M\&pk<wwT*-1|TxU73{w
    xYe~j]VCQm[m\^osmQC^]2{Kv\ms]ZHTDDD]#Zw'uV#{n|7>BOwV9t/hxp]jlIXv?jBCl+=+rTKG
    ?*}#\rzX7C~vl^}E=WeYP@DBIjI{B3REvy[(onv;>TY+s~owA*~@KT1i={_2z#v[zdCo>zG%)1Jw
    {l1{knG1uQDv1,CH7#zJY2jA>j#@5Z^J]s'B*5Yo5*9]z}vBTs>,s@rzrPvZYoR[zCgr'K1<r#GV
    ]5ixnOEIGOiozE=(5,v#\m{np"xERv*2AZ?T;*C'E>5,+cjXHp~<H[^ZKO{*z#GZU#pT1iGm>Ab!
    lE!l3WAO}uR!Bp;peoulKr,u1+?[v]K}GV7Yn1pT['ke?p5kaVr"lj+\1>+=>[==WnD@e[\Jo^JX
    $KA=jz22Z$*Yp.,)l2GW!p_GcD;RTr*-7$nTvHp;Q<$<>Vwn2kXH~'\JeyK=}R2sujW5As@_w+lW
    ~Hv;,Y=]Y]M;CK23QR#\aooA,5RcO=u#-]-*2'r3[<1e{=~+1f7[VU3<-@f8f)o?]pWqyrQ=+n^2
    13w\BojZAw_os{sa2vwQ^qrB7sjx[#WY7>l1X_8_^'56x?IvH^D@gno+W@w*1qp*GG4tXnZBom5R
    6v_KB=OEn,5i;s_jj|eRu#Jw*WoTe5+sIK=CX5C@OTBx#JHzsJKeUx6e1;5ADC<p2;uZ=#H?+Kp5
    7RV[BQJO>!'YI2uCGDG.bNr,aQinlavu,@T{+=pWX{6lsBXj?'O\',OHG!kZ1\';DO+}k*QV{3,A
    rZZU=js!(JB_I'eOuxGm36<Hn{<X]A,5DnVpZ{5OIVR7Xla-w<oCXoor~37twV~1ipU'T<r735-v
    D;Ons_OOR{=w+$!WE#$p{I]B2=lrD\kUV[w#!vox5On-&2zez3Di2e^]>x2]DgFYWW@S?DiO-XpW
    [[ivX*<K[~Ek5u5EY[u#JUl,QU[;Iu}wOe^AAVG+yT*a=*><;EDJBR!B52Y+_$jE3A5um_CnjT*K
    wwol3m<l+-*p=]*nw=],{zEYzG<wacA^v2*kEkV>lO<D~WQIV,_-$XrY!CyDCY*XnQ'DrI5"7EH,
    ~{$Ho\=5%w<+1$3wJG;owpWV>-l?];YY}jO?DrVY[D{JUH[GnWjvm^m*}?B]o~}Bi$,_uz7J];U$
    #jA\iy3RUxB7^IwV{{X>E5:wDJuE;D3_J+J07r;;sAcEvBE+q!E]}C2YuvWZI|}yQ]kKP;7n;$+=
    i$WxkG<]5!<
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#L<GW^61$jiE;o$EZ2T6o{rTi}smT<xEF:"^"[u2EN#RE?3Qu=75!j's?![_TTeH=>yAoIeA
    5OT<'k$_rPlwJ#*+[[o_n=['CW!Ul$HUlIH-BA=O#QET}7B3pZ~'#,o/s?~*@<DTber}=7VB^=@m
    +p5BQkYQZ+XT^w=#_iT2}-rQzQ5!^P}wl2/a{=i<s3?H><>B,?A;=xDKI5Inxw*^H~1$2$-nrOIo
    @>aX=?$wa5a)jOek1Y5w=JE=1mf2>+zCkeGH$J\=<<Wo1!Xvj7v$JJ\uOT_o;n#PEaH~|]J*,B<I
    ;lQ$e};;;DDjs(kY<#C;mIHoUGKnm,rw!$J[Qu*%IX,OBXEEw+x2X]DsjIT-(>=-JA{~!>VzjnXJ
    ]\@e@{$s=A$@Z]KA'GMaaovOoz=TXHY2YA<s-em-=EUOBY?OnCV[XIY$J$[sj1~_+$3xurQ"iv##
    F<'r7;ji,^Gm}'WBeL{slZ,^?pr'a;ws'G?$HUl-1Or<n3|.6gEm*~I_xo=_$-%!Bnx[iuTUUY=x
    U[;^D\eD!nK!*sT3-llR7-e73av$G3]x;$Vz={UQ$-W,Vx=wAsB*DB@Bi<Ez_eQZU,JTlkG>Tn!e
    K3Wv~r<w$}eU-{Q29r*okEIBlCOz^^]3Rp~'r>CYapEv;^oskkO3lU'jrR%xZek[?<GK>]p+Q1[T
    ,k3aDo!OeKO3YsJlwx=Q,;mOD=\#eZ_QvnKGHjTm+Gj5!m<r;QZD%[E\DL8?}#W$;l}$D#QE;Q]Q
    2q1I\;U}Hn]s+!U$pX0]svlCZ\1IUwo5en1P=hDDHwVrDO$;^$z,9$*wB7?A3s&<{[hp@-ZWG}ip
    T[alTXH'YVBs1,X^,V!a5rlW[R@Bs3Y~$T,[IYuQ@a3si[3O-r-'6wY2UC~}DB]?CVC{<xzlzXBE
    +BOm++rQ@v;V<C'#l@}$~5^!+75[W;h)BI$KUn@pDBA5SpVkU#^eDK*=}'<eDvAIrCYs$\X3I)CY
    !WR+DQ=C5^-<@lpTJJW-p,7cLXl{pBmeVQDrCNQ?^w_m'v.1AvBP|QJWk!+JoSNoul!|,z>j~ov^
    {B$uG?!OWEpeUEWwHvv5WX,E_raQ1A=eQ]Koje555nKO"~vD2kw\Q*n,*Zz$1?TV5slk2XXTsQZX
    ]_3a]Bns5]>1a+O*#mCmC,;_]q<{B_2rn>y7oYvX1+uz_TdX\Bs=Vs-I:C#U3#_@KpjaQV5Ol>UB
    Jsr$zEE<CIGJZFFQ<r#=-j?2E_!ZYik5>l'IAU]5n!;/;]]?5Y\Y<Cv{E?5@}^raP1-D@o@_,&_3
    7TQRY+1C$?wE1-p,HwZYp[',uB*Q*!{a+B;DvvjxZUk]5^ilA=+eiT)\RGT1#Hk!B=H)V$_Z_QK*
    JrG}1,oeyv[U~,Uw@=vpCn6sRZU'vp*8^-^VIs_XHA=Rzp+#oK\Ga}5w]HW~$oWp^\m^Z-@CcR=D
    C<\H}QWHViD!>[;A,z71Q5^o}3opm3HV}eD]liwa^4,1EvC=u~jDmZ1bVRK~:K,K*Ym~!j,5T.I;
    B1gQx52lzz1xx'XZwXV}U,^~[[CV#TrKQIIMG2G#^AG>N2>lGO$oa[ZjiweI7R^_]]1DR7<w~$}!
    7[u1nu7KOUEu_A$WwKEACn\TDLmC$-s,]_{j~pKj-{}T@Tmw}l7.@eCCipi@^TRBH'#G_!!n=zjW
    OYHmQHAlue=])G>CzeQ#EEklXXBnVRYljVIX,@Dku=x\QwOGZ:<+\vBW=HYxVrjzi~{}#]T>1@|H
    n-1DjVJV]ne_GX-q!5=7n{BU/nnEGXEV5i<KJjjXlp/=Vo?CB-so{>\nEursskQy'r>eW>O>I2B?
    {{J]^K!o~Iv=R#_1j1#uO5*v}nRm
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#z;DHv~aH;'_=OIQxte@X{Kl<kY=G-?=T[:"^"[u*_N#R^,!Q;=C;mTQ^27\BHz~5RnlD3e7
    Q-MX<+2U{){Ck;G$k\s{ErsSKwJUz^;};<VnzxCXX{Dwo*ap0&HU;z#xkY'a_V$@BZI=k$C[C<vr
    C?G3]AIjWWusKeH7C=wn*B_kl@EpH{!CGll95wszTr~Ri[l?7rDX2{'<RG{@CQoje1He-]oT'I1Z
    $D{Oc}<'Ux^}]I[J[U[\Kl?TwZTZ,+5K\j{*zR?zk;}iJ>n7?a\rT#<'7Ue~lLlj+o?=}p$KJ}!X
    DQ(}j>^l+A~zC@pGr;aUDWxRa>VD3wJ]7#>u]p]liA~sCjku}!=Qzuwol>GztKToDU-,I{*+Y[p'
    #N}p7vopA}]J2BfjTZX3TCuY2=aUlOCmYmv0l)+&l7i<RV[$-$=n}~-DxDC}olJ]!o}Y&~awV$+W
    w\i52_-]][,TjcNB@R}U<jW&2Xnmv!uJ[=RrC5=\^AsuxQ=[$[X_]IiJB==z?RJO'QRW[K'pIu^Z
    va}oIy+G>'(lUmGUp*r~,\Q-T~DDBo+vl?oo5=V\_'2O_QHIEA=JUW{woTr*-Oz~\O2wz3<>}!T1
    njWeE*7Q]W*U7Qu*{mAeVEi1'p}DTZ!_*nVs?,n#H~7jQ<z-z_Bk-33pZE=~Rpj\?5wDJ[}{8DQA
    o!p^7=Gq-SVK7<~Ckaz~7*eTm@[H7TzRp+EK{O1>WAf@zT^#=?2uRA^;v}WsJA\A-elwhvIO+r;v
    m#'k?3'XYeuZX}D#<#5<wOwovR___<vv!TasY={I;,3\IlWA#C{;r:^IrZVT]W2zRDrj^,>-HI}5
    jZlQ}\X+axC{<A!^5lEDkzq;=V_bC>Yl?RA@*e7@5a7J\+Q?nlHp1AXDa_VQQ$nU$E]QRz@!C*![
    d[KT#xkOHT,An7,v7er,>?XlQpJ[OwUWZn+OuTVEnk>vir2sYOCGr/kU5v*J5anD2\y'i$D4kI!+
    rlE,Vpu]v}XZ@]H*x+Eu$^>DSHo'p:^+<x|"{a+$$!u}tj+@xo$]UT,+{Jv;eGZNR~^\lGKK2GW=
    ^zaQQ5Zk*!O5,^3Uwo]!v@Gn^_o>C{n}QQFK1^*"7jz?yi{+HEA}uzW++\,k?W}\n[3lz>\jWp+Z
    C$H77UH^o7R!^{}D}B7pa_i,<i&7Q+~B~*Hl51@v$#-V@;sZX+Wk<H7naC<?X<m!Q#BAD{Y~=5}I
    !m,lROE~v7*p_au~z}vQWXvIJ_asJ7=vAr7C-@FEo!,}V$JDC]-oHY*KRmp$Z7U,#{\IIH-{qF1k
    QnY{\wH}2r91!_^EwpepQ!2\^j'W+W'%D_u}vWYr.EHB}:'Ep_D*wV<$O-kw-3]]ue4c*E'J\]!H
    uH!uuzTKxqERUX;HvAUE{*#_7,>,!D-Gg'uA;$an;]JK-_O,}GX<XH}H*HxQZJ7m^pElOx52X[*E
    T#eI3'7#ev\]k,waAWVG;Ip=V7;D;KSOl^WlHEJ}KJ>w}>#IIClTe7X5{<nzz_A1E<WrOJ_?AC5D
    ;=]Kl}~pJ]A(*qAj>R~[2vz3<_}2DlOWQ-5p;IW^m'=ZRKRV;][R><}O2eYX+Q}WrBF(m'DYIjCj
    a-uEjEkrGUm}Ao,s'$\Jji]^I}{_S:Ko+nGwD$R3U7<Q{@k>Zuo^#k*\C1_Z=wQrJa1yv$>jvV33
    t$W>>!>-vq=~aYGRC55K3jC2VD5@$I6Rvz3iGU2]Wwr$E@>YJrBrE=?x=Ejt,2l]YZ,xx8sO4J[[
    AmIwI[Ds7j=fQ!*23+n]ARrz1^Dkc!wvX$le$Ea-u#aEDBk5GHOk@o5ouDzQ~EUYTG#DEmOW'Y!Q
    ]4s}>BKHEWCWwW}=QG$YaYllJkn]]v{rATGpi#7k[Aus3@=IB,3$gF'5ZnBnOe
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#*=DOtE@\J?]VkY>lBXo1IKl<kY=G-kn][:"^"[@]ZN#R^,!Q;=p;~TO^V7^BJz~5xvssW>z
    AWO4X[+2U{){Ck;G$k\s{ErsSKwJUz^;};<VnzxCXX{Dwo*ap0&HU;z:$EmlBz_<S-R5iGZaZtv]
    CAG~D>v]3KksZmw7H=wn*B_kl@EpH{!CGll95wszTr~Ri[l?7rDX2{'<RG{@U5?_[G~oQOV\G}GY
    vnsK+I-[7!JDxC}k3A^o$HK]enA=zp\*?R[nrwQ-D>On_WXX=i~nCe#G#YHl]u$D!+Qi{R]-YA-;
    2+l}M*e+Z^*Jm~$x>TrZBx@>!kE'xVw'xA'ru,si=}n-WFQI7UB]$2Yo']oK2^{>U-Ykl\!DprtH
    &Us@CB^s^$}ys$p'KU*C3$Zkz3eieG-'y&;jT@[iuJ1T*k]$<m&*}T@7,$#~B\@.5v-EX_A-rpVR
    j5x>H[QR$vjBVJ,i5pz[EO}Ro5[x[7l,}i@Q7;7z=kl]]]+[p,O[5_pK[~O2C=,=Q\KYN\,[[>T~
    e-T;GuDIAU<he21,lU+>4z-<JEk1a$AQ[pK[{UUBJUvOJP=}I=i][u(n7Ju^kRA5U,~-{J]pW>DV
    r$+F'A7-DIK2<5Z^8ABV~Xpr~q#<X?popJv<uI%$[oZtp8kDXEZwz@lTA_7EE-6EoEUU]I7=e]Z]
    @DA'-Xx*nlTgdVHEBlB\T5>VmczKX3QZs3D,$DV@I'7~Va6WzG22RH1}C{swH]^E~2_lU[R@lEV!
    sT>V\5U|Y5j}CjIsD#~{v[R[r=-j{_1~VxeGm5<'sZ-A'5Yo1*@lH}77#+H&I,Z<IDoQO5pCj!A?
    ~e7EisB3CXo2}wK]%|7Q@X'_u#7*{_,Q?_WI-C7A*o.#l{#7a3HB2VI8U[Yr@a[>IoXC}Q{]Aoue
    H]Jef7OK<T\am>A*,,Q$@#w^1*-u+$zB_X^v}ga<}[EvV_^-wA@r;JJ57xDE!$p'J[tNex<uI'T[
    {QDGMx->>hMkp5[#*tWn{v~[HIEX!Hq]UJAHr7Ei6J=XHNCe[J*CnKiT$3usal3$_iCap?,Oj-ux
    C'oji<q!B1K3BW3@{*?!>v'cBXo<6$,!k%SU7!kvC,uUG<]fl,3AaOHX**\#GXo}Xrz<;nC12]_l
    GX2$a}a[Gml!1Eml;[
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#Qs7r?epO'I5o$+V=zEYOX\p\7n@-KY_$"[MWr?={*v[=V>}<VRlVC~7JYp7Crs]-a3=R'W_
    VAUO%S#Ull!r#~$!RVN^,]Eemp[KwJUz^;};<VnzxCXX{Dwo*ap0&HU;znIK^=^wr[@@;Q}YRnD,
    isuUZrO\5r,Aa*]RirC#o!roEuQa;$CuCIW*;Q;>jj;}1_HR[_WU!l+Js,=KAOvJp8V+B*I*AY+=
    pVk}IGwrA_6!Q*#,zE+<BHE7HJRVlj\5Ww_tr<lO}#lm{R_IUX3V=*Ap7;s_GH'wpD\@I3JHUj*E
    ~EA2$35{s<YKpmp3W^zX']m?ErUup?3e3_3[<Q?pcJ[Z>@+_neXZ<A_V<I5?[SwRE[$OzTmI5ez7
    A2eWv#Wo;3J\eB}a~op4Q{-R7v]ZkvOv^~Wllu_};nKjDT(OpoDB#*no}Ze!OsR=e@Z-TUH7{{uR
    ;I*jjps]epn;s--na*i@<Rl2XTwHC_,N=^x,uoCvAG?7,<;~D}vxep^T]_VQ%]'\RD?D\MZ1}K1j
    n^!Eri_x+51a{#VB~um'<{EmoR-IW#(lrWsU_]o6in3x,B3_GpGV3-^5e}+-},T#xY2^HRRpUYH@
    ^**^;]'<eI~}i}iXBE$u[u13LE'+HvX1Oy71HHkH7^xG#s^~$G]v-Ez_']aH{_,Hs@UoD]AnIi,@
    KxyXa\Donj~znA2kr!34v#vBiQHB.{5#^u^oGW1U,AYAVM}vQ3ge?mGipQ1xI?lB3*!/IrRKwQnK
    JDj-fLRkak|#-EuPSErVGBxAkj2[Q+[s_>Aa3U*xZ9U*A$@<^\{$;v3_enwHK$OEmXXOI2dZ[{Y3
    I7K+lHV2+m$2\'YsZ]ZkpUm2Q[#FC]lpIzJlR*@$8#>W~YjwAVsWww+x=-=Z{E!oQVjJZZ[av4jx
    Tv'?\;Y^[nx#;[DvklY+[JOgXpWQ[TVT'_!!IjvJZaajoM9oT]V1oRa'>IuOoxRr6(*=,$jXJExO
    7*dKUz{GOna;e2T+}-?j(+^$i7'G>;I,a$W_mJ\}5&(K}n{E\Roj+=2No{JIr'~,Q]?>[[oo\C@?
    =xKOC]wOZUDTD]2#\;]2)Vl-T(2z!z_Q'w7oT2rT*Y}3'_e,2=^R,Y;R\#}x=<WnJan+hDUW{=;*
    ,{XTT=75rhk};p0<Y/Gj<w7@'\|o?*k'e{a$eR@U'H[~{U$]zBZIo\s2OBpGpX*$|*VIU'[xOua2
    3GOp!axooHGvHAD>CY=;[D+'Ws?m56)|3XW-fm[<[$ema<]xi1A7\='Evs+QiR;lEWHK#wU~*{{T
    ,G,!Q+rKAr+ARD5lJ$]$=oG<_x,{nGW1XTsvZUoZ>iR+TEY]XlAj!4$r$vxzZ>'Uz-(wOeA@E=2{
    AxXXDY]#sm,A'7e/(.Roe,Z<w]67pwmNVxZu~}on~rjno*^^^]V?8[Vzemv9:*K''oOu+#jiJ*U=
    {evl*k7XpMB@mXZ*!'ruVsQoYlA<wpF;_>#eW+URpjJpnEZ*ZB;QW>n'2RAc13=$D;YUIK*@WD~>
    2_-TlX\x=s[oCvwHIImvkrR3wl7;;wBs!Dj@i{GsK>X\VUu2AVY~}~$O'lREI<T-apKGGU_>1Tv^
    8^r^;@*;][jz<X\C[TeZaqy^x'ln_#W^\33>*57@=1jusY?ejIRl>nuCCDD~rswj}?B,V+\eGUZI
    kV~s1A-n,A$aEe*Dj<!}dxDmx1<*?E^mH@}pJ?<<'+e+C7CIaQ*+zJnQ<^{v{(sQeZIz2Ie-CElm
    B19kY]<e+<\}&06eoKT5UxYgFeWl_^_pr1]{I"G=*uK{Y#_k@O}kIi_1In{CIUvVx}WTTG>}AO$O
    @ItIi2v^-D$Et<OU2VeAkC-ZK{w;j;=GI~1+z_mD?oek+tXXzo^1GZ_]ovQz?YYZr<vAmk8azGZB
    ,BX!,-$fzw}xITs?[hvBxZ\G\$m$^,x1an+O]J<]71_*#''co)>OYVDv-{(aQ]7^+'?&Jp3{_XYa
    OF3ERl-=j{T*!H2x1BB'IG#R$7oT]G3ri}DWm27->\mIU$~'@URypG+GvVGU.U,WZBKw^KT\mBB^
    Qd@eilvlu~vBr>Ka=;#&#5!1?{~Bol}AB5?Da'I_vj#H7'x;uBC*G3A7-1v~HI]@GzZRTU=vgW$$
    N\@$WoEU1mp1i@$!pHrT5IAQIyJD{]j#^D^G[kH1a=#R>XG^xIP=X*znau,i5WJs7oT2Am7;>GZ\
    E\="zmmj~Q,e}5On<,IHERBxXwum!>>-^m<Q!'I{xOo'H<pE"r!\T=W27z^-<vdRZHo^@25GxrvG
    Dp_oGz+J\;,k}2B7j!e#av{pnr\]O[RY-XaHB^mKH>H~,8NDzI*A5Gu#QJn@jH~?^-OYzwR_?C\]
    3rHeswe]pA$A[Jo?a,Gm_A#1Z[EVE;p^jO-x\}B_ovxi^J+gYOR7oI1+^w;zp\R<=Y!BeipU^8Y#
    A7nejpQ3<GEBp_l_o}[31]u$]#kRX<wrIkRm<^gqHwKYie7w}-DrI7H7'vwJvm1v5RpKw*nsoc]R
    *Z[U+Qu$HQpR?5ap]uluBo.QZ^U2E3+ROr==EHxQl}K^;+Y./EanD(Y[s\fCiUk'EKRTOG'$COxK
    z?uFIK!C}k+T_k_ue5^]H{eRxE\p(d8U[J@l1-z<wx,]~}#FYo]se-1Dn<Am8{vXaD3YGQi>k^~O
    =XOaKe*,ib|^w;GVYnu;v5Ae~W,x2*r_pw<RD}3)@CT7u{esYCoV@>T,FA<rAfC>Z[unC<LIkuKu
    eU;DaIDO#*;e2^2iQI~}T*v?Q[;#A+lTr$VTzm3jm*KVCWR*{;*Q={k3x_oXjU31{++\x_Br*ivk
    {X\\!mams\!2a@~~5A~^21+ri1E],I11,~[?T1j,W^-sua3'wI>pjVp4X$>$EO3Q4xxU^3*W#)vz
    w1-->,7C+*oVuu[hY{Zr7s$j/5C-^5@7\P,T$]I~]D]CV7XemrVoDR!']e{juXinC~]ip^xmlW={
    -K?<!Z=\jZT1p\G#HU[2nRx7XB}<K[^15OUr+mRz;2+z7#Q_\r,+s1sO!oD?7!_rwrJe_W2DmUgI
    '#J[z*m^kQKTj@lwvse#aGWG4jRxE\mDelQEm!B^G1[_skRK^\17RQp=OaD,=7Au#H=A$i$Q+uYC
    p5,[ZJj!3*J]H~RzWDaQ}Y?r*2s<>xXT<zxlj_n^eR,Ba2TZCM[_S]l\52D$-e{!3jW\se_#sX=a
    5ezQn*\[pK1_'tz;a*Tw[H[^Bu>-Ip:~xVCG@,H'sn*qv!WlVe{s_'oesBRBa,+nwz+O;^n!D2,1
    5EeTz!3J',*=KGW5q'Xep'@]<Yl^J5+l^A+3Acja*^a]s-O]EEOZs~+X$uUV^GDuCOeJ'Czs-s%a
    \p3IvjB_C~wIHJ~5IG=v9{-<H9CxDEnjj2fQwa^<_W!1sC#GZJ#i>C73\Uxy[Tmp9sw2EUV?T]W$
    Jx3Qj^p^'W{nTKCo}OK{2V\G1*REAa[X-keOl+TQ2ZD-DHYZZZB^"R=z]eJ[*YwT!lR2*_K;IieG
    [qE{6E\o>}pw+kHa>;+Ds2-u5#oWZ}o\JN}Al]{G=RI!1CCx\2zW$<CZw_@In>\ek=MEa!m95#W3
    o$RUA[_j~av]u-l+Ba@ovCXOBU{+1l'x|DC_~$lQ^G7p1bEH}}'^U3x-EX>H[o~5i1W[zIO1@RI#
    ,C*'DIQnVE1J;=k*>~?=#p,2TwR2zO+8.KHQaz!'m!CJ5@]@+cxv,?l*7Y'-wCv7C+zIeU,VU}AB
    {E[/:e[[*+aBuoWj3Uo;Ze}^QBz*zGm'5#ap;<Cpa1]7k!w><VRI11CG'Z]V#hD'IJ}]<E37=2l>
    +]LOO+5Zr+u-1HEjJ@v!^eE/E;K_+T]k,o#@VHRH.+=EOG~2jUxJIoVo@QLBj3!+*\B.}}UCk]'Z
    BKU<<7!Y*{$2[x\s#n[z1IV+kHB#Q$31_ilYpmB$OU$!XeQ}$;O7~T<5=[5vUwU501mUKiHKzUpm
    [0We+GW,mWk^xTWV_^G@_@BO][$;~;&=^=@^zY5*!as>XX=_3pV^7?^~'YjZwevz7+Tp>!<kwI=9
    v[nxQ\}m{\+@lv,Z1iWR\T$W$I1$ViGl@H\;!A'^~CxWH'@B8__,X4I!J~]Rn3xDnYi9^+eG\oXK
    oBv@x5z=5uj@o#D#Y1ZlE;UlKA<Tx5+#.,xwT_iRuL}w>]'*BC:K^w*Q]^__WErX5I2OY[R,GKB<
    B{A'a-{)5_J@RG#5<n-,^sIT2<7Q2}^_yxI_-Owpz+OKospmQ31AwxUA~Q5C{jR}ZaQJ=GiU${77
    ]pHWZw'U'TQ?[lJZ=,UQwO]3~1+@D$rw31Bk{A,~x{5V3?Djx<O~eKQ+-[{l,un<R#5@^[__C3_v
    ![K7^e@oVQj[lVEz!1u$n:n^AU~$Q]_H73ZCK@$wOT=$Z;jXHpJjJ_BRV#!5vJBO3ZC;-_AOX\?O
    WBvJHppkB'7{xz$5Wv{O]HeKp_'EJmxDKwRn1{xE5\_o5H+txQ]@9VUJ@[ZTu8H\$]lA1n1QzDAl
    ?5!pvuRj3OGHXsAaHRB\-+@,\#|77~p?_2BD7u[@lCHV>xvh}Q#*#z#JU\#Gm[nRcQDWK1~vAIWZ
    5T53Z.Z_~jz,>'VxJCRmlB>U$_Gn}}}?$Xa==EClDZT-X2H}lovErsi$TLm=u<,Un,nD7B~1AvD7
    zo_Dv35,RXR;znZ5[RU++Ob}5'U}^>xv;DpO[?sH5~z;Ea+]#@7>{nk!5;ZZ[;Ce#w'G-a^JH@^I
    ,R?zK[i&jV]{I-R;JAEB}ZYDTlG_WXpRj[>^l*[]&l!aDVrCC$r\{^X;%C]CO1W]knCO7K<aHz[W
    jxaV\RV7VJl'[HQiT;>;Z;s!~u_<Yi,mu7:^l~QvBE?sv2}7>Ea3>OEiv]#*7TZ9GX>={wHse[nR
    },-}rQ{GeG+HIDKe,3zwAV*@*Gk';aEz,K<#uw7T+A]$HA5?H=iVl,l+s<<Xkwp!k,t$]K>1n!73
    InOe?-'J];<HExWJj@G,pH@27r5IYt%\[~n=z*ox$lZ3}',Ru7-5w~19B'TAT,nUz@XzjeT~sOTx
    u-Zv2+XX,i3\n*$R$m1[j2j$8ixZI%#x<u}a*nwCAAQVT2n{Cr_K~JpjZX-D2H,Z!Ic;RU+W7B$G
    ,*m_wn[JB^I&$r]32'@Oy*[a1,'[Efk][5HB-@+13klvYCy5OB-IV'rWl2A[[15-UD2@QO!wGX_E
    _VGolJTIw2BGn!]esk<MkEkE1{=\%Y+=r!>JwOxGoY]s>xz1J?r7?DzzrI3Yr=<j?=VKZ_(K-a+E
    ;jx<>@>51o]TsH2}ivO={V,iVE3GD;KI-u[5zaO3pxIGvz3qTe^m1r\;BmsUDAWBG<n[Ir~@$GXD
    Ia{Ga>{>=Vp>%;w2DBBQ{YJ>$lO>G,]GW1r2*rvpnHXs1sOARcvK$QixAj|s'A,V_Env?3{]D5ia
    OnQ;X}$Y2>v{vvTYOn=jlWO;_l<sHXDuUl'J^B5GnZ[x2C;!C$EoRHn,pTk^Cp$!h'vC*GL,VEEk
    xH$<j}U*Z{QOpTn2^{^5QC^^speR5ev}_KKUGEm\XyHV;Cwl^z,<R$]H;Kj[VxA{a}[i3+Jr_I9O
    (T^]Ew$i$pvUTGlpuY7jVaXXk=Y1KR7$~ApD*v<~-V]5ZQYknpgju-mCFvwe@>$$^Z_,OW_H{}Xn
    m}^=Oc'#]pv_7i0~^uzunpl_IBVl(B?X;r+u+>RJ*?w2~a>}rwD^x7AF;]V3/\G;ouU,Jjv~e{EB
    kuaD^Pd?=z2k$Xu4GNZ]3pxB$idr'Tzy,\YUQ{\G\=jo^<l2mYoaf][-,oR,Jy\[?QvKW]GkO>\w
    QEeOBo7+OJH*5ZKO]lzR}>y}%LisKJ1{u!Ax^INkQ7_'mOEFR!}jEEIB_nXU?vG^an$+m5CWl'VE
    A+~eAX+{rK'BHU3_,H>rhVD{voKB3O<=5Az]]3*ovI1[^Fe;2p,njR3V8ZG~+U_$2IX~m=e'VDWA
    !nG#vY'x@{_#!eJv!dC-}lv~};%!^7TpK$*ws,?JxUC:GDI5KT3T}\$?vlp2[w}D0xO;j{C!O#s-
    o3V>ZzBUA-*@<O\lZ{{]lw}nz3x-s5Cw~^x?rOzT>YsW}_R[$?G~!fVUGC1=,X5u';_oZ?xW'\v~
    AvHjRTErm-IiekKwC<~<CErGzn.9Z-\mXIBv%CjX^v?+YaYo]FWOnkc9OR1J]wI}uCa$k$OO5_U>
    R\l~[,]}1ATJY^5k}[B_T^7$UQGE~C?3&kszU1=zl_Gj^KX^[>l7Ds]#o',AJyvH=ZH>RH~X!$^Z
    [VQnA-KR#W?*-?h?w-oQ3zml3,\^s^a2TGB}-{OavTJBs'=#I@Tvp=[GB#W>YCY5r-H\^CsUjxn=
    -}l$r]$%e+H}7xs+^[;oM'7;HY?5+dQIR}.4p)_R@3q'r5eY$A>1*O$=,G~0Ble-I,HE}En*;T+2
    G@W^Js_oFlzD'RO+;m['zEks{1*r~{^'VCmoppz*<j_V;+1{Rbn7k~k-7XD"&IvK2pm=Qwvep}rE
    1n\-UL~]'o$w1waem$G+j}>Avzn=zY6#aRpvxK}<erJCEDAe$xZoO{n$f#{2YX9B<3Y%6^nww5_D
    rT17mZ{_pu[e+zV~!X^_#v\e]OBvn\1\aS*5pk557>YBeA}4UlWKwv+ms#!^DB[J);Xx]_O7#IaV
    *rTJ3fWw=HIA]=_k{-CQm?B@T#'G^22'on\fLb#*m~z;]YK[]{.]E>5*<X_!6k5UnwC+-2,,=~r9
    5l#X>jH'fYW_7oG1BKVUOurR{=r'^('22sK[_!5{]Q]Xnap$T<D$QKx''}352^uUj~.6.Z>o2m]Q
    5JsvE[GEI]a!O}H{5^Z52C!l-'o,-<CUlPWlA$,4]A\X&1T\^0aj?GD*Gn$1klYvn-C'DIQx\5,C
    2Y{lz5G?1JZ}w2no5KQBk540-l=--\1@m<K1~lek-A+<jiG2lH*KarDxZs~V,-5lbo>2p[@^[f1>
    U\BG=QKXv+rOXD1iz]{Cn-uwxn,orO7>^K{x[u}R'?\GRA&smjV+sOwEZQU{wmlkz>YD<nDie'C$
    @a331s{@Q]J<51n5dZ};moUYvUvp[kU{A+Yw!e#n17[A1zGzu+O5JwA_AVY?75CEp5D;V$WvIjwn
    ICl\VPjIvjx4=_sa>UYk5\_\j^<w;n^>E=m;>XaC#+*a5T~aet?_#XX'~5pz{k_nQQIZxOOa27Hs
    <E^?<e{Ux!I'+AB@}?!_H'EpaKQ*Ip;]jB_IaV=ooi][\A5+R]fne<BTpJKJ<$l_pl2wB5^YXmR^
    2v<Y'u5ho1pwnY\pR<C1<TG3}~E\llV7Da=ZUzp>A\!5~$R{,^Zp]YI~;-1;p_-kKxaD}@om\Q7?
    =>*p@[Ra:j!mWOn-'*el@_ZX7+_2ZV-@?2UE~CvDWm7@m.1i,!=jXwclD''GU_$~HEYX7!XA_>Z\
    <u7u^Wx5I3z)w5;}kesljG>zkRXE%T$Bo_1['WQ]e\Ij7k,aDxs+XY7VG4=C2$Y+E;wU{G5i{o+'
    D*#{~W'En3aoTZOnx1&p@Gw#l?{g@*W^C,x#ACzGBJxD]'V=OxCr[xZVjW-zoe?$/_X,3x,_2WT'
    #'zrJJ7TCeZe+1}GB+>sD|a<IIBn5YI;[GnICjYH'EBqp71zJEi!]$\>R]!oQ{^^,\m'}O_Vk^^<
    %-_-UoBnDnV!Dz>_na$@a\3,@[>vaq#>*!IC>QW{3}QxEQzpIm<H~u]jIZH+K3_\;DRIm^=r{e#e
    ^G$C\>rA>7\pQ<2aHKr={k-}{JGsO~>*aU\Ooe{$vKvR?e_}_]E<I<*GvvwH7!tQ_Y^\nr-\B$=I
    !jG[\#{_ka1VOksx-<',!m7]sm2+CRHdGaRU,RH@XA}OHBKR'?~,]O!nVz**jT~YJ-vET]$'p\X\
    :TIm{7}eEv,@m,m{xNj@5v'_!DYtETv@&5<+rKA;DaTBaUaX'+xav*[I]KnW?I=BA5=]YLk$VDpZ
    Z{oR_1%cvEwEQHxW{C~{KlUGAp1#7I$7VmJ,Z+<36kwE2$UITioY<1re<Va-s<a1Ge_wpk+a}s+3
    J>xkV#n+JlGEpm}^$Z]3Z^=57]To${O\^OHYOW,A]y-E<?wLK}U?&'n=TswKz$na7'}]BVvZYO#$
    zCj+k=DQj1ZeT]aDe5VJ\uo{-W}on[G@,[Cw@=_+W2TDn$_eaArmr>]#*@avo2o!Z5pwX'CpuV=[
    ^z#GQaBoO?O;3=2awfp~wu>z,[rx{\in*A;wBe^}Js@OC#=$-vTjYD7K,,_m7*dC+1D2n71z2{7U
    -7@e>xneZ*X1~B{lYYoKs?Jc*a*#/(UX!<+{QpTv1$rEjj;'=C)DrR?o>\p'5p^r^V@aCCX8#IR\
    #+UTK[xrElQ]D~3^EmKY+DH$nlUX2>e<<[~[vyZ_~W-I]^8V:XErp=,\pZAl'[U7vV+o!nAjV3^E
    o:Q!mE'\5j_WJnRJ,{CG9Ox3vjB1vgx^C3NjsOAs[+*KAu,OUw]z^C1HnY#l?u7_}Up1t;a**9^>
    j+9n[jjZs;}}n5m,IV+_{Ge=7~{>B;]'aYrveJGt6r1,*_\>oNz;v-d<AEHlO!DYerii^I*+1W^%
    ksinYkIzXDXwoRaT>z]H}HI1*R~UT}Br$[=mFa=$$,#T|(QxKkj+<>UvlK)xlue7[^JRa{Ym^xA\
    zx[m{Y~zIjCr,Do#GE'^[s}P\{Zanv*\]/1u-HuIH1x\~Cp5uCIG*mWH'3nY5]eulOoCx@G#Vp$$
    QIwQ@uADn!oBX?]!xVeeT,EH${-<[57GE+2<U1PEVDs,@nOIl\I+l#;]^BCMzA=1;YB-Y+!J$CC[
    m8$^U#x\@2s]!ZTl3{BAYOrrvJ:'uRQyn}TEI1'pxXw2.!r**zOJz=aBTfv*_?p$,xoUm<'QzQSp
    V3BR\KeDi2[EBH-{,Ol2A=\WT]AeTHn'O[X-BGHOl+B=_>_~5l5^u$C[e7K<_IQ#TX?r'pHu\sVv
    ptxeIAjD,x&[@m-%B^e[^bQ@DnEv}!k<12Qei]GAsOD@{'mnIH>B3JKan2pZ$DpW2]^rYX,,Dj|+
    zu1{XoH-HJuR[-{+O5TRW@=d*Cpuj,=ee3j=oap$[K*>AV$~]-,_CW-aMjkZ;?R$#pmX'''TeW*[
    ~YaDOi'vKdG_1n'^mr@_,vpn<jR\!#5I7H,_,Gs'G=|3]QU^7?XKp5<3*Dvd-^+<VI=HXAXoi{lH
    R?u1-+A{iCzT,m$2(-*<*>pe![-TDXT=~l5^CJw7>V"~GO!\HZ{Tv<[DjWD,Vrle'uH7UEGeDjJ5
    O~+Epr#'#7^7*p}GnE1\,.2]@CKaWTTw7~_}?z4<jp==}YDZoE]>-~KJvXOYx,JnsiD~>W[cl+^u
    Z+aO-nGI!B}H_p3vH\+{pY5e~{UEvzA{l,}@\*Tk{UY7n7$A#jo{}7nQz1!Z@ormz]XvxT3-z^vu
    ?[UOCaU?Dz<5^[wCl7$]FBonAn_-rYuUkr<X{+AAUI2_VD%~Uj7cj_>o<p\J\+I!G^>ODiHG*_n*
    J'-\?lA;l}wWvVRUT^E[ce?nn>-}pBka1E@J*[!n5j3wR<6o5zE>,onD!+]DC>[3CX7Q?QG-{}V1
    QzxvOY{]Zzu~pnE]w=>[@e#{OAD7ov+?*==O~{IvZ5wI$,nVQ~l=nA^*,@soOW#n5#2{*voU5W'|
    GR[e,Evam5H<E^X^>UuTw<[vroKo~Ulpl1eG?<+*ATD>Ae}+CEs2WvmD"uH;j<^'@Y,,1I*D2wB@
    #O='R#7sK\>I@/]JGGx\Ks*<B,']T]Q\7Z[{GX~,Bm#QQ~om-=l!3>axD?$x,v~^r_*'2G@X_vQG
    r]E,R{y$j}p,z3nIE@Y$wTl?D1sw53oIs>w+Aj}wp+_c]3<XnDRm-\;j]7~ZBTeC]]?Ev'IxGG^G
    oIrs*rJ{XO+pS'o^-^ljs$I[kEp'kY~ue~*n3|E=n!C5u?>j1,pw<}a>ITVeX$0Ou2@3pVR_';rv
    C$z=i7}U^#~BVQ}=>}!V+[n6<{EG+OE<AOOD+zH[-^E<xiaw?A,+M3>1Y'1Rs!s]3iG1v:?ln=f$
    \i2O~2W=TxB}?!QTO;$h$@zzlv,VQw>}jU=Hj~mVIpw$6Q[BGnxOV=iQ]*oHRf=#J*'$~1r-T<EQ
    GrKz<vEZGKe<1=v2\{>UCr~<Yn3RY{-Iw,UC*;Y<,5pKC[D[ZmcmX-[BH_;7_Zz,C^<YV$H_DZn}
    IZ_x#sJ$^D]K7a2B}37IxmzQGDO#[zQ_']\_?[Kn\mnD;![}vRj?wA~;vz~rmuoaOJ\'?l\T<,]1
    XI22om@;on^juXsyQU!{4>+vUO'XOE>+@m$l@Ol15\Ye[oHO[Czz~zx2BLpsQ@7XOxunvu$~KGH*
    <w3B;*X=mj}m$@=KDV$x^3{lU!M~\JOy=va{pTl'}!5u>{pl-+7r~^*V{swws?m!;Vx^m^?D[]~]
    "{AY>-Ylj7'pYeCW3}!J@k7CskA_eH}V72rm]n=p^za\jU_H+O\@K[;s\o;,{G'ieKCn^VC#s#5m
    \_W]*jK-_#_ECB-nWr!1=#(]>>Z[>Gxr}#Jk}\=\Z+z3Az3zVjrVDt!THQAXnZ3R[VD<Zo;$}iV^
    5ZG!{uf@UV>OX8B^u]Ae?R.!<[UK]ZV^#Qs^*D1DDDnr3=BK<O^BGJ=3emaj=-n\;C3D#!+r\E3{
    1CJKaC'*\a2*n{AlKe$H+$Ze?oVYWW<<e'wW8?<w#T>;5p^2{_,jHYnv@fHreKd@5I{wAW;z1=^8
    +$l3>U1#']wApT$Ko~naC!m;+^sJVUmxr_AWuOz[rJeGJ$7Doe^}vKZ}kTGeUUenBj]#YG#{xSIe
    m=pDw^7\n}U5J=3Cze*2{jXQnkdT1jxr\zo[XV>]\'KQ9[XDrozmA7+TpzjoH3EV7G$~\-rZ^'vk
    v)C]kC/uI5^D\YEjzZ*|<O;Uno^}I^*G~O5Q<DW~pem-#-Kp1irK3n]T;[$i#{soBrEXR1O#iHpi
    ${5U~U^raDr_j$;lw^ZXR-~$?\s<OevKA=$x;\Z}3Xa==B<^IC!jWsmGm(+G@C?$!Xm1u$wX=iuU
    sRZU7\o!s>W7Bp1r2>0w{p=25Yr2<,Qo'Q5x*r-~O!x'R'EmI7!2-2CjrsBiUAeZ5p2p<>[;XH7G
    Y5ER>Ds\pkaX{~7yWTOVG]Rl1+_7=]G]GQs?#_A@'p]$RXXDxQIVN}#Du{_kE\>vJl}\7lC}Xeaa
    J\VokmlEBvZ7wk]]<C7-=9x}e$'Cr1r!XBAYm3{[I$3C!oxV3uN7{CD[#AkzQY,8aQ=OBTQXBi3}
    3=_+K^_E{$D,ux{ZV+eu;=X~$k,{k7*'_>$u<5Dm]-IaujTIGEHIF?'~QCkmp:$WVxx*'Hu_Ju$b
    r}rzV5jX'z}EIu<?KQ}mV*'+#\Dk,!uDRQrQl]DJ_K=a?'wKmY2OKv[#*+n\q'}+QFY$s>-oWoL=
    -7J*xB7X7}k'}?}O<n7A*1]-Vr,v3ua#jW101[(mv-zskTziw!oTnX5;_$r7-ukzuwxxsl+aG_[e
    HJl#r;rt&WxnHu-<r;,@m7p>\j~3ju[-#[mx]z#aGJ^3p1]vRB<+T[VV${C]A|rmD\]*2_E2yPl[
    i7r=\n~X=3
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!##UrKxX^<eQDJOG>CvAY!Y+p\7n@-K[mq[MWr"~UB'ex'Cqr'*olP%wQpo1RkQ[x[O,22jk]
    ;]l-Hs~{BBY6+1$'Q,AmOoXA31,['>Kj^-1V-]3]_7[{jx+v}7CmK]B#lxR^';K,wrlxi[,UN$GE
    =n<}*qBHG_}[Z>B52_\BWu'#EiYOei#>BU*T*QWjv}1WIux5KmosiZo'pm]i2*#Tv37W+Yv+\-.*
    vXB<*GXcD+V7<*77S7CQAU>T2*sa[g-7m~xlmx'A\++QUmYT}o-1GpDD!]Vx^}IU{@w+1e[TYJ&R
    |v?H\@Y_N=^=1\VV2'KTKO!OuX<Yr7B$WZ+z#-O{OurYY#TCs_1aA)Q>swRz}\'ox2*zlpb~5G@a
    '-romaY7s$ei'VI]AX]D~>B+DxR^{R@}V]s%+1*>RIC@V-a#I3\}vv27i\D^2w1{\BRswTo@j$*B
    'E_.NS&GQ1BrR]R[$GQL,lX!41-!\JQK$E;~UaYjUx=A?CUE^TD+^WxknQ~1T+}~5L{a;v^v[kJw
    {=*Z@uuH;<*;>>5>\v?^QEz&5XB#=p=uB7[jN2{Z;\nOkOL}^[xbNmHIik+Zpr*HX'*WD@>)/IVi
    'O=WR=@$ArlXY!G-JD3_IlBDW:Brm2jD?;C}U-YOJUxu}^HImHv~BowX'Oe3K}H]}-^X\1|^1DD|
    iw\#E5RlNk_B;EVQW=!J_U}z[IE}BuHpp-]VrIWCw7ZB*e!I2;O3nuteQ>]x+Aj+7\11BIamA~5K
    aIDi$[#{lKAfRG>^pXx1mOxjiB2@sY-+1Iz$#$xRn,Ej;-aBGr=exn~+2sRrwIp^>vEkWB][*W@U
    =3U=mB~wm^Vmr^lrA{~O-Cu=TaEz\rD>'n+^,wz!V'~pV@zsoznR-Iv~A<Wuj'$5,e~uQKo?w-lo
    /uaRDXVDzxAJE'H<rExwjU5HOsDXCnz!<V82OZAMJRB[npUR>[]xD{D!hjj!_OT7Q%i7rl:k1d__
    >2aYTT~A2n^i;KJ5vOA5H<{X3]^d%9mET'}U$RpT_]=XI7<5pB!eYl2UH'El-rSY@XOl\ko'HHJQ
    ve^A5#@A${CsvBOHT\<uxGk*eYwHr*'UD2[$\-DEt#*[@Hw}u,mmI}@nOGN%=Hz~CZ*mh3aEe=<]
    C?|Ck1KK9rp'!*V{3w^#v+CJl@XOxE!5p^Q!k*$JX}3^!1Y_Y(Q\kB{[eKk}-G<lv\q>C~@YrjaJ
    V\u,lR~j!,V[pj!MaUHsijC7Css,>=z,KnoYNg+Q]x72C?1@ne\#@Hp?7BqJ+uoX.saKj*x\W];v
    +=<mp}o+1v*Q@-E~[*aU']aAC@,$1_RQl}Jz]{x<$7zrrZ7}IB+1a2RX{^skC*ueel+HV{p;^rIQ
    nJ<VD"+wQ[<Dxs}7w#]V7;V2I{v^JoW'#u\DRpj){1vE;r~x}?n34"O.\2A*<^-T;1X-?a35DV*p
    n*>$=V2?Z[JeVA={IvVJeR_n<D'7K^k]mTVup$!GEm^@h_xnEL53Z@![Q2Kzl<mC=I[vkk:!GUp.
    5VxkH+QeJesQ3l->FR\}$p-uYk}vRmHu3k_vRiskTjxpCYp<}l?;v?q,X!1V;RDKa3'iUZ[>[]J[
    =Zn+5VXtixnKU-u@U.{CL#$>AUnI#!Y<B^5jUB7j*B*2l_+>x5sY7|fsi$]@>j=X\s,^i+![HrmA
    sGjQ@$iYmE\~*CG@r[omsEzM*VTpTAEV&jzVp\?Ku__<W*{na3RjEvI@JBU@GZBx#Z{@]{H<33I}
    eT5RIiH~[{-RIBmC[mXb-rXWxnR<pOaENK[n2r<]+X>2vH,lEX\Kp!-}jn\K@I\#QQ<CTC5Tp_pk
    rwB,CIul~fDB3*VmeQDQ<lDC!5IE32veVZYMB7OV$@nm{pjZkTu{a\=lm*eH=>ueI\-IDJz'O_*A
    *liHxaKJ>7BOP!<AjluY3K|_$m^wzKnOU;TY}I-BUUG~$+KWa{!Va=JQN!e<kKIW]$Q~li$@mEEX
    Y_*$piD>H(BaHaQz>eI>UQv~3u[tnO7QQ_Bo{7;-I}>v?s;[_Ii@BD#T}>],_5#7DxmA6sv'ZY>$
    z$uQ~uGmT$@Koslpn*EI*>x>Ipi,;<rG[w5>>IzR#;Q]i5K-m};JR1{n!^Z'!'H[wlYz]m1,YQ^m
    1z7\{q7xH!21@X:8*zQ+erHmjaH-3Q>C5XpuAI[~kwT@Q,T@3EaK]E#Y_>n5D:j^\J[2TW(F73p;
    )#-T,D;a{rEnn+AzGnHKqf{[!]yl@xjL11s_?T=Vw7ZO}W!7Ujz*UwlvBGVUJG3l*lBs['11[52+
    Vs5XQ+a^GA'eu';r>1@'{punq@<QEAXZ##x$A\UVU9^.sX+<kXJ}B,<uk>wsS6kOZ5z73BEm]+}3
    Wm*^^J-\BQjB7l[>wC.Q9>vk*,r#@RDGvvss@D$Zj1QiK_O51U>xuznpJnIo/i>[a3e~VvVZa2VI
    e}!Ap}IX35E'
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#8<]sO2j#,jxCYYuu5Rm*2+1z[,R'Cp]=i7;B,=;Qp\zI7QvR2^O{nEZR$VW'pe=BR:=uYi3
    =}xHTnnB;Q+|EfkQ'Be-e]iXa[zx^QI}VCX$sD72[lNW'#Zz1Hk5~{U_3~^EZKk{}*TD]ia)j$Z~
    6mQ-3vlA![$n?fA+n@V$Cm:"69fO(2IarBCw>y}-;eK+[1[G27ROY$7;x_QZaDlKUe!&Qz3x#^x5
    TTn-+$$!<pEBcrde?A*DsR1=,*>21JoR^wD$UjIh~T7ghz<{k(a[mk^-~Y-rro-I~Cl;]<]7ls2E
    ojs]$W!TvWa<Ho"2]1i0%_KYiA[mz]2HYer5-NA]@<#1;@bMwImlvc.7>V?IesEz_2GF*O'';},Z
    rAX[-}l!sX\'r>K_R?DJ^\W=0l9;jQme!uk=;[_1TDZ!Y5s&^1Z$h#]mZV+nm<nTpBz~vaOGp6J'
    amrs]5a[sCeTr}wUoi2s]aB\XD\ODuDj?}(xn<GZOalY3ms5J3>{T=!I;aR-x+BoE~oQ\-Ql#ruw
    }[?O^~=3Ql2v25vem2A]a'!1rYDGH$eYa*H'KnO+G!v91X[lj[71'@3Bqs#B{Y_3A.X1<=vkd1-z
    G-{J$7UjCYUV~zXBZJV]Bp|$QV]yXE}$GXoxr'_5YCA[D!=k?}zv^W+e]_>^3<EBr[To{$HTm>OE
    Z{u2$vs*A$W#h~r?ju,@[V1~E}<oplkj~#w-Z=\?wAzUpb={-m?_275y*rE]H[IB^B?s$sY'_~5a
    eun<Ix'psxp=]esmu}+*^lx${C]WpV2pO}x2l9}mH]p<@[//*kY2r'CeB'BE<''Ueu=!*;sJowWT
    I1zzzW>U9B,iE_2C?rszRV\>BYea!z*pm{aV#Q?'=ZIpp7l?kx;Q$,]EeIIk3>j}').=;+puo{r{
    lmOC7<O&aC_Y]n+~\=kmKH'<]z~>7Q-C[2Z[@e_uC{'VrI^zn,2<V#J=;*$vA-B{sO5T=E#@+je@
    Iku#\l\OWa'k9lOU@@O<Oy?};#U>H$V1zWUG@OG!URrGUm}[<Qn,svma@JBri+k*H7'g7[Y+Q$;!
    n>'<7xYzYt3E'uiHBA-EJK+12!_}ej6J_O~<XoOr7#-a$a,#^C]AG}Zm{>j&HHHOO5ABCTV;|;'D
    Jx#,UcJEesV2'aQAWvA7<m~}T=B[$kB+eYh_i[!#5EvW$!YG*aD%3$wTWQeJTs^vM^nvA+rr}3_1
    RpRC3y9eBnO1I{ovE#n=(ArJA~[JU^{Y_A]D+WHAT'D,sV5wWpo7@_prusZji!}p~ajiEs3[~vD-
    Q@>jVpQ2I*+'$pvJvp\OB_qTIpl%l?QQD;@BcM=OnTD$D,OvesdHw7'?Q-^[C$['{K;pi+z*<G#W
    }sAvl7a|ix=H-Cwmu_-C7=O^'Z_[ViWI^<U@lsU$#Q7k#r[vi1w=_]R<Tnl[<xWI%\1^Z2,Kr=RZ
    ZpJDsuRGI\["f,Xp7K_C]=?@}3Txw{+-2jeAxlkvBLB?+2Nfkj#p>s-HvKa{#l2WRD@AYC7j'!,=
    Jw<5D'\u{sZ5[#l\m$n>QDZE;<{nraC'y7H<r>z}HEZw;BI'kk$XEK\-j#<vn#Ye3s,Vjw=2R\pC
    ?pWj58,Fo#{woV#{@awlk_x{~7x;l;YBjOD~ao1,j:1iBjL>7[+TU-Cn[*\Kj3GJv^nY-j?'O5lw
    xJTDA***oH=']Y>ujxso'szB[-EG#@3,~aD*+],2s*{rYJ;?+=B=ceIw[#Qp~X+Ts7i['N*+1wzO
    ~='C7lLnj_Z$CBi}wEz$7e3]X>CG~,<E5k;pi,}d=z77iU+DPsTTp#+$nXauBlKHG?=k~GTnr=re
    AVxe5iXapOd<T>#zr!u-s5xUEV_FPx7soevzV'5+TR35;xnp,5^?uGli]ZjQ2O1OTWE*a0'jIi<r
    $Ks>3!7KjB6@}VHDWm^c3a;ECcJs;#_Qa3KEa{@'jkYj*$=vI$Y!>DVA+@=7r2kV>pT7Kp]iUz1U
    3]t3zpx"@]opOB-C2D3j*#(G+\[;Q=*"[?g?<@;WU[Dk'=jJ[KR135-Dou=e@ZsTa[]=_KU5ivQK
    jAj*n7})l]{v9pJ!7\U<lrXewG!['l}Z=iXKOOJJn2YR2|v[U\spV=_Azkgom+a'2~]{O'+zC{R-
    lKp_eGUB[=JI[{p|>1<Uh=CTk7'#w|2,>;sOYuj372L1lDeL<z[~~57aXR\[j>z#.>\xp3Y#+/}*
    Up'B$HE?gQ^YUS/Kl{<3,+[+7JC3H+X}pZ}Cm\p3+xk0fkHVnty],<kv!Wk?zQ}emCo_Ce7*v_1P
    QE+ZG+pwa{jX[X-!x17reCX~+>A}[k\'x@zAp>aYRi]!F~'?vZoKGVX*Y$HsC?1K;G}<H_aKBzR,
    v\RBQQi,ej1Uo!C;~J$sH?1mTV7C2,!{Ql3$j7<5WDjG_$iozo$*_oV=koAOXxUJoIJv\s_n7\JW
    ^,HO_repV[x]G;[RsD~_WEu-Vztx<@Ct$8%|eY~_RxoDb7WuJxhGD;WaIQTK}A}eaC-Dx_}r]HG#
    wA;:=E]=^vmpO!T$H}JEAoA#Pxu}aK\j^y?5<kQIkv5rvx87Z>mQXVpT>m{Dux?w{aux#u!F*7uI
    ,^[s!\Km&Z{l~FB"B^'}}I[sN}#3!,@Z][m>jQsY'$ZG#R^piYn{HrX<ZtH7U7^O}Xwn[B_('ZRl
    Gj#?vrl=Ua!vo^>?e<123--J'klolLk+*r^k-!vnm'WT;DuEmrGvAE#V$BvjO*1UuOIzEwC[oEu7
    ?K]r}Urh*B[~m]u}J,'Ik'J;>Tn^rwl+oe5arD2Q&Awu!CbvVW@0%#a7pet0*i1[\O=Wl@vpj2G$
    -},pvI'<XzD_<hGJVDnlE=[31xkQu7D?{Y["}#<7R,^Y'[oa~E_=[OupggYu!RGdMOZGX!BD[TR_
    Xe@1mxiOZrpA{jx~lsJ5]rJEoeK<;8=Ajle^ZI[-I'd3X3#r<>DBwBAK,k!2=xD}Cp;>n}m^aHj>
    ,YrI;onf(*u[BX<UB!*$R#]{^6j'C7(5LplrxFAA_sI^[WG]i{UB]>BZIR^YYpv!l,<w{@0Hp4r2
    w!\5-55mmvKR!^$nwKvRRUZXAD8$+CeQuV^R4EDu>lR'sR\We7-XCv;z!%K,Os7K7ekCBU,Ce]&K
    zkO-EDlEj5Tj>-XvK^j_VJ=m[DuQl_~KaIXm{,CJna;=s*15B<V'[>;;BOJUs=^+{D+R{Vzp5-3-
    UH]Eihi<{_o'ws-lXzyzr#l+eYl_G+k^s+~b5-K*YC}rGl~1h0Q_pA{Tl1nas!{Q_[AO,3^;G{W5
    K=,QG~j=|}Z5JI2\uaAE[KGIZ!RQK#wJlD>YEUE=Ts+mR}-$,Ixo$LOa[;:Bl[K.xCO[pZVVIWoz
    oAA!QMz7m~s[vip@lZNEXjuz>1m=+G;>xW2WOU~=;E=v-xE?<OOFe<nX<pG^!Il$ns\$*sXVD\IE
    KepkovC~8B?G;#*JC2D$KKILmB!1$!TI|53-TIGOeCTZYDY5>M[G7ER\.A{}kk-<3i,*-'~-Gs2x
    ec<'-o>e*{D?3GnwT5Qn3+J{ZCaRQZ?EGGC0aR}Deo+wD<7Z'=j}~{joTC[>EAT~ZQ'ztY@12-Hv
    U!]H7UIZ7{wa@q{<^w<<$kp1pAV\X$QmeOI;2ll5>s!O{u;ziJ\QTY_<*jR,?!T-e,L}G<~BQI^.
    #QYBoU+n'JD*w}=]a,oX1^>2QHH]uR[~1ziJ~v{e@nlGAxjIsJpw}s-zTHO^C3wx~6/CQmWQD;vE
    ;^ir$}DvCu7#v3KBsZKlmj]rlnXW^]Y!]EJ5YZ?u]'HBm$W-pJ<*e+-3AA[v]2*va7~KTlE9?*5]
    zxl}I>,7e'~OB3p=pV;@ur}uE<-3>TXn\~mzoEE3#TV[F"Om*=\w>eyu<}}>rZTZx~}HU$=!AJ5#
    B}#y^iWnVu2uEx2Q#B,lRUaIUV+Yh=no$VTK^[Y~Cc@x3@KA!*nliDN~>A*$en=gV[ms\>\iRl--
    R!H}K]=rJnxl1$}Rzzjkvlu2;aV'}m_{uoBX7zCKm[nw:]Tje^+5A#LLD!;[!7xBfK+HhU>*C9AA
    oRUCT;,CB?L(p^Q<xp@=xQ<HZUsImY#G}6I-2@^Uo+{{m5Z-B]=7p}?o<J='<QDIEsCWX{LADE'^
    ~a2e>25lR8!B5YNQBwU$pkBU*-U!}5^Q<R*Bixj}Q}7La[oXi7YZujv3p?ZB_aW\$;^Qpr>nImaj
    B?j?4?j$Czv]?U[srw*J1#E#1{ALEWVE&'Y,-Sw]U#G_AU+{m;Z5x~~[ps{DH;^n<X$K~nr]mV6w
    UWC1Ts_OsTeu\@u^^D*A<CQ*XX{x@B{t1rXvpn!kA}'n5=z;z]Op^i,$YT2w1X1AWoO'GDY-2'Xe
    ;oo^DTGR1jzUs3a#vGp+IT5}nIou~BQ^kasC=-w]?Unp@DAT>7]'Yck5i-?E[@BQ$J);'p]1O#>5
    JYm89nv13lXA~?[j2Q#7ZA}Avx3@Y<s{>mv5$(>Y5aC.5ar!:-$U*UUoGvij;y[U3+#a$E]zi@A>
    I\1UnOLKoQ?cRZB-HnOTWVEuxvC2zj\*>n^,A_fzjmWSCRUvv?K-*m[p)T5mjvk[{1aX]~5e-R>+
    7np>1~7E@/AlB@\mseLcC+=e,p?BIO5E2]AszX7@1C^KLsC#Q\e^=a*!r=WpHZEAzd<+BGh*kB}<
    Y[ViBQ<qjTY2'<w-5EZuzux>]Yyow@sIejlB,,lVIezBI]=V\_{k_\^R{D2Xs+jWl,*svmk]C*nU
    \i{T7]vAr+Y2*~$aO25~IHx[C;ZqHO]aHC_At0Dn^?%7<}DxlvC]ZUn,vG@TlaXDwQEIC}Bk>*RT
    TX2'$_BBlR];a{;XS5Uw>Ew,'G@Z?{-A$QQ~VY[K<L9sB1Hjr#Q!j#$xjjpU=*7I;3<6oIjve(nn
    }3rDTTQ@m1nDVkJE*[^xQkw^j<]7^~lH{[N$O>n9R-v_G[AA%JYm{Uv_lr,$I+1GvvnG$Y~Al1=]
    1>Vkn2H$G:Gj]'?5YsBi'jTDIGDB[Y*-=]Cj<EB,J>5sos2[AEA*!{DM5Hoa{s-Ya'_1};E?&<5@
    7K+QZ|Givo^V<=;s?}1se=Y_iaY'lvoQz3VIH7W][jork>paZ?QIG,#x[e$@Yi,mG_cxB_}UwOva
    1R'^uDRTC,=VkvIuYe;7?CH!I]uE?G@ZjlCx<nHxmWvTGrBaG;2MI?'^SMATC@v3DkWv~]?XnQQY
    vB-{{1>zDx+,W,m$J1p!<Rr-'7yAHjRm-=@~{T]{v5*p{BnxVi_{*J@J*1eAeJD]res1_en^IZsv
    1vI*glUG*%=lU!m5Z7=wr{nCa_C^IjCWTjA5Tl93,ArB**u2G1VxwBwNU=Ysi[R3j$<DhjBeo?VO
    }'wRC*7koGZ}<sen_Go7Ej*KRQiDYI']KlCaQVKl5?xk+IWoi+T$1]_ko1#!Ue{QEr^;JQCOuq5B
    R\:}]E$G2W}Xo]C$^E^sv_lzjx5m_i@5'oame25[_G;m]7?GmasE'rz&RrYTz-_z5>Ba]uAQNVT$
    !-EHj]^k@=,,vFH-+Q7Evj--Vz;ea>6D>$n{vmW$;<K_'s^e1-Iv,$3ZD!rfI)-AB@CpIoq_s5wr
    r@?+\-]k>jI-,^7,{>$\2Z3Rnl,"weGAX}$+@_RRQaCOJv}-[D;K7}lW]]W72A!\]lWOp<o1#_U5
    z\]HVraUY\e-+QIUBK~*en7>qOJ,{eA-Vnz7{=@-R__+ZO1<W\JQ1{^p~-U*Z!xIK^]Hm63Y^u^Y
    o1v{wAU_^jZCi'LAIr7Xl1WU\Tm~oR]+{Q,V->J,r{BT{T-?_>2\5IR}#ClvT!C_5nA\}57X7>$V
    5z*,Kj!2GAs.#HwTsElJ,nm{'p&o\@kG]\RI5[TfkCAUilIK*E3H*s2GnenHYGrXSD-eAO2_E0{n
    Z3RG>3<]pDGXQO,nvRdl'[p3Cr}Kj{^X}'+tEInp+wVax^;XR[<a*{H-\QHDK\,<K,1{?ja2}Bow
    e#j!7nQ<=,WWW'eI&I[*!1CKE2j+E=Q^I\@uJLe[JVBxJDD+o>]\s_kYkeG_?W]kTJXjr?in!5fX
    '7=?YiU^]B{*[_~_s]?1O-^3VVKtV{XJ>psBuAp#fx_OCaU]UYmz7&kUOzp];a[z73{-vKsH*-[B
    AoeA];$ooK=u@JpRu;I3Q!aY}5inmD/r-$s3&~z=J5,-5*K'#)k=vJQY]-]xICDR_!{T*XoBAph5
    EeV6bXX<m5;7jjK+AKBomo-1$WDs--x[n{o+?9HH>GRBRkI}D[yB-x12e!;CjpO1p+,oa~Y5o-<q
    ,>o]xowK\R{r,?2Ks5s]=sO+~{KR{*wsZUD!KD=zRTB*+x$j-{[U{5nA]]z^T^2XoT@BKrWWR,{K
    r@}I4=BT!^b1_1{gDe5!Kw!}oO#K?OWKuvrG?<$^#oK{v*11ju+k,Czps<~$Tl-I{l^@[Vp;Loi*
    {apK2axG*'<UTrm!}#IWD8}O+n$1!73Xo+]mJp{GzRoDA~Cj,\3=V+^mZ<z2C~QxRuRQ1GlsxKv}
    C[jR7D313ly6;-;RJTjk{&'z2@v,Z#e3vZ_1<?puZoN#rJ}&bAsImpkB@X1KwCT3>[k7\$u}\JV3
    \'izYJ+nQe]IE*uT2B=#>GInkZX>#:=j*e*AGDi5xz![*K6w$<,,@5{$QTkc[;p-rQ1sB+KrSlJ-
    aWQ=^UnuRl?o\Gw,p}XxTR:#v{k$WCp}]-w',K{]1nD+GkEur2x}p5aA}mQ\Q*!xKvooJ-r1!}3'
    \_Kon\i&k};I>je<[o+QT_oZ@Yj>A{I2)2+R^7aB<RlmzJ[p$OV3ea'j;vq-'Eu'{_Y3R_Je>l+Y
    nRDUYZ^uHVr$X+zoEzIi[vQJr]Zo,sj*wcq>RZ=OC^'7=UQAs5H%eH5ZYei?#<l3B}Um#77]*'TW
    }ji'-_G@^=aOQm+XZ}O7Aj'ZsFi'$V\pT2A+wJC#5o13,-ZE[DE$<AQ*HuG<o3Ax}#NjV5KrHCx{
    s*3u\v'x^OoEE7KTn$7O3OJbW_a'E=k\&I*-j7$un*FF's\2GQUsiG!D]}<'KX_<n_k~D=eA\JU'
    '++7$J$QOr~21Z+U-QQ3Y>3Ernsj4+=E36&]=GWXli[1*W_R3OVZ,^xP_KE]lJA1seo3C5UZuY3$
    sVjkK<,'[=^7p:A]_<FlaGTUI2=Uwpx9iV*oRWJDDY?QQr7\Z,HseAZ]3Tn-_-aBZ+<V#>W]0-w+
    ~D_uI7vJU]kY^RHQJY_I'z7D;T]J^\f6xT_[FBrk^[CjTmOIAG\m{\UYGu_$@[ArOswa?Yp_jBsK
    5R<+3U,K+@XpZy~\<^,QjJD=;3'W=o.Iw5$?Ol]ur,\<wQ<ex{,eIuD,EE>V^HkAwxa\p?#'Yrp3
    DeiJsKp-{5Uxo?$KQEv1wKX
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#XwJppsCvz@Z17EXK<o_iUsmAlVHj,[,B|*;JC7ZBO5*o!Q,z7k-<^_=+$y]E^;G?D2=J*u+
    =ECTTWn\;z+|EfkQ'Be-e]iXa[zx^QI}VCX$sD72[l|W'#Zz1a,F|,*Eka1#>Y]J}vX}BJD72-a-
    u[,K=;Cs3H$Cr<_An?<7~H1+pGuxsx#<B#a;-7{rWYIr!=kvrlm'Tt#E]a%EXI7q{_AovOH]Q~YZ
    Yw2U}Q-+oxE_P\2,TEQk2<{'5;A}<XX<[}?uZ}XzkMI?<aYR<$~lxj7CY_[mQ+na'~j,U^pmw$#7
    TWY*ijl+IoB_jV{*nG-={Z^x2e{UnGvvkZYWZj@VQ]v3~@qe,2v1I,,3',km\!kY>o^(bs$};RmH
    -.p?D[-_BB-Exrz?}=]#I-2[]^AG~^!5W@2\Hu?<-D=Y+ep~5j\&wrBWxn'2mBv@-+Q!eF3<wolG
    $<=HR}}avr*;3QInOl52V[_GTrUvww<]jJ\xR[QIj~YZ*=wAVBw[CJ2*WIuoE1$R$UeHR<u<R7GR
    nYl_<K'sX7l1#@uH^E~R!GWU]1A$u{5}z[EI_?s2AO+5@73+_@llCl\>K>R!,EkUuvlmZ}"k>HUD
    J-r/}nY<CDQJM2j+{]2E?ek[amrQ_VDYO,!wxqovvW[$Bs[K~uaE#,k+1Emag1?YGmQr{1<w3vBI
    3YRG=:HjITC}'+n^]e,zBu*AA>=C~}ri7;i>su5iAWxei[iOV$,]suo{r-HzXmeU3J#{o>XG3,i^
    ZV]IsQI2_$eG-$uARQ&lr?pQw]pkUmTo}arB^W[v_=_YHe>2}H'EV*^B3;r!IR<jv!QOj+zr3G-x
    #pWCo{[$R>[s#I\r@JZ*j<pkp*Z1V'mh#IoKCCoRD,,2x>bY>2_G!n>r^aUKO^lKDvQkYDVD}{Ap
    *k]ZzkDl\-m=QKWe;*YoCje$aD'JU<]R<!2I3JHjuQe>HT2eoGW6sA]2kweUDTzsXlv5GCoY4'?$
    p?7=ZWvOjMLu[<kOli2rYp<uwlZ'xT*PJ\TpEZA!m>**x>a]]@QInYGaY[<1y%F[QjDqB'kaQo\x
    +o'7Zo;e[k>]5,j$5!OWW5BlEVwDI*!\Jz~v-XY[;<'OB=W,$+*,.1z'HIf^U+Ck\~@kQuO}Ip@'
    A{Q7!_!\E^TX,;-{D?RxGn},U]m+p+'rU*-{s-pl--2<>rafEU7nm^1r$D7}_Uo{DDB=]?VI]!R*
    1lv@==pax@]*$K0I7ajKoGT?_Cl*#<jZI1?Ts}p1VUV(e5=n${D@YBEGI<Wx!jET';VwR}n35>xv
    +QU=Cr!^}s'a*Iu~ACYU3Xx5Gx~*,>n{q{$T\6>aCkmB<r{R!Ip]s7+nOQ~OX}#'X^{+IO1'7A=O
    j5}YpKIAe-x[llHzVor2w~Cv^\VHoX$xj=7DT*7]]k7a=Dua}C#Auri$uZUap*?}>#^25,{w}2qo
    {Yoaw;o$_?=AsT3HDWr]x@u'aE]-a;5\K~uw*lQHxj5OWXG'\mOj$}p'<=XT$wu\sC]os*Uu5+sU
    1w@Y^[\Qs;T~<_s1sR,#EekJU~~{5w<VKCWx[R\p'j+~Bn]83IjC+]zn,B,pToQD}*X<~+JJ4@+]
    riV!;v>DpQ^=7,@_?A1Esrjerm1rB~'KHrG+Jv!D]ua1<vaC2,GWs!wCe'Az~A]ZJm[v;R]^3?',
    ZY'>HDr^JC-Xw>\aWu\Uz"6^iju=#ApeD?{Wv3C[Yv]REHY$JzO^C{=BTQROK,@D72ID?rJaaJQw
    V*shKR,'Yx2!NE?_~'?GG3$TBFv3[wnRDl]V_u1W{\7ZAp/|3R{xmRX=$l!^WYlaVmaJ]^g'ADi4
    PYH<K^$D>#'v@\u5!\iurr@_]j~Z-Ww2K~_aTvR>G\7wpV0$lmYGuHwqCaC{Bz5Wuz>}W7VavBjZ
    $e+5*[\^pD!<s>U$_UZ5$e_oU$wX,z;pRe=x@wJ{>=A<-*]rG_}<_@*5opVjQ@vr2T21{wQGz<Ux
    dWEwJAAQ]*3T-p2osQxe*AaBo*12DCZ{$?zlKB*B_"u'i{O_HQf^AoE5E}Kp+Uxp?VukskKGT{IC
    WBEJ<_vkUj;g-p5[:sBu>}QH5fRB1ukQa?$5O19=DGGQCIC-Er-H>vZBHm<C\}eC.jQ$Q*TTWZ-r
    aJ$-ozB#?Q]=!HHY>l_=xHCoKuq*X_>JUejI1i{e}eO?vj#e^z'OA~CGXVx<VQ+Fzn37EoenA<=,
    p<w;R7#Kle3w%_wlVjzEz*I$}U[X-@Y$<je\5#jaZJz^;lTeiXpu#UD-z'2j3<-Ov'{R5R;3-soY
    1}*~G;Cm,AC1Ci_+UhVk<jvOV~#_+z><+5r^![l_$Oq?>GZGW!U--+r^eX?z'D3[ZpeB3$KEREIM
    {U*R)!Qj?uVIIvCC>>AHRxb+Rm5H>K!8=Ka'ed"1Ksk$J2jN)y{&=GX-E>>,~aZ;Lko_D]E[uiA{
    b?^;j}]>_*_suojp@>aj?oGuQ,]7'^*D]l1>\5{pp[U+~sBaWQxzkl6v2rZ#='z@j_BY=C;E7aoU
    1!<AvD~GoT?}Gu74Y@pRiYji[^$m3'[B{,u\aOBI%URRHenA+pYzs{Cxz7H_^^5=2e!E}xC3Z['\
    @?X![9^K,k][*WD+1Tou<H>z<B5UDp1eJwxnvDxwYrVaj'1TZo5eA#DHVD$>3j<,']jn_=-Y\Q!+
    Vs{$H1+YI_Buse5$V$!DejC?H<\@-\VHAwiXC]THGD6=!@5+>YWE7+AraI]n=!Kxb6Jo>^FU<]TQ
    vTX1_,{V+C2AQT7W=+36-_~{O'31=mJ}Zs(pep,{Cz#L}wnz;B3<r~jkxXG]$_-15x]B7sC-HOEI
    I'*mId+CCfXUX2ElZkAnID'wR_g{'-\RBA$'{{v,7_V-apz<BT'w->jW<;'z?W7GmK2m==C\;HHT
    njW.{aX]jGR@B3;_uvXn5OeXwtB_W_$WzxeaJpl_e5^sRBsE6/B[{*n{vG7A!,-7naO<Em1vzx5v
    1@+IK_~<<mTnO{B-O$,~7\Sk'T>1Gj+s?pnlX7sm'[j8]*5\h}~]xeWN*De}-A,xI@J\_Oe1'2ou
    Q>u}xUZr]e@#QrI1K1*=B2]uB_]rj+2Dv^[El+m\WEI7vH2KDQ;E#a,oOsnBG#l\.+E>nuURu$[O
    Ww7X_+Xro?QV^H_}li_$l|$;}oTR5xKGe=Ro>?i1^[~}kGC}?,;aO#EnCBDeBUp^ruJ8Gl$I]eJ,
    >oHkITjl#z+2<A^<]-\![w;!\'OTQx,1x{eAi]\^m}=p^#1>x!v1>V_1JGrw*B-ZD<KAM{>DKAVR
    HHxwCV7D*BrVJ!$i5ix31YOEwAokYEWVYu[[2gDKW_)@xims*D{m5572wDCM4.Bu+Q$DT5F?o[7*
    aOW1<DD-e[<m<Te/=Q]m@YvC~B-xhEa-^BuA#X1HI!1CO2z'D<\^;UXdjaWXzok^n'jTx'I^|xze
    $*\-nXA'VnvXWO_;*=V\XQKOuujV-XHCVj,@U/i,^l+x;3s*[}1_!ws$apYE'#HOn_n]i;zDD~5$
    praE[GBVC?VG<?,-!WXw]s1CrAqQvwG>an,=+sWt!-rG;w+?oT<IE!]RWCzV,Hr$lGZv5G\w@Y7_
    ^QX^Ysw3_AJ^E>m*"!n*A2=ZxA{G+~aVBml@V>'~@pZ>zykv~soj{\HCCIwTw~w^B}qBGs-'o7zg
    Kn\+G?~}s3wuTep,XX~~Zrolb2[$oE@R?^I,Ba[O,3&aV}YR=uIe?u#Xsvlr?v'@jn;JQps7slY[
    ![XXR?*#Q$Ir[3Hzwsa5IA^Gl'ExOe],'IVk,ZpaREir}n5~rk=?x,5P=kwUD+Q~$>,@UpjIjejB
    )p!wGCxElr>lQmQT{uDJ]>ET!h*H2]C_$]<XOzes1]]xi\XB,EK{^}@e]o\uTKHC<\>I]^vG##UY
    5@;$>Q^<\$\5zp.rwxVv'[^8zKD\U^nQmO]jJEHGRIU<R45YC!)o?A@Up}eIEo5~75!A<+r3X-Za
    snB{D$nJlA#w{B>7re\[GJuu$D*lu2ki1_^Yr@,as$W{<Dpu<Re4G1XjO}+{;pTn$WR*Uz->lZEG
    sm@G-]2^!x>jl7pEUEs]BJ2?Y7}x/,?wQs$iI1\vaw*\,Oa2k#<1-i'K=C!lm~>w]I<+;o7m<AYA
    w]Hu<<\\_R~E#D\#wUU-H=GukY@-?@}{@YVkQV\Vvl@Xu!$^r:l3wnx-'<wxRQ_!v>#}{*7xJ$B_
    HZE2[z_R_orW{lplm!*En\G$jQ@eID1r\~,-CZ2CaKcxvHn<z2!1->mxue@n}WVO7U1m_E>WD\@j
    eH}m==v^p5\GV$=$+jY\kGD=JmIt\3=p{VKYGwU=x*#6>xKQryC!mB6,]Z7P*nm<oYn2pCW[i5+m
    }7@n*?r_,r(r]U7@wruSZ{[\8j~T]O!Z=-v]lkaA!A$#\xWVlIJjoj!_#}p\u[JH?lR#O,R<GWrs
    \Bw^XI!ADUUU[3RXZ=Xu[h^?K-ZCEm}[<U3>7^z5<DJa@<oQ+DUa$HzwnlZ{]I}UIiwR#\+CVGoX
    Ck771a\ZG-<pll,evr*2!E[5TBEj7?U1Y?j?'z^#W]En}a5QW}G'\r#zu]{nU[Il3HnQ_aEH!sE]
    <zb[KGC4A+QR'Xm=$,,H;a+'9v@lv[?G{t_;DY>>-1^J--4I>$O^I#@$v5*Qo_s?pp}T+Uj2XKuQ
    i2EBVm,>G#{OrnUBu$*!sx>\vGB,++$>wj!gZ<!>*mQYITwU_p?G,KCXH<HO,BCKr^zWlzVaXHG=
    ~l7_jJ;x_xw5U+pDWR[TGwJeG,@R7DovyFv2E?e*_jnxC@9P'Kxlv]+xE{X;]Tl'21v,Mu[-x5@]
    $|@YJpJj$Uf5-W#v'mAv*oll]3Kluj7s'lT[{^[vi<O<rR+2-,K+-X__W5\<1p<!nV;:C5Y<2D=[
    vRlj*OQ,GssGV*5+O$zjcjQ+exCuo*O+O[o+rTE=ZD[O2q1Eu~l>Jz*7BYs+!#Yk1>(*?rU~=owp
    BX,[GWjqaoO[^'RrJ*?kpAAOpX@'o^'e'_={^]CojmRx2elrwaVo77@Xq;1eu#pV*on2IoV>E]7o
    Qmo\Ee[K?l}m[%TQ+n><\CIv^p_Bnn~>3DZ<e@{+JZO~==]oGk\z~C7Q;1BB11D=**lB\?~Ca'I+
    V>^<Y-}~J$:X+$U*KpuO*Ak!x<pY?lJ_~V[4V==w=!H5HA[@SX>nw#>VvEG\,v_rU3_@}DlwY1B+
    QC}l*qTD#]D-usmXT_xBGZ0~C5e{D^3Kv;s=mJ,9nOe,snA?D17iKa'~'R?kr}7rGJUrouX!,i5n
    q{aCaVBEX7-UOE-T?1U[kJ]=Ub/Gz!vfBve'C^^Cr+2laT73?][U>e>YEGrlq.,_pK-T!\E>$_/5
    <[]K+{xnlWBvwZ'I_AC;&^NL7iOZIB{;;+\QzxWW|e272*iQ{1[~-kEnK)uI'uODp\GrD@RX;vRv
    zxRRDvx\,e@^R{Yw=JzI?uk]75jT1Rx!BG_ZJ#C*R}e-Uw~C]iU^w^p5@2-XAlf[KQ*q-TY;~o{~
    ?Q}j1[wH&1tEKx=b>A$o3E5G,}pa8p/*4;p1Xtl1TXps#UEjaUzexn}Wvxw,QYanK}=exKiDl@GQ
    w$xpw#n}iH^IOAn[<Rl5OWxmw7L*K_'*RR=VgY\E;VkY$XnCE42Qe7iVA]xW]J[U}$N^I;-jF*an
    s,QI'}}ZlBZas[>@a=BRYK=wlx{Q@rv~A#<He|YB3ZeUa+AOD3X[*3Kz5@BkU#Y[ArO,Qv{V,'J*
    w,n],-_uIBw'}Ak6nt#Azn!Y*ms3vnu][?Z,J~7x3vZU[mm=RViRvpA[~e}xJIjknopT_s#<@$O[
    ^=ZjjQ7zHV+$'r&vzax7r2CrT5B\!s~uHGD*oVk[+ojev@v0BIxzxw~Uoc1"5~Q~z?Ysnl32pzR]
    Ho?w~^_COY5'I;3o>rE77ZWWn}>vZ\YXq\=_uYQ,k-[CCn$>=X5+HuE~DrJpR+DEusXXK|IoR]Co
    [+?sXIw[Y2}m^j-BIv9ER75iru7>\mu$-Il=*\Oj'I'c>,aW/QFBJ3+IJrDl3RE,Onvzk}kh\;wE
    '<nOaGQmxW5,z<\je~HzOAAz_;RpxjX#hx2R{YAm1$Um!jO1ZaG*p_EOlviV$ZGT+R77m?Ho!;<a
    OiX35Q,BRBXrp=RlnJ=}Ha]@B^'V5n+{}k=aV+[[x}2V>-A7,KG\G|aGXBopQJkO!Y{U\{XDvU_=
    -Vx<peaXI,Y1}nQJ+K*+}JNl&'Y--(5>wpW\Ie^<,Z-aXeDQ^-_]s@!OK'j1=pj3{2aDv@zusiTs
    lG=rWRXT+xv\zZJaQEJE1QVKy.(u[U23OjuO=$BI"Wr_mDzuaB@xo5**X5{\Jvz-z3pv$}lz#UYm
    EO\*aU\@7f;Vl?|<E\]4e\a_Xlw@^k3p}IsQw5Vil,j1+>EG1T;nsKek=Y[GCd!HTW<${2EgRQur
    HBsiAO;IWO3weEAm28DGwG'N=WQoZ,}'<l#1>okTI=Wn?5rx
`endprotected
//pragma protect end



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_csi2_rx) #(
    parameter tLPX_NS = 50,
    parameter tINIT_NS = 100000,
    parameter tCLK_TERM_EN_NS = 38,
    parameter tD_TERM_EN_NS = 35,
    parameter tHS_SETTLE_NS = 85,
    parameter tHS_PREPARE_ZERO_NS = 145,
    parameter NUM_DATA_LANE = 4,
    parameter HS_BYTECLK_MHZ = 187,
    parameter CLOCK_FREQ_MHZ = 100,
    parameter DPHY_CLOCK_MODE = "Continuous",  //"Continuous", "Discontinuous"
    parameter PIXEL_FIFO_DEPTH = 1024,
    parameter AREGISTER = 8,
    parameter ENABLE_USER_DESKEWCAL = 0,
    parameter ENABLE_VCX = 0,
    parameter FRAME_MODE = "GENERIC",    //1-ACCURATE, 0-GENERIC
    parameter ASYNC_STAGE = 2,
    parameter PACK_TYPE = 4'b1111
)(
    input logic           reset_n,
    input logic           clk,				//100Mhz
    input logic           reset_byte_HS_n,
    input logic           clk_byte_HS,
    input logic           reset_pixel_n,
    input logic           clk_pixel,
    // LVDS clock lane   
    input logic           Rx_LP_CLK_P, 
	input logic           Rx_LP_CLK_N,
    output logic          Rx_HS_enable_C, 
	output logic          LVDS_termen_C,
	
    // LVDS RX data lane
    input logic  [NUM_DATA_LANE-1:0]      Rx_LP_D_P, 
	input logic  [NUM_DATA_LANE-1:0]      Rx_LP_D_N,
    input logic  [7:0]                    Rx_HS_D_0,
    input logic  [7:0]                    Rx_HS_D_1,
    input logic  [7:0]                    Rx_HS_D_2,
    input logic  [7:0]                    Rx_HS_D_3,
    input logic  [7:0]                    Rx_HS_D_4,
    input logic  [7:0]                    Rx_HS_D_5,
    input logic  [7:0]                    Rx_HS_D_6,
    input logic  [7:0]                    Rx_HS_D_7,
    // control signal to LVDS IO
    output logic [NUM_DATA_LANE-1:0]      Rx_HS_enable_D, 
	output logic [NUM_DATA_LANE-1:0]      LVDS_termen_D,
	output logic [NUM_DATA_LANE-1:0]      fifo_rd_enable,                            
	input  logic [NUM_DATA_LANE-1:0]      fifo_rd_empty,
    output logic [NUM_DATA_LANE-1:0]      DLY_enable_D,
	output logic [NUM_DATA_LANE-1:0]      DLY_inc_D,
	input  logic [NUM_DATA_LANE-1:0]      u_dly_enable_D, //user control the IO delay
	input  logic [NUM_DATA_LANE-1:0]      u_dly_inc_D, //user control the IO delay

    //AXI4-Lite Interface
    input                 axi_clk,
    input                 axi_reset_n,
    input          [5:0]  axi_awaddr,//Write Address. byte address.
    input                 axi_awvalid,//Write address valid.
    output logic          axi_awready,//Write address ready.
    input          [31:0] axi_wdata,//Write data bus.
    input                 axi_wvalid,//Write valid.
    output logic          axi_wready,//Write ready.
                          
    output logic          axi_bvalid,//Write response valid.
    input                 axi_bready,//Response ready.      
    input          [5:0]  axi_araddr,//Read address. byte address.
    input                 axi_arvalid,//Read address valid.
    output logic          axi_arready,//Read address ready.
    output logic   [31:0] axi_rdata,//Read data.
    output logic          axi_rvalid,//Read valid.
    input                 axi_rready,//Read ready.
	
    output logic          hsync_vc0,
    output logic          hsync_vc1,
    output logic          hsync_vc2,
    output logic          hsync_vc3,
    output logic          vsync_vc0,
    output logic          vsync_vc1,
    output logic          vsync_vc2,
    output logic          vsync_vc3,
    
    output logic          hsync_vc4,
    output logic          hsync_vc5,
    output logic          hsync_vc6,
    output logic          hsync_vc7,
    output logic          hsync_vc8,
    output logic          hsync_vc9,
    output logic          hsync_vc10,
    output logic          hsync_vc11,
    output logic          hsync_vc12,
    output logic          hsync_vc13,
    output logic          hsync_vc14,
    output logic          hsync_vc15,
    output logic          vsync_vc4,
    output logic          vsync_vc5,
    output logic          vsync_vc6,
    output logic          vsync_vc7,
    output logic          vsync_vc8,
    output logic          vsync_vc9,
    output logic          vsync_vc10,
    output logic          vsync_vc11,
    output logic          vsync_vc12,
    output logic          vsync_vc13,
    output logic          vsync_vc14,
    output logic          vsync_vc15,
    
    output logic [1:0]    vc,
    output logic [1:0]    vcx,
    output logic [15:0]   word_count,
    output logic [15:0]   shortpkt_data_field,
    output logic [5:0]    datatype,
    output logic [3:0]    pixel_per_clk,
    output logic [63:0]   pixel_data,
    output logic          pixel_data_valid,
`ifdef MIPI_CSI2_RX_DEBUG
    input  logic [31:0]   mipi_debug_in,
    output logic [31:0]   mipi_debug_out,
`endif
`ifdef MIPI_CSI2_RX_PIXEL_SIDEBAND
    output logic [15:0]   pixel_line_num,
    output logic [15:0]   pixel_frame_num,
    output logic [5:0]    pixel_datatype,
    output logic [15:0]   pixel_wordcount,
    output logic [1:0]    pixel_vc,
    output logic [1:0]    pixel_vcx,
`endif
    output logic          irq
    
);

logic [7:0] RxDataHS_0, RxDataHS_1, RxDataHS_2, RxDataHS_3, RxDataHS_4, RxDataHS_5, RxDataHS_6, RxDataHS_7;
logic RxValidHS_0, RxValidHS_1, RxValidHS_2, RxValidHS_3, RxValidHS_4, RxValidHS_5, RxValidHS_6, RxValidHS_7;
// logic [NUM_DATA_LANE-1:0][7:0] RxDataHS;
logic [NUM_DATA_LANE-1:0] RxValidHS, RxSyncHS;
logic RxUlpsClkNot, RxUlpsActiveClkNot;
logic [NUM_DATA_LANE-1:0] RxErrEsc, RxErrControl, RxErrSotSyncHS;
logic [NUM_DATA_LANE-1:0] RxUlpsEsc, RxUlpsActiveNot, RxSkewCalHS, RxStopState; 

generate
if (NUM_DATA_LANE == 1) begin
// assign RxDataHS[0] = RxDataHS_0;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = 1'b0;
assign RxValidHS_2 = 1'b0;
assign RxValidHS_3 = 1'b0;
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end             
else if (NUM_DATA_LANE == 2) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = 1'b0;
assign RxValidHS_3 = 1'b0;
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end
else if (NUM_DATA_LANE == 4) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
// assign RxDataHS[2] = RxDataHS_2;
// assign RxDataHS[3] = RxDataHS_3;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = RxValidHS[2];
assign RxValidHS_3 = RxValidHS[3];
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end
else if (NUM_DATA_LANE == 8) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
// assign RxDataHS[2] = RxDataHS_2;
// assign RxDataHS[3] = RxDataHS_3;
// assign RxDataHS[4] = RxDataHS_4;
// assign RxDataHS[5] = RxDataHS_5;
// assign RxDataHS[6] = RxDataHS_6;
// assign RxDataHS[7] = RxDataHS_7;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = RxValidHS[2];
assign RxValidHS_3 = RxValidHS[3];
assign RxValidHS_4 = RxValidHS[4];
assign RxValidHS_5 = RxValidHS[5];
assign RxValidHS_6 = RxValidHS[6];
assign RxValidHS_7 = RxValidHS[7];
end                              
endgenerate

`IP_MODULE_NAME(efx_dphy_rx) #(
    .tLPX_NS              (tLPX_NS),
    .tCLK_TERM_EN_NS      (tCLK_TERM_EN_NS),
    .tD_TERM_EN_NS        (tD_TERM_EN_NS),
    .tHS_SETTLE_NS        (tHS_SETTLE_NS),
    .tHS_PREPARE_ZERO_NS  (tHS_PREPARE_ZERO_NS),
    .HS_BYTECLK_MHZ       (HS_BYTECLK_MHZ),
    .CLOCK_FREQ_MHZ       (CLOCK_FREQ_MHZ),
    .NUM_DATA_LANE        (NUM_DATA_LANE),
    .ENABLE_USER_DESKEWCAL(ENABLE_USER_DESKEWCAL),
    .DPHY_CLOCK_MODE      (DPHY_CLOCK_MODE)
) dphy_rx_inst (
    .reset_n              (reset_n),
    .clk                  (clk),				
    .reset_byte_HS_n      (reset_byte_HS_n),
    .clk_byte_HS          (clk_byte_HS),
    //To LVDS clock lane   
    .Rx_LP_CLK_P          (Rx_LP_CLK_P), 
	.Rx_LP_CLK_N          (Rx_LP_CLK_N),
    .Rx_HS_enable_C       (Rx_HS_enable_C), 
	.LVDS_termen_C        (LVDS_termen_C), 
	
	//ULPS clock
	.RxUlpsClkNot         (RxUlpsClkNot),
	.RxUlpsActiveClkNot   (RxUlpsActiveClkNot),
	
	//To LVDS data lane 0
	.Rx_LP_D_P            (Rx_LP_D_P     ),
	.Rx_LP_D_N            (Rx_LP_D_N     ),
	.Rx_HS_D_0            (Rx_HS_D_0     ),
	.Rx_HS_D_1            (Rx_HS_D_1     ),
	.Rx_HS_D_2            (Rx_HS_D_2     ),
	.Rx_HS_D_3            (Rx_HS_D_3     ),
	.Rx_HS_D_4            (Rx_HS_D_4     ),
	.Rx_HS_D_5            (Rx_HS_D_5     ),
	.Rx_HS_D_6            (Rx_HS_D_6     ),
	.Rx_HS_D_7            (Rx_HS_D_7     ),
	.Rx_HS_enable_D       (Rx_HS_enable_D),
	.LVDS_termen_D        (LVDS_termen_D ),
	.fifo_rd_enable       (fifo_rd_enable),
	.fifo_rd_empty        (fifo_rd_empty ),
	.DLY_enable_D         (DLY_enable_D  ),
	.DLY_inc_D            (DLY_inc_D     ),
	.u_dly_enable_D       (u_dly_enable_D),
	.u_dly_inc_D          (u_dly_inc_D),	                   
	//To CSI2 lane 0      
    .RxUlpsEsc            (RxUlpsEsc),
    .RxUlpsActiveNot      (RxUlpsActiveNot),
    .RxErrEsc             (RxErrEsc),
    .RxErrControl         (RxErrControl),
    .RxErrSotSyncHS       (RxErrSotSyncHS),
    .RxDataHS_0           (RxDataHS_0), 
    .RxDataHS_1           (RxDataHS_1),
    .RxDataHS_2           (RxDataHS_2), 
    .RxDataHS_3           (RxDataHS_3),
    .RxDataHS_4           (RxDataHS_4), 
    .RxDataHS_5           (RxDataHS_5),
    .RxDataHS_6           (RxDataHS_6), 
    .RxDataHS_7           (RxDataHS_7),
    .RxValidHS            (RxValidHS), 
    .RxActiveHS           (),
    .RxSyncHS             (RxSyncHS),
    .RxSkewCalHS          (RxSkewCalHS),
    .RxStopState          (RxStopState),
    //LPDT mode only supported in DSI
    .RxLPDTEsc            (),
    .RxValidEsc           (),
    .RxDataEsc_0          (),
    .RxDataEsc_1          (),
    .RxDataEsc_2          (),
    .RxDataEsc_3          (),
    .RxDataEsc_4          (),
    .RxDataEsc_5          (),
    .RxDataEsc_6          (),
    .RxDataEsc_7          ()
);

`IP_MODULE_NAME(efx_csi2_rx_top) #(
    .HS_DATA_WIDTH         (8),
    .tINIT_NS              (tINIT_NS),
    .CLOCK_FREQ_MHZ        (CLOCK_FREQ_MHZ),
    .NUM_DATA_LANE         (NUM_DATA_LANE),
    .PACK_TYPE             (PACK_TYPE),
    .AREGISTER             (AREGISTER),
    .ENABLE_VCX            (ENABLE_VCX),
    .FRAME_MODE            (FRAME_MODE),
    .ASYNC_STAGE            (ASYNC_STAGE),
    .PIXEL_FIFO_DEPTH      (PIXEL_FIFO_DEPTH)
) csi2_rx_top_inst (
    .reset_n              (reset_n),
    .clk                  (clk),				
    .reset_byte_HS_n      (reset_byte_HS_n),
    .clk_byte_HS          (clk_byte_HS),
    .reset_pixel_n        (reset_pixel_n),
    .clk_pixel            (clk_pixel),
    .axi_clk              (axi_clk),
    .axi_reset_n          (axi_reset_n),
    .axi_awaddr           (axi_awaddr),
    .axi_awvalid          (axi_awvalid),
    .axi_awready          (axi_awready),
    .axi_wdata            (axi_wdata),
    .axi_wvalid           (axi_wvalid),
    .axi_wready           (axi_wready),                        
    .axi_bvalid           (axi_bvalid),
    .axi_bready           (axi_bready),
    .axi_araddr           (axi_araddr),
    .axi_arvalid          (axi_arvalid),
    .axi_arready          (axi_arready),
    .axi_rdata            (axi_rdata),
    .axi_rvalid           (axi_rvalid),
    .axi_rready           (axi_rready),
    
	.RxUlpsClkNot         (RxUlpsClkNot),
	.RxUlpsActiveClkNot   (RxUlpsActiveClkNot),
    .RxErrEsc             (RxErrEsc),
    .RxClkEsc             ({NUM_DATA_LANE{1'b0}}),
    .RxErrControl         (RxErrControl),
    .RxErrSotSyncHS       (RxErrSotSyncHS),
    .RxUlpsEsc            (RxUlpsEsc),
    .RxUlpsActiveNot      (RxUlpsActiveNot),
    .RxSkewCalHS          (RxSkewCalHS),
    .RxStopState          (RxStopState),
    .RxSyncHS             (RxSyncHS),
    .RxDataHS0            (RxDataHS_0),
    .RxDataHS1            (RxDataHS_1),  
    .RxDataHS2            (RxDataHS_2),
    .RxDataHS3            (RxDataHS_3),
    .RxDataHS4            (RxDataHS_4),
    .RxDataHS5            (RxDataHS_5),
    .RxDataHS6            (RxDataHS_6),
    .RxDataHS7            (RxDataHS_7),
    .RxValidHS0           (RxValidHS_0),
    .RxValidHS1           (RxValidHS_1),
    .RxValidHS2           (RxValidHS_2),
    .RxValidHS3           (RxValidHS_3),
    .RxValidHS4           (RxValidHS_4),
    .RxValidHS5           (RxValidHS_5),
    .RxValidHS6           (RxValidHS_6),
    .RxValidHS7           (RxValidHS_7),
    
    .hsync_vc0            (hsync_vc0),
    .hsync_vc1            (hsync_vc1),
    .hsync_vc2            (hsync_vc2),
    .hsync_vc3            (hsync_vc3),
    .vsync_vc0            (vsync_vc0),
    .vsync_vc1            (vsync_vc1),
    .vsync_vc2            (vsync_vc2),
    .vsync_vc3            (vsync_vc3), 
                          
    .hsync_vc4            (hsync_vc4),
    .hsync_vc5            (hsync_vc5),
    .hsync_vc6            (hsync_vc6),
    .hsync_vc7            (hsync_vc7),
    .hsync_vc8            (hsync_vc8),
    .hsync_vc9            (hsync_vc9),
    .hsync_vc10           (hsync_vc10),
    .hsync_vc11           (hsync_vc11),
    .hsync_vc12           (hsync_vc12),
    .hsync_vc13           (hsync_vc13),
    .hsync_vc14           (hsync_vc14),
    .hsync_vc15           (hsync_vc15),
    .vsync_vc4            (vsync_vc4),
    .vsync_vc5            (vsync_vc5),
    .vsync_vc6            (vsync_vc6),
    .vsync_vc7            (vsync_vc7),
    .vsync_vc8            (vsync_vc8),
    .vsync_vc9            (vsync_vc9),
    .vsync_vc10           (vsync_vc10),
    .vsync_vc11           (vsync_vc11),
    .vsync_vc12           (vsync_vc12),
    .vsync_vc13           (vsync_vc13),
    .vsync_vc14           (vsync_vc14),
    .vsync_vc15           (vsync_vc15),
    .vc                   (vc),
    .vcx                  (vcx),
    .word_count           (word_count),
    .shortpkt_data_field  (shortpkt_data_field),
    .datatype             (datatype),  
    .pixel_per_clk        (pixel_per_clk),
    .pixel_data           (pixel_data), 
    .pixel_data_valid     (pixel_data_valid),
`ifdef MIPI_CSI2_RX_DEBUG
    .mipi_debug_in        (mipi_debug_in    ),
    .mipi_debug_out       (mipi_debug_out   ),
`endif 
`ifdef MIPI_CSI2_RX_PIXEL_SIDEBAND
    .pixel_line_num       (pixel_line_num   ),
    .pixel_frame_num      (pixel_frame_num  ),
    .pixel_datatype       (pixel_datatype   ),
    .pixel_wordcount      (pixel_wordcount  ),
    .pixel_vc             (pixel_vc         ),
    .pixel_vcx            (pixel_vcx        ),
`endif 
    .irq                  (irq)
);


endmodule


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#lao5p[7xm5JD+Qn}}p@Iu+p\7n@-K[J="[MWr?=]\3[=V>}<VRlOCm7KDR7>D*<ln3kVk2?
    Q*nO:X<+2U{){Ck;G$k\s{ErsSKwJUz^;};<VnzxCXX{Dwo*=B~]lpzzn5*1_jpEoiTTRWYm,jW]
    ?}>w!{><7~x)$jA[@GI'_=kaT*<3vevBYejz72Z_KR^n*m2[m{aHnCiT^s#$e#x^[,v@VCY=x0P}
    !HkZHB=zQlJ$[7JD>7jVwIe#Hxa'RT]\#~sIUHR[HBJR~AQilTo{x3<G7@l}4pZx,5BB=_,5-uEH
    [jY]s!CQ#G'AR<aDACTTITp[ofsU-o#V{m$rps}vj=JY<*=Xa[pKvB}}#+Y-x>2wp}#ez,vUU=a^
    *7}Yj=*a>]o6BWUCqiDAuMYr#Z)=YD>_]Dps;]u!>EH+GOX^Ya;[^DoZ1u+RRaTkOrij]l+LPVH3
    $l~j,=Y5K>'@$5D<EasRYF91!{CBuvsE<{o#^m<EeQ[CjK^<]J*rXI[I^Q2[IxD]vo;2<Y$v${@s
    Z7V#wo@%|kwpTQ*@GiQaWps=^{DZW\*v{C5aam}p5[]x?qC^#{#5{~<]]IBn$[RJTmTaGVUr!QWl
    ep_saA#R#]']TkRI7#;vIHKjX$U\e3[Je'iXH{^7u@V]]-4mXKYZ,##,B2wnOYpQl^B=XXuKYnrH
    >+zxEZeN+*$i!7kT8J'Z#k+A3FrB$<Elo\3YHZEp[^t/mY1r21pK.mQ'ox5kjm,*zB]=zy?5IV5w
    _2jV=,o,AeD\@C+_xryz__AO'uIhrX1QE~BnA5[~rCTzHj1A=HXaV<pr<Xs@YK7v;HalH*A~cit}
    #2[px,\GWn_nsoa^?s,pW[Yj^xE$vvZH11IX\A;|Fy-H@p&ECm@ITE^)]B=W*j*AsTRWs_W[}l\Z
    !XrX,w<~'!;?E*k!rBoD2Ujl_#5**3n\%B~=o&.DvDOur1jvR@HKDjaf'~@Z;'jjbWaVAao3U!B]
    I5rornU'R>e<~lVZo0iXE5*X+KH>=GO@DmsX}TK-Q,cck}'rO\ezBG~*$GGD_X^A~,kC4vVJ[iQp
    u8mV'+;]3![;vxuAA+vRWrP~IH;Wn^XEK$s=dpTBRrp_[,<A'S-hsuD,l2,pYVCUGGl;u}\C$^~{
    9pl}1vqIeoKREr~+,xCLK7<'kLM-zG}<Q{Q?[1aWBpwDu1olC]HsO^x=nl]pZ''X',rpjmr>{U^i
    ,mlUp7Cp1{l[v2+#EG+C6n[VnJr}=J+R5YJwRVUYXGamRm^3W\1\?o$}UtYEi3DTxex-T\WnT_J*
    >-]{Wr++a<piE5OEsx1]5R-X]XS<LM]m=k]I9eWTYeXY'WTTEGl]5M^D5UR+C^C,]-DnE~,x2AA=
    vHmHZV-p^K\=+zIsZJ->+,k*IWiHHKAxUHsinwk*U[Z'\BA1}GC@>T?GCYjVQ@zIj~<R7<ZG~n,'
    Vaw-D$iE<,{1}5]<[^EU{5q)CzziC2~UYWmwt71i<9=iQmBYEw@Y!
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#D}Ea$>>B|<,J}#nU5moDvU}Rp^K{$\*y&1+D["^YQ1>'C5em3}#\}ix?<ourCp==Wa#1p*!
    YBZLv--s~{BBY6+1$'Q,AmOoXA31,['>Kj^-1V-]-]_ipHj'ms1+B>w[[sB;&}ZEW]G$w{,ACHa~
    [_!v}SOxfF.GmunCJ-;Wo_\0o=zsn1TW-\Ipo^2-9RoJu<=w@ZY@nk7iYQu3Cu$?uq6<<C<-Qk<U
    aZT1DXp*@}l~wls\!nKe#zeI?'n~17<Ex?Kg?Y$=]9AT[wYm3!C631a]3}WT6[kGA^R=!G<R$L*$
    T^dsG]Z?-U^75c['C2!a~,HX<Esw>B#7vWa5H'Ox#Oc^T,'$2+<DA~prVxJe#oe:?j~2T8(W>W?7
    O13l;D$+wWA;7DTxCV?e1$}'TKYj5oa1!lOm,7n6y*m<#*ue\os<aYg!.C[>eK-Uem\BTb};oYEw
    Cl"B{<llnBo#nz[vur<-[On]!$#KoXY~Az?+,G<QkQT2+X3![\uDvR~9g^nWJ2\*__?.hrIj_/-j
    5^!>mRlz[QK5;aVKWw),Ck[P*Jue}nHD[}lO~nx?F[=<Q14vOBw'?RU{HUp[B3E7elp+^+$a,u?L
    =C_W\1^+NQ?5C1HYIJ$@m(<pAGHpip%&\\[xBm^>CaO-?+T{C_$_n9[VjjD{sAoWYu|bSl*$H!zY
    Hlf-D,H7$<or1\v'XE]T9[Ds>OV=OU}1@<z23;X1o"$n$!>Q-nnH@D\\aD,A5]lEG+{T^p&G=;k^
    eUeQn,T525p@_e<\kDWupk{Sj;]>IrH+qRY_X$}V\1s$iJe{'G{lHwlUJIvJTjU\u[D{}c,mVT!R
    err!KJXBqXH}U.DAO;{aADeV5Rvp@[iwz}.z,'T}~@W]iW#5r+*a5AGT]J7R2zQ2*R]-D3?eI*vC
    57I,^W@'Jj2;s$s"=nAXx|BlTIEx1#[1A[.Q[C*l{x1CH+p"3ETZQRY5JX+evX,7;Ao,};RioYx!
    <Em#CA-w%U,DVm^z#Eu@X.Y$R!S'szseVGiVW1ap!Rk>=1_v=#Zj~;}EAVz=}k1Rj2+8mVJvKT]}
    y@Al+gI7-,Rz2_z3XYBu=3,KaOYjYm:d',[Y6TAR!i[p^=@mlr{>>H{e;zau@,3Ts+sJ-=la#~Cx
    jl1o2s_xJXo'lQj-l^;Al~}xK[T,]73R{PGBpCHUD7D-,ArCx'aHO-JHAH}OVTxR'mS8e\je_W>R
    ~TIO%Ks5TsGnWlH}dwIm]r3@mj$]O[|IE1jWo^;IT+Xpk}i1:whc=n=#_wYik5-=K<nm({eKOTY2
    Q5'[A,GkJv>\H*o~7B,[EI6vJu>ww$+OT'A[VX;\>wAu_zOI@BD8}f?DZEh~sw1}p<+5Br?VA}$I
    ]51lA*1jE^vn>E;2*R~EDJ73x*X7X<TC;^J1[<,=A\~a,~pE3z>mDVAY*!wxa@3Eu[uv<w3\e,u<
    7U_\maWg=Q*2[@jiG3<J_3H=)xRWBex_{0,nZz-HWB\s-Qv<G,*GDjz#Ra^@j'{s};^+GXQ,=+*p
    @UXQwj'BQvQeDJTIvA5#+*:bnw$nXEYoH-<$p6pn<$WoGZmD$Jf!U+G@1E~5~5TRiCZLWA+\G$TJ
    BjR\C[^Vl+ev{AB7GmEnc^,poVmNkXW=?wEBl'zk+C<!JrYXk5l'7r\o2'm^\vu\R*jp2o^Z1Gu+
    =~p$-oE,J\iDE'anE@]1$@rwI*Be*uV;Rk![1\{Gvnu+!n\\$G-j^{<#2X!ZUB;\COpj*C@^1?7Q
    KI,3:T9{a'#I*,}72a[WoX#li_muB'Wn\I^!oK2!wXGC{\zUsQ;Wx=Ckj5n%@avifAl=GO}H~wV_
    {MD3K'k=jKw-vO\]OxR=#;[q=HI>j!wTr#T^;++~;C7V]-E<uI?u!_}^mp<wz})z#3Dz[xX{HzBr
    +>Oorr1({+r~eE-O=J^>a7>}BvwQ)sVZQ5[7mhD~+V$Urj\R?;+YQu5~=5aYJpH+z#UEJrGO\;66
    kI!p!la$+]AU5'_=)<s_~I-E~pAaY>n,#&-j=\EaaRD?lD}2XokHR2KoCZD7izV]^iw7l~$o+=VJ
    ;pG1C[Z{{'IxTUfZp+>7sA[I_VITn_R?X^HbJ=O}$,!pIO~Gep*Xwj_H>x$o=5]m*Z'Z!_rBZr~-
    h5TD;QeUwKaY]avGrNW++@YIi<wR;7@>,a?wnoI]3^{5A,+[@]KV3RuUwj7A3O-w7D<]<3+RY~<s
    sXu12>o72_l~pv7]}2WGB-O]X@4[*e_JIw@;wH[JGa+@lHXaXQ$j#RRBr-R|E,QE{^nQ-T3!KC@W
    :V^HJBZQ'O1SCD#ev@ACEk<oA'\5,$e5$E>Ks}eXl!-s,Yz#TImj_op\BUwWgo0[EOz:jJGz@p+_
    moTDOHDUYJ>HC^+?+an;[Z=mCOi<IUsBK]sUj)?^wGVpIHKpx!17J~TrQpGnj{p?~\D+5am-Tm<^
    o#*zkoY|H7ouW+m@{oim=#$m,p~=rvp-a\s,=illZn<'FIEoTfi=w?BY75{,QX-jvIHED'a]X?Z^
    @Ks!7}M^p7Ea[qBlzZ}zApKw*,3UA7-E5Ja5A?T{xX_GmeTCO<j<[["*a>]{e_$"}WXWz*<JH_eX
    'Y<?$k{X={VTb{|eD13G"XY=+x=CuSPJAnGB>ZQ&mUaA$@j+~}u$43>[wE]-2uo3RG3r}rzRzmR,
    Tn5a^h7@nD3>^RIB~78_aaE#'ZHDHZaJVG>+ano,2ImW_<?TsKxn{+CnGrAC#R,CG{{BJ->2w;X'
    A,w+5GRes{GlG3#QrpURnT?ZHJ+^~1o><<nJv#V%NJ\n?p3uE.#O[3?e_om5!~Ov7x'G1'$A3!_o
    jCvKVWHAR#@zEH7w,^gXn3,Q]r$E(E1=*KC}CoZAn']u}a7n~}pE],X@T<TDQrZ}+Ts-3y@G+D:,
    JYn,=GUnv3=7]DOiXQ@Wa*E3=$2P}KD@,QCO[-3,_!o!G@3H/Lw[>$LY;A>JQoUv@^~u\VH^i[GT
    DuEaI+sCUB$@vlv[jJ2,w1o@lO!R3UY7PgPDCnpIx7
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#Krx*-IG5UY$}G#n2~pG#UVK;^w{]G~1}N&1+D[xKm[R*?;+17kCin1^_{$[Y[;O;*$=!*=d
    w}@o-lX2zCC[EfkQ'Be-e]iXa[zx^QI}VCX$E7$W=3NWGuElK3nva{U%{oQ#'Ol^9<5uE:L)1C=X
    $*+''C}rBl^mYCRjWUZ>J=i2IoI2sUQoiB'is\Jpte$_@^zzZaUpzCi1i\lGv(<Q2[zWWke1[}W=
    @}$lJuD*e'v1H*3jk!2_{=DmE@:3=+]Q]#?~=@o8*33;=r1<BmK_K<3^.6HA>TDt1Z;HJsVa~+!U
    =spwan;]G\A$iUrpoEDWsB'DH<sALICAD!TDBrk-*T5={Vr+Hmv7X.Y1<X/oiJ*eRn\!UVrG-Kuu
    p?2JE^[eer~lW3l%[x,j*kle1@~@K5[!gIkzHO\VZV>Qm_s5w?B_-e{m{=m*#I5#a1Q,B3''C_$i
    2,gGxW={sIIQQ5'*#lTgeCKBkj1a!{UwSL;xzxoA!RxEYTR?^@ae7XKro1pY^VQ!1R$3OV1@\E{^
    >QGZz+XXN3<<-CKoJ\}Ho(7Y}@|JoWIQ-5]<1#@$is*IUo<'+Ex=Iv{E2<$7vT;m'E7=/*v,Tj?,
    O7ERs*raZA-pl;HwDPY#*>p3]pluXoCo+Wxla3$<nU#7;KEOO}I+7n,Z$pH\*>J1WwR3xmCYWOsW
    W#xmZZqX=HI+QJ^fTBi!^orE1<2-Q@j-=1$uIEun9'}1$v~\2>ADGG7;@'l@Bwvu#!j;-!E_AxWm
    zbxu'Ea^2~~zW$nT_\y^TrCCX$Kp~T-H{Xjv_X*vp]u1H_lXn*@ro#e<CuADr~~Bxxe(AQ{Ju,}{
    Ix3B^$T@<epZ\<uTWv7uQQT!^IQB<z+=Ep,eHLe?x$;sWzCT~G2'\>u&RnO>+nel#>'=v1V?GvC!
    93Bj+OxkJRGD=IG3G7xKl'C*1$UeRfKo{K~+mv_jC**#5uVz}j-5=}~<U;!<T]Xz#jHTW!r}C_w=
    $u?Q{GH_;}0$Y},[Qw2mR#V?T=Qs@Cn\l_[TV2oxuK=12E*np\^T_,j8lvzllkYIvmBQM^pwjJ<T
    @oZ}7m{e]\,An)leG{},]'i1Tuersk<_=l}$+I?>G<kTau:Xj'u,#_5u*#vs2>##CAU}JDaCXZR{
    w2Uh/?]r3glG^wH^XJp+<{JzEnxeCwn\*IVT37D,<Yez[Xxiz=a=v?#}$2$Cv'XQk[$#ws3jDa*1
    k7n]R<Tx<lI?C;lwmVOTV[@zv-9Epv[=[e3C<Z^u}u@1zX+ee3Djhv2+vH*;p\lzuBn-r'@*WHVu
    \+UY=uOYi~TXR=}##OIA2Tx\}@EZ5V^,vC5_^*IJKr@=5H7HO=xXXS-5_Tp'=Klv;Wj,XG?RWE=$
    <m=~+p73RGo'^Ha$m]=p+V)WRWrmAQl/o]GJ\1pCqtpkp@[k~JQ@r^v~'EIJvuK_K-28qQ^sJIH{
    @AjACx,'J7n]pa'}'Z=nH/=?wj5Bv>%{$\}nlH>R=Jumej}}5xaoB#IvlH3<RE2]J{Xz-7XxD\kY
    mskXeBpj9ln=}a_k$0<s_JHEkCK5nC<]A,OQ@7A<C73pBvN2\w!rwpv(oaX[?+O[(w=ex1EU_|s}
    *$~<T#*uTB^[pA#a<pXza<![}R[5-pk]j@;D$A!R'Kv_GKjBaszUZE>'_Rr-;-%C]2[p+n^Ytie\
    \=r;w-nB[)?pka7Qp}eXs#xE!?lWY!nQBH3OwT?CxDI~112UOHzC;+%{Q-~@=xpAn{'zGB2!jjK#
    ,;?^-Z=/+HTAY5IDUAl]R<J!uB#?{{Z-Un,7D}3o>'l~rK<Y69=sTE<IY2cjapvwH_~M*}@eijpI
    oV+5\{l@l{~}>XX]Duxm\B~kIvJ;;O^l}}Uj2s\sp<aWjmAHHH3=oG}rje}^v3UD1a*'Y@ojh;w[
    B*k]UYHBXTw[ErOe73rU2\H@afx-KX'\'Ks$eO>r\$*5vanOosr*sUIil1bEKAYVG@C1H\I='UKi
    Yeuxl$Zr+lAmCHH7T-;w[D@qvijI]+YA?{}wR{ZX|Yx*H!ERmej1RQ[{+rTEXJHvT2en;DxxQTQG
    vr?2$sX=zRX'5)j1T,c]X]jHIX@j$;1G'E2H_H{8Bux_jw!Xk_!-seGv+1{KFT>!+-'Tn+V+jY9]
    !jX~5z5p5}[{=Cny=n+njm_n5'vTGwAe0pO3,]_++B\Y]=KW+EjZC+oIQ^wO[aD]i3SFQRv_H+AY
    Q>n#o+n2zXslx!2xosm!#$nRE#,1z+zV}E7$U[V+-T]^]]k25n[ErspQkp\a*-lwlAvk9vpaY>Ao
    -[<^vBY2p?rjVv2aO?]RXq9fZaQY,EUO-EjlpkOE97!;Aawv-53-_pOi;=m71CQI_j!]{R5WmN11
    @HK}K^1rBeW>@-sDJX~\zGz<Jz_~><OK{#A>j-sJs@rsvj]Q!{(Dk5J^s2v72pmy4_Qf~whjQ*U]
    r,7,m]!5#mrw><ro}Zl'C]^=!{zsp-m<{VozxaEXwGCk5<>lkvXE*[ED-+v=<zA@rUJ,I,T+XI\*
    Uo'!$iXFqw_#sB=Jwh,iTB}Yp-&EuD#VA5_\n$EA1KO<]}@?YinYKpAg7IIHE<'AK]#W!+-rov~Y
    Z';ws+GoX7QJksTm{7+B}G2u<Az3DU!kaw{7W-1#~$5'D}@l_z-T&Es2+u9+p1p<QpU.7~}ve'r#
    Q3!!lAp@M3aZZw$Ca[HU~kE\7!';!aOl,V-!ZiwJm7Zaen]_1zQRn@aCsMe>WXa5i\^U\anI7~Ye
    G?UHQorma~OemslGI}op<ktu[Dn2-w'bQHUGC}_x52OzHY1l#EIv.7!j-m7<He2~o3t{UrY7$>e^
    JoC)5Aw=I^Jr,zv!JCm#VA{en}_x\y7bw'#*3E$[o96p7=2Hnepc^^k7f0BG@n_E60e5EEKY?]
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#iwI!^-!RerlCkoUsN-Ga]U}Rp^K{$\Hf&1+D[xi*?=?W2G12?\\BoI2[AP\p>V~Uz-QD3ml
    Q-MX<+2U{){Ck;G$k\s{ErsSKwJUz^;}MJ'+H1vHa0xzuR!{H2H_zo<{O^0"Q!{w$*,iY;=B|mTA
    [vZJ7DZA[]Jr=rv3<N2C@[3(*@K>t,se$]<[D;oB7&7Q]7x<UB=\AAe'v,~H*3$B@3KpDarm_{bJ
    Y7x=5HQl^zkGDo;RzA\T_YRzRin5=zIyK6}?G-eJ*5h=wY~*$ZJ&sz{r]l[i;jeV]2T#,V1<UIe]
    'r7BJ}'1C$oBqIRO2]WYA;o[By}J[Dxr}l|@5D7dZe+2@7wrxxrnKwr7esk_<l]r[,,zIuC#s$Xu
    pl\$exiozxx~jQzi'A}_,W^JlV]}Qo2pY?Cvcm{X]1xZ!0;{uIhnjuV=lU?~UZQHYrjSqwsz]p~+
    #B!\zy1#{vWIGn'4CTn5RZ-\6CWZX]NO^7QY-Y^G1$-skGlHrzU^~B*,>OVJ51sQuX~emAIXGxYz
    <j[B$I}]uzVu+{EV>)vUG!9dRCv^l;Bo75k2ReY?KvR[R_vA!r1xGYow\+'Qrov@*_Dxs]e#HDu2
    7va\=\<lU1ia''77bC#jopI<E'2Ya1puC_A>-sY{JU<Q{maC?ZIi]GR@V-CGs$,xn$B2T#r7;Bce
    TXD^^,*qVEpOBZ*G~eW!DVZu&O;,[R~sR21RY_UX$i]_YvAnsoH11-=]7opv]<5Hjr'R'OE5AU]'
    __R>u^u5$O},r\~a]w_IUo}<o@aB;i[bpw3CEpTH{j-#)B_$~pA-<UDxxUH1[Ip!r_pA?IU;2,,l
    -wrk;a'XXb$^B!Ue-sBuY5q{r]7-rZ[[;<@kUvR_^jG~EQQVoeQ,';_<E?aUa_lOAWzJnW}fAUI*
    Y+\>l#OlI*O[NE@2BWH7ATa{eiDkUKa-~w[Vur}+-+R]<c=!+nj'jH3D7[1>w{x4jK^jHUxT1H<a
    ^O'}\BD[ce5+r7@*[r<p2B1XB.,Di^|5j?5V>TkjG~~}U{~IQvj1K,AvBp}YoOo]~j<2E+oIPI-]
    [jm[,!D,rC'O5YW*}v>WB&QCuwF-7UB~paG2X[^FA7wTjHw<62z[p+tbmIOi3]HJkB*7T5]k}3-@
    ^JoU^E<R2ouW7AK!vXJ$8Krux*K7@xrY;HDXeCi@*{n*^M,G}utGn^lU+QGQW<Gp^meGG=GdH_N}
    mpn\jD>'w-]IE1YweJGB#rCl2wvN*KX}l-[##eXQrQr$erv<sUQQ:a}]pr4,\k{-H$3~+<uhl4U^
    x<[VT@.Eu-2GJsZh?_}pL;1z+6,n]z+^Qw<{{<czR=[V<A_[sH7=nmv2rJ{\z!Y@vB$1BaDMI~+#
    X^GmeYAWrX~lc*K1VJ**AkV+{-YH'DlVD-E=iyl{JVq]Qr1'2*=wQz~G3T?pnJ!$ilOoOk[B5x[k
    {O\sJB2O2Wu@je_Yiakr]J#[#O,]eAxTp>=?>Y7f^dv\z!]*r?X>mY}ngDVauIBQ#8zGX7*Wal?D
    [n\VYzjnGTyN]>J;|==pz:HHjTBiTX]rj~*&15vCTOwY{V_$z?-$=5V18_r=K*O>_C?V~@Bz-2o7
    _QU@3vA(h!\JX:n{K\_a3uHxiKEG!$%Lol<\w^;zrG*^$ep5#=nQij}@o@K}'Hxi']',2Cgo\AlY
    2[vN'J{mT5QmYZro?DkIo>Gl2Y>$M*DDpUV3!@Dk-WE5U[3D_ra-jHY*7SnUJ[pu=_@[u!v2@mV$
    X-)6-s'e~Uv-Qa=al7}ILKC~e$UUaUV1>yn11i-Gx#i-\luUGBa*iD]VkaI7@HLYjD5I}1[pV1~o
    u!D_7_JHG>2V?pprn1_};@B5R~,RVUop>\pI{*@p#W-v+3U1w@BdB_VVHCoxXr'zQQ<pH=YCrTrJ
    N-jj[bnrC1s<AZ_}KE!ji!*H]o/n<{3%a+-I+D{EvzG~e2HDIVrW,!{ZC7Q~WH-~HEi=~j_$'?HK
    wCL!{AXyiU;=W\u7MJ'Ei?w;J\uWlqY]p+M]a5vB2DsUN>w!rfjmo{BWZwv;o>.U<T7Jnrupj3wu
    A$=UGa2<_>VnVV7eek=-x^j_sux\$A]#aHHsx<G?.<H\{xv7EG~p=sT1sZYs-'@35Ie5rTzk[V\p
    5%$zim#lJ2V'#E<Yln;vr9VUXZBGKQZe'-\wJ5$BH-jQw}Y#@ut{a}Zr*zxNoBGOleBW;<]@SWLY
    -Iin>$+5pXUO7m[*R}?[Eu!-=T{3}?*/_snDYY-ojlAO~oY@VDW70wEI!]Z7;ravG+Dixc_s#kvX
    K'j?[;'pU@[YX~v?JJU'rvYYWV'Z<1x+12aUw5UQnIX{uwIeA-E+zBpXCz}^u]aX]#!ID@Rm~_eG
    '1N,#~r~a~?[A[\9%'1<xwn1u^m^D,E{R->vQQ=ieGBQ27}UB[?_*u1>V$2,eI[[EAoXW60,3^XK
    n_5}5^_:GYmKHRA=3[a2-YJzS_^Xu,naXHD$Tuv??U_,}conJ+Ld!YRa:$|{--U'7E}|InW?Y}*m
    lk~jvo^B$BUx[\k?oiaa3O7?,Xu2ioAD.i=$$[B$D[BZs3H\C>7wa+j!{s?>2erv[E5slaYG=$>H
    $YCQV5XjCE!,BX';vD@\li'J7R2p]|}vJ5<Blu~{5__Im?OeH}oxs?rj*J[+XK=J=-Ow~Y\o{~DV
    UK_2=)ARolP_iDTpW;2:CnlTU*wReOsEI!zmOGXrBU>}1QeiFDrmYkwH]A^CX7w5i3pvB",,JX1*
    U3Io+EH5ZY#$<Z&GAOxn_BHOa7-u\@2cIJo>IHZ]xp,^,}>V2Cp]aE-asx<*p~zmYpovvODTQ5T{
    B$='>wp~B7}s'X[EKXaZBXBezAn7V[7ZbC*k@]l<z5Nr!nOi=^'#TjT;'1lO5;GbYCkOI!wK7uIQ
    ']5T3luny*UHo1zTV.=5}kFj/klpzB\nvQPs>!AW{I{]xBjr'u7aVKEx-Tp_L#>uOmUI*iBs@R<e
    [U-olG+_\'A>[G@E+w^Ju];Bp@sr!25KC${Z?.'i,=l\*r)zmxp+xlQ$O;}=HV~2R@oQ_G*5G'@l
    #zG|[@,aEZ'\i>uvHU=Z,7B1nnuAX<;EuHzk=1enu[su~+-!A,5Dlzm5xV$^,BXe7ls=Y],^UY@n
    Bor[YvDakAjUp!5DDi^D?>\Cx$z5?o@sB~AOU{Qmnw\^p\>$pAHlG;In\COH*_apbFr$1B1n@rr6
    5J5-P&IKnpEe}J3^_,YzpaxHok\sXs_wZ^C{KX@_-B5KW]JX7wq(upoKwrz'T]p,_xnYROs}nYnU
    HHJ,'Y+[][;=_E1n7e#>C>xZzY\D[j2sl!QH-nn3Q}r}{+OX*Q$$2Il2KBe[DnUI*cCT+C;pwj;v
    pRMr[T@'=BXJ1<z}Ux+nw3Wj}JuHV5=IKx_T+*3Ue']ioIkUlH~r5+s;nV@E1]n2rkWr~7<G5llY
    ;D}4MmA>~*]j$#wAwr=T}O>x,5kozCpm+;Qw]cBl>[(Cs=_HOXB,5vCW_r-7DBTraw{Cv;<[],'u
    p<TX*[s717rETu]TGoAEnw$ooxunD<CAa}O>SzJ[W_,V?rn\nzV<'xH3lZ};XUQ,sG'Uuv5@}VEe
    HAvp!na+*1OclaT!iQ1Bx[_!wD2?DO>3<$O[so^rHBXXTs)(%jZ1_{As]Rk*[^v<OB[*a2SeE$;E
    U3oRx{Ow-aeV-l]e{_p.xnC}p~8Q+xrZOeI1zK]d_f>^v+;j{Gr[{}[aXpm-wETEn=z>!nF\+*H}
    5Y_r*YBZ1?O,'lj[{UJ'BJJiU{Ch@*?YDkpp7\Ij$ODHVp5$_K]BcsRQoZ,eU5!U}[5ir]!vn35j
    C
`endprotected
//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin
`protected

    MTI!#*uD=%BBo5,~GA^R5DdwE1x*\[X@[mBB|'~IV|"[QeD,,C<eH3rB}}iy(__m,7*U?oJju7Ia
    R-{AC@X=pgaap<+1$'Q,AmOoXA31,['>Kj^iY\|_j-RJ+z[v5Xe;TGR_$Cpek][omYK$@=ekU~p<
    *n'[+Ar;'$UreU,g=!x7aYQ*,O{I)eYH}eWx,=]1sZwjDzj#]K_jTkBrDI+<s>5*3rk-jpp\e5X<
    }{$#2[}'oJz'*$Qjr}'><Om,;i5oV'_?pupETO-Ojo'=x!,\*GlWssCD>Ba2zlsk?r*_i2XTk_'i
    1#sD*DH=Ook3_]#vC^2$$}_jiOiu>!+*luC~<I?XCCmmX<O-1Os2x_J~BQm{B}{ua*5kTz^}7#,1
    Uq[l\OIA[nzW'kzsVWTa@5p\HWC=\5S!sB,{YEm~$A2Je@$h=m'^arpkI'5@Rn->V>7-p'i59[?C
    BN1iKw1u{Kx>=j?]I3m7?l_*upgiwVDP{Y;!]e'Xg<<;BKaaKo<2}Nve$$2-$\'[$\!YRu!H,lV1
    ET1<BB|1;Ol=5e_7k]D'!pGHr~^^jaIYYioY5!uq?1?_?C*ITQBV_[rUUEYu*#eX}4Q=V{yuns@V
    R;a7uB?C7e5Le<
`endprotected
//pragma protect end


//pragma protect
//pragma protect begin
`protected

    MTI!#eE;XOZ{a2=XJo}asID!o,>jW5#aXisaO[M\\#[QkJpI?32<1zYa}JKJzCiV!3vcen1*yeVf
    mT_#<|OZz%lwJ#*+[[o_n=['CW!Ul$HUl=U7BvVO#oskU2*3vH!'#Z-C_Gs_KH9,@Ce|)v++ot1x
    l@r?jHNj;Xo]g}ZJQVK!?N5CD-^sz50)^Rm*^a,mx*R^uoB?\='E-DGK>&ov3^Z',?nl@av^l}ZD
    Ym]{-X\!EHvA'^97$+n}m^A%}Vu[Y+RY!|KA^XQmRH$D-+<,G@fpz\$mo?pKYjr'zTuQ$i]Vmz@D
    i]\W&nj5!1x1CosorzxGDQzVs['[RzkTr.%i]V<uTK1-CYrS]'U#Ollre=2QBHmX$Kpuz[,H_$oA
    I7k@r^}k[~[no_o$W$oeRv!2lr<,NQuaQt?<p72EC{nO?CGWzlVW~e$+}UG;u<U-]Z]xk*q5zBUu
    jCzu_xZC[?;7#=YN+OW+[5?+o$m}_e]='1?5*kY'[O!pOT]u{w1Ai$W;;{-Z*\r~lQOpIapvYan[
    e\Xu37DWw<AZ5#+#$nMVQ{ZxZpWmY3*H-{IFVJ@o7*rVLij3j\_kwBV@a7+UlIO2WYnmQvX_~&A\
    W^@Xj<'pV3:J7l}B]i2o2Y~,ez^[Iwm(1,zi\7v\/Yg,BiC,roaNo}O,ACV$R$xE5Uw[L7^X\_@s
    ^}jJaD;-u3a{7VZV![JO+BekGz*Cu4'DQ[$?jvOI7sjln-GKV,lGxu?Dl!;C-m$&={{E]!Q$JnE{
    QOgm]7Zf#^xoB]#m\eGs'^BaGZ'?IDM_VQHpquTX\ri\,#,e#%LQaneWz1^E5K^jUTow5Yk5s{I7
    #Jxw]]?=*+EAqKBr<pxaunjkX_nOD0*jkaDd\WVOIE$nYr5~]o7@ou=Z1zYnRVQ}l!),UCDT5<\|
    L8\[$J>EERK'!IIlC3l{@QN,3X!^~n_]l\ex]n]7WC[qixRij]>ATEp>?Ow#@T[Y>Uax,pE*BU]o
    !}ippx;!IZnV]aBx_,X<DW\e<^uJBTY@KzI3Hw>sv@HWZ1p}}p?@=elH=xnR><<J7+ppe2O{73*z
    @Dov@zReY+\!n>YIGz+w+[Tsu-@~qyajmesY2CRI*o,'#5TD#2?[g+-@eClKU'3aoqtx1{?R<+A[
    a]ef1U3;\<'}G><1^'''YX7*=IxzXxKzk_3*xuT^7?}]^-5rR5=pr3'VI1wXs?mVa1-;@X!562pQ
    vMw\~<B>2T}x~Bq7Z<v}HYu1O_D~GQ3.c1T;~XX1~><Di^],]YxR3*5emOjUjO)~DooNJ{;<-l~]
    Cm21?A>+HH{UC2aOs@z-iHIZ,{<Wz*A3VOORv^o,:lupi-Gl'k]R}iRi7SoCY!'m!1,Y[lB]EBB+
    5!:UX1@BA$YrG]@EB}3Ve*3r_X>T5lzUXI{Dk_17k_GI;slW{=Y@\C253OW#Te7CII2pIu[mRBk-
    5opazZpAv~usVnrs*Q7\n~H<l$]v~VK2jG7O,u!eH-H'uV^Y3+r3o3'ck\>R*us$lr_zaQ1u'+;p
    5<]T2>E^i=Q#I25<Q^GaljzRIm$rEE,Qi1-1e^DAvom5Zz+}[zIJo;_VTO\3,wKE]?QWIpwxLKDD
    _<'J#'[?w=+rotY>XZ[Zo,oDzn=D+^Ux3G=,]$o_Y_WDsDcr7p~sB<Zz<X7fT<m!}2,TkR~\j1OA
    EStl^[3Y=TKV5<3p>KEC#[zQv~Eo![^C@WY7[R+T{I>y;EaY2lEKiYZ@#eI{xE='CJ1@G5]pxK]#
    uz=HAxI;zBe#11$?avE5ix[z}vHx+=U'GvZo,35v[z'uX{mzaIIx\7R$QkAH=jV^vKIH)\DEkA[?
    *Gl=XK+nou{5#m,l9rDXVrYKxEw+Hizz3~oz1f*uD!s!p'c;Bp}9:}[w1-=Irx7w22HQ5VI^V'B#
    owoC*$Hnu>'CzOnH1*iaYJ}IQEV;2VH5zsuYQIz{=B7QoX$2EsVm'5?TKr;[j13Ho^n$_aO7eVB'
    IA[,E+C7Yhz+QaC1D+Br@OWDE<V<5ZRT^?7J\<?YEkKn-TzCxTqb\{{\#T2>[)-}JrJw{aQxXBvZ
    }>ur;#]IY,?on[OumV@Cv<1[
`endprotected
//pragma protect end


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_top) # (
    parameter FAMILY             = "TRION",       // New Param
    parameter SYNC_CLK           = 0,
    parameter BYPASS_RESET_SYNC  = 0,             // New Param
    parameter SYNC_STAGE         = 2,             // New Param
    parameter MODE               = "STANDARD",
    parameter DEPTH              = 512,           // Reverted (Equivalent to WDATA_DEPTH) 
    parameter DATA_WIDTH         = 32,            // Reverted (Equivalent to WDATA_WIDTH)
    parameter PIPELINE_REG       = 1,             // Reverted (By default is ON)
    parameter OPTIONAL_FLAGS     = 1,             // Reverted
    parameter OUTPUT_REG         = 0,
    parameter PROGRAMMABLE_FULL  = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_FULL_ASSERT   = 27,
    parameter PROG_FULL_NEGATE   = 23,
    parameter PROGRAMMABLE_EMPTY = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_EMPTY_ASSERT  = 5,
    parameter PROG_EMPTY_NEGATE  = 7,
    parameter ALMOST_FLAG        = OPTIONAL_FLAGS,
    parameter HANDSHAKE_FLAG     = OPTIONAL_FLAGS,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter WADDR_WIDTH        = depth2width(DEPTH),
    parameter RDATA_WIDTH        = rdwidthcompute(ASYM_WIDTH_RATIO,DATA_WIDTH),
    parameter RD_DEPTH           = rddepthcompute(DEPTH,DATA_WIDTH,RDATA_WIDTH),
    parameter RADDR_WIDTH        = depth2width(RD_DEPTH),
    parameter ENDIANESS		      = 0,
    parameter OVERFLOW_PROTECT   = 1,
    parameter UNDERFLOW_PROTECT  = 1,
    parameter RAM_STYLE          = "block_ram"

)(
    input  wire                   a_rst_i,
    input  wire                   a_wr_rst_i,
    input  wire                   a_rd_rst_i,
    input  wire                   clk_i,
    input  wire                   wr_clk_i,
    input  wire                   rd_clk_i,
    input  wire                   wr_en_i,
    input  wire                   rd_en_i,
    input  wire [DATA_WIDTH-1:0]  wdata,
    output wire                   almost_full_o,
    output wire                   prog_full_o,
    output wire                   full_o,
    output wire                   overflow_o,
    output wire                   wr_ack_o,
    output wire [WADDR_WIDTH :0]  datacount_o,
    output wire [WADDR_WIDTH :0]  wr_datacount_o,
    output wire                   empty_o,
    output wire                   almost_empty_o,
    output wire                   prog_empty_o,
    output wire                   underflow_o,
    output wire                   rd_valid_o,
    output wire [RDATA_WIDTH-1:0] rdata,
    output wire [RADDR_WIDTH :0]  rd_datacount_o,
    output wire                   rst_busy
); 

localparam WR_DEPTH      = DEPTH;
localparam WDATA_WIDTH   = DATA_WIDTH;
localparam RAM_MUX_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? 32 :
                           (RDATA_WIDTH <= WDATA_WIDTH/16) ? 16 :
                           (RDATA_WIDTH <= WDATA_WIDTH/8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH/4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH/2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH)    ? 1  :
                           (RDATA_WIDTH <= WDATA_WIDTH*2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH*4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH*8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH*16) ? 16 : 32;           

wire                   wr_rst_int;
wire                   rd_rst_int;
wire                   wr_en_int;
wire                   rd_en_int;
wire [WADDR_WIDTH-1:0] waddr;
wire [RADDR_WIDTH-1:0] raddr;
wire                   wr_clk_int;
wire                   rd_clk_int;
wire [WADDR_WIDTH :0]  wr_datacount_int;
wire [RADDR_WIDTH :0]  rd_datacount_int;

generate
    if (ASYM_WIDTH_RATIO == 4) begin
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
            assign datacount_o    = wr_datacount_int;
            assign wr_datacount_o = {(WADDR_WIDTH+1){1'b0}};
            assign rd_datacount_o = {(RADDR_WIDTH+1){1'b0}};
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
            assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
            assign wr_datacount_o = wr_datacount_int;
            assign rd_datacount_o = rd_datacount_int;
        end
    end
    else begin
        assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
        assign wr_datacount_o = wr_datacount_int;
        assign rd_datacount_o = rd_datacount_int;
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
        end
    end    

    if (!SYNC_CLK) begin
        //(* async_reg = "true" *) reg [1:0] wr_rst;
        //(* async_reg = "true" *) reg [1:0] rd_rst;
        //
        //always @ (posedge wr_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        wr_rst <= 2'b11;
        //    else 
        //        wr_rst <= {wr_rst[0],1'b0};            
        //end
        //
        //always @ (posedge rd_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        rd_rst <= 2'b11;
        //    else 
        //        rd_rst <= {rd_rst[0],1'b0};            
        //end
        

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_wr_rst_i;
            assign rd_rst_int = a_rd_rst_i; 
            assign rst_busy   = 1'b0;
        end 
        else begin
            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_wr_rst (
               .clk   (wr_clk_int),
               .reset (a_rst_i),
               .d_o   (wr_rst_int)
            );

            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_rd_rst (
               .clk   (rd_clk_int),
               .reset (a_rst_i),
               .d_o   (rd_rst_int)
            );
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end

    end
    else begin
        //(* async_reg = "true" *) reg [1:0] a_rst;
        //
        //always @ (posedge clk_i or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        a_rst <= 2'b11;
        //    else 
        //        a_rst <= {a_rst[0],1'b0};            
        //end
        wire   a_rst;

        `IP_MODULE_NAME(efx_resetsync) #(
           .ACTIVE_LOW (0)
        ) efx_resetsync_a_rst (
           .clk   (clk_i),
           .reset (a_rst_i),
           .d_o   (a_rst)
        );

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_rst_i;
            assign rd_rst_int = a_rst_i;
            assign rst_busy   = 1'b0;
        end
        else begin
            assign wr_rst_int = a_rst;
            assign rd_rst_int = a_rst;   
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end 
endgenerate

`IP_MODULE_NAME(efx_fifo_ram) # (
    .FAMILY        (FAMILY),
    .WR_DEPTH      (WR_DEPTH),
    .RD_DEPTH      (RD_DEPTH),
    .WDATA_WIDTH   (WDATA_WIDTH),
    .RDATA_WIDTH   (RDATA_WIDTH),
    .WADDR_WIDTH   (WADDR_WIDTH),
    .RADDR_WIDTH   (RADDR_WIDTH),
    .OUTPUT_REG    (OUTPUT_REG),
    .RAM_MUX_RATIO (RAM_MUX_RATIO),
    .ENDIANESS	    (ENDIANESS),
    .RAM_STYLE     (RAM_STYLE)
) xefx_fifo_ram (
    .wdata (wdata), 
    .waddr (waddr),
    .raddr (raddr), 
    .we    (wr_en_int), 
    .re    (rd_en_int),
    .wclk  (wr_clk_int),
    .rclk  (rd_clk_int),
    .rdata (rdata)
);

`IP_MODULE_NAME(efx_fifo_ctl) # (
    .SYNC_CLK           (SYNC_CLK),
    .SYNC_STAGE         (SYNC_STAGE),
    .MODE               (MODE),
    .WR_DEPTH           (WR_DEPTH),
    .WADDR_WIDTH        (WADDR_WIDTH),
    .RADDR_WIDTH        (RADDR_WIDTH),
    .ASYM_WIDTH_RATIO   (ASYM_WIDTH_RATIO),
    .RAM_MUX_RATIO      (RAM_MUX_RATIO),
    .PIPELINE_REG       (PIPELINE_REG),
    .ALMOST_FLAG        (ALMOST_FLAG),
    .PROGRAMMABLE_FULL  (PROGRAMMABLE_FULL),
    .PROG_FULL_ASSERT   (PROG_FULL_ASSERT),
    .PROG_FULL_NEGATE   (PROG_FULL_NEGATE),
    .PROGRAMMABLE_EMPTY (PROGRAMMABLE_EMPTY),
    .PROG_EMPTY_ASSERT  (PROG_EMPTY_ASSERT),
    .PROG_EMPTY_NEGATE  (PROG_EMPTY_NEGATE),
    .OUTPUT_REG         (OUTPUT_REG),
    .HANDSHAKE_FLAG     (HANDSHAKE_FLAG),
    .OVERFLOW_PROTECT   (OVERFLOW_PROTECT),
    .UNDERFLOW_PROTECT  (UNDERFLOW_PROTECT)
) xefx_fifo_ctl (
    .wr_rst          (wr_rst_int),
    .rd_rst          (rd_rst_int),
    .wclk            (wr_clk_int),
    .rclk            (rd_clk_int),
    .we              (wr_en_i),
    .re              (rd_en_i),
    .wr_full         (full_o),
    .wr_ack          (wr_ack_o),
    .rd_empty        (empty_o),
    .wr_almost_full  (almost_full_o),
    .rd_almost_empty (almost_empty_o),
    .wr_prog_full    (prog_full_o),
    .rd_prog_empty   (prog_empty_o),
    .wr_en_int       (wr_en_int),
    .rd_en_int       (rd_en_int),
    .waddr           (waddr),
    .raddr           (raddr),
    .wr_datacount    (wr_datacount_int),
    .rd_datacount    (rd_datacount_int),
    .rd_vld          (rd_valid_o),
    .wr_overflow     (overflow_o),
    .rd_underflow    (underflow_o)
);

function integer depth2width;
input [31:0] depth;
begin : fnDepth2Width
    if (depth > 1) begin
        depth = depth - 1;
        for (depth2width=0; depth>0; depth2width = depth2width + 1)
            depth = depth>>1;
        end
    else
       depth2width = 0;
end
endfunction 

function integer width2depth;
input [31:0] width;
begin : fnWidth2Depth
    width2depth = width**2;
end
endfunction

function integer rdwidthcompute;
input [31:0] asym_option;
input [31:0] wr_width;
begin : RdWidthCompute
    rdwidthcompute = (asym_option==0)? wr_width/16 :
                     (asym_option==1)? wr_width/8  :
                     (asym_option==2)? wr_width/4  :
                     (asym_option==3)? wr_width/2  :
                     (asym_option==4)? wr_width/1  :
                     (asym_option==5)? wr_width*2  :
                     (asym_option==6)? wr_width*4  :
                     (asym_option==7)? wr_width*8  :
                     (asym_option==8)? wr_width*16 : wr_width/1;
end
endfunction

function integer rddepthcompute;
input [31:0] wr_depth;
input [31:0] wr_width;
input [31:0] rd_width;
begin : RdDepthCompute
    rddepthcompute = (wr_depth * wr_width) / rd_width;
end
endfunction

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ram) #(
    parameter FAMILY        = "TRION",
    parameter WR_DEPTH      = 512,
    parameter RD_DEPTH      = 512,
    parameter WDATA_WIDTH   = 8,
    parameter RDATA_WIDTH   = 8,
    parameter WADDR_WIDTH   = 9,
    parameter RADDR_WIDTH   = 9,
    parameter OUTPUT_REG    = 1,
    parameter RAM_MUX_RATIO = 4,
    parameter ENDIANESS 	 = 0, //0: Big endian (default)   1: Little endian 
    parameter RAM_STYLE     = "block_ram"
) (
    input  wire                     wclk,
    input  wire                     rclk,
    input  wire                     we,
    input  wire                     re,
    input  wire [(WDATA_WIDTH-1):0] wdata,
    input  wire [(WADDR_WIDTH-1):0] waddr,
    input  wire [(RADDR_WIDTH-1):0] raddr,
    output wire [(RDATA_WIDTH-1):0] rdata
);

localparam MEM_DEPTH         = (WR_DEPTH > RD_DEPTH) ? WR_DEPTH : RD_DEPTH;
localparam MEM_DATA_WIDTH    = (WDATA_WIDTH > RDATA_WIDTH) ? RDATA_WIDTH : WDATA_WIDTH;   
localparam LSB_WIDTH         = (WADDR_WIDTH > RADDR_WIDTH) ? (WADDR_WIDTH - RADDR_WIDTH) : (RADDR_WIDTH - WADDR_WIDTH);
localparam RDATA_WDATA_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? "ONE_THIRTYTWO" :
                               (RDATA_WIDTH <= WDATA_WIDTH/16) ? "ONE_SIXTEENTH" :
                               (RDATA_WIDTH <= WDATA_WIDTH/8)  ? "ONE_EIGHTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/4)  ? "ONE_FOURTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/2)  ? "ONE_HALF"      :
                               (RDATA_WIDTH <= WDATA_WIDTH)    ? "ONE"           :
                               (RDATA_WIDTH <= WDATA_WIDTH*2)  ? "TWO_TIMES"     :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "FOUR_TIMES"    :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "EIGHT_TIMES"   :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "SIXTEEN_TIMES" : "THIRTYTWO_TIMES";

(* syn_ramstyle = RAM_STYLE *) reg [MEM_DATA_WIDTH-1:0] ram[MEM_DEPTH-1:0];
reg [RDATA_WIDTH-1:0]    r_rdata_1P;
reg [RDATA_WIDTH-1:0]    r_rdata_2P;

wire                     re_int;

generate
    if (FAMILY == "TRION") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                end
                r_rdata_2P <= r_rdata_1P;
            end
        end
        
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            if (ENDIANESS == 0) begin 
            	integer i;
            	always @ (posedge wclk) begin
                	for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                    end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                	end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
        end
        
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  ||RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
            //integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end 
            end
            else begin //endianess == 1
            	always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end
            end
        end
        if (OUTPUT_REG) begin
            assign re_int = re;
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end
    end
    else begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                    r_rdata_2P <= r_rdata_1P;
                end
            end
        end 
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            //integer i;
            if (ENDIANESS == 0) begin
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
            
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
        end
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  || RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
        	//integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
            
            else begin //endianess ==1
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
        end
        // NIC
        //if (MODE == "STANDARD") begin     
        //    if (OUTPUT_REG) begin
        //        reg re_r;
        //        always @ (posedge rclk) begin
        //            re_r <= re;
        //        end
        //        assign re_int = re | re_r;        
        //        assign rdata  = r_rdata_2P;            
        //    end
        //    else begin
        //        assign re_int = re;
        //        assign rdata  = r_rdata_1P;
        //    end
        //end
        //else begin
        //    assign re_int = re;
        //    assign rdata  = r_rdata_1P;
        //end    
        if (OUTPUT_REG) begin
            reg re_r;
            always @ (posedge rclk) begin
                re_r <= re;
            end
            assign re_int = re | re_r;        
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end    
    end
endgenerate

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2021 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   gray2bin.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Gray to Binary Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_gray2bin)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] bin_o,
 // input
 input [WIDTH-1:0] gray_i);

//---------------------------------------------------------------------
// Recursive Module
// Description: reduction xor
generate
   if (WIDTH > 1) begin
      wire [1:0] bin_1;
      assign bin_1 = {gray_i[WIDTH-1], gray_i[WIDTH-1]^gray_i[WIDTH-2]};
      if (WIDTH == 2) begin
	 assign bin_o = bin_1;
      end
      else begin
	 assign bin_o[WIDTH-1] = bin_1[1];
	 `IP_MODULE_NAME(efx_fifo_gray2bin) #(.WIDTH(WIDTH-1)) u_gray2bin (.bin_o(bin_o[WIDTH-2:0]), .gray_i({bin_1[0], gray_i[WIDTH-3:0]}));
      end
   end
   else /* if (WIDTH == 1) */
     assign bin_o = gray_i;
endgenerate
   
endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   pipe_reg.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Parallel Pipelining Shift Register
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_datasync) #(
    parameter STAGE = 32,
    parameter WIDTH = 4
) (
 input  wire             clk_i,
 input  wire [WIDTH-1:0] d_i,
 output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] pipe_reg [STAGE-1:0];
integer i;

always @(posedge clk_i) begin
  for (i=STAGE-1; i>0; i = i - 1) begin
    pipe_reg[i] <= pipe_reg[i-1];
  end
  pipe_reg[0] <= d_i;
end
assign d_o = pipe_reg[STAGE-1];
   

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_resetsync) #(
    parameter ASYNC_STAGE = 2,
    parameter ACTIVE_LOW = 1
) (
    input  wire             clk,
    input  wire             reset,
    output wire             d_o
);


generate
   if (ACTIVE_LOW == 1) begin: active_low
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (1),
         .RST_VALUE  (0)
      ) efx_resetsync_active_low (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b1),
         .d_o     (d_o)
      );
   end
   else begin: active_high
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (0),
         .RST_VALUE  (1)
      ) efx_resetsync_active_high (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b0),
         .d_o     (d_o)
      );
   end
endgenerate

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ctl) # (
    parameter SYNC_CLK           = 1,
    parameter SYNC_STAGE         = 2,
    parameter MODE               = "STANDARD",
    parameter WR_DEPTH           = 512,
    parameter WADDR_WIDTH        = 9,
    parameter RADDR_WIDTH        = 9,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter RAM_MUX_RATIO      = 1,
    parameter PIPELINE_REG       = 1,
    parameter ALMOST_FLAG        = 1,
    parameter PROGRAMMABLE_FULL  = "NONE",
    parameter PROG_FULL_ASSERT   = 0,
    parameter PROG_FULL_NEGATE   = 0,
    parameter PROGRAMMABLE_EMPTY = "NONE",
    parameter PROG_EMPTY_ASSERT  = 0,
    parameter PROG_EMPTY_NEGATE  = 0,
    parameter OUTPUT_REG         = 0,
    parameter HANDSHAKE_FLAG     = 1, 
    parameter OVERFLOW_PROTECT   = 0,
    parameter UNDERFLOW_PROTECT  = 0
)(
    input   wire                   wr_rst,
    input   wire                   rd_rst,
    input   wire                   wclk,
    input   wire                   rclk,
    input   wire                   we,
    input   wire                   re,
    output  wire                   wr_full,
    output  reg                    wr_ack,
    output  wire                   wr_almost_full,
    output  wire                   rd_empty,
    output  wire                   rd_almost_empty,
    output  wire                   wr_prog_full,
    output  wire                   rd_prog_empty,
    output  wire                   wr_en_int,
    output  wire                   rd_en_int,
    output  wire [WADDR_WIDTH-1:0] waddr,
    output  wire [RADDR_WIDTH-1:0] raddr,
    output  wire [WADDR_WIDTH:0]   wr_datacount,
    output  wire [RADDR_WIDTH:0]   rd_datacount,
    output  wire                   rd_vld,
    output  reg                    wr_overflow,
    output  reg                    rd_underflow
);

reg  [WADDR_WIDTH:0] waddr_cntr;
reg  [WADDR_WIDTH:0] waddr_cntr_r;
reg  [RADDR_WIDTH:0] raddr_cntr;
reg                  rd_valid;

wire [WADDR_WIDTH:0] waddr_int;
wire [RADDR_WIDTH:0] raddr_int;
wire                 rd_empty_int;
wire [WADDR_WIDTH:0] wr_datacount_int;
wire [RADDR_WIDTH:0] rd_datacount_int;

assign waddr     = waddr_cntr[WADDR_WIDTH-1:0];
// NIC
wire [RADDR_WIDTH:0] ram_raddr;
assign raddr     = (MODE == "FWFT") ? ram_raddr[RADDR_WIDTH-1:0] : raddr_cntr[RADDR_WIDTH-1:0];
//assign raddr     = raddr_cntr[RADDR_WIDTH-1:0];
//assign wr_en_int = we & ~wr_full;
assign wr_en_int = OVERFLOW_PROTECT ? we & ~wr_full : we;

assign wr_datacount  = wr_datacount_int;
assign rd_datacount  = ASYM_WIDTH_RATIO == 4 && SYNC_CLK ? wr_datacount_int : rd_datacount_int;


generate
    if (MODE == "FWFT") begin    
        // NIC
        //assign rd_en_int     = (~rd_empty_int & rd_empty) | (re & ~rd_empty_int);
        //assign rd_empty      = rd_empty_fwft;

        assign rd_en_int     = 1'b1;
        //assign rd_empty      = rd_empty_int;
        
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        init_set <= 1'b1;
        //    end
        //    else if (~init_set & rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //    else if (~rd_empty_int) begin
        //        init_set <= 1'b0;            
        //    end
        //    else if (rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //end
        // NIC 
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        rd_empty_fwft <= 1'b1;
        //    end
        //    else if (rd_en_int) begin
        //        rd_empty_fwft <= 1'b0;            
        //    end
        //    else if (re) begin
        //        rd_empty_fwft <= 1'b1;            
        //    end
        //end          
        
        //if (FAMILY == "TRION") begin
        if (OUTPUT_REG) begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid <= 1'b0;
                end
                else begin
                    rd_valid <= ~rd_empty;
                end
            end
            assign rd_vld = rd_valid;                
        end    
        else begin
            assign rd_vld = ~rd_empty;                
        end
        
        assign rd_empty = rd_empty_int;
    end
    else begin
        assign rd_en_int     = UNDERFLOW_PROTECT ? re & ~rd_empty_int : re;
        assign rd_empty      = rd_empty_int;
        
        if (OUTPUT_REG) begin
            reg rd_valid_r;
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid_r <= 'h0;
                    rd_valid   <= 'h0;
                end
                else begin
                    {rd_valid,rd_valid_r} <= {rd_valid_r,rd_en_int};
                end
            end
            assign rd_vld = rd_valid;            
        end
        else begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid  <= 'h0;
                end
                else begin
                    rd_valid <= rd_en_int;
                end
            end
            assign rd_vld = rd_valid;
        end
    end
    
    if (ALMOST_FLAG) begin
        assign wr_almost_full   = wr_datacount >= WR_DEPTH-1;
        assign rd_almost_empty  = rd_datacount <= 'd1;
    end
    else begin
        assign wr_almost_full   = 1'b0;
        assign rd_almost_empty  = 1'b0;        
    end
    
    if (PROGRAMMABLE_FULL == "STATIC_SINGLE") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_datacount >= PROG_FULL_ASSERT;
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end  
    end
    else if (PROGRAMMABLE_FULL == "STATIC_DUAL") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_prog_full_int ? wr_datacount >= PROG_FULL_NEGATE : wr_datacount >= PROG_FULL_ASSERT;   

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end              
    end
    else begin
        assign wr_prog_full  = 1'b0; 
    end
        
    if (PROGRAMMABLE_EMPTY == "STATIC_SINGLE") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty  = rd_datacount <= PROG_EMPTY_ASSERT;
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end
    end
    else if (PROGRAMMABLE_EMPTY == "STATIC_DUAL") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty = rd_prog_empty_int ? (rd_datacount <= PROG_EMPTY_NEGATE) : (rd_datacount <= PROG_EMPTY_ASSERT);
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end  
    end
    else begin
        assign rd_prog_empty  = 1'b0; 
    end
    
    if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_ack <= 1'b0;
            end
            else begin
                // NIC
                //wr_ack <= wr_en_int & ~wr_overflow;
                wr_ack <= OVERFLOW_PROTECT ? wr_en_int & ~wr_overflow : wr_en_int;
            end
        end
    end

    if (OVERFLOW_PROTECT) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else if (we && wr_full) begin
                wr_overflow <= 1'b1;
            end
            else begin
                wr_overflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else begin
                wr_overflow <= we && wr_full ? 1'b1 : wr_overflow;
            end
        end
    end

    if (UNDERFLOW_PROTECT) begin     
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else if (re && rd_empty) begin
                rd_underflow <= 1'b1;
            end
            else begin
                rd_underflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else begin
                rd_underflow <= re && rd_empty ? 1'b1 : rd_underflow;
            end
        end
    end
    
    localparam RATIO_WIDTH = (RADDR_WIDTH >= WADDR_WIDTH)? RADDR_WIDTH - WADDR_WIDTH : WADDR_WIDTH - RADDR_WIDTH;
    
    if (ASYM_WIDTH_RATIO < 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:RATIO_WIDTH]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:RATIO_WIDTH];
        assign wr_datacount_int = waddr_cntr - (raddr_int/RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int*RAM_MUX_RATIO)-raddr_cntr;
    end
    // NIC
    else if (ASYM_WIDTH_RATIO == 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:0]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - raddr_int;
        assign rd_datacount_int = waddr_int - raddr_cntr;
    end
    else begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:RATIO_WIDTH] == raddr_int[RADDR_WIDTH-1:0]);
        // NIC
        //assign rd_empty_int     = (waddr_int- raddr_cntr*RAM_MUX_RATIO) < RAM_MUX_RATIO;
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:RATIO_WIDTH] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - (raddr_int*RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int/RAM_MUX_RATIO)-raddr_cntr;
    end
endgenerate

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr   <= 'h0;
    end
    else if (wr_en_int) begin
        waddr_cntr   <= waddr_cntr + 1'b1;
    end
end

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr_r  <= 'h0;
    end
    else begin
        waddr_cntr_r  <= waddr_cntr;
    end
end

always @ (posedge rclk or posedge rd_rst) begin
    if (rd_rst) begin
        raddr_cntr   <= 'h0;
    end
    // NIC
    //else if (rd_en_int) begin
    else begin
        //raddr_cntr   <= raddr_cntr + 1'b1;
        //raddr_cntr   <= raddr_cntr + (re & ~rd_empty_int);
        raddr_cntr   <= ram_raddr;
    end
end
// NIC
assign ram_raddr = raddr_cntr + (UNDERFLOW_PROTECT ? re & ~rd_empty_int : re);


generate
    if (SYNC_CLK) begin : sync_clk
       if (MODE == "FWFT") begin
          assign waddr_int = waddr_cntr_r;
          assign raddr_int        = raddr_cntr;
       end
       else begin
          assign waddr_int        = waddr_cntr;
          assign raddr_int        = raddr_cntr;
       end
    end
    else begin : async_clk
        reg  [RADDR_WIDTH:0] raddr_cntr_gry_r;
        reg  [WADDR_WIDTH:0] waddr_cntr_gry_r;
        
        wire [RADDR_WIDTH:0] raddr_cntr_gry;
        wire [RADDR_WIDTH:0] raddr_cntr_gry_sync;
        wire [RADDR_WIDTH:0] raddr_cntr_sync_g2b;
        wire [WADDR_WIDTH:0] waddr_cntr_gry;
        wire [WADDR_WIDTH:0] waddr_cntr_gry_sync;
        wire [WADDR_WIDTH:0] waddr_cntr_sync_g2b;

        if (PIPELINE_REG) begin
            reg  [RADDR_WIDTH:0] raddr_cntr_sync_g2b_r;    
            reg  [WADDR_WIDTH:0] waddr_cntr_sync_g2b_r;        
        
            assign waddr_int        = waddr_cntr_sync_g2b_r;
            assign raddr_int        = raddr_cntr_sync_g2b_r;
            
            always @ (posedge wclk or posedge wr_rst) begin
                if (wr_rst) begin
                    raddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    raddr_cntr_sync_g2b_r <= raddr_cntr_sync_g2b;
                end
            end
            
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    waddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    waddr_cntr_sync_g2b_r <= waddr_cntr_sync_g2b;
                end
            end
        end
        else begin
            assign waddr_int        = waddr_cntr_sync_g2b;
            assign raddr_int        = raddr_cntr_sync_g2b;        
        end
      
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                raddr_cntr_gry_r <= 'h0;
            end
            else begin
                raddr_cntr_gry_r <= raddr_cntr_gry;
            end
        end
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_bin2gray    (.bin_i(raddr_cntr), .gray_o(raddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (RADDR_WIDTH+1), .ACTIVE_LOW(0)) xrd2wr_addr_sync   (.clk(wclk), .reset_n(wr_rst), .d_i(raddr_cntr_gry_r), .d_o(raddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_gray2bin    (.gray_i(raddr_cntr_gry_sync), .bin_o(raddr_cntr_sync_g2b));
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                waddr_cntr_gry_r <= 'h0;
            end
            else begin
                waddr_cntr_gry_r <= waddr_cntr_gry;
            end
        end        
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_bin2gray  (.bin_i(waddr_cntr), .gray_o(waddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (WADDR_WIDTH+1), .ACTIVE_LOW(0)) wr2rd_addr_sync (.clk(rclk), .reset_n(rd_rst), .d_i(waddr_cntr_gry_r), .d_o(waddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_gray2bin  (.gray_i(waddr_cntr_gry_sync), .bin_o(waddr_cntr_sync_g2b));        

    end
endgenerate
endmodule


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   bin2gray.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Binary to Gray Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_bin2gray)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] gray_o,
 // input
 input [WIDTH-1:0] bin_i
 );

//---------------------------------------------------------------------
// Function :   bit_xor
// Description: reduction xor
function bit_xor (
  input [31:0] nex_bit,
  input [31:0] curr_bit,
  input [WIDTH-1:0] xor_in);
  begin : fn_bit_xor
    bit_xor = xor_in[nex_bit] ^ xor_in[curr_bit];
  end
endfunction

// Convert Binary to Gray, bit by bit
generate 
begin
  genvar bit_idx;
  for(bit_idx=0; bit_idx<WIDTH-1; bit_idx=bit_idx+1) begin : gBinBits
    assign gray_o[bit_idx] = bit_xor(bit_idx+1, bit_idx, bin_i);
  end
  assign   gray_o[WIDTH-1] = bin_i[WIDTH-1];
end
endgenerate

endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////

`undef IP_UUID
`undef IP_NAME_CONCAT
`undef IP_MODULE_NAME
