// =============================================================================
// Generated by efx_ipmgr
// Version: 2023.1.150
// IP Version: 5.1
// =============================================================================

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2023 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//                                                                       
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//                                                                       
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.                                                
//
////////////////////////////////////////////////////////////////////////////////

`define IP_UUID _5ee7df9aa375494db25875c4929bcb33
`define IP_NAME_CONCAT(a,b) a``b
`define IP_MODULE_NAME(name) `IP_NAME_CONCAT(name,`IP_UUID)
module DdrCtrl (
input clk,
input core_clk,
input twd_clk,
input tdqss_clk,
input tac_clk,
input reset_n,
output reset,
output cs,
output ras,
output cas,
output we,
output cke,
output [15:0] addr,
output [2:0] ba,
output odt,
output [2:0] shift,
output [4:0] shift_sel,
output shift_ena,
input cal_ena,
output cal_done,
output cal_pass,
output [6:0] cal_fail_log,
output [2:0] cal_shift_val,
output [1:0] o_dm_hi,
output [1:0] o_dm_lo,
input [1:0] i_dqs_hi,
input [1:0] i_dqs_lo,
input [1:0] i_dqs_n_hi,
input [1:0] i_dqs_n_lo,
output [1:0] o_dqs_hi,
output [1:0] o_dqs_lo,
output [1:0] o_dqs_n_hi,
output [1:0] o_dqs_n_lo,
output [1:0] o_dqs_oe,
output [1:0] o_dqs_n_oe,
input [15:0] i_dq_hi,
input [15:0] i_dq_lo,
output [15:0] o_dq_hi,
output [15:0] o_dq_lo,
output [15:0] o_dq_oe,
input [7:0] axi_aid,
input [31:0] axi_aaddr,
input [7:0] axi_alen,
input [2:0] axi_asize,
input [1:0] axi_aburst,
input [1:0] axi_alock,
input axi_avalid,
output axi_aready,
input axi_atype,
input [7:0] axi_wid,
input [127:0] axi_wdata,
input [15:0] axi_wstrb,
input axi_wlast,
input axi_wvalid,
output axi_wready,
output [7:0] axi_rid,
output [127:0] axi_rdata,
output axi_rlast,
output axi_rvalid,
input axi_rready,
output [1:0] axi_rresp,
output [7:0] axi_bid,
output [1:0] axi_bresp,
output axi_bvalid,
input axi_bready
);
`IP_MODULE_NAME(efx_ddr3_soft_controller) u_efx_ddr3_soft_controller(
.clk ( clk ),
.core_clk ( core_clk ),
.twd_clk ( twd_clk ),
.tdqss_clk ( tdqss_clk ),
.tac_clk ( tac_clk ),
.reset_n ( reset_n ),
.reset ( reset ),
.cs ( cs ),
.ras ( ras ),
.cas ( cas ),
.we ( we ),
.cke ( cke ),
.addr ( addr ),
.ba ( ba ),
.odt ( odt ),
.shift ( shift ),
.shift_sel ( shift_sel ),
.shift_ena ( shift_ena ),
.cal_ena ( cal_ena ),
.cal_done ( cal_done ),
.cal_pass ( cal_pass ),
.cal_fail_log ( cal_fail_log ),
.cal_shift_val ( cal_shift_val ),
.o_dm_hi ( o_dm_hi ),
.o_dm_lo ( o_dm_lo ),
.i_dqs_hi ( i_dqs_hi ),
.i_dqs_lo ( i_dqs_lo ),
.i_dqs_n_hi ( i_dqs_n_hi ),
.i_dqs_n_lo ( i_dqs_n_lo ),
.o_dqs_hi ( o_dqs_hi ),
.o_dqs_lo ( o_dqs_lo ),
.o_dqs_n_hi ( o_dqs_n_hi ),
.o_dqs_n_lo ( o_dqs_n_lo ),
.o_dqs_oe ( o_dqs_oe ),
.o_dqs_n_oe ( o_dqs_n_oe ),
.i_dq_hi ( i_dq_hi ),
.i_dq_lo ( i_dq_lo ),
.o_dq_hi ( o_dq_hi ),
.o_dq_lo ( o_dq_lo ),
.o_dq_oe ( o_dq_oe ),
.axi_aid ( axi_aid ),
.axi_aaddr ( axi_aaddr ),
.axi_alen ( axi_alen ),
.axi_asize ( axi_asize ),
.axi_aburst ( axi_aburst ),
.axi_alock ( axi_alock ),
.axi_avalid ( axi_avalid ),
.axi_aready ( axi_aready ),
.axi_atype ( axi_atype ),
.axi_wid ( axi_wid ),
.axi_wdata ( axi_wdata ),
.axi_wstrb ( axi_wstrb ),
.axi_wlast ( axi_wlast ),
.axi_wvalid ( axi_wvalid ),
.axi_wready ( axi_wready ),
.axi_rid ( axi_rid ),
.axi_rdata ( axi_rdata ),
.axi_rlast ( axi_rlast ),
.axi_rvalid ( axi_rvalid ),
.axi_rready ( axi_rready ),
.axi_rresp ( axi_rresp ),
.axi_bid ( axi_bid ),
.axi_bresp ( axi_bresp ),
.axi_bvalid ( axi_bvalid ),
.axi_bready ( axi_bready )
);

endmodule

//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
gE9JMRDJvZj64vyfpJBmlvGFdik8LfJsGBmolR+MYQ8eYC0xrEq9n5CaMtrxyeXK
QzsBnXhicFn0Vq9VXjcG2UVDq00U2gnArapwvj/nWxN3XwJdsClWTSg+E1LzUyuI
CPYHljQfMmNwPuan6OlF6fYsJwHdrzn9mlE4qUufUmrloWOm0h+jdgSP9hGa1mId
YaqqlqHfpAEUJITgS82PvxKRnBWspZoZP6y9J3is+CSdwW/Zh/2eBwmmmUZ+eEyY
Cx6iVI8bGW8hipTupthA7QDwj0nE+yt/LiGi5rjn0H00RtSrMnTsnMPs0ryRcznd
g5n/sRyMh3cXinlJSPAopw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 672 )
`pragma protect data_block
ey6GyUsoP4DbC+FrJwmmd7qaF/MVw/0524n0K7VXdkHVtMVT2xKAm+xw1kq8KEHJ
1PdEWQ3ne/SZ0v/lF2YMOIx68L/hiACCp9uhYmroob7An0brWhogqrgJrlhIZtW5
PbdPK4suj4ZE8+fSTgCYTemE1AFKuww0F5Bepy7O9ZSMydqVRV2Qpz08RI307QAa
tW3pgRDQpL8sxvRQMv9rrd+Qs0FM2j4WaCnXUn/awxfDLvAcvrDf/4he+9UFQNQq
GnnXmZze76SsAGJ7QxIeYj/UZaKUbAc1QOkJdGwHetC8aA99zZ7MDuVcRyLMMsgS
qzXU8kBf6y/uPlJzFSJYHOQVLwC6CLgCWQnW67I5FvGnCSYDFrlda4gn7gD2PTop
+QSCEPJlivlIx36QonxfRDzvQmY7/oXxno774MltiMyRXor6zi3aQMVWH9ttPXgX
pMDSQutJYBW2rDK+PHZWCIudaOgeMeC7wRKy7pzofQjdDrBG106Mpzc5vgEj6TH0
qu8s5KxvauC9FKzrWG4He5wpLw14GXI44xafatFoGkwzMElRtX+fD0w8wtmjdqQq
IBVgbBCRbI3vDIisF8cKO29kgaQXoL5dIF3xvJ5RH4mgWAZbg0IYDTNrkYpfcV/B
FUS3CvMjDgsxaSOs3EQbLigsvQMxzFfJDwmf9kP00yOJvUU34Ydha/odPjsPvep7
qkA5QJc8uuSzkgyiYbDAKNRvJeHV3OX8jibQZJS+3qrtC8R3vQN1X9C2CGWw6T7k
Yb72MlvNB5E+fQdGmtE788EQumdpgj0MasMihT7n2xwFO//J50J4TLjY0kE/IZJI
WraTEehOrTkRLUSdHG2NfXQaV2JzlvHWRU47AuRv2kY1UaxSz/LS23g3L+o6urSU
`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
EGkc0+qjK3SKKt7Wszat+tKuTfMmR9OVcRDvLHL7xOk+JUrb3/HLkbdZup5FjttA
PVWMj3a0QSQpmTSi2ZxbBQa+3qXmrSi1EcozcKf5GUGaqKSc+4ujfXT0x76MNiTM
bvyUVYNHn3KfHKIw5IkhjuNL+EWqz3fmmQgAPqiCqwUhqj0lEpStAdRsSEUUQLk1
5lwa5J3tTfThanKce9P/OPBDnu0DgdhJu36USvt9kWhNTDn9reZr5q+C4BE7NO6k
k91vI8sohYRxaRfmcqMCS0BU6EjkuOCamymJKc4Tqy9eo3i1Ec2FX31QsdOhVnxj
v0hCKM6tkBdNo0F/brGeew==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 672 )
`pragma protect data_block
DsqAm2Tps+mnDuhfJbHsZ3BbaKIQ43p0b6LNbqfldKwJxtHU20fc18TNI06LCmeI
ybWTANDRhrpyqWs/XUzKQ5EpPE6uKxpWhctMj8yQPAkYHhRWj5doROSCPMh+DFP1
j9iD0tqjKfTP8MwydYIYe4tMNxbCO0lb/Vkw9MAsBEARasN2ewQfLmt6UHk+XpoR
OsUqiWAOVulVsk2Gu4M9Ds9S4Bjd+cGKaTKSs2sAAL4WrEdQisTv1jyBWS3QOgMM
r4UwwOejMCzBrXhgXOugy9PbIF/jn40Qbxr9cFy0ikp1I7/SKOvECuXTxYF6k/kL
s0bYXdrMU0ZxQcTECJpqKWzN4D+AS0Hjtd0L8pmCJW2D8toWtr7eWO3ZMxktN9r7
fPCrAOC/fjbn769be6vwjJaFJR0yUhBEzyk+R5wpDnMukjZDOjU2Jh4DBhJ2TD0T
tEvOf+uiwzQP43LM35NWgV1Bdg91PcPZZYwo0fEtd8Qef/cAEI1kFV8v9ShZZOyF
MoWgSN/pg8QQE1nLLodfuyDbPXToQ57b/1EfQea6IELwugPQ9x1A49HJMhDAtyt4
xJPqK4//snCd/FuOtzJnkymDWfpgLRyaDYe3RtmFbQ99Aj+RqKHmA/z9r2U/TaTR
mAL5n/yJezceQ4PEy1ZZ6PZNWerw72oGHS0j+CcjTAmdWjm6XaUdjaPsfjJJ5IHO
z1BNM0l6gtZDEd0mIp6PpOXBFlOImmAeO/+dfLPjaxSRTWetormpPnVGvqEGtXuZ
tHv35P2qDb0pXhneuA9pBmIYfLGKVxQVKVDDujvzgIBuzVWhdBDZ+JrKn3qLybNH
YVfTVeail7pPEBORSDTmcidpKAzEL/EFYckF4vMR6OK3JJyhMLYd3zR5sw0uXQk/
`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
CZznY14z5WJeGUk2aCIHtyRyoKOWxR13im3uRnYchvFDVCdbi7Se0hYVQzQJIM8+
ukW21nR8vCa/2jGcvitMqpqdWWajNCSwfSNgUCKPmCl7B7BHxjLb942zhKg7G1S0
3DTEi9zvzYhPtw2TM1ToQA8e1Hd75iMhVp2YDTi1Z6a7JdJuTe03WH+k3fenxrTp
+Nl7Gtv0AH1OkL1cSWkzNL56fd1heTresYl8qgyV3IOQDvPCX7fL4Mn6OIaf0x2f
kf0YND58uxzwvKpQghkgXRjT1HLwwVwWKMfjS6hdJ4KXVlDiPZoaT7B741e7XgaB
YjcWRIT4uVwMb6y+fVnG5g==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 13424 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
B70AGpmIW+5SXcfhZOJGtrcyaaRUWLSt+4CNhFcQYAo/9K7aBbLy1oukDRq9lQrG
6E2UumECs0WNu2NfQi295TEDk6Asnpb/7QBrjPXxOSDblevPLbpm3nqm73ppn4Cy
C6RydcgTktM/7oGWBrk+N9hNIQrGOwL098qrAxfY/wtTSbySZOmO0YvFq/Pk+4FI
8+4OEgbW9Y3aTF0ZYvGh1AvWFpyOLzr7G/NnBJle36v3XOFuqslx1ELuEwOGyzXW
kebL9JzENrYt7jn9CWmVF+VJ9kd0p64EH2Vlun/T1g8l89Wneq43uoAw4w83xowp
wD00qPysD7na6x1qbEDeOg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 39728 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
bTooZrptpOiuX7LQvnwWAqae18HoRWiSpTHPV9ZzwqA+3FNHCqTRbzv9tIldpSc1
wZRWXW4TI3KDc4iRFwQO2P91VBx75M2CgvwKpeTNQ/ed4rpv107/GE0bVNohQLQZ
NLY1kOPctBbcz9F2PQUiA6A0xa1YyCJOo9YndonmwZacxnIsMvTQEoXhB7D1xRKw
tfLV4DB8woIYuVBglI3qvq04od2LSH9V5eZOlkcZ37ljtJxfEqXj1neSiihUclr2
OrBiqBxOv5FPB4LNSwThvpWAa+yhoEYhdLZpIiGMnKk8nalQyGuOD3sWi4PGwFc3
rAEdZDQZF6oW0tLYVPJaMQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 5424 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
Uk8p7ppRdxppH4E5IHNinYPOAmRvPHKsnQaBHbQelHn1jLs9AIX74LLNQDuKjwAo
zVMgAMZPF5XDstcvTPp9wxd0FptvKPC0OK7ph0sMabzZyFO6D+VQ/e3EqPvr8ruY
GDfaAFxmOyHjlwAbUVU2Fgx5qDCOiffETVR9MWTeOQgAacN5IWH64EYVyHLt0rbB
7V8JXhWtYSwAPhte/dAU4KfmQgnaHitslCLfcnyqKPzNCvguJV6DNdb2vyYfr3oB
yxR16m6q8jOAK7hz9CPtdmLsNyZ+g8+qz5T4UFNufSkmrzsb1zJXbjmzkahpCJ+7
ljpWH9AC7sGu1ZczFTHHYw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 1776 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
QkjyQcyHVC9UzBfLfgpAfrsIctgTqnoqJw9UohUYqI5QXlLjmH0myuTQy3Y9XNBv
rmvyHg2S5J3XpDzlQ4gD/HI9Lkd4l5WDEWDrpgLvYnt4rTIOgOx9nj7/VDxl5i0N
2dSRq+ekk7Fvja0m6BCtI8ghhulyNJwge7SewWL6EkMZhnQOOGp1CEjF8iZgnDqQ
GKVA17AI9sd5+a13oWa2s+l76u3fYm8uhly4pqCKqgTWzW6ehVrdC2xBGkY5AXZG
h7cPayPoAnIfRPdwo+0EAmfqRodpreaFaJokxTmhrjr6EbNxw8/S2oHsI7CUH/vh
FTmdw7L3xwcc92CP2Cdyyw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 1760 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
bcznKdzBLAR2s6wahTl69lWYuCHT4qOsZjHOLrgHUm+C81w5OddYqAozq8NofmlX
DpIBeuvkUlgYXsV/R+JT9m2ouDbfLA304kwYqMUx3Ye7urZEJYIWIk0o5kCPRn6c
tJ3FNrthsG1y4zaeOw5IHwmvG6zlDuYX+YAXkBNqGgymBqiX2ltkIyzTPlHhUz0o
IAtLP5lw/gFBbE4o1zwywX8JoD4pstZuwiy4DrZiElbRk9NxsFqyQ67Z9Ka0ilTJ
Yq7eJkv7hP92uLqkVp5Hcxx3gSVefHI3IuCPSKAoa7WAcjimutSBYV6OaLc9750B
inDIS1h1aMHeWk7+fXGS7w==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 1600 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
gIOPyA5YiWDgiBhj/ZSv0P3wP3+aeABQv+9prl6JDuPRia5r8+lVsr0mJmKcYrf2
Roixba6MbcTxpE+ZFB690YMIefNAzik64lg1fVzSA2LSx8sQDpoF4lxgsX6N1hlQ
8x5EsaerBp3lJzD0FD+9xQgy9GMQkHnTuEEXcS4dzmLXkSq+JycDeIsF8JYa74hX
laPuzixgXQA6oPCmpK401cfHnG9lYXzyhDDR7RqyCVgHPKry8Y27fZwj36ClMycU
TRuHFlbl3Pu2DLe6Uv4s7QjnZluouyIdGywQyJXpUy3bVJfULWts1xR9ZntspKrn
C+J1zsR5b+yovH82//b+JA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 1952 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
module `IP_MODULE_NAME(efx_ddr3_soft_controller)
(
clk,
core_clk,
twd_clk,
tdqss_clk,
tac_clk,
reset_n,
reset,
cs,
ras,
cas,
we,
cke,
addr,
ba,
odt,
o_dm_hi,
o_dm_lo,
i_dqs_hi,
i_dqs_lo,
i_dqs_n_hi,
i_dqs_n_lo,
o_dqs_hi,
o_dqs_lo,
o_dqs_n_hi,
o_dqs_n_lo,
o_dqs_oe,
o_dqs_n_oe,
i_dq_hi,
i_dq_lo,
o_dq_hi,
o_dq_lo,
o_dq_oe,
wr_busy,
wr_data,
wr_datamask,
wr_addr,
wr_en,
wr_addr_en,
wr_ack,
rd_busy,
rd_addr,
rd_addr_en,
rd_en,
rd_data,
rd_valid,
rd_ack,
shift,
shift_sel,
shift_ena,
cal_ena,
cal_done,
cal_pass,
cal_fail_log,
cal_shift_val,
axi_aid,
axi_aaddr,
axi_alen,
axi_asize,
axi_aburst,
axi_alock,
axi_avalid,
axi_aready,
axi_atype,
axi_wid,
axi_wdata,
axi_wstrb,
axi_wlast,
axi_wvalid,
axi_wready,
axi_rid,
axi_rdata,
axi_rlast,
axi_rvalid,
axi_rready,
axi_rresp,
axi_bid,
axi_bvalid,
axi_bresp,
axi_bready
);
input clk;
input core_clk;
input twd_clk;
input tdqss_clk;
input tac_clk;
input reset_n;
output reset;
output cs;
output ras;
output cas;
output we;
output cke;
output [15:0]addr;
output [2:0]ba;
output odt;
output [`DRAM_GROUP-1'b1:0] o_dm_hi;
output [`DRAM_GROUP-1'b1:0] o_dm_lo;
input [`DRAM_GROUP-1'b1:0]i_dqs_hi;
input [`DRAM_GROUP-1'b1:0]i_dqs_lo;
input [`DRAM_GROUP-1'b1:0]i_dqs_n_hi;
input [`DRAM_GROUP-1'b1:0]i_dqs_n_lo;
output [`DRAM_GROUP-1'b1:0]o_dqs_hi;
output [`DRAM_GROUP-1'b1:0]o_dqs_lo;
output [`DRAM_GROUP-1'b1:0]o_dqs_n_hi;
output [`DRAM_GROUP-1'b1:0]o_dqs_n_lo;
output [`DRAM_GROUP-1'b1:0]o_dqs_oe;
output [`DRAM_GROUP-1'b1:0]o_dqs_n_oe;
input [`DRAM_WIDTH-1'b1:0] i_dq_hi;
input [`DRAM_WIDTH-1'b1:0] i_dq_lo;
output [`DRAM_WIDTH-1'b1:0] o_dq_hi;
output [`DRAM_WIDTH-1'b1:0] o_dq_lo;
output [`DRAM_WIDTH-1'b1:0] o_dq_oe;
output 						wr_busy;
input [`WFIFO_WIDTH-1'b1:0]	wr_data;
input [`DM_BIT_WIDTH-1'b1:0] wr_datamask;
input [31:0]				wr_addr;
input 						wr_en;
input						wr_addr_en;
output 						wr_ack;
output 						rd_busy;
input  [31:0] 				rd_addr;
input  				    rd_addr_en;
input  				    rd_en;
output [`WFIFO_WIDTH-1'b1:0]	rd_data;
output 						rd_valid;
output 						rd_ack;
output [2:0]shift;
output [4:0]shift_sel;
output shift_ena;
input cal_ena;
output cal_done;
output cal_pass;
output [6:0]cal_fail_log;
output [2:0]cal_shift_val;
input wire [7:0]    axi_aid;
input wire [31:0]   axi_aaddr;
input wire [7:0]    axi_alen;
input wire [2:0]    axi_asize;
input wire [1:0]    axi_aburst;
input wire [1:0]    axi_alock;
input wire          axi_avalid;
output wire          axi_aready;
input wire          axi_atype;
input wire [7:0]    axi_wid;
input wire [`WFIFO_WIDTH-1:0]  axi_wdata;
input wire [`DM_BIT_WIDTH-1'b1:0]   axi_wstrb;
input wire          axi_wlast;
input wire          axi_wvalid;
output wire          axi_wready;
output wire [7:0]    axi_rid;
output wire [`WFIFO_WIDTH-1:0]  axi_rdata;
output wire          axi_rlast;
output wire          axi_rvalid;
input wire          axi_rready;
output wire [1:0]    axi_rresp;
output wire [7:0]    axi_bid;
output wire          axi_bvalid;
output wire [1:0]    axi_bresp;
input wire          axi_bready;
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
HZjgb5+luvmVOCFdc/tQTZItdJFSTQldnbvQfIlfXY+lTfdY1Gx9TmFr772jWvdS
r8I8oOnkhDhYpeMWG+/Qi9Df7plsRFFDB11+4Ig6nWwS8qfASopXCrH0Y2GaDfwj
UWEt9fD+zT70Wkijbzpti4ggPNYCmZAHnqolulusc5OHP+R8n2MWwG+DCMhLKeQ8
aYDmK86JOZh0KkmQLnlluz8J/0IHs7DFPEpS3TQDpdezFnxy32B4thh9LWc/C99W
k932pD1XqJImwrKBIukl3RZKRGlX6mnRWBmNuF97yNI8bfJLb3u7EImYMO6DXT5b
j+O4SWDvQruSC0ayRaayLA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 3264 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
cwqPkFm2QinmrZwfu8MHVqzjZSGm5v/XfzTgIXCNUB4ej4J9w5gAJ3QLAKoZUnH9
LZ9vN9W29wewOHJwFxF8eMZ1SjkZ7SWnenlTf2uWsdzNN8jkMbThQ+Pq7vmjliIE
WlvI+NU04g6cuK2G1Ltv11Uh2K4+SWUjigS3UAfWviDroRZDfftY+y2/Ta76zRAQ
vxSJeohhsMm4jdfqUTgXXgwrmjQVtEItt/UozlsKkKDwFhbHdtJmK8qU5IfoQ4B9
yIFVlL7pSUXtSxWNoQQ8Vm7/OnqHdwINNBL1gBgD3E92g0gW1tq+Gfw/UlSZ+qDp
Ulw2eAb11lfihAe0k0Oefw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 992 )
`pragma protect data_block
82kR9Y9GFY1pWrrnReg8QTDm6orvArCpZqcJ6gGPY4Gm4dIeqGfanKQtYPMhAxRE
aq9bcp/AsPgK0Pe1rA1a9Uvu98mhCJeuyCY7lpM0bR7h0E3srL1KaMutUYCKh3Zb
yBcJ2GNgmbrQoIgqAs2zi/tzMjWiiXtwErpiMfpXGA0eA7G9IGYFv5Z3ODbs+aCw
YtfPtlkVI8H8vJFtjy15C1jeqoLcyvi3PEuGWbru9oHj5Eu6Yx6TFyl2zj2l20CL
E+Q1yCLbelpf1w6uSdUdL2DH5RFMVK54ebFHteAPQfpURX5RL2b78i+CPV/oQQKd
sf5rrm5U0Z56Y25XrVA37yb5J7BnJf9X/+sfAY7izUgW/0/U5EEfwrcugc2VfiPo
y2OrjHX5n6pYIgqjKbL+QeaTXfytpr+z+zlQmoYxlzG03UOkuQko5Dg9GvrO7EZI
xQ+0N3+CMjMRpGU27K7MmdZOGiGezU/MYPTgPOEjM0tGtN8MmdqAmL9VJ9J8rCM9
9blYqdxVbLJpaLIVo2HkWhcpo8V2e+sbZkhrAG9xCvcsflE7Tb+tw019tz6nb7Og
0JRCXD2Q8YRaniWZ/DoInyviGWJ7CqBdjKEiW4l2qxzcvfzmKvEHi6HrsOQNtqlr
LBRXM6ohagqy93C2Zj7ikA5GZUU2jNvIVtVIkPnxoonWmfEejvwjUbXJ6cF5a2Mz
orBKCldspTUvUhzHPRB53wkML6LBzLNfXw8rjOzDQhP5anQaaD6Mvpw2ZDzKRS5m
ywVhosMYbp0EfCNZ03G+YzHbePA4VO7R3iVYoybULBFEWdbrDfv5LXEHTfN2nzSi
oHltpUVenQmlwT7ErqSK8X0TSevY3eTGnJzOkllF2nksZ7BPScbnKaLh5IOIWCyv
FEPH3krUXUuri5soJMCAqb7wjpLCHgXJL8C2xvjKEdpDuxdpv4AB2H0YbHQZmbMj
H7wiCF/9P4sLjF3FTVU3Eegw/y6nfYw8Z9+hl64td6m5j6g9oiy5TXBRCZKcxDQv
UFzDlhY+VWszf2YdTB4r2XSO4iVanyyn/3fGs+vXe80492+9PXQOEwEjPUX78+Dv
06sm3u5XwO82noE6TJybJum4tPQf2FHT0ql2iM5WBwSed1mEJri7h2KlNSt+Mb1l
8nCukkf4eSN3CKaaGevQ7d1iaV0EcRLlrfecQrUL8PsDNSgTGohJHcFx0pYHwkRA
dLuhWOt34NqioK/KslANDItnj1MubamMxlG1yeadqh1o1Vy5OqcptEkhsWGvqZbK
UwaSwllkaiOUDuSSJ7JTal0cfS6MHs9oTro641czv18=
`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
eYm3gLDtZyE4MHlp3WURMRzSxqPPpUFUPVcSoQfUKjVyoLNcvbtC9YM00awkdfcI
TPog1pWyhi4UQ0aE0acf9pwyDjcC5te2oExcKWjHahp1GFVi6wGXQenxyR0NzMjc
GgmPHQnbaNZaLloBxteNvw99vzGdPuBIcJDFBPwjvfqvqnoL3X+XR5MQds3S+yaG
WB+ISgNeVg0wY0uRo3nm6pK+MSM8vbQMbjIysvhGI+8P3V+kd4YNEr1nStriqcGv
X7DJ1svwY1dzsNvpFlz32GFwPZhyhDKK6lOZMXQ7NZQC9dU+jl+Oa+4nOFYl+5Bg
3LW4P8BTtYJGMNYQr3E7Qw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 14768 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
PIrsrRepw4jBsvxlVtrlHnbWYw3DDStm1yP4UPLBPwYQpDXL63NAblDGQJ7oQlnW
EjXckpW0H/1bykbv2fNQvWLMqBsjIWT+1M4tnFw4pEGynmMOXNkfMyzQszgMzyOX
gvztBfnompahKmbimEQ1UKjJv/OK5hmp+OSRMdEO0judWZVuehPXnugUu5xamOTd
wAwTxt5JKRj6TzEbSJJbHqRHtKNtdBVN9bkfeHYhEhhWRG4tKXVfv5aLrYFCPpaO
VSmjCc5cQ+7y7//AOE7RhKG3HlREKDDhzte4bHICHkDlUUjjD87gMmBqXHf8Dwst
Qsy8z9fOAw5GmwX5V9+pag==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 4256 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`include "ddr3_controller.vh"
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Efinix Inc." , key_keyname = "EFX_K01"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
dbQPXQ5PF47k62GRo0Q8+42ujwOHKxEauFlcAMYvoOL/NEMXNTb7Zwh4y4JWvOTI
ZYZH4gc90OX/qnVa+BiA54kJJO3p6z4ba7RrLTeTcoPU+vK9A2cTdageaveYVSTP
RIZA2r86SW1qwl+XBc7awgfry5yT+ymE5lG53JM7QRSqoXcoJPojx4fzbNiXqPCs
OyHp2y/xeFtT+rjvyHFVFoeqTdjP2LXVUVdHkqnY826Mr+ERL4IUlak4OYIxJiWG
FVKD+CcMrjygfxyUHXEZ67NoaOWXQtcF4RHybQkKvvCWdZnrLUpcFubjBvDGJ7k9
koethllK6peRuQgXbDl8ag==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 13264 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_top) # (
    parameter FAMILY             = "TRION",       // New Param
    parameter SYNC_CLK           = 0,
    parameter BYPASS_RESET_SYNC  = 0,             // New Param
    parameter SYNC_STAGE         = 2,             // New Param
    parameter MODE               = "STANDARD",
    parameter DEPTH              = 512,           // Reverted (Equivalent to WDATA_DEPTH) 
    parameter DATA_WIDTH         = 32,            // Reverted (Equivalent to WDATA_WIDTH)
    parameter PIPELINE_REG       = 1,             // Reverted (By default is ON)
    parameter OPTIONAL_FLAGS     = 1,             // Reverted
    parameter OUTPUT_REG         = 0,
    parameter PROGRAMMABLE_FULL  = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_FULL_ASSERT   = 27,
    parameter PROG_FULL_NEGATE   = 23,
    parameter PROGRAMMABLE_EMPTY = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_EMPTY_ASSERT  = 5,
    parameter PROG_EMPTY_NEGATE  = 7,
    parameter ALMOST_FLAG        = OPTIONAL_FLAGS,
    parameter HANDSHAKE_FLAG     = OPTIONAL_FLAGS,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter WADDR_WIDTH        = depth2width(DEPTH),
    parameter RDATA_WIDTH        = rdwidthcompute(ASYM_WIDTH_RATIO,DATA_WIDTH),
    parameter RD_DEPTH           = rddepthcompute(DEPTH,DATA_WIDTH,RDATA_WIDTH),
    parameter RADDR_WIDTH        = depth2width(RD_DEPTH),
    parameter ENDIANESS		 = 0

)(
    input  wire                   a_rst_i,
    input  wire                   a_wr_rst_i,
    input  wire                   a_rd_rst_i,
    input  wire                   clk_i,
    input  wire                   wr_clk_i,
    input  wire                   rd_clk_i,
    input  wire                   wr_en_i,
    input  wire                   rd_en_i,
    input  wire [DATA_WIDTH-1:0]  wdata,
    output wire                   almost_full_o,
    output wire                   prog_full_o,
    output wire                   full_o,
    output wire                   overflow_o,
    output wire                   wr_ack_o,
    output wire [WADDR_WIDTH :0]  datacount_o,
    output wire [WADDR_WIDTH :0]  wr_datacount_o,
    output wire                   empty_o,
    output wire                   almost_empty_o,
    output wire                   prog_empty_o,
    output wire                   underflow_o,
    output wire                   rd_valid_o,
    output wire [RDATA_WIDTH-1:0] rdata,
    output wire [RADDR_WIDTH :0]  rd_datacount_o,
    output wire                   rst_busy
); 

localparam WR_DEPTH      = DEPTH;
localparam WDATA_WIDTH   = DATA_WIDTH;
localparam RAM_MUX_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? 32 :
                           (RDATA_WIDTH <= WDATA_WIDTH/16) ? 16 :
                           (RDATA_WIDTH <= WDATA_WIDTH/8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH/4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH/2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH)    ? 1  :
                           (RDATA_WIDTH <= WDATA_WIDTH*2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH*4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH*8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH*16) ? 16 : 32;           

wire                   wr_rst_int;
wire                   rd_rst_int;
wire                   wr_en_int;
wire                   rd_en_int;
wire [WADDR_WIDTH-1:0] waddr;
wire [RADDR_WIDTH-1:0] raddr;
wire                   wr_clk_int;
wire                   rd_clk_int;
wire [WADDR_WIDTH :0]  wr_datacount_int;
wire [RADDR_WIDTH :0]  rd_datacount_int;

generate
    if (ASYM_WIDTH_RATIO == 4) begin
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
            assign datacount_o    = wr_datacount_int;
            assign wr_datacount_o = 'd0;
            assign rd_datacount_o = 'd0;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
            assign datacount_o    = 'd0;
            assign wr_datacount_o = wr_datacount_int;
            assign rd_datacount_o = rd_datacount_int;
        end
    end
    else begin
        assign datacount_o    = 'd0;
        assign wr_datacount_o = wr_datacount_int;
        assign rd_datacount_o = rd_datacount_int;
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
        end
    end    

    if (!SYNC_CLK) begin
        (* async_reg = "true" *) reg [1:0] wr_rst;
        (* async_reg = "true" *) reg [1:0] rd_rst;
        
        always @ (posedge wr_clk_int or posedge a_rst_i) begin
            if (a_rst_i) 
                wr_rst <= 2'b11;
            else 
                wr_rst <= {wr_rst[0],1'b0};            
        end
        
        always @ (posedge rd_clk_int or posedge a_rst_i) begin
            if (a_rst_i) 
                rd_rst <= 2'b11;
            else 
                rd_rst <= {rd_rst[0],1'b0};            
        end

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_wr_rst_i;
            assign rd_rst_int = a_rd_rst_i; 
            assign rst_busy   = 1'b0;
        end 
        else begin
            assign wr_rst_int = wr_rst[1];
            assign rd_rst_int = rd_rst[1]; 
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end
    else begin
        (* async_reg = "true" *) reg [1:0] a_rst;
        
        always @ (posedge clk_i or posedge a_rst_i) begin
            if (a_rst_i) 
                a_rst <= 2'b11;
            else 
                a_rst <= {a_rst[0],1'b0};            
        end

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_rst_i;
            assign rd_rst_int = a_rst_i;
            assign rst_busy   = 1'b0;
        end
        else begin
            assign wr_rst_int = a_rst[1];
            assign rd_rst_int = a_rst[1];   
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end 
endgenerate

`IP_MODULE_NAME(efx_fifo_ram) # (
    .FAMILY        (FAMILY),
    .MODE          (MODE),
    .WR_DEPTH      (WR_DEPTH),
    .RD_DEPTH      (RD_DEPTH),
    .WDATA_WIDTH   (WDATA_WIDTH),
    .RDATA_WIDTH   (RDATA_WIDTH),
    .WADDR_WIDTH   (WADDR_WIDTH),
    .RADDR_WIDTH   (RADDR_WIDTH),
    .OUTPUT_REG    (OUTPUT_REG),
    .RAM_MUX_RATIO (RAM_MUX_RATIO),
    .ENDIANESS	   (ENDIANESS)
) xefx_fifo_ram (
    .wdata (wdata), 
    .waddr (waddr),
    .raddr (raddr), 
    .we    (wr_en_int), 
    .re    (rd_en_int),
    .wclk  (wr_clk_int),
    .rclk  (rd_clk_int),
    .rdata (rdata)
);

`IP_MODULE_NAME(efx_fifo_ctl) # (
    .FAMILY             (FAMILY),
    .SYNC_CLK           (SYNC_CLK),
    .SYNC_STAGE         (SYNC_STAGE),
    .MODE               (MODE),
    .WR_DEPTH           (WR_DEPTH),
    .WADDR_WIDTH        (WADDR_WIDTH),
    .RADDR_WIDTH        (RADDR_WIDTH),
    .ASYM_WIDTH_RATIO   (ASYM_WIDTH_RATIO),
    .RAM_MUX_RATIO      (RAM_MUX_RATIO),
    .PIPELINE_REG       (PIPELINE_REG),
    .ALMOST_FLAG        (ALMOST_FLAG),
    .PROGRAMMABLE_FULL  (PROGRAMMABLE_FULL),
    .PROG_FULL_ASSERT   (PROG_FULL_ASSERT),
    .PROG_FULL_NEGATE   (PROG_FULL_NEGATE),
    .PROGRAMMABLE_EMPTY (PROGRAMMABLE_EMPTY),
    .PROG_EMPTY_ASSERT  (PROG_EMPTY_ASSERT),
    .PROG_EMPTY_NEGATE  (PROG_EMPTY_NEGATE),
    .OUTPUT_REG         (OUTPUT_REG),
    .HANDSHAKE_FLAG     (HANDSHAKE_FLAG)
) xefx_fifo_ctl (
    .wr_rst          (wr_rst_int),
    .rd_rst          (rd_rst_int),
    .wclk            (wr_clk_int),
    .rclk            (rd_clk_int),
    .we              (wr_en_i),
    .re              (rd_en_i),
    .wr_full         (full_o),
    .wr_ack          (wr_ack_o),
    .rd_empty        (empty_o),
    .wr_almost_full  (almost_full_o),
    .rd_almost_empty (almost_empty_o),
    .wr_prog_full    (prog_full_o),
    .rd_prog_empty   (prog_empty_o),
    .wr_en_int       (wr_en_int),
    .rd_en_int       (rd_en_int),
    .waddr           (waddr),
    .raddr           (raddr),
    .wr_datacount    (wr_datacount_int),
    .rd_datacount    (rd_datacount_int),
    .rd_vld          (rd_valid_o),
    .wr_overflow     (overflow_o),
    .rd_underflow    (underflow_o)
);

function integer depth2width;
input [31:0] depth;
begin : fnDepth2Width
    if (depth > 1) begin
        depth = depth - 1;
        for (depth2width=0; depth>0; depth2width = depth2width + 1)
            depth = depth>>1;
        end
    else
    depth2width = 0;
end
endfunction 

function integer width2depth;
input [31:0] width;
begin : fnWidth2Depth
    width2depth = width**2;
end
endfunction

function integer rdwidthcompute;
input [31:0] asym_option;
input [31:0] wr_width;
begin : RdWidthCompute
    rdwidthcompute = (asym_option==0)? wr_width/16 :
                     (asym_option==1)? wr_width/8  :
                     (asym_option==2)? wr_width/4  :
                     (asym_option==3)? wr_width/2  :
                     (asym_option==4)? wr_width/1  :
                     (asym_option==5)? wr_width*2  :
                     (asym_option==6)? wr_width*4  :
                     (asym_option==7)? wr_width*8  :
                     (asym_option==8)? wr_width*16 : wr_width/1;
end
endfunction

function integer rddepthcompute;
input [31:0] wr_depth;
input [31:0] wr_width;
input [31:0] rd_width;
begin : RdDepthCompute
    rddepthcompute = (wr_depth * wr_width) / rd_width;
end
endfunction

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ram) #(
    parameter FAMILY        = "TRION",
    parameter MODE          = "STANDARD",
    parameter WR_DEPTH      = 512,
    parameter RD_DEPTH      = 512,
    parameter WDATA_WIDTH   = 8,
    parameter RDATA_WIDTH   = 8,
    parameter WADDR_WIDTH   = 9,
    parameter RADDR_WIDTH   = 9,
    parameter OUTPUT_REG    = 1,
    parameter RAM_MUX_RATIO = 4,
    parameter ENDIANESS 	= 0 //0: Big endian (default)   1: Little endian 
) (
    input  wire                     wclk,
    input  wire                     rclk,
    input  wire                     we,
    input  wire                     re,
    input  wire [(WDATA_WIDTH-1):0] wdata,
    input  wire [(WADDR_WIDTH-1):0] waddr,
    input  wire [(RADDR_WIDTH-1):0] raddr,
    output wire [(RDATA_WIDTH-1):0] rdata
);

localparam MEM_DEPTH         = (WR_DEPTH > RD_DEPTH) ? WR_DEPTH : RD_DEPTH;
localparam MEM_DATA_WIDTH    = (WDATA_WIDTH > RDATA_WIDTH) ? RDATA_WIDTH : WDATA_WIDTH;   
localparam LSB_WIDTH         = (WADDR_WIDTH > RADDR_WIDTH) ? (WADDR_WIDTH - RADDR_WIDTH) : (RADDR_WIDTH - WADDR_WIDTH);
localparam RDATA_WDATA_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? "ONE_THIRTYTWO" :
                               (RDATA_WIDTH <= WDATA_WIDTH/16) ? "ONE_SIXTEENTH" :
                               (RDATA_WIDTH <= WDATA_WIDTH/8)  ? "ONE_EIGHTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/4)  ? "ONE_FOURTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/2)  ? "ONE_HALF"      :
                               (RDATA_WIDTH <= WDATA_WIDTH)    ? "ONE"           :
                               (RDATA_WIDTH <= WDATA_WIDTH*2)  ? "TWO_TIMES"     :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "FOUR_TIMES"    :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "EIGHT_TIMES"   :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "SIXTEEN_TIMES" : "THIRTYTWO_TIMES";

reg [MEM_DATA_WIDTH-1:0] ram[MEM_DEPTH-1:0];
reg [RDATA_WIDTH-1:0]    r_rdata_1P;
reg [RDATA_WIDTH-1:0]    r_rdata_2P;

wire                     re_int;

generate
    if (FAMILY == "TRION") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                end
                r_rdata_2P <= r_rdata_1P;
            end
        end
        
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            if (ENDIANESS == 0) begin 
            	integer i;
            	reg  [LSB_WIDTH-1 :0 ]   lsbaddr;
            	always @ (posedge wclk) begin
                	for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	lsbaddr = RAM_MUX_RATIO-1-i;
                    	if (we) begin
                            ram[{waddr,lsbaddr}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                    end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
            else begin //endianess == 1
            	integer i;
            	reg  [LSB_WIDTH-1 :0 ]   lsbaddr;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	lsbaddr = i;
                    	if (we) begin
                            ram[{waddr,lsbaddr}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                	end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
        end
        
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  ||RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
            //integer i;
        	//reg  [LSB_WIDTH-1 :0 ]   lsbaddr;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		reg  [LSB_WIDTH-1 :0 ]   lsbaddr; 
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	lsbaddr = RAM_MUX_RATIO-1-i;
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,lsbaddr}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end 
            end
            else begin //endianess == 1
            	always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		reg  [LSB_WIDTH-1 :0 ]   lsbaddr;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	lsbaddr = i;
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,lsbaddr}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end
            end
        end
        if (OUTPUT_REG) begin
            assign re_int = re;
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end
    end
    else if (FAMILY == "TITANIUM") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                    r_rdata_2P <= r_rdata_1P;
                end
            end
        end 
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            //integer i;
            //reg  [LSB_WIDTH-1 :0 ]   lsbaddr;
            if (ENDIANESS == 0) begin
            	integer i;
            	reg  [LSB_WIDTH-1 :0 ]   lsbaddr;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	lsbaddr = RAM_MUX_RATIO-1-i;
                    	if (we) begin
                            	ram[{waddr,lsbaddr}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
            
            else begin //endianess == 1
            	integer i;
            	reg  [LSB_WIDTH-1 :0 ]   lsbaddr;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	lsbaddr = i;
                    	if (we) begin
                            	ram[{waddr,lsbaddr}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
        end
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  || RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
        	//integer i;
            //reg  [LSB_WIDTH-1 :0 ]   lsbaddr;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	reg  [LSB_WIDTH-1 :0 ]   lsbaddr;        
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	lsbaddr = RAM_MUX_RATIO-1-i;
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,lsbaddr}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
            
            else begin //endianess ==1
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		reg  [LSB_WIDTH-1 :0 ]   lsbaddr;        
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	lsbaddr = i;
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,lsbaddr}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
        end
        if (MODE == "STANDARD") begin     
            if (OUTPUT_REG) begin
                reg re_r;
                always @ (posedge rclk) begin
                    re_r <= re;
                end
                assign re_int = re | re_r;        
                assign rdata  = r_rdata_2P;            
            end
            else begin
                assign re_int = re;
                assign rdata  = r_rdata_1P;
            end
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end        
    end
endgenerate

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2021 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   gray2bin.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Gray to Binary Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_gray2bin)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] bin_o,
 // input
 input [WIDTH-1:0] gray_i);

//---------------------------------------------------------------------
// Recursive Module
// Description: reduction xor
generate
   if (WIDTH > 1) begin
      wire [1:0] bin_1;
      assign bin_1 = {gray_i[WIDTH-1], gray_i[WIDTH-1]^gray_i[WIDTH-2]};
      if (WIDTH == 2) begin
	 assign bin_o = bin_1;
      end
      else begin
	 assign bin_o[WIDTH-1] = bin_1[1];
	 `IP_MODULE_NAME(efx_fifo_gray2bin) #(.WIDTH(WIDTH-1)) u_gray2bin (.bin_o(bin_o[WIDTH-2:0]), .gray_i({bin_1[0], gray_i[WIDTH-3:0]}));
      end
   end
   else /* if (WIDTH == 1) */
     assign bin_o = gray_i;
endgenerate
   
endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   pipe_reg.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Parallel Pipelining Shift Register
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_datasync) #(
    parameter STAGE = 32,
    parameter WIDTH = 4
) (
 input  wire             clk_i,
 input  wire [WIDTH-1:0] d_i,
 output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] pipe_reg [STAGE-1:0];
integer i;

always @(posedge clk_i) begin
  for (i=STAGE-1; i>0; i = i - 1) begin
    pipe_reg[i] <= pipe_reg[i-1];
  end
  pipe_reg[0] <= d_i;
end
assign d_o = pipe_reg[STAGE-1];
   

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ctl) # (
    parameter FAMILY             = "TRION",
    parameter SYNC_CLK           = 1,
    parameter SYNC_STAGE         = 2,
    parameter MODE               = "STANDARD",
    parameter WR_DEPTH           = 512,
    parameter WADDR_WIDTH        = 9,
    parameter RADDR_WIDTH        = 9,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter RAM_MUX_RATIO      = 1,
    parameter PIPELINE_REG       = 1,
    parameter ALMOST_FLAG        = 1,
    parameter PROGRAMMABLE_FULL  = "NONE",
    parameter PROG_FULL_ASSERT   = 0,
    parameter PROG_FULL_NEGATE   = 0,
    parameter PROGRAMMABLE_EMPTY = "NONE",
    parameter PROG_EMPTY_ASSERT  = 0,
    parameter PROG_EMPTY_NEGATE  = 0,
    parameter OUTPUT_REG         = 0,
    parameter HANDSHAKE_FLAG     = 1
)(
    input   wire                   wr_rst,
    input   wire                   rd_rst,
    input   wire                   wclk,
    input   wire                   rclk,
    input   wire                   we,
    input   wire                   re,
    output  wire                   wr_full,
    output  reg                    wr_ack,
    output  wire                   wr_almost_full,
    output  wire                   rd_empty,
    output  wire                   rd_almost_empty,
    output  wire                   wr_prog_full,
    output  wire                   rd_prog_empty,
    output  wire                   wr_en_int,
    output  wire                   rd_en_int,
    output  wire [WADDR_WIDTH-1:0] waddr,
    output  wire [RADDR_WIDTH-1:0] raddr,
    output  wire [WADDR_WIDTH:0]   wr_datacount,
    output  wire [RADDR_WIDTH:0]   rd_datacount,
    output  wire                   rd_vld,
    output  reg                    wr_overflow,
    output  reg                    rd_underflow
);

reg  [WADDR_WIDTH:0] waddr_cntr;
reg  [RADDR_WIDTH:0] raddr_cntr;
reg  [RADDR_WIDTH:0] raddr_cntr_r;
reg                  rd_valid;

wire [RADDR_WIDTH:0] raddr_cntr_w;
wire [WADDR_WIDTH:0] waddr_int;
wire [RADDR_WIDTH:0] raddr_int;
wire [RADDR_WIDTH:0] raddr_int_dcount;
wire [RADDR_WIDTH:0] raddr_dcount;
wire                 rd_empty_int;
wire [WADDR_WIDTH:0] wr_datacount_int;
wire [RADDR_WIDTH:0] rd_datacount_int;

assign waddr     = waddr_cntr[WADDR_WIDTH-1:0];
assign raddr     = raddr_cntr[RADDR_WIDTH-1:0];
assign wr_en_int = we & ~wr_full;

generate
    if (MODE == "FWFT") begin    
        reg init_set;
        reg rd_empty_fwft;
        assign rd_en_int     = (~rd_empty_int & rd_empty) | (re & ~rd_empty_int);
        assign rd_empty      = rd_empty_fwft;
        assign raddr_cntr_w  = ~rd_empty ? raddr_cntr_r/*raddr_cntr-1*/ : raddr_cntr;
        
        if (ASYM_WIDTH_RATIO < 4) begin
            assign wr_datacount  = wr_datacount_int;
            assign rd_datacount  = rd_empty ? rd_datacount_int : ~init_set ? (rd_datacount_int+1'b1) : rd_datacount_int;
        end
        else begin
            assign wr_datacount  = wr_datacount_int;
            assign rd_datacount  = rd_datacount_int;        
        end
        
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                init_set <= 1'b1;
            end
            else if (~init_set & rd_empty) begin
                init_set <= 1'b1;            
            end
            else if (~rd_empty_int) begin
                init_set <= 1'b0;            
            end
            else if (rd_empty) begin
                init_set <= 1'b1;            
            end
        end
        
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_empty_fwft <= 1'b1;
            end
            else if (rd_en_int) begin
                rd_empty_fwft <= 1'b0;            
            end
            else if (re) begin
                rd_empty_fwft <= 1'b1;            
            end
        end          
        
        if (FAMILY == "TRION") begin
            if (OUTPUT_REG) begin
                always @ (posedge rclk or posedge rd_rst) begin
                    if (rd_rst) begin
                        rd_valid <= 1'b0;
                    end
                    else begin
                        rd_valid <= ~rd_empty;
                    end
                end
                assign rd_vld = rd_valid;                
            end    
            else begin
                assign rd_vld = ~rd_empty;                
            end
        end
        else begin
            assign rd_vld = ~rd_empty;
        end
    end
    else begin
        assign rd_en_int     = re & ~rd_empty_int;
        assign rd_empty      = rd_empty_int;
        assign raddr_cntr_w  = raddr_cntr;
        assign wr_datacount  = wr_datacount_int;
        assign rd_datacount  = rd_datacount_int;
        
        if (OUTPUT_REG) begin
            reg rd_valid_r;
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid_r <= 'h0;
                    rd_valid   <= 'h0;
                end
                else begin
                    {rd_valid,rd_valid_r} <= {rd_valid_r,rd_en_int};
                end
            end
            assign rd_vld = rd_valid;            
        end
        else begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid  <= 'h0;
                end
                else begin
                    rd_valid <= rd_en_int;
                end
            end
            assign rd_vld = rd_valid;
        end
    end
    
    if (ALMOST_FLAG) begin
        assign wr_almost_full   = wr_datacount_int >= WR_DEPTH-1;
        assign rd_almost_empty  = rd_datacount_int <= 'd1;  
    end
    else begin
        assign wr_almost_full   = 1'b0;
        assign rd_almost_empty  = 1'b0;        
    end
    
    if (PROGRAMMABLE_FULL == "STATIC_SINGLE") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_datacount >= PROG_FULL_ASSERT;
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end  
    end
    else if (PROGRAMMABLE_FULL == "STATIC_DUAL") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_prog_full_int ? wr_datacount >= PROG_FULL_NEGATE : wr_datacount >= PROG_FULL_ASSERT;   

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end              
    end
    else begin
        assign wr_prog_full  = 1'b0; 
    end
        
    if (PROGRAMMABLE_EMPTY == "STATIC_SINGLE") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty  = rd_datacount <= PROG_EMPTY_ASSERT;
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end
    end
    else if (PROGRAMMABLE_EMPTY == "STATIC_DUAL") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty = rd_prog_empty_int ? (rd_datacount <= PROG_EMPTY_NEGATE) : (rd_datacount <= PROG_EMPTY_ASSERT);
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end  
    end
    else begin
        assign rd_prog_empty  = 1'b0; 
    end
    
    if (HANDSHAKE_FLAG) begin
    
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_ack <= 1'b0;
            end
            else begin
                wr_ack <= wr_en_int & ~wr_overflow;
            end
        end

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else if (we && wr_full) begin
                wr_overflow <= 1'b1;
            end
            else begin
                wr_overflow <= 1'b0;            
            end
        end
        
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else if (re && rd_empty) begin
                rd_underflow <= 1'b1;
            end
            else begin
                rd_underflow <= 1'b0;            
            end
        end
    end
    
    localparam RATIO_WIDTH = (RADDR_WIDTH >= WADDR_WIDTH)? RADDR_WIDTH - WADDR_WIDTH : WADDR_WIDTH - RADDR_WIDTH;
    
    if (ASYM_WIDTH_RATIO < 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:RATIO_WIDTH]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:RATIO_WIDTH];
        assign wr_datacount_int = waddr_cntr - (raddr_int/RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int*RAM_MUX_RATIO)-raddr_cntr;
    end
    else begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:RATIO_WIDTH] == raddr_int[RADDR_WIDTH-1:0]);
        assign rd_empty_int     = (waddr_int- raddr_cntr*RAM_MUX_RATIO) < RAM_MUX_RATIO;
        assign wr_datacount_int = waddr_cntr - (raddr_int*RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int/RAM_MUX_RATIO)-raddr_cntr_w;
    end
endgenerate

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr <= 'h0;
    end
    else if (wr_en_int) begin
        waddr_cntr <= waddr_cntr + 1'b1;
    end
end

always @ (posedge rclk or posedge rd_rst) begin
    if (rd_rst) begin
        raddr_cntr   <= 'h0;
        raddr_cntr_r <= 'h0;
    end
    else if (rd_en_int) begin
        raddr_cntr   <= raddr_cntr + 1'b1;
        raddr_cntr_r <= raddr_cntr;
    end
end

generate
    if (SYNC_CLK) begin
        assign waddr_int        = waddr_cntr;
        assign raddr_int        = raddr_cntr_w;
    end
    else begin
        reg  [RADDR_WIDTH:0] raddr_cntr_gry_r;
        reg  [WADDR_WIDTH:0] waddr_cntr_gry_r;
        
        wire [RADDR_WIDTH:0] raddr_cntr_gry;
        wire [RADDR_WIDTH:0] raddr_cntr_gry_sync;
        wire [RADDR_WIDTH:0] raddr_cntr_sync_g2b;
        wire [WADDR_WIDTH:0] waddr_cntr_gry;
        wire [WADDR_WIDTH:0] waddr_cntr_gry_sync;
        wire [WADDR_WIDTH:0] waddr_cntr_sync_g2b;

        if (PIPELINE_REG) begin
            reg  [RADDR_WIDTH:0] raddr_cntr_sync_g2b_r;    
            reg  [WADDR_WIDTH:0] waddr_cntr_sync_g2b_r;        
        
            assign waddr_int        = waddr_cntr_sync_g2b_r;
            assign raddr_int        = raddr_cntr_sync_g2b_r;
            
            always @ (posedge wclk or posedge wr_rst) begin
                if (wr_rst) begin
                    raddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    raddr_cntr_sync_g2b_r <= raddr_cntr_sync_g2b;
                end
            end
            
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    waddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    waddr_cntr_sync_g2b_r <= waddr_cntr_sync_g2b;
                end
            end
        end
        else begin
            assign waddr_int        = waddr_cntr_sync_g2b;
            assign raddr_int        = raddr_cntr_sync_g2b;        
        end
      
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                raddr_cntr_gry_r <= 'h0;
            end
            else begin
                raddr_cntr_gry_r <= raddr_cntr_gry;
            end
        end
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(RADDR_WIDTH+1)                     ) xrd2wr_bin2gray    (.bin_i(raddr_cntr_w), .gray_o(raddr_cntr_gry));
        `IP_MODULE_NAME(efx_fifo_datasync) # (.STAGE(SYNC_STAGE), .WIDTH (RADDR_WIDTH+1)) xrd2wr_addr_sync   (.clk_i(wclk), .d_i(raddr_cntr_gry_r), .d_o(raddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(RADDR_WIDTH+1)                     ) xrd2wr_gray2bin    (.gray_i(raddr_cntr_gry_sync), .bin_o(raddr_cntr_sync_g2b));
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                waddr_cntr_gry_r <= 'h0;
            end
            else begin
                waddr_cntr_gry_r <= waddr_cntr_gry;
            end
        end        
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(WADDR_WIDTH+1)                     ) wr2rd_bin2gray  (.bin_i(waddr_cntr), .gray_o(waddr_cntr_gry));
        `IP_MODULE_NAME(efx_fifo_datasync) # (.STAGE(SYNC_STAGE), .WIDTH (WADDR_WIDTH+1)) wr2rd_addr_sync (.clk_i(rclk), .d_i(waddr_cntr_gry_r), .d_o(waddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(WADDR_WIDTH+1)                     ) wr2rd_gray2bin  (.gray_i(waddr_cntr_gry_sync), .bin_o(waddr_cntr_sync_g2b));        

    end
endgenerate
endmodule


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   bin2gray.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Binary to Gray Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_bin2gray)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] gray_o,
 // input
 input [WIDTH-1:0] bin_i
 );

//---------------------------------------------------------------------
// Function :   bit_xor
// Description: reduction xor
function bit_xor (
  input [31:0] nex_bit,
  input [31:0] curr_bit,
  input [WIDTH-1:0] xor_in);
  begin : fn_bit_xor
    bit_xor = xor_in[nex_bit] ^ xor_in[curr_bit];
  end
endfunction

// Convert Binary to Gray, bit by bit
generate 
begin
  genvar bit_idx;
  for(bit_idx=0; bit_idx<WIDTH-1; bit_idx=bit_idx+1) begin : gBinBits
    assign gray_o[bit_idx] = bit_xor(bit_idx+1, bit_idx, bin_i);
  end
  assign   gray_o[WIDTH-1] = bin_i[WIDTH-1];
end
endgenerate

endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////

`undef IP_UUID
`undef IP_NAME_CONCAT
`undef IP_MODULE_NAME
