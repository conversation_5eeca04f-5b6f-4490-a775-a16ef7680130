`define DDR3_MODE 2
`define READ_ENABLE_PIPELINE 7
`define RAM_IDLE 9'd0
`define RAM_ACT 9'd3
`define RAM_WR 9'd11
`define RAM_RD 9'd13
`define RAM_RD2PREA 9'd15
`define RAM_WR2PREA 9'd21
`define RAM_ACT2PREA 9'd31
`define RAM_WR2RD 9'd41
`define RAM_RD2WR 9'd48
`define RAM_PREA2REF 9'd51
`define RAM_REF 9'd61
`define RAM_NOP 9'd131
`define RAM_WPAW 9'd133
`define RAM_WPAR 9'd149
`define RAM_RPAW 9'd165
`define RAM_RPAR 9'd174
`define RAM_SRE 9'd183
`define RAM_SR 9'd185
`define RAM_SRX 9'd187
`define RAM_RZQS 9'd257
`define RAM_INIT 9'd290
`define RAM_WAITING 9'd377
`define BRAM_LEN 381
`define BRAM_I_WIDTH 9
`define BRAM_D_WIDTH 72
`define MICRO_SEC 384
`define REF_INTERVAL 2995.2
`define DRAM_WIDTH 16
`define GROUP_WIDTH 8
`define DRAM_GROUP 2
`define DM_BIT_WIDTH 16
`define ROW 16
`define COL 10
`define BANK 3
`define BA_BIT_WIDTH 8
`define WFIFO_WIDTH 128
`define BL 8
`define usReset 200
`define usCKE 500
`define tZQinit 512
`define tZQCS 16'd1024
`define ODTH8 6
`define tRL 5
`define tWL 5
`define ARBITER_INIT 1
`define ARBITER_COUNT 4
`define RAM_FILE "ddr3_controller.bin"
`define PLL_OUT_SEL 5'd4
