/*-----------------------------------------------------------------------
                                 \\\|///
                               \\  - -  //
                                (  @ @  )
+-----------------------------oOOo-(_)-oOOo-----------------------------+
CONFIDENTIAL IN CONFIDENCE
This confidential and proprietary software may be only used as authorized
by a licensing agreement from CrazyBingo (Thereturnofbingo).
In the event of publication, the following notice is applicable:
Copyright (C) 2013-20xx CrazyBingo Corporation
The entire notice above must be reproduced on all authorized copies.
Author              :       CrazyBingo
Technology blogs    :       www.crazyfpga.com
Email Address       :       <EMAIL>
Filename            :       VIP_AutoWhiteBalance.v
Date                :       2025-10-09
Description         :       Auto White Balance for Video Image Processor.
                            Based on Gray World Assumption algorithm
                            Support real-time processing for 1024x600@30FPS
Modification History    :
Date            By          Version         Change Description
=========================================================================
25/10/09        CrazyBingo  1.0             Original
-------------------------------------------------------------------------
|                                     Oooo                              |
+-------------------------------oooO--(   )-----------------------------+
                               (   )   ) /
                                \ (   (_/
                                 \_)
-----------------------------------------------------------------------*/

`timescale 1ns/1ns
module VIP_AutoWhiteBalance
#(
    parameter   [13:0]  IMG_HDISP = 11'd1024,   //1024*600
    parameter   [13:0]  IMG_VDISP = 11'd600
)
(
    //global clock
    input               clk,                //cmos video pixel clock
    input               rst_n,              //global reset
    
    //AWB control
    input               awb_enable,         //AWB enable control
    
    //RGB888 data input
    input               per_frame_vsync,    //Prepared Image data vsync valid signal
    input               per_frame_href,     //Prepared Image data href vaild  signal
    input       [23:0]  per_rgb_data,       //Prepared RGB888 data [23:16]R [15:8]G [7:0]B
    
    //RGB888 data output
    output reg          post_frame_vsync,   //Processed Image data vsync valid signal
    output reg          post_frame_href,    //Processed Image data href vaild  signal
    output reg  [23:0]  post_rgb_data       //Processed RGB888 data [23:16]R [15:8]G [7:0]B
);

//Extract RGB channels
wire [7:0] per_img_red   = per_rgb_data[23:16];
wire [7:0] per_img_green = per_rgb_data[15:8];
wire [7:0] per_img_blue  = per_rgb_data[7:0];

//Frame control signals
reg per_frame_vsync_r;
reg frame_start, frame_end;
always@(posedge clk or negedge rst_n)
begin
    if(!rst_n) begin
        per_frame_vsync_r <= 0;
        frame_start <= 0;
        frame_end <= 0;
    end
    else begin
        per_frame_vsync_r <= per_frame_vsync;
        frame_start <= per_frame_vsync & ~per_frame_vsync_r;  //vsync rising edge
        frame_end   <= ~per_frame_vsync & per_frame_vsync_r;  //vsync falling edge
    end
end

//RGB accumulator for statistics (24-bit to prevent overflow)
reg [23:0] acc_red, acc_green, acc_blue;
reg [19:0] pixel_count;  //max 1024*600 = 614400 < 2^20

always@(posedge clk or negedge rst_n)
begin
    if(!rst_n) begin
        acc_red <= 0;
        acc_green <= 0;
        acc_blue <= 0;
        pixel_count <= 0;
    end
    else begin
        if(frame_start) begin  //Reset at frame start
            acc_red <= 0;
            acc_green <= 0;
            acc_blue <= 0;
            pixel_count <= 0;
        end
        else if(per_frame_href && awb_enable) begin  //Accumulate during valid pixels
            acc_red <= acc_red + per_img_red;
            acc_green <= acc_green + per_img_green;
            acc_blue <= acc_blue + per_img_blue;
            pixel_count <= pixel_count + 1;
        end
    end
end

//Calculate average values at frame end
reg [7:0] avg_red, avg_green, avg_blue;
always@(posedge clk or negedge rst_n)
begin
    if(!rst_n) begin
        avg_red <= 128;    //Default middle value
        avg_green <= 128;
        avg_blue <= 128;
    end
    else begin
        if(frame_end && awb_enable && pixel_count > 0) begin
            avg_red   <= acc_red[23:16];    //Simple division by 256 for speed
            avg_green <= acc_green[23:16];
            avg_blue  <= acc_blue[23:16];
        end
    end
end

//Calculate gain coefficients (Gray World Algorithm)
//gain_r = avg_green / avg_red, gain_b = avg_green / avg_blue
reg [15:0] gain_red, gain_blue;
always@(posedge clk or negedge rst_n)
begin
    if(!rst_n) begin
        gain_red <= 256;   //1.0 in 8.8 fixed point
        gain_blue <= 256;
    end
    else begin
        if(frame_end && awb_enable) begin
            //Prevent division by zero and extreme gains
            if(avg_red > 16 && avg_red < 240) 
                gain_red <= (avg_green << 8) / avg_red;  //8.8 fixed point
            else
                gain_red <= 256;  //Keep 1.0 if invalid
                
            if(avg_blue > 16 && avg_blue < 240)
                gain_blue <= (avg_green << 8) / avg_blue;
            else
                gain_blue <= 256;
        end
    end
end

//Apply white balance gains with overflow protection
reg [15:0] corrected_red, corrected_blue;
reg [7:0] final_red, final_green, final_blue;

always@(posedge clk or negedge rst_n)
begin
    if(!rst_n) begin
        corrected_red <= 0;
        corrected_blue <= 0;
        final_red <= 0;
        final_green <= 0;
        final_blue <= 0;
    end
    else begin
        if(awb_enable) begin
            //Apply gains (multiply by 8.8 fixed point)
            corrected_red  <= (per_img_red * gain_red) >> 8;
            corrected_blue <= (per_img_blue * gain_blue) >> 8;
            
            //Clamp to 8-bit range with overflow protection
            final_red   <= (corrected_red > 255) ? 8'hFF : corrected_red[7:0];
            final_green <= per_img_green;  //Green channel unchanged
            final_blue  <= (corrected_blue > 255) ? 8'hFF : corrected_blue[7:0];
        end
        else begin
            //Bypass mode when AWB disabled
            final_red   <= per_img_red;
            final_green <= per_img_green;
            final_blue  <= per_img_blue;
        end
    end
end

//Output timing alignment (2 clock delay for processing)
reg [1:0] vsync_delay, href_delay;
always@(posedge clk or negedge rst_n)
begin
    if(!rst_n) begin
        vsync_delay <= 0;
        href_delay <= 0;
        post_frame_vsync <= 0;
        post_frame_href <= 0;
        post_rgb_data <= 0;
    end
    else begin
        //Delay control signals by 2 clocks to match processing delay
        vsync_delay <= {vsync_delay[0], per_frame_vsync};
        href_delay <= {href_delay[0], per_frame_href};
        
        post_frame_vsync <= vsync_delay[1];
        post_frame_href <= href_delay[1];
        post_rgb_data <= {final_red, final_green, final_blue};
    end
end

endmodule
