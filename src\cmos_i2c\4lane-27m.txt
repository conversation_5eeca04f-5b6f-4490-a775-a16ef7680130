/*-------------------------------------------------------------------------
This confidential and proprietary software may be only used as authorized
by a licensing agreement from CrazyBingo.www.cnblogs.com/crazybingo
(C) COPYRIGHT 2012 CrazyBingo. ALL RIGHTS RESERVED
Filename            :       I2C_SC130GS_12801024_Config.v
Author              :       CrazyBingo
Date                :       2019-08-03
Version             :       1.0
Description         :       I2C Configure Data of AR0135.
Modification History    :
Date            By          Version         Change Description
===========================================================================
19/08/03        CrazyBingo  1.0             Original
--------------------------------------------------------------------------*/

`timescale 1ns/1ns
module  I2C_SC130GS_12801024_4Lanes_Config  //1280*1024@60 with AutO/Manual Exposure
(
    input       [7:0]   LUT_INDEX,
    output  reg [23:0]  LUT_DATA,
    output      [7:0]   LUT_SIZE
);
assign  LUT_SIZE = 106 + 1;

//-----------------------------------------------------------------
/////////////////////   Config Data LUT   //////////////////////////    
always@(*)
begin
    case(LUT_INDEX)
0:	LUT_DATA = {16'h0103, 8'h01}; 
1:	LUT_DATA = {16'h0100, 8'h00,
16'h3039, 8'h80,
16'h3034, 8'h80,
16'h3001, 8'h00,
16'h3018, 8'h70,
16'h3019, 8'h00,
16'h301f, 8'h47,
16'h3022, 8'h10,
16'h302b, 8'h80,
16'h3030, 8'h01,
16'h3000, 8'h00,
16'h3031, 8'h08,
16'h3035, 8'hd2,
16'h3036, 8'h00,
16'h3038, 8'h4b,
16'h303a, 8'h35,
16'h303b, 8'h0e,
16'h303c, 8'h06,
16'h303d, 8'h03,
16'h303f, 8'h11,
16'h3202, 8'h00,
16'h3203, 8'h00,
16'h3205, 8'h8b,
16'h3206, 8'h02,
16'h3207, 8'h04,
16'h320a, 8'h04,
16'h320b, 8'h00,
16'h320c, 8'h03,
16'h320d, 8'h0c,
16'h320e, 8'h02,
16'h320f, 8'h0f,
16'h3211, 8'h08,
16'h3213, 8'h04,
16'h3300, 8'h20,
16'h3302, 8'h0c,
16'h3306, 8'h48,
16'h3308, 8'h50,
16'h330a, 8'h01,
16'h330b, 8'h20,
16'h330e, 8'h1a,
16'h3310, 8'hf0,
16'h3311, 8'h10,
16'h3319, 8'he8,
16'h3333, 8'h90,
16'h3334, 8'h30,
16'h3348, 8'h02,
16'h3349, 8'hee,
16'h334a, 8'h02,
16'h334b, 8'he0,
16'h335d, 8'h00,
16'h3380, 8'hff,
16'h3382, 8'he0,
16'h3383, 8'h0a,
16'h3384, 8'he4,
16'h3400, 8'h53,
16'h3416, 8'h31,
16'h3518, 8'h07,
16'h3519, 8'hc8,
16'h3620, 8'h24,
16'h3621, 8'h0a,
16'h3622, 8'h06,
16'h3623, 8'h14,
16'h3624, 8'h20,
16'h3625, 8'h00,
16'h3626, 8'h00,
16'h3627, 8'h01,
16'h3630, 8'h63,
16'h3632, 8'h74,
16'h3633, 8'h63,
16'h3634, 8'hff,
16'h3635, 8'h44,
16'h3638, 8'h82,
16'h3639, 8'h74,
16'h363a, 8'h24,
16'h363b, 8'h00,
16'h3640, 8'h03,
16'h3658, 8'h9a,
16'h3663, 8'h88,
16'h3664, 8'h06,
16'h3c00, 8'h41,
16'h3d08, 8'h00,
16'h3e01, 8'h20,
16'h3e02, 8'h50,
16'h3e03, 8'h0b,
16'h3e08, 8'h02,
16'h3e09, 8'h20,
16'h3e0e, 8'h00,
16'h3e0f, 8'h15,
16'h3e14, 8'hb0,
16'h3f08, 8'h04,
16'h4501, 8'hc0,
16'h4502, 8'h16,
16'h5000, 8'h01,
16'h5050, 8'h0c,
16'h5b00, 8'h02,
16'h5b01, 8'h03,
16'h5b02, 8'h01,
16'h5b03, 8'h01,
16'h3039, 8'h44,
16'h3034, 8'h01,
16'h363a, 8'h24,
102:	LUT_DATA = {16'h3630, 8'h63,
106:	LUT_DATA = {16'h0100, 8'h01,

