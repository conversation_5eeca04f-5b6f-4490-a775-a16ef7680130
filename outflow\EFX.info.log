
///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:23:32
///////////////////////////////////

[EFX-0012 VERI-INFO] default VHDL library search path is now "C:/Efinity/2023.2/sim_models/vhdl/packages/vhdl_2008" (VHDL-1504) 
[EFX-0012 VERI-INFO] undeclared symbol 'la_capture_mask', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4391)
[EFX-0012 VERI-INFO] undeclared symbol 'next_segment_pointer_eq', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4749)
[EFX-0012 VERI-INFO] undeclared symbol 'next_segment_msb_xor', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4750)
[EFX-0011 VERI-WARNING] port 'probe1' remains unconnected for this instance (VERI-1927) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:108)
[EFX-0012 VERI-INFO] compiling module 'edb_top' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:10)
[EFX-0012 VERI-INFO] compiling module 'EFX_GBUFCE(CE_POLARITY=1'b1)' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:48)
[EFX-0012 VERI-INFO] compiling module 'edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b01100111010001110100100101101110011101010100101001001101111001101000101001011011100010111000010000110011000001111110011111111000,PROBE0_TYPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:481)
[EFX-0012 VERI-INFO] compiling module 'edb_adbg_crc32' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:182)
[EFX-0012 VERI-INFO] compiling module 'compare_unit(WIDTH=11'b01,PIPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5472)
[EFX-0012 VERI-INFO] compiling module 'trigger_unit(WIDTH=32'b01,PIPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5677)
[EFX-0012 VERI-INFO] compiling module 'la_biu(CAPTURE_WIDTH=32'b01)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4826)
[EFX-0012 VERI-INFO] compiling module 'fifo_with_read(DATA_WIDTH=32'b010)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4568)
[EFX-0012 VERI-INFO] compiling module 'fifo_address_trancode_unit' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4518)
[EFX-0012 VERI-INFO] compiling module 'edb_simple_dual_port_ram(DATA_WIDTH=32'b010,ADDR_WIDTH=10,RAM_INIT_FILE="")' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:391)
[EFX-0012 VERI-INFO] extracting RAM for identifier 'ram' (VERI-2571) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:406)
[EFX-0012 VERI-INFO] compiling module 'debug_hub' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:260)
[EFX-0011 VERI-WARNING] input port 'probe1[0]' remains unconnected for this instance (VDB-1053) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0000 INFO] ... Setting Synthesis Option: mode=speed
[EFX-0012 VERI-INFO] compiling module 'EFX_ADD' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:8)
[EFX-0012 VERI-INFO] compiling module 'EFX_FF' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:21)
[EFX-0012 VERI-INFO] compiling module 'EFX_COMB4' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:38)
[EFX-0012 VERI-INFO] compiling module 'EFX_GBUFCE' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:48)
[EFX-0012 VERI-INFO] compiling module 'EFX_LUT4' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:57)
[EFX-0012 VERI-INFO] compiling module 'EFX_MULT' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:65)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP48' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:100)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP24' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:156)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP12' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:209)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_4K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:262)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_5K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:322)
[EFX-0012 VERI-INFO] compiling module 'EFX_DPRAM_5K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:394)
[EFX-0012 VERI-INFO] compiling module 'RAMB5' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:499)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_10K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:561)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM10' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:653)
[EFX-0012 VERI-INFO] compiling module 'EFX_DPRAM10' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:754)
[EFX-0012 VERI-INFO] compiling module 'EFX_SRL8' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:884)
[EFX-0000 INFO] ... Pre-synthesis checks begin
[EFX-0200 WARNING] Removing redundant signal : compared_in[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5478)
[EFX-0200 WARNING] Removing redundant signal : mask_in[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5479)
[EFX-0200 WARNING] Removing redundant signal : trigger_in. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5689)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:406)
[EFX-0200 WARNING] Removing redundant signal : din[1]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4581)
[EFX-0200 WARNING] Removing redundant signal : trig_out. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4860)
[EFX-0200 WARNING] Removing redundant signal : trig_out_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4861)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1014)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1016)
[EFX-0200 WARNING] Removing redundant signal : bscan_SEL. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1017)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1021)
[EFX-0200 WARNING] Removing redundant signal : trig_in_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1032)
[EFX-0200 WARNING] Removing redundant signal : mux_capture_cmp[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:3444)
[EFX-0200 WARNING] Removing redundant signal : bscan_CAPTURE. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:265)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:266)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:268)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:273)
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_in=0).
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_out_ack=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe1[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe2[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe3[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe4[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe5[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe6[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe7[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe8[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe9[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe10[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0000 INFO] ... Pre-synthesis checks end (Real time : 0s)
[EFX-0000 INFO] ... NameSpace init begin
[EFX-0000 INFO] ... NameSpace init end (Real time : 0s)
[EFX-0000 INFO] ... Mapping design "edb_top"
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_adbg_crc32" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_adbg_crc32" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "compare_unit(WIDTH=11'b01,PIPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "compare_unit(WIDTH=11'b01,PIPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "trigger_unit(WIDTH=32'b01,PIPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "trigger_unit(WIDTH=32'b01,PIPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_address_trancode_unit" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_address_trancode_unit" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_simple_dual_port_ram(DATA_WIDTH=32'b010,ADDR_WIDTH=10,RAM_INIT_FILE="")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_simple_dual_port_ram(DATA_WIDTH=32'b010,ADDR_WIDTH=10,RAM_INIT_FILE="")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_with_read(DATA_WIDTH=32'b010)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_with_read(DATA_WIDTH=32'b010)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "la_biu(CAPTURE_WIDTH=32'b01)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "la_biu(CAPTURE_WIDTH=32'b01)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b01100111010001110100100101101110011101010100101001001101111001101000101001011011100010111000010000110011000001111110011111111000,PROBE0_TYPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b01100111010001110100100101101110011101010100101001001101111001101000101001011011100010111000010000110011000001111110011111111000,PROBE0_TYPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "debug_hub" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "debug_hub" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" begin
[EFX-0000 INFO] ... Clock Enable Synthesis Performed on 130 flops.
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Flat synthesis begin
[EFX-0000 INFO] ... Flat synthesis end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Check and break combinational loops begin
[EFX-0000 INFO] ... Check and break combinational loops end (Real time : 0s)
[EFX-0000 INFO] ... SOP modeling begin
[EFX-0000 INFO] ... SOP modeling end (Real time : 0s)
[EFX-0000 INFO] ... LUT mapping begin
[EFX-0000 INFO] ... LS, strategy: 3, nd: 801, ed: 2738, lv: 7, pw: 1346.56
[EFX-0000 INFO] ... LUT mapping end (Real time : 5s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation end (Real time : 0s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing end (Real time : 0s)
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_DRCK is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_RUNTEST is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_TMS is unconnected and will be removed 
[EFX-0012 VERI-INFO] Found 3 warnings in the post-synthesis netlist. 
[EFX-0000 INFO] Resource Summary 
[EFX-0000 INFO] =============================== 
[EFX-0000 INFO] EFX_ADD         : 	86
[EFX-0000 INFO] EFX_LUT4        : 	785
[EFX-0000 INFO] EFX_FF          : 	674
[EFX-0000 INFO] EFX_SRL8        : 	1
[EFX-0000 INFO] EFX_RAM10       : 	1
[EFX-0000 INFO] EFX_GBUFCE      : 	1
[EFX-0000 INFO] =============================== 

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:23:46
///////////////////////////////////

[EFX-0000 INFO] ... Setting Synthesis Option: mode=speed
[EFX-0000 INFO] ... Pre-synthesis checks begin
[EFX-0256 WARNING] The primary output port 'csi_ctl0_o' wire 'csi_ctl0_o' is not driven.
[EFX-0256 WARNING] The primary output port 'csi_ctl1_o' wire 'csi_ctl1_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_rst_o' wire 'dsi_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_oe' wire 'dsi_txc_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_o' wire 'dsi_txc_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_oe' wire 'dsi_txc_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_o' wire 'dsi_txc_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_oe' wire 'dsi_txc_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[7]' wire 'dsi_txc_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[6]' wire 'dsi_txc_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[5]' wire 'dsi_txc_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[4]' wire 'dsi_txc_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[3]' wire 'dsi_txc_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[2]' wire 'dsi_txc_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[1]' wire 'dsi_txc_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[0]' wire 'dsi_txc_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_rst_o' wire 'dsi_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_oe' wire 'dsi_txd0_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[7]' wire 'dsi_txd0_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[6]' wire 'dsi_txd0_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[5]' wire 'dsi_txd0_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[4]' wire 'dsi_txd0_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[3]' wire 'dsi_txd0_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[2]' wire 'dsi_txd0_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[1]' wire 'dsi_txd0_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[0]' wire 'dsi_txd0_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_oe' wire 'dsi_txd0_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_o' wire 'dsi_txd0_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_oe' wire 'dsi_txd0_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_o' wire 'dsi_txd0_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_rst_o' wire 'dsi_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_oe' wire 'dsi_txd1_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_o' wire 'dsi_txd1_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_oe' wire 'dsi_txd1_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_o' wire 'dsi_txd1_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_oe' wire 'dsi_txd1_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[7]' wire 'dsi_txd1_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[6]' wire 'dsi_txd1_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[5]' wire 'dsi_txd1_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[4]' wire 'dsi_txd1_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[3]' wire 'dsi_txd1_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[2]' wire 'dsi_txd1_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[1]' wire 'dsi_txd1_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[0]' wire 'dsi_txd1_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_rst_o' wire 'dsi_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_oe' wire 'dsi_txd2_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_o' wire 'dsi_txd2_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_oe' wire 'dsi_txd2_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_o' wire 'dsi_txd2_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_oe' wire 'dsi_txd2_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[7]' wire 'dsi_txd2_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[6]' wire 'dsi_txd2_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[5]' wire 'dsi_txd2_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[4]' wire 'dsi_txd2_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[3]' wire 'dsi_txd2_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[2]' wire 'dsi_txd2_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[1]' wire 'dsi_txd2_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[0]' wire 'dsi_txd2_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_rst_o' wire 'dsi_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_oe' wire 'dsi_txd3_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_o' wire 'dsi_txd3_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_oe' wire 'dsi_txd3_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_o' wire 'dsi_txd3_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_oe' wire 'dsi_txd3_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[7]' wire 'dsi_txd3_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[6]' wire 'dsi_txd3_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[5]' wire 'dsi_txd3_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[4]' wire 'dsi_txd3_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[3]' wire 'dsi_txd3_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[2]' wire 'dsi_txd3_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[1]' wire 'dsi_txd3_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[0]' wire 'dsi_txd3_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'uart_tx_o' wire 'uart_tx_o' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sclk' wire 'cmos_sclk' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OUT' wire 'cmos_sdat_OUT' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OE' wire 'cmos_sdat_OE' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl2' wire 'cmos_ctl2' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl3' wire 'cmos_ctl3' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_oe' wire 'lvds_txc_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[6]' wire 'lvds_txc_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[5]' wire 'lvds_txc_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[4]' wire 'lvds_txc_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[3]' wire 'lvds_txc_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[2]' wire 'lvds_txc_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[1]' wire 'lvds_txc_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[0]' wire 'lvds_txc_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_rst_o' wire 'lvds_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_oe' wire 'lvds_txd0_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[6]' wire 'lvds_txd0_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[5]' wire 'lvds_txd0_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[4]' wire 'lvds_txd0_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[3]' wire 'lvds_txd0_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[2]' wire 'lvds_txd0_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[1]' wire 'lvds_txd0_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[0]' wire 'lvds_txd0_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_rst_o' wire 'lvds_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_oe' wire 'lvds_txd1_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[6]' wire 'lvds_txd1_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[5]' wire 'lvds_txd1_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[4]' wire 'lvds_txd1_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[3]' wire 'lvds_txd1_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[2]' wire 'lvds_txd1_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[1]' wire 'lvds_txd1_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[0]' wire 'lvds_txd1_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_rst_o' wire 'lvds_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_oe' wire 'lvds_txd2_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[6]' wire 'lvds_txd2_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[5]' wire 'lvds_txd2_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[4]' wire 'lvds_txd2_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[3]' wire 'lvds_txd2_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[2]' wire 'lvds_txd2_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[1]' wire 'lvds_txd2_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[0]' wire 'lvds_txd2_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_rst_o' wire 'lvds_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_oe' wire 'lvds_txd3_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[6]' wire 'lvds_txd3_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[5]' wire 'lvds_txd3_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[4]' wire 'lvds_txd3_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[3]' wire 'lvds_txd3_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[2]' wire 'lvds_txd3_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[1]' wire 'lvds_txd3_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[0]' wire 'lvds_txd3_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_rst_o' wire 'lvds_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_o' wire 'lcd_tp_sda_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_oe' wire 'lcd_tp_sda_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_o' wire 'lcd_tp_scl_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_oe' wire 'lcd_tp_scl_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_o' wire 'lcd_tp_int_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_oe' wire 'lcd_tp_int_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_rst_o' wire 'lcd_tp_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_pwm_o' wire 'lcd_pwm_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_blen_o' wire 'lcd_blen_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_vs_o' wire 'lcd_vs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_hs_o' wire 'lcd_hs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_de_o' wire 'lcd_de_o' is not driven.
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][31]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][30]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][29]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][28]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:3223)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wclk=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[9]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[10]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[11]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[12]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[13]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[14]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[15]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[16]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[17]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[18]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[19]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[20]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[21]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[22]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[23]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[24]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[25]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[26]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[27]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[28]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[29]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[30]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[31]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[32]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[33]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[34]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[35]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[36]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[37]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[38]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[39]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[40]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[41]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[42]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[43]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[44]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[45]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[46]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[47]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[48]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[49]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[50]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[51]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[52]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[53]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[54]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[55]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[56]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[57]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[58]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[59]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[60]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[61]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[62]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[63]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[64]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[65]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[66]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[67]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[68]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[69]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[70]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[71]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (we=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (re=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_push_payload[0]=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.a_rst_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.clk_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'fifo_ack' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_ar_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_aw_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'mux0' input pin tied to constant (b_rd_en=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[0]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[1]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[2]=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[3]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[2]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[3]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[5]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[6]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[7]=0).
[EFX-0266 WARNING] Module Instance 'u_wr_addr_fifo' input pin tied to constant (I_Wr_Data[42]=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[127]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[126]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[125]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[124]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[123]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[122]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\csi_rx\csi_rx.sv:6919)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\csi_rx\csi_rx.sv:6919)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\W0_FIFO_64\W0_FIFO_64.v:721)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\R0_FIFO_8\R0_FIFO_8.v:719)
[EFX-0000 INFO] ... Pre-synthesis checks end (Real time : 0s)
[EFX-0000 INFO] ... NameSpace init begin
[EFX-0000 INFO] ... NameSpace init end (Real time : 0s)
[EFX-0000 INFO] ... Mapping design "example_top"
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_DEPTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_DEPTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=48,DATA_DEPTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=48,DATA_DEPTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "native_mux_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "native_mux_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=4,CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=4,CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_1_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_1_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=144)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=144)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33_renamed_due_excessive_length_1" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33_renamed_due_excessive_length_1" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=52)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=52)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "user_dual_port_ram_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=72,OUTPUT_REG="FALSE",RAM_INIT_FILE="ddr3_controller.bin")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "user_dual_port_ram_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=72,OUTPUT_REG="FALSE",RAM_INIT_FILE="ddr3_controller.bin")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "ddr_3_controller_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "ddr_3_controller_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "controller_top_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "controller_top_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=2,CYCLE=5,IN_RST_ACTIVE=2'b0,OUT_RST_ACTIVE=2'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=2,CYCLE=5,IN_RST_ACTIVE=2'b0,OUT_RST_ACTIVE=2'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "phase_shift_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "phase_shift_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "top_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "top_5ee7df9aa375494db25875c4929bcb33" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_axi_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_axi_5ee7df9aa375494db25875c4929bcb33" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "DdrCtrl" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "DdrCtrl" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "AXI4_AWARMux(AID_LEN=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "AXI4_AWARMux(AID_LEN=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "I2C_SC130GS_12801024_4Lanes_Config" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "I2C_SC130GS_12801024_4Lanes_Config" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "PWMLite" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "PWMLite" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_clane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_clane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dlane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dlane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dword_align_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dword_align_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_deskew_cal_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_deskew_cal_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_csr_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_csr_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_ecc_check_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_ecc_check_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_crc16_d8_gen_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_crc16_d8_gen_e7fb069599c849bca10d5e09e6d98480" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depacketizer_e7fb069599c849bca10d5e09e6d98480(ENABLE_VCX=1'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depacketizer_e7fb069599c849bca10d5e09e6d98480(ENABLE_VCX=1'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_3" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_3" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_lane_aligner_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_lane_aligner_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack40bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack40bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack48bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack48bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack56bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack56bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack64bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack64bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_5" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_5" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_4" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_4" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_byte2pixel_e7fb069599c849bca10d5e09e6d98480(PIXEL_FIFO_DEPTH=1024,ENABLE_VCX=1'b0,PACK_TYPE=15)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_byte2pixel_e7fb069599c849bca10d5e09e6d98480(PIXEL_FIFO_DEPTH=1024,ENABLE_VCX=1'b0,PACK_TYPE=15)" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_top_e7fb069599c849bca10d5e09e6d98480(HS_DATA_WIDTH=8,tINIT_NS=1000,CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,PACK_TYPE=15,ENABLE_VCX=1'b0,PIXEL_FIFO_DEPTH=1024)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_top_e7fb069599c849bca10d5e09e6d98480(HS_DATA_WIDTH=8,tINIT_NS=1000,CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,PACK_TYPE=15,ENABLE_VCX=1'b0,PIXEL_FIFO_DEPTH=1024)" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_2" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_2" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "csi_rx" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "csi_rx" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Sensor_Image_XYCrop(IMAGE_HSIZE_SOURCE=160,IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=160,IMAGE_YSIZE_TARGET=720)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Sensor_Image_XYCrop(IMAGE_HSIZE_SOURCE=160,IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=160,IMAGE_YSIZE_TARGET=720)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_26a5ebe291f84e1e832c846719c7bd6f(MODE="FWFT",WR_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=128,WADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_26a5ebe291f84e1e832c846719c7bd6f(MODE="FWFT",WR_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=128,WADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_26a5ebe291f84e1e832c846719c7bd6f(STAGE=3,WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_26a5ebe291f84e1e832c846719c7bd6f(STAGE=3,WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_26a5ebe291f84e1e832c846719c7bd6f(STAGE=3,WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_26a5ebe291f84e1e832c846719c7bd6f(STAGE=3,WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_26a5ebe291f84e1e832c846719c7bd6f_renamed_due_excessive_length_7" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_26a5ebe291f84e1e832c846719c7bd6f_renamed_due_excessive_length_7" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_26a5ebe291f84e1e832c846719c7bd6f_renamed_due_excessive_length_6" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_26a5ebe291f84e1e832c846719c7bd6f_renamed_due_excessive_length_6" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "W0_FIFO_64" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "W0_FIFO_64" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_8ff540a3c0cf4ec6ad5f65a53419c763(FAMILY="TITANIUM",MODE="FWFT",RD_DEPTH=8192,WDATA_WIDTH=128,RADDR_WIDTH=13,OUTPUT_REG=0,RAM_MUX_RATIO=16)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_8ff540a3c0cf4ec6ad5f65a53419c763(FAMILY="TITANIUM",MODE="FWFT",RD_DEPTH=8192,WDATA_WIDTH=128,RADDR_WIDTH=13,OUTPUT_REG=0,RAM_MUX_RATIO=16)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=14)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=14)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=12)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=12)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=13)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=13)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_9" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_9" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_8" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_8" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "R0_FIFO_8" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "R0_FIFO_8" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=921600)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=921600)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "lcd_driver" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "lcd_driver" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "tmds_channel" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "tmds_channel" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "rgb2dvi(ENABLE_OSERDES=0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "rgb2dvi(ENABLE_OSERDES=0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "example_top" begin
[EFX-0000 INFO] ... Clock Enable Synthesis Performed on 1120 flops.
[EFX-0000 INFO] ... Hierarchical pre-synthesis "example_top" end (Real time : 11s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Flat synthesis begin
[EFX-0000 INFO] ... Flat synthesis end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Check and break combinational loops begin
[EFX-0000 INFO] ... Check and break combinational loops end (Real time : 0s)
[EFX-0000 INFO] ... SOP modeling begin
[EFX-0000 INFO] ... SOP modeling end (Real time : 1s)
[EFX-0000 INFO] ... LUT mapping begin
[EFX-0000 INFO] ... LS, strategy: 3, nd: 5153, ed: 16794, lv: 9, pw: 13644.84
[EFX-0000 INFO] ... LUT mapping end (Real time : 18s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation end (Real time : 1s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing end (Real time : 1s)
[EFX-0000 INFO] Resource Summary 
[EFX-0000 INFO] =============================== 
[EFX-0000 INFO] EFX_ADD         : 	949
[EFX-0000 INFO] EFX_LUT4        : 	5713
[EFX-0000 INFO] EFX_FF          : 	4382
[EFX-0000 INFO] EFX_SRL8        : 	579
[EFX-0000 INFO] EFX_RAM10       : 	52
[EFX-0000 INFO] EFX_GBUFCE      : 	1
[EFX-0000 INFO] =============================== 

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:32:49
///////////////////////////////////

[EFX-0012 VERI-INFO] default VHDL library search path is now "C:/Efinity/2023.2/sim_models/vhdl/packages/vhdl_2008" (VHDL-1504) 
[EFX-0012 VERI-INFO] undeclared symbol 'la_capture_mask', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4391)
[EFX-0012 VERI-INFO] undeclared symbol 'next_segment_pointer_eq', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4749)
[EFX-0012 VERI-INFO] undeclared symbol 'next_segment_msb_xor', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4750)
[EFX-0011 VERI-WARNING] port 'probe1' remains unconnected for this instance (VERI-1927) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:108)
[EFX-0012 VERI-INFO] compiling module 'edb_top' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:10)
[EFX-0012 VERI-INFO] compiling module 'EFX_GBUFCE(CE_POLARITY=1'b1)' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:48)
[EFX-0012 VERI-INFO] compiling module 'edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b01110001110010110101010011101110011001101110010001000001100000111010100011100000011110111010111000001000111101010011101100111110,PROBE0_TYPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:481)
[EFX-0012 VERI-INFO] compiling module 'edb_adbg_crc32' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:182)
[EFX-0012 VERI-INFO] compiling module 'compare_unit(WIDTH=11'b01,PIPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5472)
[EFX-0012 VERI-INFO] compiling module 'trigger_unit(WIDTH=32'b01,PIPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5677)
[EFX-0012 VERI-INFO] compiling module 'la_biu(CAPTURE_WIDTH=32'b01)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4826)
[EFX-0012 VERI-INFO] compiling module 'fifo_with_read(DATA_WIDTH=32'b010)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4568)
[EFX-0012 VERI-INFO] compiling module 'fifo_address_trancode_unit' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4518)
[EFX-0012 VERI-INFO] compiling module 'edb_simple_dual_port_ram(DATA_WIDTH=32'b010,ADDR_WIDTH=10,RAM_INIT_FILE="")' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:391)
[EFX-0012 VERI-INFO] extracting RAM for identifier 'ram' (VERI-2571) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:406)
[EFX-0012 VERI-INFO] compiling module 'debug_hub' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:260)
[EFX-0011 VERI-WARNING] input port 'probe1[0]' remains unconnected for this instance (VDB-1053) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0000 INFO] ... Setting Synthesis Option: mode=speed
[EFX-0012 VERI-INFO] compiling module 'EFX_ADD' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:8)
[EFX-0012 VERI-INFO] compiling module 'EFX_FF' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:21)
[EFX-0012 VERI-INFO] compiling module 'EFX_COMB4' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:38)
[EFX-0012 VERI-INFO] compiling module 'EFX_GBUFCE' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:48)
[EFX-0012 VERI-INFO] compiling module 'EFX_LUT4' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:57)
[EFX-0012 VERI-INFO] compiling module 'EFX_MULT' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:65)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP48' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:100)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP24' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:156)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP12' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:209)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_4K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:262)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_5K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:322)
[EFX-0012 VERI-INFO] compiling module 'EFX_DPRAM_5K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:394)
[EFX-0012 VERI-INFO] compiling module 'RAMB5' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:499)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_10K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:561)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM10' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:653)
[EFX-0012 VERI-INFO] compiling module 'EFX_DPRAM10' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:754)
[EFX-0012 VERI-INFO] compiling module 'EFX_SRL8' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:884)
[EFX-0000 INFO] ... Pre-synthesis checks begin
[EFX-0200 WARNING] Removing redundant signal : compared_in[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5478)
[EFX-0200 WARNING] Removing redundant signal : mask_in[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5479)
[EFX-0200 WARNING] Removing redundant signal : trigger_in. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5689)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:406)
[EFX-0200 WARNING] Removing redundant signal : din[1]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4581)
[EFX-0200 WARNING] Removing redundant signal : trig_out. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4860)
[EFX-0200 WARNING] Removing redundant signal : trig_out_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4861)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1014)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1016)
[EFX-0200 WARNING] Removing redundant signal : bscan_SEL. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1017)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1021)
[EFX-0200 WARNING] Removing redundant signal : trig_in_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1032)
[EFX-0200 WARNING] Removing redundant signal : mux_capture_cmp[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:3444)
[EFX-0200 WARNING] Removing redundant signal : bscan_CAPTURE. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:265)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:266)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:268)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:273)
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_in=0).
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_out_ack=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe1[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe2[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe3[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe4[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe5[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe6[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe7[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe8[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe9[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe10[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0000 INFO] ... Pre-synthesis checks end (Real time : 0s)
[EFX-0000 INFO] ... NameSpace init begin
[EFX-0000 INFO] ... NameSpace init end (Real time : 0s)
[EFX-0000 INFO] ... Mapping design "edb_top"
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_adbg_crc32" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_adbg_crc32" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "compare_unit(WIDTH=11'b01,PIPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "compare_unit(WIDTH=11'b01,PIPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "trigger_unit(WIDTH=32'b01,PIPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "trigger_unit(WIDTH=32'b01,PIPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_address_trancode_unit" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_address_trancode_unit" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_simple_dual_port_ram(DATA_WIDTH=32'b010,ADDR_WIDTH=10,RAM_INIT_FILE="")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_simple_dual_port_ram(DATA_WIDTH=32'b010,ADDR_WIDTH=10,RAM_INIT_FILE="")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_with_read(DATA_WIDTH=32'b010)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_with_read(DATA_WIDTH=32'b010)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "la_biu(CAPTURE_WIDTH=32'b01)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "la_biu(CAPTURE_WIDTH=32'b01)" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b01110001110010110101010011101110011001101110010001000001100000111010100011100000011110111010111000001000111101010011101100111110,PROBE0_TYPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b01110001110010110101010011101110011001101110010001000001100000111010100011100000011110111010111000001000111101010011101100111110,PROBE0_TYPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "debug_hub" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "debug_hub" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" begin
[EFX-0000 INFO] ... Clock Enable Synthesis Performed on 130 flops.
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Flat synthesis begin
[EFX-0000 INFO] ... Flat synthesis end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Check and break combinational loops begin
[EFX-0000 INFO] ... Check and break combinational loops end (Real time : 0s)
[EFX-0000 INFO] ... SOP modeling begin
[EFX-0000 INFO] ... SOP modeling end (Real time : 0s)
[EFX-0000 INFO] ... LUT mapping begin
[EFX-0000 INFO] ... LS, strategy: 3, nd: 812, ed: 2774, lv: 7, pw: 1350.07
[EFX-0000 INFO] ... LUT mapping end (Real time : 6s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation end (Real time : 0s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing end (Real time : 0s)
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_DRCK is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_RUNTEST is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_TMS is unconnected and will be removed 
[EFX-0012 VERI-INFO] Found 3 warnings in the post-synthesis netlist. 
[EFX-0000 INFO] Resource Summary 
[EFX-0000 INFO] =============================== 
[EFX-0000 INFO] EFX_ADD         : 	86
[EFX-0000 INFO] EFX_LUT4        : 	797
[EFX-0000 INFO] EFX_FF          : 	674
[EFX-0000 INFO] EFX_SRL8        : 	1
[EFX-0000 INFO] EFX_RAM10       : 	1
[EFX-0000 INFO] EFX_GBUFCE      : 	1
[EFX-0000 INFO] =============================== 

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:33:03
///////////////////////////////////

[EFX-0210 ERROR] No Top design.

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:34:46
///////////////////////////////////

[EFX-0012 VERI-INFO] default VHDL library search path is now "C:/Efinity/2023.2/sim_models/vhdl/packages/vhdl_2008" (VHDL-1504) 
[EFX-0012 VERI-INFO] undeclared symbol 'la_capture_mask', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4391)
[EFX-0012 VERI-INFO] undeclared symbol 'next_segment_pointer_eq', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4749)
[EFX-0012 VERI-INFO] undeclared symbol 'next_segment_msb_xor', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4750)
[EFX-0011 VERI-WARNING] port 'probe1' remains unconnected for this instance (VERI-1927) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:108)
[EFX-0012 VERI-INFO] compiling module 'edb_top' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:10)
[EFX-0012 VERI-INFO] compiling module 'EFX_GBUFCE(CE_POLARITY=1'b1)' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:48)
[EFX-0012 VERI-INFO] compiling module 'edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b0101100001101100011101001010111111000110110011101000100001010001010011010100000111110100000011111100011100101110111001110101110,PROBE0_WIDTH=8,PROBE0_TYPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:481)
[EFX-0012 VERI-INFO] compiling module 'edb_adbg_crc32' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:182)
[EFX-0012 VERI-INFO] compiling module 'compare_unit(WIDTH=11'b01000,PIPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5472)
[EFX-0012 VERI-INFO] compiling module 'trigger_unit(WIDTH=32'b01,PIPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5677)
[EFX-0012 VERI-INFO] compiling module 'la_biu(CAPTURE_WIDTH=32'b01000)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4826)
[EFX-0012 VERI-INFO] compiling module 'fifo_with_read(DATA_WIDTH=32'b01001)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4568)
[EFX-0012 VERI-INFO] compiling module 'fifo_address_trancode_unit' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4518)
[EFX-0012 VERI-INFO] compiling module 'edb_simple_dual_port_ram(DATA_WIDTH=32'b01001,ADDR_WIDTH=10,RAM_INIT_FILE="")' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:391)
[EFX-0012 VERI-INFO] extracting RAM for identifier 'ram' (VERI-2571) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:406)
[EFX-0012 VERI-INFO] compiling module 'debug_hub' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:260)
[EFX-0011 VERI-WARNING] input port 'probe1[0]' remains unconnected for this instance (VDB-1053) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0000 INFO] ... Setting Synthesis Option: mode=speed
[EFX-0012 VERI-INFO] compiling module 'EFX_ADD' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:8)
[EFX-0012 VERI-INFO] compiling module 'EFX_FF' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:21)
[EFX-0012 VERI-INFO] compiling module 'EFX_COMB4' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:38)
[EFX-0012 VERI-INFO] compiling module 'EFX_GBUFCE' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:48)
[EFX-0012 VERI-INFO] compiling module 'EFX_LUT4' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:57)
[EFX-0012 VERI-INFO] compiling module 'EFX_MULT' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:65)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP48' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:100)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP24' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:156)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP12' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:209)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_4K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:262)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_5K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:322)
[EFX-0012 VERI-INFO] compiling module 'EFX_DPRAM_5K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:394)
[EFX-0012 VERI-INFO] compiling module 'RAMB5' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:499)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_10K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:561)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM10' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:653)
[EFX-0012 VERI-INFO] compiling module 'EFX_DPRAM10' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:754)
[EFX-0012 VERI-INFO] compiling module 'EFX_SRL8' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:884)
[EFX-0000 INFO] ... Pre-synthesis checks begin
[EFX-0200 WARNING] Removing redundant signal : trigger_in. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5689)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:406)
[EFX-0200 WARNING] Removing redundant signal : din[8]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4581)
[EFX-0200 WARNING] Removing redundant signal : trig_out. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4860)
[EFX-0200 WARNING] Removing redundant signal : trig_out_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4861)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1014)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1016)
[EFX-0200 WARNING] Removing redundant signal : bscan_SEL. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1017)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1021)
[EFX-0200 WARNING] Removing redundant signal : trig_in_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1032)
[EFX-0200 WARNING] Removing redundant signal : mux_capture_cmp[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:3444)
[EFX-0200 WARNING] Removing redundant signal : bscan_CAPTURE. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:265)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:266)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:268)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:273)
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_in=0).
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_out_ack=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe1[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe2[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe3[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe4[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe5[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe6[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe7[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe8[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe9[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe10[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0000 INFO] ... Pre-synthesis checks end (Real time : 0s)
[EFX-0000 INFO] ... NameSpace init begin
[EFX-0000 INFO] ... NameSpace init end (Real time : 0s)
[EFX-0000 INFO] ... Mapping design "edb_top"
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_adbg_crc32" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_adbg_crc32" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "compare_unit(WIDTH=11'b01000,PIPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "compare_unit(WIDTH=11'b01000,PIPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "trigger_unit(WIDTH=32'b01,PIPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "trigger_unit(WIDTH=32'b01,PIPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_address_trancode_unit" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_address_trancode_unit" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_simple_dual_port_ram(DATA_WIDTH=32'b01001,ADDR_WIDTH=10,RAM_INIT_FILE="")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_simple_dual_port_ram(DATA_WIDTH=32'b01001,ADDR_WIDTH=10,RAM_INIT_FILE="")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_with_read(DATA_WIDTH=32'b01001)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_with_read(DATA_WIDTH=32'b01001)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "la_biu(CAPTURE_WIDTH=32'b01000)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "la_biu(CAPTURE_WIDTH=32'b01000)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b0101100001101100011101001010111111000110110011101000100001010001010011010100000111110100000011111100011100101110111001110101110,PROBE0_WIDTH=8,PROBE0_TYPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b0101100001101100011101001010111111000110110011101000100001010001010011010100000111110100000011111100011100101110111001110101110,PROBE0_WIDTH=8,PROBE0_TYPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "debug_hub" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "debug_hub" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" begin
[EFX-0000 INFO] ... Clock Enable Synthesis Performed on 130 flops.
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Flat synthesis begin
[EFX-0000 INFO] ... Flat synthesis end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Check and break combinational loops begin
[EFX-0000 INFO] ... Check and break combinational loops end (Real time : 0s)
[EFX-0000 INFO] ... SOP modeling begin
[EFX-0000 INFO] ... SOP modeling end (Real time : 0s)
[EFX-0000 INFO] ... LUT mapping begin
[EFX-0000 INFO] ... LS, strategy: 3, nd: 848, ed: 2850, lv: 7, pw: 1432.42
[EFX-0000 INFO] ... LUT mapping end (Real time : 4s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation end (Real time : 0s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing end (Real time : 0s)
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_DRCK is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_RUNTEST is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_TMS is unconnected and will be removed 
[EFX-0012 VERI-INFO] Found 3 warnings in the post-synthesis netlist. 
[EFX-0000 INFO] Resource Summary 
[EFX-0000 INFO] =============================== 
[EFX-0000 INFO] EFX_ADD         : 	86
[EFX-0000 INFO] EFX_LUT4        : 	841
[EFX-0000 INFO] EFX_FF          : 	715
[EFX-0000 INFO] EFX_SRL8        : 	8
[EFX-0000 INFO] EFX_RAM10       : 	1
[EFX-0000 INFO] EFX_GBUFCE      : 	1
[EFX-0000 INFO] =============================== 

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:35:00
///////////////////////////////////

[EFX-0000 INFO] ... Setting Synthesis Option: mode=speed
[EFX-0000 INFO] ... Pre-synthesis checks begin
[EFX-0256 WARNING] The primary output port 'csi_ctl0_o' wire 'csi_ctl0_o' is not driven.
[EFX-0256 WARNING] The primary output port 'csi_ctl1_o' wire 'csi_ctl1_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_rst_o' wire 'dsi_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_oe' wire 'dsi_txc_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_o' wire 'dsi_txc_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_oe' wire 'dsi_txc_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_o' wire 'dsi_txc_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_oe' wire 'dsi_txc_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[7]' wire 'dsi_txc_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[6]' wire 'dsi_txc_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[5]' wire 'dsi_txc_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[4]' wire 'dsi_txc_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[3]' wire 'dsi_txc_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[2]' wire 'dsi_txc_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[1]' wire 'dsi_txc_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[0]' wire 'dsi_txc_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_rst_o' wire 'dsi_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_oe' wire 'dsi_txd0_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[7]' wire 'dsi_txd0_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[6]' wire 'dsi_txd0_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[5]' wire 'dsi_txd0_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[4]' wire 'dsi_txd0_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[3]' wire 'dsi_txd0_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[2]' wire 'dsi_txd0_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[1]' wire 'dsi_txd0_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[0]' wire 'dsi_txd0_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_oe' wire 'dsi_txd0_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_o' wire 'dsi_txd0_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_oe' wire 'dsi_txd0_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_o' wire 'dsi_txd0_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_rst_o' wire 'dsi_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_oe' wire 'dsi_txd1_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_o' wire 'dsi_txd1_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_oe' wire 'dsi_txd1_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_o' wire 'dsi_txd1_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_oe' wire 'dsi_txd1_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[7]' wire 'dsi_txd1_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[6]' wire 'dsi_txd1_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[5]' wire 'dsi_txd1_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[4]' wire 'dsi_txd1_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[3]' wire 'dsi_txd1_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[2]' wire 'dsi_txd1_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[1]' wire 'dsi_txd1_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[0]' wire 'dsi_txd1_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_rst_o' wire 'dsi_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_oe' wire 'dsi_txd2_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_o' wire 'dsi_txd2_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_oe' wire 'dsi_txd2_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_o' wire 'dsi_txd2_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_oe' wire 'dsi_txd2_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[7]' wire 'dsi_txd2_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[6]' wire 'dsi_txd2_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[5]' wire 'dsi_txd2_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[4]' wire 'dsi_txd2_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[3]' wire 'dsi_txd2_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[2]' wire 'dsi_txd2_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[1]' wire 'dsi_txd2_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[0]' wire 'dsi_txd2_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_rst_o' wire 'dsi_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_oe' wire 'dsi_txd3_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_o' wire 'dsi_txd3_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_oe' wire 'dsi_txd3_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_o' wire 'dsi_txd3_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_oe' wire 'dsi_txd3_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[7]' wire 'dsi_txd3_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[6]' wire 'dsi_txd3_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[5]' wire 'dsi_txd3_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[4]' wire 'dsi_txd3_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[3]' wire 'dsi_txd3_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[2]' wire 'dsi_txd3_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[1]' wire 'dsi_txd3_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[0]' wire 'dsi_txd3_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'uart_tx_o' wire 'uart_tx_o' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sclk' wire 'cmos_sclk' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OUT' wire 'cmos_sdat_OUT' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OE' wire 'cmos_sdat_OE' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl2' wire 'cmos_ctl2' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl3' wire 'cmos_ctl3' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_oe' wire 'lvds_txc_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[6]' wire 'lvds_txc_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[5]' wire 'lvds_txc_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[4]' wire 'lvds_txc_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[3]' wire 'lvds_txc_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[2]' wire 'lvds_txc_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[1]' wire 'lvds_txc_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[0]' wire 'lvds_txc_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_rst_o' wire 'lvds_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_oe' wire 'lvds_txd0_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[6]' wire 'lvds_txd0_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[5]' wire 'lvds_txd0_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[4]' wire 'lvds_txd0_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[3]' wire 'lvds_txd0_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[2]' wire 'lvds_txd0_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[1]' wire 'lvds_txd0_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[0]' wire 'lvds_txd0_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_rst_o' wire 'lvds_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_oe' wire 'lvds_txd1_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[6]' wire 'lvds_txd1_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[5]' wire 'lvds_txd1_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[4]' wire 'lvds_txd1_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[3]' wire 'lvds_txd1_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[2]' wire 'lvds_txd1_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[1]' wire 'lvds_txd1_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[0]' wire 'lvds_txd1_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_rst_o' wire 'lvds_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_oe' wire 'lvds_txd2_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[6]' wire 'lvds_txd2_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[5]' wire 'lvds_txd2_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[4]' wire 'lvds_txd2_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[3]' wire 'lvds_txd2_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[2]' wire 'lvds_txd2_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[1]' wire 'lvds_txd2_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[0]' wire 'lvds_txd2_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_rst_o' wire 'lvds_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_oe' wire 'lvds_txd3_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[6]' wire 'lvds_txd3_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[5]' wire 'lvds_txd3_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[4]' wire 'lvds_txd3_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[3]' wire 'lvds_txd3_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[2]' wire 'lvds_txd3_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[1]' wire 'lvds_txd3_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[0]' wire 'lvds_txd3_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_rst_o' wire 'lvds_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_o' wire 'lcd_tp_sda_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_oe' wire 'lcd_tp_sda_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_o' wire 'lcd_tp_scl_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_oe' wire 'lcd_tp_scl_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_o' wire 'lcd_tp_int_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_oe' wire 'lcd_tp_int_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_rst_o' wire 'lcd_tp_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_pwm_o' wire 'lcd_pwm_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_blen_o' wire 'lcd_blen_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_vs_o' wire 'lcd_vs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_hs_o' wire 'lcd_hs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_de_o' wire 'lcd_de_o' is not driven.
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][31]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][30]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][29]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][28]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:3223)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wclk=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[9]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[10]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[11]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[12]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[13]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[14]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[15]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[16]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[17]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[18]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[19]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[20]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[21]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[22]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[23]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[24]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[25]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[26]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[27]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[28]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[29]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[30]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[31]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[32]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[33]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[34]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[35]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[36]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[37]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[38]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[39]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[40]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[41]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[42]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[43]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[44]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[45]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[46]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[47]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[48]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[49]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[50]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[51]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[52]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[53]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[54]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[55]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[56]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[57]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[58]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[59]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[60]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[61]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[62]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[63]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[64]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[65]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[66]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[67]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[68]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[69]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[70]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[71]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (we=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (re=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_push_payload[0]=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.a_rst_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.clk_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'fifo_ack' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_ar_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_aw_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'mux0' input pin tied to constant (b_rd_en=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[0]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[1]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[2]=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[3]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[2]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[3]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[5]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[6]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[7]=0).
[EFX-0266 WARNING] Module Instance 'u_wr_addr_fifo' input pin tied to constant (I_Wr_Data[42]=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[127]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[126]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[125]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[124]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[123]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[122]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\csi_rx\csi_rx.sv:6919)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\csi_rx\csi_rx.sv:6919)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\W0_FIFO_64\W0_FIFO_64.v:721)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\R0_FIFO_8\R0_FIFO_8.v:719)
[EFX-0000 INFO] ... Pre-synthesis checks end (Real time : 0s)
[EFX-0000 INFO] ... NameSpace init begin
[EFX-0000 INFO] ... NameSpace init end (Real time : 0s)
[EFX-0000 INFO] ... Mapping design "example_top"
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_DEPTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_DEPTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=48,DATA_DEPTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=48,DATA_DEPTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "native_mux_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "native_mux_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=4,CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=4,CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_1_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_1_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=144)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=144)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33_renamed_due_excessive_length_1" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33_renamed_due_excessive_length_1" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=52)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=52)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "user_dual_port_ram_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=72,OUTPUT_REG="FALSE",RAM_INIT_FILE="ddr3_controller.bin")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "user_dual_port_ram_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=72,OUTPUT_REG="FALSE",RAM_INIT_FILE="ddr3_controller.bin")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "ddr_3_controller_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "ddr_3_controller_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "controller_top_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "controller_top_5ee7df9aa375494db25875c4929bcb33" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=2,CYCLE=5,IN_RST_ACTIVE=2'b0,OUT_RST_ACTIVE=2'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=2,CYCLE=5,IN_RST_ACTIVE=2'b0,OUT_RST_ACTIVE=2'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "phase_shift_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "phase_shift_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "top_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "top_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_axi_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_axi_5ee7df9aa375494db25875c4929bcb33" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "DdrCtrl" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "DdrCtrl" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "AXI4_AWARMux(AID_LEN=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "AXI4_AWARMux(AID_LEN=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "I2C_SC130GS_12801024_4Lanes_Config" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "I2C_SC130GS_12801024_4Lanes_Config" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "PWMLite" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "PWMLite" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_clane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_clane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dlane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dlane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dword_align_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dword_align_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_deskew_cal_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_deskew_cal_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_csr_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_csr_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_ecc_check_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_ecc_check_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_crc16_d8_gen_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_crc16_d8_gen_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depacketizer_e7fb069599c849bca10d5e09e6d98480(ENABLE_VCX=1'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depacketizer_e7fb069599c849bca10d5e09e6d98480(ENABLE_VCX=1'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_3" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_3" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_lane_aligner_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_lane_aligner_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack40bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack40bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack48bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack48bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack56bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack56bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack64bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack64bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_5" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_5" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_4" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_4" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_byte2pixel_e7fb069599c849bca10d5e09e6d98480(PIXEL_FIFO_DEPTH=1024,ENABLE_VCX=1'b0,PACK_TYPE=15)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_byte2pixel_e7fb069599c849bca10d5e09e6d98480(PIXEL_FIFO_DEPTH=1024,ENABLE_VCX=1'b0,PACK_TYPE=15)" end (Real time : 2s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_top_e7fb069599c849bca10d5e09e6d98480(HS_DATA_WIDTH=8,tINIT_NS=1000,CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,PACK_TYPE=15,ENABLE_VCX=1'b0,PIXEL_FIFO_DEPTH=1024)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_top_e7fb069599c849bca10d5e09e6d98480(HS_DATA_WIDTH=8,tINIT_NS=1000,CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,PACK_TYPE=15,ENABLE_VCX=1'b0,PIXEL_FIFO_DEPTH=1024)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_2" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_2" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "csi_rx" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "csi_rx" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Sensor_Image_XYCrop(IMAGE_HSIZE_SOURCE=160,IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=160,IMAGE_YSIZE_TARGET=720)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Sensor_Image_XYCrop(IMAGE_HSIZE_SOURCE=160,IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=160,IMAGE_YSIZE_TARGET=720)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_26a5ebe291f84e1e832c846719c7bd6f(MODE="FWFT",WR_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=128,WADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_26a5ebe291f84e1e832c846719c7bd6f(MODE="FWFT",WR_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=128,WADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_26a5ebe291f84e1e832c846719c7bd6f(STAGE=3,WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_26a5ebe291f84e1e832c846719c7bd6f(STAGE=3,WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_26a5ebe291f84e1e832c846719c7bd6f(STAGE=3,WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_26a5ebe291f84e1e832c846719c7bd6f(STAGE=3,WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_26a5ebe291f84e1e832c846719c7bd6f(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_26a5ebe291f84e1e832c846719c7bd6f_renamed_due_excessive_length_7" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_26a5ebe291f84e1e832c846719c7bd6f_renamed_due_excessive_length_7" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_26a5ebe291f84e1e832c846719c7bd6f_renamed_due_excessive_length_6" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_26a5ebe291f84e1e832c846719c7bd6f_renamed_due_excessive_length_6" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "W0_FIFO_64" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "W0_FIFO_64" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_8ff540a3c0cf4ec6ad5f65a53419c763(FAMILY="TITANIUM",MODE="FWFT",RD_DEPTH=8192,WDATA_WIDTH=128,RADDR_WIDTH=13,OUTPUT_REG=0,RAM_MUX_RATIO=16)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_8ff540a3c0cf4ec6ad5f65a53419c763(FAMILY="TITANIUM",MODE="FWFT",RD_DEPTH=8192,WDATA_WIDTH=128,RADDR_WIDTH=13,OUTPUT_REG=0,RAM_MUX_RATIO=16)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=14)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=14)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=12)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=12)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=13)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=13)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_9" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_9" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_8" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_8" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "R0_FIFO_8" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "R0_FIFO_8" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=921600)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=921600)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "lcd_driver" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "lcd_driver" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "tmds_channel" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "tmds_channel" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "rgb2dvi(ENABLE_OSERDES=0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "rgb2dvi(ENABLE_OSERDES=0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "example_top" begin
[EFX-0000 INFO] ... Clock Enable Synthesis Performed on 1120 flops.
[EFX-0000 INFO] ... Hierarchical pre-synthesis "example_top" end (Real time : 12s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Flat synthesis begin
[EFX-0000 INFO] ... Flat synthesis end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 1s)
[EFX-0000 INFO] ... Check and break combinational loops begin
[EFX-0000 INFO] ... Check and break combinational loops end (Real time : 0s)
[EFX-0000 INFO] ... SOP modeling begin
[EFX-0000 INFO] ... SOP modeling end (Real time : 0s)
[EFX-0000 INFO] ... LUT mapping begin

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:36:21
///////////////////////////////////

[EFX-0012 VERI-INFO] default VHDL library search path is now "C:/Efinity/2023.2/sim_models/vhdl/packages/vhdl_2008" (VHDL-1504) 
[EFX-0012 VERI-INFO] undeclared symbol 'la_capture_mask', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4391)
[EFX-0012 VERI-INFO] undeclared symbol 'next_segment_pointer_eq', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4749)
[EFX-0012 VERI-INFO] undeclared symbol 'next_segment_msb_xor', assumed default net type 'wire' (VERI-2561) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4750)
[EFX-0011 VERI-WARNING] port 'probe1' remains unconnected for this instance (VERI-1927) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:108)
[EFX-0012 VERI-INFO] compiling module 'edb_top' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:10)
[EFX-0012 VERI-INFO] compiling module 'EFX_GBUFCE(CE_POLARITY=1'b1)' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:48)
[EFX-0012 VERI-INFO] compiling module 'edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b10010000010011110101101010111010110001000101111001000000010001101001011001000100101011001000101001111111010010111110000111010000,PROBE0_WIDTH=8,PROBE0_TYPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:481)
[EFX-0012 VERI-INFO] compiling module 'edb_adbg_crc32' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:182)
[EFX-0012 VERI-INFO] compiling module 'compare_unit(WIDTH=11'b01000,PIPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5472)
[EFX-0012 VERI-INFO] compiling module 'trigger_unit(WIDTH=32'b01,PIPE=1)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5677)
[EFX-0012 VERI-INFO] compiling module 'la_biu(CAPTURE_WIDTH=32'b01000)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4826)
[EFX-0012 VERI-INFO] compiling module 'fifo_with_read(DATA_WIDTH=32'b01001)' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4568)
[EFX-0012 VERI-INFO] compiling module 'fifo_address_trancode_unit' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4518)
[EFX-0012 VERI-INFO] compiling module 'edb_simple_dual_port_ram(DATA_WIDTH=32'b01001,ADDR_WIDTH=10,RAM_INIT_FILE="")' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:391)
[EFX-0012 VERI-INFO] extracting RAM for identifier 'ram' (VERI-2571) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:406)
[EFX-0012 VERI-INFO] compiling module 'debug_hub' (VERI-1018) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:260)
[EFX-0011 VERI-WARNING] input port 'probe1[0]' remains unconnected for this instance (VDB-1053) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0000 INFO] ... Setting Synthesis Option: mode=speed
[EFX-0012 VERI-INFO] compiling module 'EFX_ADD' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:8)
[EFX-0012 VERI-INFO] compiling module 'EFX_FF' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:21)
[EFX-0012 VERI-INFO] compiling module 'EFX_COMB4' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:38)
[EFX-0012 VERI-INFO] compiling module 'EFX_GBUFCE' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:48)
[EFX-0012 VERI-INFO] compiling module 'EFX_LUT4' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:57)
[EFX-0012 VERI-INFO] compiling module 'EFX_MULT' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:65)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP48' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:100)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP24' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:156)
[EFX-0012 VERI-INFO] compiling module 'EFX_DSP12' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:209)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_4K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:262)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_5K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:322)
[EFX-0012 VERI-INFO] compiling module 'EFX_DPRAM_5K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:394)
[EFX-0012 VERI-INFO] compiling module 'RAMB5' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:499)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM_10K' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:561)
[EFX-0012 VERI-INFO] compiling module 'EFX_RAM10' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:653)
[EFX-0012 VERI-INFO] compiling module 'EFX_DPRAM10' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:754)
[EFX-0012 VERI-INFO] compiling module 'EFX_SRL8' (VERI-1018) (C:/Efinity/2023.2/sim_models/maplib/efinix_maplib.v:884)
[EFX-0000 INFO] ... Pre-synthesis checks begin
[EFX-0200 WARNING] Removing redundant signal : trigger_in. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5689)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:406)
[EFX-0200 WARNING] Removing redundant signal : din[8]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4581)
[EFX-0200 WARNING] Removing redundant signal : trig_out. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4860)
[EFX-0200 WARNING] Removing redundant signal : trig_out_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4861)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1014)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1016)
[EFX-0200 WARNING] Removing redundant signal : bscan_SEL. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1017)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1021)
[EFX-0200 WARNING] Removing redundant signal : trig_in_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1032)
[EFX-0200 WARNING] Removing redundant signal : mux_capture_cmp[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:3444)
[EFX-0200 WARNING] Removing redundant signal : bscan_CAPTURE. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:265)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:266)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:268)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:273)
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_in=0).
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_out_ack=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe1[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe2[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe3[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe4[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe5[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe6[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe7[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe8[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe9[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe10[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0000 INFO] ... Pre-synthesis checks end (Real time : 0s)
[EFX-0000 INFO] ... NameSpace init begin
[EFX-0000 INFO] ... NameSpace init end (Real time : 0s)
[EFX-0000 INFO] ... Mapping design "edb_top"
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_adbg_crc32" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_adbg_crc32" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "compare_unit(WIDTH=11'b01000,PIPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "compare_unit(WIDTH=11'b01000,PIPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "trigger_unit(WIDTH=32'b01,PIPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "trigger_unit(WIDTH=32'b01,PIPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_address_trancode_unit" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_address_trancode_unit" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_simple_dual_port_ram(DATA_WIDTH=32'b01001,ADDR_WIDTH=10,RAM_INIT_FILE="")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_simple_dual_port_ram(DATA_WIDTH=32'b01001,ADDR_WIDTH=10,RAM_INIT_FILE="")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_with_read(DATA_WIDTH=32'b01001)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "fifo_with_read(DATA_WIDTH=32'b01001)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "la_biu(CAPTURE_WIDTH=32'b01000)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "la_biu(CAPTURE_WIDTH=32'b01000)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b10010000010011110101101010111010110001000101111001000000010001101001011001000100101011001000101001111111010010111110000111010000,PROBE0_WIDTH=8,PROBE0_TYPE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_la_top(INPUT_PIPE_STAGES=1,UUID=128'b10010000010011110101101010111010110001000101111001000000010001101001011001000100101011001000101001111111010010111110000111010000,PROBE0_WIDTH=8,PROBE0_TYPE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "debug_hub" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "debug_hub" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" begin
[EFX-0000 INFO] ... Clock Enable Synthesis Performed on 130 flops.
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Flat synthesis begin
[EFX-0000 INFO] ... Flat synthesis end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Check and break combinational loops begin
[EFX-0000 INFO] ... Check and break combinational loops end (Real time : 0s)
[EFX-0000 INFO] ... SOP modeling begin
[EFX-0000 INFO] ... SOP modeling end (Real time : 0s)
[EFX-0000 INFO] ... LUT mapping begin
[EFX-0000 INFO] ... LS, strategy: 3, nd: 850, ed: 2884, lv: 7, pw: 1445.14
[EFX-0000 INFO] ... LUT mapping end (Real time : 4s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation end (Real time : 0s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing end (Real time : 0s)
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_DRCK is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_RUNTEST is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_TMS is unconnected and will be removed 
[EFX-0012 VERI-INFO] Found 3 warnings in the post-synthesis netlist. 
[EFX-0000 INFO] Resource Summary 
[EFX-0000 INFO] =============================== 
[EFX-0000 INFO] EFX_ADD         : 	86
[EFX-0000 INFO] EFX_LUT4        : 	843
[EFX-0000 INFO] EFX_FF          : 	715
[EFX-0000 INFO] EFX_SRL8        : 	8
[EFX-0000 INFO] EFX_RAM10       : 	1
[EFX-0000 INFO] EFX_GBUFCE      : 	1
[EFX-0000 INFO] =============================== 

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:36:32
///////////////////////////////////

[EFX-0000 INFO] ... Setting Synthesis Option: mode=speed
[EFX-0000 INFO] ... Pre-synthesis checks begin
[EFX-0256 WARNING] The primary output port 'csi_ctl0_o' wire 'csi_ctl0_o' is not driven.
[EFX-0256 WARNING] The primary output port 'csi_ctl1_o' wire 'csi_ctl1_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_rst_o' wire 'dsi_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_oe' wire 'dsi_txc_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_o' wire 'dsi_txc_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_oe' wire 'dsi_txc_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_o' wire 'dsi_txc_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_oe' wire 'dsi_txc_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[7]' wire 'dsi_txc_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[6]' wire 'dsi_txc_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[5]' wire 'dsi_txc_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[4]' wire 'dsi_txc_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[3]' wire 'dsi_txc_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[2]' wire 'dsi_txc_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[1]' wire 'dsi_txc_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[0]' wire 'dsi_txc_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_rst_o' wire 'dsi_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_oe' wire 'dsi_txd0_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[7]' wire 'dsi_txd0_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[6]' wire 'dsi_txd0_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[5]' wire 'dsi_txd0_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[4]' wire 'dsi_txd0_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[3]' wire 'dsi_txd0_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[2]' wire 'dsi_txd0_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[1]' wire 'dsi_txd0_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[0]' wire 'dsi_txd0_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_oe' wire 'dsi_txd0_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_o' wire 'dsi_txd0_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_oe' wire 'dsi_txd0_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_o' wire 'dsi_txd0_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_rst_o' wire 'dsi_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_oe' wire 'dsi_txd1_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_o' wire 'dsi_txd1_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_oe' wire 'dsi_txd1_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_o' wire 'dsi_txd1_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_oe' wire 'dsi_txd1_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[7]' wire 'dsi_txd1_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[6]' wire 'dsi_txd1_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[5]' wire 'dsi_txd1_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[4]' wire 'dsi_txd1_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[3]' wire 'dsi_txd1_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[2]' wire 'dsi_txd1_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[1]' wire 'dsi_txd1_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[0]' wire 'dsi_txd1_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_rst_o' wire 'dsi_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_oe' wire 'dsi_txd2_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_o' wire 'dsi_txd2_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_oe' wire 'dsi_txd2_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_o' wire 'dsi_txd2_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_oe' wire 'dsi_txd2_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[7]' wire 'dsi_txd2_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[6]' wire 'dsi_txd2_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[5]' wire 'dsi_txd2_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[4]' wire 'dsi_txd2_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[3]' wire 'dsi_txd2_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[2]' wire 'dsi_txd2_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[1]' wire 'dsi_txd2_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[0]' wire 'dsi_txd2_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_rst_o' wire 'dsi_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_oe' wire 'dsi_txd3_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_o' wire 'dsi_txd3_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_oe' wire 'dsi_txd3_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_o' wire 'dsi_txd3_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_oe' wire 'dsi_txd3_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[7]' wire 'dsi_txd3_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[6]' wire 'dsi_txd3_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[5]' wire 'dsi_txd3_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[4]' wire 'dsi_txd3_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[3]' wire 'dsi_txd3_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[2]' wire 'dsi_txd3_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[1]' wire 'dsi_txd3_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[0]' wire 'dsi_txd3_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'uart_tx_o' wire 'uart_tx_o' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sclk' wire 'cmos_sclk' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OUT' wire 'cmos_sdat_OUT' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OE' wire 'cmos_sdat_OE' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl2' wire 'cmos_ctl2' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl3' wire 'cmos_ctl3' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_oe' wire 'lvds_txc_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[6]' wire 'lvds_txc_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[5]' wire 'lvds_txc_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[4]' wire 'lvds_txc_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[3]' wire 'lvds_txc_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[2]' wire 'lvds_txc_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[1]' wire 'lvds_txc_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[0]' wire 'lvds_txc_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_rst_o' wire 'lvds_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_oe' wire 'lvds_txd0_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[6]' wire 'lvds_txd0_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[5]' wire 'lvds_txd0_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[4]' wire 'lvds_txd0_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[3]' wire 'lvds_txd0_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[2]' wire 'lvds_txd0_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[1]' wire 'lvds_txd0_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[0]' wire 'lvds_txd0_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_rst_o' wire 'lvds_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_oe' wire 'lvds_txd1_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[6]' wire 'lvds_txd1_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[5]' wire 'lvds_txd1_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[4]' wire 'lvds_txd1_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[3]' wire 'lvds_txd1_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[2]' wire 'lvds_txd1_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[1]' wire 'lvds_txd1_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[0]' wire 'lvds_txd1_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_rst_o' wire 'lvds_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_oe' wire 'lvds_txd2_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[6]' wire 'lvds_txd2_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[5]' wire 'lvds_txd2_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[4]' wire 'lvds_txd2_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[3]' wire 'lvds_txd2_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[2]' wire 'lvds_txd2_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[1]' wire 'lvds_txd2_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[0]' wire 'lvds_txd2_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_rst_o' wire 'lvds_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_oe' wire 'lvds_txd3_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[6]' wire 'lvds_txd3_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[5]' wire 'lvds_txd3_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[4]' wire 'lvds_txd3_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[3]' wire 'lvds_txd3_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[2]' wire 'lvds_txd3_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[1]' wire 'lvds_txd3_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[0]' wire 'lvds_txd3_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_rst_o' wire 'lvds_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_o' wire 'lcd_tp_sda_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_oe' wire 'lcd_tp_sda_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_o' wire 'lcd_tp_scl_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_oe' wire 'lcd_tp_scl_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_o' wire 'lcd_tp_int_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_oe' wire 'lcd_tp_int_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_rst_o' wire 'lcd_tp_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_pwm_o' wire 'lcd_pwm_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_blen_o' wire 'lcd_blen_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_vs_o' wire 'lcd_vs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_hs_o' wire 'lcd_hs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_de_o' wire 'lcd_de_o' is not driven.
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][31]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][30]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][29]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][28]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:3223)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wclk=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[9]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[10]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[11]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[12]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[13]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[14]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[15]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[16]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[17]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[18]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[19]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[20]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[21]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[22]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[23]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[24]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[25]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[26]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[27]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[28]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[29]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[30]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[31]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[32]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[33]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[34]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[35]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[36]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[37]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[38]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[39]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[40]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[41]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[42]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[43]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[44]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[45]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[46]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[47]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[48]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[49]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[50]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[51]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[52]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[53]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[54]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[55]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[56]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[57]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[58]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[59]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[60]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[61]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[62]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[63]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[64]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[65]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[66]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[67]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[68]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[69]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[70]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[71]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (we=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (re=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_push_payload[0]=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.a_rst_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.clk_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'fifo_ack' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_ar_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_aw_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'mux0' input pin tied to constant (b_rd_en=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[0]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[1]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[2]=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[3]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[2]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[3]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[5]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[6]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[7]=0).
[EFX-0266 WARNING] Module Instance 'u_wr_addr_fifo' input pin tied to constant (I_Wr_Data[42]=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[127]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[126]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[125]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[124]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[123]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[122]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\csi_rx\csi_rx.sv:6919)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\csi_rx\csi_rx.sv:6919)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\W0_FIFO_32\W0_FIFO_32.v:709)
[EFX-0677 INFO] ... Zero initialization of uninitialized memory block 'ram'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\R0_FIFO_8\R0_FIFO_8.v:719)
[EFX-0000 INFO] ... Pre-synthesis checks end (Real time : 0s)
[EFX-0000 INFO] ... NameSpace init begin
[EFX-0000 INFO] ... NameSpace init end (Real time : 0s)
[EFX-0000 INFO] ... Mapping design "example_top"
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=8,DATA_DEPTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_DEPTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_DEPTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=48,DATA_DEPTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Ddr_Ctrl_Sc_Fifo_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=48,DATA_DEPTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "native_mux_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "native_mux_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=4,CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=4,CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(CYCLE=4,IN_RST_ACTIVE=4'b0,OUT_RST_ACTIVE=4'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_1_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "buffercc_1_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=144)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=144)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_5ee7df9aa375494db25875c4929bcb33(WDATA_WIDTH=128,RDATA_WIDTH=128,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_5ee7df9aa375494db25875c4929bcb33(STAGE=2,WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_5ee7df9aa375494db25875c4929bcb33(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33_renamed_due_excessive_length_1" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_5ee7df9aa375494db25875c4929bcb33_renamed_due_excessive_length_1" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=128)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=52)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "streamfifocc64_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=52)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "user_dual_port_ram_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=72,OUTPUT_REG="FALSE",RAM_INIT_FILE="ddr3_controller.bin")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "user_dual_port_ram_5ee7df9aa375494db25875c4929bcb33(DATA_WIDTH=72,OUTPUT_REG="FALSE",RAM_INIT_FILE="ddr3_controller.bin")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "ddr_3_controller_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "ddr_3_controller_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "controller_top_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "controller_top_5ee7df9aa375494db25875c4929bcb33" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_sub_5ee7df9aa375494db25875c4929bcb33(OUT_RST_ACTIVE="LOW",CYCLE=5)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=2,CYCLE=5,IN_RST_ACTIVE=2'b0,OUT_RST_ACTIVE=2'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "reset_ctrl_5ee7df9aa375494db25875c4929bcb33(NUM_RST=2,CYCLE=5,IN_RST_ACTIVE=2'b0,OUT_RST_ACTIVE=2'b0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "phase_shift_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "phase_shift_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "top_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "top_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_axi_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_axi_5ee7df9aa375494db25875c4929bcb33" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_ddr3_soft_controller_5ee7df9aa375494db25875c4929bcb33" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "DdrCtrl" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "DdrCtrl" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "AXI4_AWARMux(AID_LEN=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "AXI4_AWARMux(AID_LEN=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "i2c_timing_ctrl_16bit(CLK_FREQ=96000000,I2C_FREQ=50000)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "I2C_SC130GS_12801024_4Lanes_Config" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "I2C_SC130GS_12801024_4Lanes_Config" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "PWMLite" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "PWMLite" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_clane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_clane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dlane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dlane_fsm_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dword_align_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_dword_align_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_deskew_cal_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "dphy_rx_deskew_cal_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_dphy_rx_e7fb069599c849bca10d5e09e6d98480(CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,ENABLE_USER_DESKEWCAL=1'b0,DPHY_CLOCK_MODE="Discontinuous")" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_csr_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_csr_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_ecc_check_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_ecc_check_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_crc16_d8_gen_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_crc16_d8_gen_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depacketizer_e7fb069599c849bca10d5e09e6d98480(ENABLE_VCX=1'b0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depacketizer_e7fb069599c849bca10d5e09e6d98480(ENABLE_VCX=1'b0)" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,RD_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=16,WADDR_WIDTH=4,RADDR_WIDTH=4,ALMOST_FLAG=0,PROG_FULL_ASSERT=16,PROG_FULL_NEGATE=16,HANDSHAKE_FLAG=0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_3" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_3" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_lane_aligner_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_lane_aligner_e7fb069599c849bca10d5e09e6d98480(NUM_DATA_LANE=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_asyncreg_e7fb069599c849bca10d5e09e6d98480(WIDTH=16)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack40bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack40bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack48bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack48bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack56bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack56bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack64bit_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_depack64bit_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_e7fb069599c849bca10d5e09e6d98480(MODE="FWFT",WR_DEPTH=1024,RD_DEPTH=1024,WDATA_WIDTH=64,RDATA_WIDTH=64,WADDR_WIDTH=10,RADDR_WIDTH=10,OUTPUT_REG=0,RAM_MUX_RATIO=1)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_e7fb069599c849bca10d5e09e6d98480(STAGE=2,WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_e7fb069599c849bca10d5e09e6d98480(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_5" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_5" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_4" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_4" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_byte2pixel_e7fb069599c849bca10d5e09e6d98480(PIXEL_FIFO_DEPTH=1024,ENABLE_VCX=1'b0,PACK_TYPE=15)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_byte2pixel_e7fb069599c849bca10d5e09e6d98480(PIXEL_FIFO_DEPTH=1024,ENABLE_VCX=1'b0,PACK_TYPE=15)" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_top_e7fb069599c849bca10d5e09e6d98480(HS_DATA_WIDTH=8,tINIT_NS=1000,CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,PACK_TYPE=15,ENABLE_VCX=1'b0,PIXEL_FIFO_DEPTH=1024)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_top_e7fb069599c849bca10d5e09e6d98480(HS_DATA_WIDTH=8,tINIT_NS=1000,CLOCK_FREQ_MHZ=50,NUM_DATA_LANE=1,PACK_TYPE=15,ENABLE_VCX=1'b0,PIXEL_FIFO_DEPTH=1024)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_2" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_csi2_rx_e7fb069599c849bca10d5e09e6d98480_renamed_due_excessive_length_2" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "csi_rx" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "csi_rx" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Sensor_Image_XYCrop(IMAGE_HSIZE_SOURCE=160,IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=160,IMAGE_YSIZE_TARGET=720)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "Sensor_Image_XYCrop(IMAGE_HSIZE_SOURCE=160,IMAGE_VSIZE_SOURCE=960,IMAGE_HSIZE_TARGET=160,IMAGE_YSIZE_TARGET=720)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_9133b310c90946e99246ad457ba54d5f(MODE="FWFT",WR_DEPTH=2048,WDATA_WIDTH=32,RDATA_WIDTH=128,WADDR_WIDTH=11,OUTPUT_REG=0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_9133b310c90946e99246ad457ba54d5f(MODE="FWFT",WR_DEPTH=2048,WDATA_WIDTH=32,RDATA_WIDTH=128,WADDR_WIDTH=11,OUTPUT_REG=0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=12)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_9133b310c90946e99246ad457ba54d5f(WIDTH=12)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=12)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_9133b310c90946e99246ad457ba54d5f(STAGE=3,WIDTH=12)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=12)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_9133b310c90946e99246ad457ba54d5f(WIDTH=12)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_9133b310c90946e99246ad457ba54d5f_renamed_due_excessive_length_7" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_9133b310c90946e99246ad457ba54d5f_renamed_due_excessive_length_7" end (Real time : 1s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_9133b310c90946e99246ad457ba54d5f_renamed_due_excessive_length_6" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_9133b310c90946e99246ad457ba54d5f_renamed_due_excessive_length_6" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "W0_FIFO_32" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "W0_FIFO_32" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_8ff540a3c0cf4ec6ad5f65a53419c763(FAMILY="TITANIUM",MODE="FWFT",RD_DEPTH=8192,WDATA_WIDTH=128,RADDR_WIDTH=13,OUTPUT_REG=0,RAM_MUX_RATIO=16)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ram_8ff540a3c0cf4ec6ad5f65a53419c763(FAMILY="TITANIUM",MODE="FWFT",RD_DEPTH=8192,WDATA_WIDTH=128,RADDR_WIDTH=13,OUTPUT_REG=0,RAM_MUX_RATIO=16)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=14)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=14)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=2)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=2)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=3)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=3)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=4)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=4)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=6)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=6)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=7)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=7)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=8)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=8)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=9)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=9)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=11)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=11)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=12)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=12)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=13)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=13)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_gray2bin_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=14)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_bin2gray_8ff540a3c0cf4ec6ad5f65a53419c763(WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=10)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_datasync_8ff540a3c0cf4ec6ad5f65a53419c763(STAGE=3,WIDTH=10)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_9" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_ctl_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_9" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_8" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "efx_fifo_top_8ff540a3c0cf4ec6ad5f65a53419c763_renamed_due_excessive_length_8" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "R0_FIFO_8" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "R0_FIFO_8" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=921600,C_W_WIDTH=32)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "axi4_ctrl(C_ID_LEN=4,C_RD_END_ADDR=921600,C_W_WIDTH=32)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "lcd_driver" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "lcd_driver" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "tmds_channel" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "tmds_channel" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "rgb2dvi(ENABLE_OSERDES=0)" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "rgb2dvi(ENABLE_OSERDES=0)" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" begin
[EFX-0000 INFO] ... Hierarchical pre-synthesis "edb_top" end (Real time : 0s)
[EFX-0000 INFO] ... Hierarchical pre-synthesis "example_top" begin
[EFX-0000 INFO] ... Clock Enable Synthesis Performed on 1120 flops.
[EFX-0000 INFO] ... Hierarchical pre-synthesis "example_top" end (Real time : 9s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Flat synthesis begin
[EFX-0000 INFO] ... Flat synthesis end (Real time : 0s)
[EFX-0000 INFO] ... Flat optimizations begin
[EFX-0000 INFO] ... Flat optimizations end (Real time : 0s)
[EFX-0000 INFO] ... Check and break combinational loops begin
[EFX-0000 INFO] ... Check and break combinational loops end (Real time : 0s)
[EFX-0000 INFO] ... SOP modeling begin
[EFX-0000 INFO] ... SOP modeling end (Real time : 0s)
[EFX-0000 INFO] ... LUT mapping begin
[EFX-0000 INFO] ... LS, strategy: 3, nd: 5085, ed: 16492, lv: 9, pw: 13506.16
[EFX-0000 INFO] ... LUT mapping end (Real time : 19s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist creation end (Real time : 0s)
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing begin
[EFX-0000 INFO] ... Post-synthesis Verific netlist unification/params processing end (Real time : 0s)
[EFX-0000 INFO] Resource Summary 
[EFX-0000 INFO] =============================== 
[EFX-0000 INFO] EFX_ADD         : 	950
[EFX-0000 INFO] EFX_LUT4        : 	5638
[EFX-0000 INFO] EFX_FF          : 	4313
[EFX-0000 INFO] EFX_SRL8        : 	586
[EFX-0000 INFO] EFX_RAM10       : 	52
[EFX-0000 INFO] EFX_GBUFCE      : 	1
[EFX-0000 INFO] =============================== 
