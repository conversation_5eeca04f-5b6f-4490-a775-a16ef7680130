
Efinity Interface Designer Timing Report
Version: 2025.1.110.2.15
Date: 2025-07-22 18:24

Copyright (C) 2013 - 2025 Efinix Inc. All rights reserved.

Device: Ti60F225
Project: Ti60_Demo
Timing Model: C4 (final)

---------- 1. PLL Timing Report (begin) ----------

+--------------+----------+-----------------+--------------------------+---------+-----------------------+---------------------------------+---------------------------------+
| PLL Instance | Resource | Reference Clock | Core Clock Reference Pin | FB Mode |   Core Feedback Pin   | PLL Compensation Delay Max (ns) | PLL Compensation Delay Min (ns) |
+--------------+----------+-----------------+--------------------------+---------+-----------------------+---------------------------------+---------------------------------+
|   dsi_pll    | PLL_BL0  |       core      |   clk_sys~CLKOUT~18~1    |  local  |                       |              0.000              |              0.000              |
|   ddr_pll    | PLL_BR0  |       core      |   clk_sys~CLKOUT~202~1   |   core  | core_clk~CLKOUT~203~1 |              1.778              |              1.150              |
|   sys_pll    | PLL_TL0  |     external    |                          |   core  |  clk_sys~CLKOUT~1~304 |              1.881              |              1.217              |
|   lvds_pll   | PLL_TR0  |       core      |  clk_sys~CLKOUT~200~322  |  local  |                       |              0.000              |              0.000              |
+--------------+----------+-----------------+--------------------------+---------+-----------------------+---------------------------------+---------------------------------+

+---------------+-------------+----------------------------+-----------------------+----------+
|     Clock     | Period (ns) | Enable Dynamic Phase Shift | Phase Shift (degrees) | Inverted |
+---------------+-------------+----------------------------+-----------------------+----------+
|  dsi_refclk_i |    20.833   |           False            |          0.0          |  false   |
| dsi_byteclk_i |    23.810   |           False            |          0.0          |  false   |
|  dsi_txcclk_i |    5.952    |           False            |         135.0         |  false   |
|  dsi_serclk_i |    5.952    |           False            |          45.0         |  false   |
|   tdqss_clk   |    2.604    |           False            |          0.0          |  false   |
|    core_clk   |    5.208    |           False            |          0.0          |  false   |
|    tac_clk    |    2.604    |            True            |          0.0          |  false   |
|    twd_clk    |    2.604    |           False            |          90.0         |  false   |
|    clk_sys    |    10.417   |           False            |          0.0          |  false   |
|   clk_pixel   |    13.441   |           False            |          0.0          |  false   |
|  clk_pixel_2x |    6.720    |           False            |          0.0          |  false   |
| clk_pixel_10x |    1.344    |           False            |          90.0         |  false   |
|  clk_lvds_1x  |    20.833   |           False            |          0.0          |  false   |
|  clk_lvds_7x  |    2.976    |           False            |          90.0         |  false   |
|    clk_27m    |    62.500   |           False            |          0.0          |  false   |
|    clk_54m    |    31.250   |           False            |          0.0          |  false   |
+---------------+-------------+----------------------------+-----------------------+----------+

---------- PLL Timing Report (end) ----------

---------- 2. GPIO Timing Report (begin) ----------

Clkout GPIO Configuration:
===========================

+---------------+-----------+--------------+----------+----------+------------------------+
| Instance Name | Clock Pin |  Parameter   | Max (ns) | Min (ns) |   Reference Pin Name   |
+---------------+-----------+--------------+----------+----------+------------------------+
|   cmos_xclk   |  clk_27m  | GPIO_CLK_OUT |  3.740   |  2.493   | clk_27m~CLKOUT~209~322 |
+---------------+-----------+--------------+----------+----------+------------------------+

Non-registered GPIO Configuration:
===================================

+---------------+---------------+-----------+----------+----------+
| Instance Name |    Pin Name   | Parameter | Max (ns) | Min (ns) |
+---------------+---------------+-----------+----------+----------+
|    clk_24m    |    clk_24m    |  GPIO_IN  |  1.907   |  1.271   |
|    clk_25m    |    clk_25m    |  GPIO_IN  |  1.907   |  1.271   |
|   cmos_ctl1   |   cmos_ctl1   |  GPIO_IN  |  1.907   |  1.271   |
|   cmos_ctl2   |   cmos_ctl2   |  GPIO_OUT |  5.740   |  2.493   |
|   cmos_sclk   |   cmos_sclk   |  GPIO_OUT |  5.740   |  2.493   |
|    led_o[0]   |    led_o[0]   |  GPIO_OUT |  5.740   |  2.493   |
|    led_o[1]   |    led_o[1]   |  GPIO_OUT |  5.740   |  2.493   |
|    led_o[2]   |    led_o[2]   |  GPIO_OUT |  5.740   |  2.493   |
|    led_o[3]   |    led_o[3]   |  GPIO_OUT |  5.740   |  2.493   |
|    led_o[4]   |    led_o[4]   |  GPIO_OUT |  5.740   |  2.493   |
|    led_o[5]   |    led_o[5]   |  GPIO_OUT |  5.740   |  2.493   |
|   cmos_sdat   |  cmos_sdat_IN |  GPIO_IN  |  1.907   |  1.271   |
|   cmos_sdat   | cmos_sdat_OUT |  GPIO_OUT |  5.740   |  2.493   |
|   cmos_sdat   |  cmos_sdat_OE |  GPIO_OUT |  3.984   |  2.656   |
+---------------+---------------+-----------+----------+----------+

Registered GPIO Configuration:
===============================

+---------------+------------+----------------+----------------+---------------+---------------+-----------------------+-----------------------+
| Instance Name | Clock Pin  | Max Setup (ns) | Min Setup (ns) | Max Hold (ns) | Min Hold (ns) | Max Clock To Out (ns) | Min Clock To Out (ns) |
+---------------+------------+----------------+----------------+---------------+---------------+-----------------------+-----------------------+
|  cmos_data[0] | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
|  cmos_data[1] | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
|  cmos_data[2] | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
|  cmos_data[3] | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
|  cmos_data[4] | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
|  cmos_data[5] | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
|  cmos_data[6] | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
|  cmos_data[7] | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
|   cmos_href   | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
|   cmos_vsync  | ~cmos_pclk |     1.431      |     0.954      |     -0.363    |     -0.242    |                       |                       |
+---------------+------------+----------------+----------------+---------------+---------------+-----------------------+-----------------------+

---------- GPIO Timing Report (end) ----------

---------- 3.1 HSIO GPIO Timing Report (begin) ----------

Clkout GPIO Configuration:
===========================

+---------------+--------------+--------------+----------+----------+-------------------------+
| Instance Name |  Clock Pin   |  Parameter   | Max (ns) | Min (ns) |    Reference Pin Name   |
+---------------+--------------+--------------+----------+----------+-------------------------+
|   lcd_pclk_o  | ~clk_lvds_1x | GPIO_CLK_OUT |  1.921   |  1.281   | clk_lvds_1x~CLKOUT~1~48 |
+---------------+--------------+--------------+----------+----------+-------------------------+

Non-registered HSIO GPIO Configuration:
========================================

+----------------+----------------+-------------+----------+----------+
| Instance Name  |    Pin Name    |  Parameter  | Max (ns) | Min (ns) |
+----------------+----------------+-------------+----------+----------+
|   cmos_pclk    |   cmos_pclk    | GPIO_CLK_IN |  1.773   |  1.182   |
|   uart_rx_i    |   uart_rx_i    |   GPIO_IN   |  0.828   |  0.552   |
|     verf0      |     verf0      |   GPIO_IN   |  1.001   |  0.667   |
|     vref1      |     vref1      |   GPIO_IN   |  1.001   |  0.667   |
|   cmos_ctl3    |   cmos_ctl3    |   GPIO_OUT  |  2.205   |  1.470   |
|   dsi_pwm_o    |   dsi_pwm_o    |   GPIO_OUT  |  2.205   |  1.470   |
|  dsi_resetn_o  |  dsi_resetn_o  |   GPIO_OUT  |  2.205   |  1.470   |
|   lcd_blen_o   |   lcd_blen_o   |   GPIO_OUT  |  2.205   |  1.470   |
|    lcd_de_o    |    lcd_de_o    |   GPIO_OUT  |  2.205   |  1.470   |
|    lcd_hs_o    |    lcd_hs_o    |   GPIO_OUT  |  2.205   |  1.470   |
|   lcd_pwm_o    |   lcd_pwm_o    |   GPIO_OUT  |  2.205   |  1.470   |
|  lcd_tp_rst_o  |  lcd_tp_rst_o  |   GPIO_OUT  |  2.205   |  1.470   |
|    lcd_vs_o    |    lcd_vs_o    |   GPIO_OUT  |  1.921   |  1.281   |
|   spi_sck_o    |   spi_sck_o    |   GPIO_OUT  |  2.205   |  1.470   |
|   spi_ssn_o    |   spi_ssn_o    |   GPIO_OUT  |  2.205   |  1.470   |
|   uart_tx_o    |   uart_tx_o    |   GPIO_OUT  |  2.205   |  1.470   |
|  csi_ctl0_io   |   csi_ctl0_i   |   GPIO_IN   |  0.828   |  0.552   |
|  csi_ctl0_io   |   csi_ctl0_o   |   GPIO_OUT  |  2.205   |  1.470   |
|  csi_ctl0_io   |  csi_ctl0_oe   |   GPIO_OUT  |  1.953   |  1.302   |
|  csi_ctl1_io   |   csi_ctl1_i   |   GPIO_IN   |  0.828   |  0.552   |
|  csi_ctl1_io   |   csi_ctl1_o   |   GPIO_OUT  |  2.205   |  1.470   |
|  csi_ctl1_io   |  csi_ctl1_oe   |   GPIO_OUT  |  1.953   |  1.302   |
|   csi_scl_io   |   csi_scl_i    |   GPIO_IN   |  0.828   |  0.552   |
|   csi_scl_io   |   csi_scl_o    |   GPIO_OUT  |  2.205   |  1.470   |
|   csi_scl_io   |   csi_scl_oe   |   GPIO_OUT  |  1.953   |  1.302   |
|   csi_sda_io   |   csi_sda_i    |   GPIO_IN   |  0.828   |  0.552   |
|   csi_sda_io   |   csi_sda_o    |   GPIO_OUT  |  2.205   |  1.470   |
|   csi_sda_io   |   csi_sda_oe   |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_b7_0_io[0] | lcd_b7_0_i[0]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_b7_0_io[0] | lcd_b7_0_o[0]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_b7_0_io[0] | lcd_b7_0_oe[0] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_b7_0_io[1] | lcd_b7_0_i[1]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_b7_0_io[1] | lcd_b7_0_o[1]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_b7_0_io[1] | lcd_b7_0_oe[1] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_b7_0_io[2] | lcd_b7_0_i[2]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_b7_0_io[2] | lcd_b7_0_o[2]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_b7_0_io[2] | lcd_b7_0_oe[2] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_b7_0_io[3] | lcd_b7_0_i[3]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_b7_0_io[3] | lcd_b7_0_o[3]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_b7_0_io[3] | lcd_b7_0_oe[3] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_b7_0_io[4] | lcd_b7_0_i[4]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_b7_0_io[4] | lcd_b7_0_o[4]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_b7_0_io[4] | lcd_b7_0_oe[4] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_b7_0_io[5] | lcd_b7_0_i[5]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_b7_0_io[5] | lcd_b7_0_o[5]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_b7_0_io[5] | lcd_b7_0_oe[5] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_b7_0_io[6] | lcd_b7_0_i[6]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_b7_0_io[6] | lcd_b7_0_o[6]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_b7_0_io[6] | lcd_b7_0_oe[6] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_b7_0_io[7] | lcd_b7_0_i[7]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_b7_0_io[7] | lcd_b7_0_o[7]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_b7_0_io[7] | lcd_b7_0_oe[7] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_g7_0_io[0] | lcd_g7_0_i[0]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_g7_0_io[0] | lcd_g7_0_o[0]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_g7_0_io[0] | lcd_g7_0_oe[0] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_g7_0_io[1] | lcd_g7_0_i[1]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_g7_0_io[1] | lcd_g7_0_o[1]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_g7_0_io[1] | lcd_g7_0_oe[1] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_g7_0_io[2] | lcd_g7_0_i[2]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_g7_0_io[2] | lcd_g7_0_o[2]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_g7_0_io[2] | lcd_g7_0_oe[2] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_g7_0_io[3] | lcd_g7_0_i[3]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_g7_0_io[3] | lcd_g7_0_o[3]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_g7_0_io[3] | lcd_g7_0_oe[3] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_g7_0_io[4] | lcd_g7_0_i[4]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_g7_0_io[4] | lcd_g7_0_o[4]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_g7_0_io[4] | lcd_g7_0_oe[4] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_g7_0_io[5] | lcd_g7_0_i[5]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_g7_0_io[5] | lcd_g7_0_o[5]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_g7_0_io[5] | lcd_g7_0_oe[5] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_g7_0_io[6] | lcd_g7_0_i[6]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_g7_0_io[6] | lcd_g7_0_o[6]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_g7_0_io[6] | lcd_g7_0_oe[6] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_g7_0_io[7] | lcd_g7_0_i[7]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_g7_0_io[7] | lcd_g7_0_o[7]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_g7_0_io[7] | lcd_g7_0_oe[7] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_r7_0_io[0] | lcd_r7_0_i[0]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_r7_0_io[0] | lcd_r7_0_o[0]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_r7_0_io[0] | lcd_r7_0_oe[0] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_r7_0_io[1] | lcd_r7_0_i[1]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_r7_0_io[1] | lcd_r7_0_o[1]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_r7_0_io[1] | lcd_r7_0_oe[1] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_r7_0_io[2] | lcd_r7_0_i[2]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_r7_0_io[2] | lcd_r7_0_o[2]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_r7_0_io[2] | lcd_r7_0_oe[2] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_r7_0_io[3] | lcd_r7_0_i[3]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_r7_0_io[3] | lcd_r7_0_o[3]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_r7_0_io[3] | lcd_r7_0_oe[3] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_r7_0_io[4] | lcd_r7_0_i[4]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_r7_0_io[4] | lcd_r7_0_o[4]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_r7_0_io[4] | lcd_r7_0_oe[4] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_r7_0_io[5] | lcd_r7_0_i[5]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_r7_0_io[5] | lcd_r7_0_o[5]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_r7_0_io[5] | lcd_r7_0_oe[5] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_r7_0_io[6] | lcd_r7_0_i[6]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_r7_0_io[6] | lcd_r7_0_o[6]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_r7_0_io[6] | lcd_r7_0_oe[6] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_r7_0_io[7] | lcd_r7_0_i[7]  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_r7_0_io[7] | lcd_r7_0_o[7]  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_r7_0_io[7] | lcd_r7_0_oe[7] |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_tp_int_io  |  lcd_tp_int_i  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_tp_int_io  |  lcd_tp_int_o  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_tp_int_io  | lcd_tp_int_oe  |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_tp_scl_io  |  lcd_tp_scl_i  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_tp_scl_io  |  lcd_tp_scl_o  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_tp_scl_io  | lcd_tp_scl_oe  |   GPIO_OUT  |  1.953   |  1.302   |
| lcd_tp_sda_io  |  lcd_tp_sda_i  |   GPIO_IN   |  0.828   |  0.552   |
| lcd_tp_sda_io  |  lcd_tp_sda_o  |   GPIO_OUT  |  2.205   |  1.470   |
| lcd_tp_sda_io  | lcd_tp_sda_oe  |   GPIO_OUT  |  1.953   |  1.302   |
+----------------+----------------+-------------+----------+----------+

Registered HSIO GPIO Configuration:
====================================

+---------------+-----------+----------------+----------------+---------------+---------------+-----------------------+-----------------------+
| Instance Name | Clock Pin | Max Setup (ns) | Min Setup (ns) | Max Hold (ns) | Min Hold (ns) | Max Clock To Out (ns) | Min Clock To Out (ns) |
+---------------+-----------+----------------+----------------+---------------+---------------+-----------------------+-----------------------+
|    addr[0]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[1]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[2]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[3]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[4]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[5]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[6]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[7]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[8]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[9]    | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[10]   | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[11]   | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[12]   | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[13]   | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[14]   | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|    addr[15]   | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|     ba[0]     | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|     ba[1]     | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|     ba[2]     | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|      cas      | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|      cke      | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|     clk_n     | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|     clk_p     | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|       cs      | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|     dm[0]     |  twd_clk  |                |                |               |               |         1.596         |         1.064         |
|     dm[1]     |  twd_clk  |                |                |               |               |         1.596         |         1.064         |
|      odt      | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|      ras      | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|     reset     | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|       we      | tdqss_clk |                |                |               |               |         1.596         |         1.064         |
|     dq[0]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[0]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[1]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[1]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[2]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[2]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[3]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[3]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[4]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[4]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[5]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[5]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[6]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[6]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[7]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[7]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[8]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[8]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[9]     |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[9]     |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[10]    |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[10]    |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[11]    |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[11]    |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[12]    |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[12]    |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[13]    |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[13]    |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[14]    |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[14]    |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dq[15]    |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dq[15]    |  twd_clk  |                |                |               |               |         1.596         |         1.022         |
|     dqs[0]    |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dqs[0]    | tdqss_clk |                |                |               |               |         1.596         |         1.022         |
|     dqs[1]    |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|     dqs[1]    | tdqss_clk |                |                |               |               |         1.596         |         1.022         |
|    dqs_n[0]   |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|    dqs_n[0]   | tdqss_clk |                |                |               |               |         1.596         |         1.022         |
|    dqs_n[1]   |  tac_clk  |     0.791      |     0.527      |     -0.581    |     -0.387    |                       |                       |
|    dqs_n[1]   | tdqss_clk |                |                |               |               |         1.596         |         1.022         |
+---------------+-----------+----------------+----------------+---------------+---------------+-----------------------+-----------------------+

---------- HSIO GPIO Timing Report (end) ----------

---------- 3.2.1 LVDS Tx Timing Report (begin) ----------

---------- LVDS Tx Timing Report (end) ----------

---------- 3.3.1 MIPI RX Lane Timing Report (begin) ----------

+---------------+-----------------+-----------------+----------+----------+
| Instance Name |     Pin Name    |    Parameter    | Max (ns) | Min (ns) |
+---------------+-----------------+-----------------+----------+----------+
|    csi_rxc    |  csi_rxc_lp_p_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
|    csi_rxc    |  csi_rxc_lp_n_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
|    csi_rxd0   | csi_rxd0_lp_p_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
|    csi_rxd0   | csi_rxd0_lp_n_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
|    csi_rxd1   | csi_rxd1_lp_p_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
|    csi_rxd1   | csi_rxd1_lp_n_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
|    csi_rxd2   | csi_rxd2_lp_p_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
|    csi_rxd2   | csi_rxd2_lp_n_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
|    csi_rxd3   | csi_rxd3_lp_p_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
|    csi_rxd3   | csi_rxd3_lp_n_i | MIPI_RX_LANE_IN |  2.226   |  1.484   |
+---------------+-----------------+-----------------+----------+----------+

---------- MIPI RX Lane Timing Report (end) ----------

---------- 3.3.2 MIPI TX Lane Timing Report (begin) ----------

+---------------+------------------+------------------+----------+----------+
| Instance Name |     Pin Name     |    Parameter     | Max (ns) | Min (ns) |
+---------------+------------------+------------------+----------+----------+
|    dsi_txc    |  dsi_txc_hs_oe   | MIPI_TX_LANE_OUT |  1.575   |  1.050   |
|    dsi_txc    | dsi_txc_lp_n_oe  | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txc    | dsi_txc_lp_p_oe  | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txc    |  dsi_txc_lp_n_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
|    dsi_txc    |  dsi_txc_lp_p_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
|    dsi_txd0   |  dsi_txd0_hs_oe  | MIPI_TX_LANE_OUT |  1.575   |  1.050   |
|    dsi_txd0   | dsi_txd0_lp_n_oe | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txd0   | dsi_txd0_lp_p_oe | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txd0   | dsi_txd0_lp_n_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
|    dsi_txd0   | dsi_txd0_lp_p_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
|    dsi_txd1   |  dsi_txd1_hs_oe  | MIPI_TX_LANE_OUT |  1.575   |  1.050   |
|    dsi_txd1   | dsi_txd1_lp_n_oe | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txd1   | dsi_txd1_lp_p_oe | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txd1   | dsi_txd1_lp_n_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
|    dsi_txd1   | dsi_txd1_lp_p_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
|    dsi_txd2   |  dsi_txd2_hs_oe  | MIPI_TX_LANE_OUT |  1.575   |  1.050   |
|    dsi_txd2   | dsi_txd2_lp_n_oe | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txd2   | dsi_txd2_lp_p_oe | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txd2   | dsi_txd2_lp_n_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
|    dsi_txd2   | dsi_txd2_lp_p_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
|    dsi_txd3   |  dsi_txd3_hs_oe  | MIPI_TX_LANE_OUT |  1.575   |  1.050   |
|    dsi_txd3   | dsi_txd3_lp_n_oe | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txd3   | dsi_txd3_lp_p_oe | MIPI_TX_LANE_OUT |  2.100   |  1.400   |
|    dsi_txd3   | dsi_txd3_lp_n_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
|    dsi_txd3   | dsi_txd3_lp_p_o  | MIPI_TX_LANE_OUT |  2.058   |  1.372   |
+---------------+------------------+------------------+----------+----------+

---------- MIPI TX Lane Timing Report (end) ----------

---------- 4. Clock Network Delay Report (begin) ----------

+---------------+----------+----------+
|   Clock Pin   | Max (ns) | Min (ns) |
+---------------+----------+----------+
|    clk_27m    |  1.775   |  1.148   |
|  clk_lvds_1x  |  1.840   |  1.156   |
|  clk_lvds_7x  |  1.857   |  1.201   |
|   clk_pixel   |  1.820   |  1.178   |
| clk_pixel_10x |  1.793   |  1.161   |
|    clk_sys    |  1.881   |  1.183   |
|   cmos_pclk   |  1.802   |  1.166   |
|    core_clk   |  1.778   |  1.150   |
| dsi_byteclk_i |  1.888   |  1.222   |
|  dsi_serclk_i |  1.959   |  1.268   |
|  dsi_txcclk_i |  1.928   |  1.248   |
|    tac_clk    |  1.849   |  1.197   |
|   tdqss_clk   |  1.888   |  1.187   |
|    twd_clk    |  1.856   |  1.201   |
+---------------+----------+----------+

---------- Clock Network Delay Report (end) ----------
