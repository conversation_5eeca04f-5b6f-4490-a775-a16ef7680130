`timescale 1ns/1ps

module csi_rx (
input reset_n,
input clk,
input reset_byte_HS_n,
input clk_byte_HS,
input reset_pixel_n,
input clk_pixel,
input Rx_LP_CLK_P,
input Rx_LP_CLK_N,
output Rx_HS_enable_C,
output LVDS_termen_C,
input [3:0] Rx_LP_D_P,
input [3:0] Rx_LP_D_N,
input [7:0] Rx_HS_D_0,
input [7:0] Rx_HS_D_1,
input [7:0] Rx_HS_D_2,
input [7:0] Rx_HS_D_3,
input [7:0] Rx_HS_D_4,
input [7:0] Rx_HS_D_5,
input [7:0] Rx_HS_D_6,
input [7:0] Rx_HS_D_7,
output [3:0] Rx_HS_enable_D,
output [3:0] LVDS_termen_D,
output [3:0] fifo_rd_enable,
input [3:0] fifo_rd_empty,
output [3:0] DLY_enable_D,
output [3:0] DLY_inc_D,
input [3:0] u_dly_enable_D,
output vsync_vc1,
output vsync_vc15,
output vsync_vc12,
output vsync_vc9,
output vsync_vc7,
output vsync_vc14,
output vsync_vc13,
output vsync_vc11,
output vsync_vc10,
output vsync_vc8,
output vsync_vc6,
output vsync_vc4,
output vsync_vc0,
output vsync_vc5,
output irq,
output pixel_data_valid,
output [63:0] pixel_data,
output [3:0] pixel_per_clk,
output [5:0] datatype,
output [15:0] shortpkt_data_field,
output [15:0] word_count,
output [1:0] vcx,
output [1:0] vc,
output hsync_vc3,
output hsync_vc2,
output hsync_vc8,
output hsync_vc12,
output hsync_vc7,
output hsync_vc10,
output hsync_vc1,
output hsync_vc0,
output hsync_vc13,
output hsync_vc4,
output hsync_vc11,
output hsync_vc6,
output hsync_vc9,
output hsync_vc15,
output hsync_vc14,
output hsync_vc5,
input axi_rready,
output axi_rvalid,
output [31:0] axi_rdata,
output axi_arready,
input axi_arvalid,
input [5:0] axi_araddr,
input axi_bready,
output axi_bvalid,
output axi_wready,
input axi_wvalid,
input [31:0] axi_wdata,
output vsync_vc3,
output vsync_vc2,
output axi_awready,
input [3:0] u_dly_inc_D,
input axi_clk,
input axi_reset_n,
input [5:0] axi_awaddr,
input axi_awvalid
);

endmodule


