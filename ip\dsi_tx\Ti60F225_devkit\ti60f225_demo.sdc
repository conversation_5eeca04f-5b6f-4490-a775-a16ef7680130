
# Auto-generated by Interface Designer
#
# WARNING: Any manual changes made to this file will be lost when generating constraints.

# Efinity Interface Designer SDC
# Version: 2024.M.263
# Date: 2024-10-02 09:41

# Copyright (C) 2013 - 2024 Efinix Inc. All rights reserved.

# Device: Ti60F225
# Project: ti60f225_demo
# Timing Model: C4 (final)

# PLL Constraints
#################
create_clock -period 10.000 i_mipi_clk
create_clock -waveform {1.000 2.000} -period 2.000 i_mipi_txc_sclk
create_clock -waveform {0.500 1.500} -period 2.000 i_mipi_txd_sclk
create_clock -period 8.000 i_mipi_tx_pclk
create_clock -period 40.000 i_fb_clk
create_clock -period 8.000 i_sysclk
create_clock -period 16.000 i_sysclk_div_2

set_clock_groups -exclusive -group {i_sysclk} -group {i_mipi_clk} -group {i_sysclk_div_2} -group {i_fb_clk} -group {i_mipi_tx_pclk}

set_multicycle_path 3 -setup -to [get_ports mipi_dp_clk_HS_OUT[*]] -end
set_multicycle_path 2 -hold -to [get_ports mipi_dp_clk_HS_OUT[*]] -end

# GPIO Constraints
####################
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {i_arstn}]
# set_input_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {i_arstn}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -max <MAX CALCULATION> [get_ports {o_lcd_rstn}]
# set_output_delay -clock <CLOCK> [-reference_pin <clkout_pad>] -min <MIN CALCULATION> [get_ports {o_lcd_rstn}]

# HSIO GPIO Constraints
#########################

# MIPI TX Lane Constraints
############################
set_output_delay -clock i_mipi_txd_sclk -reference_pin [get_ports {i_mipi_txd_sclk~CLKOUT~218~274}] -max 0.378 [get_ports {mipi_dp_clk_HS_OUT[*]}]
set_output_delay -clock i_mipi_txd_sclk -reference_pin [get_ports {i_mipi_txd_sclk~CLKOUT~218~274}] -min -0.140 [get_ports {mipi_dp_clk_HS_OUT[*]}]
set_output_delay -clock i_mipi_txd_sclk -reference_pin [get_ports {i_mipi_txd_sclk~CLKOUT~218~274}] -max 0.315 [get_ports {mipi_dp_clk_RST}]
set_output_delay -clock i_mipi_txd_sclk -reference_pin [get_ports {i_mipi_txd_sclk~CLKOUT~218~274}] -min -0.140 [get_ports {mipi_dp_clk_RST}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~300}] -max 0.378 [get_ports {mipi_dp_data0_HS_OUT[*]}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~300}] -min -0.140 [get_ports {mipi_dp_data0_HS_OUT[*]}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~300}] -max 0.315 [get_ports {mipi_dp_data0_RST}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~300}] -min -0.140 [get_ports {mipi_dp_data0_RST}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~286}] -max 0.378 [get_ports {mipi_dp_data1_HS_OUT[*]}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~286}] -min -0.140 [get_ports {mipi_dp_data1_HS_OUT[*]}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~286}] -max 0.315 [get_ports {mipi_dp_data1_RST}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~286}] -min -0.140 [get_ports {mipi_dp_data1_RST}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~261}] -max 0.378 [get_ports {mipi_dp_data2_HS_OUT[*]}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~261}] -min -0.140 [get_ports {mipi_dp_data2_HS_OUT[*]}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~261}] -max 0.315 [get_ports {mipi_dp_data2_RST}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~261}] -min -0.140 [get_ports {mipi_dp_data2_RST}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~251}] -max 0.378 [get_ports {mipi_dp_data3_HS_OUT[*]}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~251}] -min -0.140 [get_ports {mipi_dp_data3_HS_OUT[*]}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~251}] -max 0.315 [get_ports {mipi_dp_data3_RST}]
set_output_delay -clock i_mipi_tx_pclk -reference_pin [get_ports {i_mipi_tx_pclk~CLKOUT~218~251}] -min -0.140 [get_ports {mipi_dp_data3_RST}]

# Clock Latency Constraints
############################
# set_clock_latency -source -setup <pll_clk_latency_i_fb_clk_max -0.007> [get_ports {i_mipi_clk}]
# set_clock_latency -source -hold <pll_clk_latency_i_fb_clk_min -0.005> [get_ports {i_mipi_clk}]
# set_clock_latency -source -setup <pll_clk_latency_i_fb_clk_max -0.007> [get_ports {i_mipi_txc_sclk}]
# set_clock_latency -source -hold <pll_clk_latency_i_fb_clk_min -0.005> [get_ports {i_mipi_txc_sclk}]
# set_clock_latency -source -setup <pll_clk_latency_i_fb_clk_max -0.007> [get_ports {i_mipi_txd_sclk}]
# set_clock_latency -source -hold <pll_clk_latency_i_fb_clk_min -0.005> [get_ports {i_mipi_txd_sclk}]
# set_clock_latency -source -setup <pll_clk_latency_i_fb_clk_max -0.007> [get_ports {i_mipi_tx_pclk}]
# set_clock_latency -source -hold <pll_clk_latency_i_fb_clk_min -0.005> [get_ports {i_mipi_tx_pclk}]
# set_clock_latency -source -setup <board_max -1.053> [get_ports {i_fb_clk}]
# set_clock_latency -source -hold <board_min -0.665> [get_ports {i_fb_clk}]
# set_clock_latency -source -setup <board_max -1.053> [get_ports {i_sysclk}]
# set_clock_latency -source -hold <board_min -0.665> [get_ports {i_sysclk}]
# set_clock_latency -source -setup <board_max -1.053> [get_ports {i_sysclk_div_2}]
# set_clock_latency -source -hold <board_min -0.665> [get_ports {i_sysclk_div_2}]
# set_clock_latency -source -setup <board_max -1.053> [get_ports {i_mipi_rx_pclk}]
# set_clock_latency -source -hold <board_min -0.665> [get_ports {i_mipi_rx_pclk}]
