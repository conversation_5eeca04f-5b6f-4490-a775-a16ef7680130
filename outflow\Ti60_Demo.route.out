WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:14] No valid object(s) found for 'dsi_refclk_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:14] No valid pin(s) found for clock
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:14] Unable to run 'create_clock' constraint due to warnings found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:24] No valid object(s) found for 'clk_pixel_2x'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:24] No valid pin(s) found for clock
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:24] Unable to run 'create_clock' constraint due to warnings found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:29] No valid object(s) found for 'clk_54m'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:29] No valid pin(s) found for clock
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:29] Unable to run 'create_clock' constraint due to warnings found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:35] set_clock_groups: Defaulting to -exclusive since none of the required option was specified
WARNING  : No ports matched 'cmos_data[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:78] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:78] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:79] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:79] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:80] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:80] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:81] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:81] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[2]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:82] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:82] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[2]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:83] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:83] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[3]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:84] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:84] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[3]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:85] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:85] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[4]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:86] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:86] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[4]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:87] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:87] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[5]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:88] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:88] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[5]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:89] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:89] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[6]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:90] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:90] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[6]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:91] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:91] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[7]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:92] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:92] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[7]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:93] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:93] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_href'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:94] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:94] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_href'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:95] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:95] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_vsync'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:96] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:96] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_vsync'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:97] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:97] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_hi[0]'
WARNING  : No ports matched 'i_dqs_n_lo[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:341] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:341] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_lo[0]'
WARNING  : No ports matched 'i_dqs_n_hi[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:342] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:342] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_lo[1]'
WARNING  : No ports matched 'i_dqs_n_hi[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:347] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:347] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_hi[1]'
WARNING  : No ports matched 'i_dqs_n_lo[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:348] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:348] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:576] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:576] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:577] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:577] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:578] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:578] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:579] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:579] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:580] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:580] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:581] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:581] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:582] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:582] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:583] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:583] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:584] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:584] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:585] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:585] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:586] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:586] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:587] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:587] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:588] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:588] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:589] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:589] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:590] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:590] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:591] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:591] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:592] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:592] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:593] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:593] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:594] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:594] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:595] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:595] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:596] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:596] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:597] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:597] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:598] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:598] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:599] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:599] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:600] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:600] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:601] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:601] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:602] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:602] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:603] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:603] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:604] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:604] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:605] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:605] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:606] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:606] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:607] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:607] Unable to run 'set_input_delay' constraint due to warnings found
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Router Setup
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : [Setup Parallel Routing Scheduler]
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Route Circuit with 32 threads
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Beneficial skew enabled! Optimizing clock skews ... 
INFO     : Non-legalized beneficial skew improved geomean CPD by -0.04% (from 2779ps to 2780ps)
INFO     : CSS improved CPD of clock tdqss_clk by 0.00% (from 1995ps to 1995ps)
INFO     : CSS improved CPD of clock core_clk by 0.00% (from 3861ps to 3861ps)
INFO     : CSS improved CPD of clock tac_clk by 0.00% (from 1989ps to 1989ps)
INFO     : CSS improved CPD of clock twd_clk by 0.00% (from 1983ps to 1983ps)
INFO     : CSS improved CPD of clock clk_sys by 0.00% (from 5313ps to 5313ps)
INFO     : CSS improved CPD of clock clk_lvds_1x by -0.50% (from 2588ps to 2601ps)
INFO     : CSS improved CPD of clock csi_rxc_i by 0.00% (from 3068ps to 3068ps)
INFO     : Discretized beneficial skew improved geomean CPD by -0.07% (from 2779ps to 2781ps in 5 iterations and 0.4 sec)
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Overridding delay budgets for 415 synchronizer-related pins

INFO     : Iter  1: overlap=16559   heapops=2294696    (0%) cpd=815   frr=1.00  msec=1130
INFO     : [Setup Delay Budgets | 181.4 ms]
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Hold Fixing
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Total min wire delay:   3377466
INFO     : Hold fix wire delay:         78
INFO     : Hold fix delay ratio:     0.00%
INFO     : Pins requiring hold fix:  0.00%
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Iter  2: overlap=7088    heapops=1696717    (36%) cpd=815   frr=1.00  msec=356
INFO     : Iter  3: overlap=4334    heapops=1280294    (51%) cpd=815   frr=1.00  msec=308
INFO     : Iter  4: overlap=2582    heapops=929375     (62%) cpd=815   frr=1.00  msec=228
INFO     : Iter  5: overlap=1343    heapops=669310     (71%) cpd=815   frr=1.00  msec=181
INFO     : Iter  6: overlap=707     heapops=476787     (77%) cpd=815   frr=1.00  msec=164
INFO     : Iter  7: overlap=290     heapops=259309     (84%) cpd=815   frr=1.00  msec=120
INFO     : [Rebuild Parallel Routing Scheduler]
INFO     : Iter  8: overlap=99      heapops=136633     (86%) cpd=815   frr=1.00  msec=115
INFO     : Iter  9: overlap=23      heapops=82898      (88%) cpd=815   frr=1.00  msec=81
INFO     : Iter 10: overlap=10      heapops=47661      (88%) cpd=815   frr=1.00  msec=69
INFO     : Iter 11: overlap=4       heapops=44587      (88%) cpd=815   frr=1.00  msec=68
INFO     : Iter 12: overlap=4       heapops=43578      (88%) cpd=815   frr=1.00  msec=72
INFO     : Iter 13: overlap=4       heapops=43316      (88%) cpd=815   frr=1.00  msec=74
INFO     : Iter 14: overlap=3       heapops=43278      (88%) cpd=815   frr=1.00  msec=68
INFO     : Iter 15: overlap=1       heapops=42530      (88%) cpd=820   frr=1.01  msec=66
INFO     : Iter 16: overlap=1       heapops=42532      (88%) cpd=820   frr=1.01  msec=64
INFO     : Iter 17: overlap=1       heapops=42529      (88%) cpd=820   frr=1.01  msec=67
INFO     : Iter 18: overlap=1       heapops=42527      (88%) cpd=820   frr=1.01  msec=69
INFO     : Iter 19: overlap=1       heapops=42527      (88%) cpd=820   frr=1.01  msec=66
INFO     : Iter 20: overlap=1       heapops=42652      (88%) cpd=820   frr=1.01  msec=67
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Beneficial skew enabled! Optimizing clock skews ... 
INFO     : Non-legalized beneficial skew improved geomean CPD by 0.00% (from 2984ps to 2984ps)
INFO     : CSS improved CPD of clock tdqss_clk by 0.00% (from 1951ps to 1951ps)
INFO     : CSS improved CPD of clock core_clk by 0.00% (from 4137ps to 4137ps)
INFO     : CSS improved CPD of clock tac_clk by 0.00% (from 2083ps to 2083ps)
INFO     : CSS improved CPD of clock twd_clk by 0.00% (from 2023ps to 2023ps)
INFO     : CSS improved CPD of clock clk_sys by 0.00% (from 5818ps to 5818ps)
INFO     : CSS improved CPD of clock clk_lvds_1x by 0.00% (from 2945ps to 2945ps)
INFO     : CSS improved CPD of clock csi_rxc_i by 0.00% (from 3620ps to 3620ps)
INFO     : Discretized beneficial skew improved geomean CPD by 0.00% (from 2984ps to 2984ps in 1 iterations and 0.4 sec)
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Iter 21: overlap=0       heapops=43469      (88%) cpd=820   frr=1.01  msec=474
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Routing Result
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Routing Succeeded in 4.27 seconds taking 21 iterations! 
INFO     : 
INFO     : First iteration critical path delay = 0.815 ns 
INFO     : Last iteration critical path delay  = 0.820 ns (ratio = 1.01)
INFO     : Max Routing Heap Size = 2,240
INFO     : Routing trace written to file 'D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.troutingtraces' 
INFO     : ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
INFO     : Routing took 12.5726 seconds.
INFO     : 	Routing took 29.3281 seconds (approximately) in total CPU time.
INFO     : Routing virtual memory usage: begin = 185.24 MB, end = 290.684 MB, delta = 105.444 MB
INFO     : 	Routing peak virtual memory usage = 410.02 MB
INFO     : Routing resident set memory usage: begin = 198.912 MB, end = 303.332 MB, delta = 104.42 MB
INFO     : 	Routing peak resident set memory usage = 389.472 MB
           ***** Beginning stage final timing analysis ... *****
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:14] No valid object(s) found for 'dsi_refclk_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:14] No valid pin(s) found for clock
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:14] Unable to run 'create_clock' constraint due to warnings found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:24] No valid object(s) found for 'clk_pixel_2x'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:24] No valid pin(s) found for clock
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:24] Unable to run 'create_clock' constraint due to warnings found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:29] No valid object(s) found for 'clk_54m'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:29] No valid pin(s) found for clock
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:29] Unable to run 'create_clock' constraint due to warnings found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:35] set_clock_groups: Defaulting to -exclusive since none of the required option was specified
WARNING  : No ports matched 'cmos_data[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:78] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:78] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:79] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:79] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:80] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:80] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:81] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:81] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[2]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:82] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:82] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[2]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:83] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:83] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[3]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:84] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:84] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[3]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:85] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:85] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[4]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:86] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:86] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[4]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:87] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:87] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[5]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:88] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:88] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[5]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:89] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:89] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[6]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:90] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:90] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[6]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:91] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:91] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[7]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:92] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:92] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_data[7]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:93] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:93] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_href'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:94] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:94] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_href'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:95] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:95] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_vsync'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:96] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:96] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'cmos_vsync'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:97] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:97] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_lo[0]'
WARNING  : No ports matched 'i_dqs_n_hi[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:341] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:341] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_lo[0]'
WARNING  : No ports matched 'i_dqs_n_hi[0]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:342] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:342] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_lo[1]'
WARNING  : No ports matched 'i_dqs_n_hi[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:347] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:347] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'i_dqs_n_lo[1]'
WARNING  : No ports matched 'i_dqs_n_hi[1]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:348] set_input_delay: No valid input port(s) found
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:348] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:576] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:576] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:577] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:577] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:578] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:578] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:579] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:579] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:580] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:580] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : No ports matched 'csi_rxd0_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:581] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:581] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:582] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:582] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~278'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:583] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:583] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:584] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:584] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:585] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:585] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:586] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:586] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:587] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:587] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:588] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:588] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:589] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:589] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:590] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:590] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~294'
WARNING  : No ports matched 'csi_rxd1_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:591] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:591] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:592] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:592] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:593] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:593] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:594] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:594] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:595] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:595] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:596] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:596] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:597] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:597] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:598] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:598] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~245'
WARNING  : No ports matched 'csi_rxd2_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:599] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:599] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:600] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:600] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_rd_o'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:601] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:601] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:602] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:602] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:603] set_output_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:603] Unable to run 'set_output_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:604] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:604] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_fifo_empty_i'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:605] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:605] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:606] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:606] Unable to run 'set_input_delay' constraint due to warnings found
WARNING  : No ports matched 'csi_rxc_i~CLKOUT~218~255'
WARNING  : No ports matched 'csi_rxd3_hs_i[*]'
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:607] set_input_delay: No valid output port found for -reference_pin
WARNING  : [SDC D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/Ti60_Demo.pt.sdc:607] Unable to run 'set_input_delay' constraint due to warnings found
Maximum possible analyzed clocks frequency
 Clock Name  Period (ns)  Frequency (MHz)     Edge
tdqss_clk       2.061        485.201         (R-R)
core_clk        4.247        235.460         (R-R)
tac_clk         2.193        455.996         (R-R)
twd_clk         2.133        468.823         (R-R)
clk_sys         5.928        168.691         (R-R)
clk_lvds_1x     3.055        327.332         (R-R)
csi_rxc_i       3.730        268.097         (R-R)

Geomean max period: 3.102

Setup (Max) Clock Relationship
  Launch Clock    Capture Clock   Constraint (ns)     Slack (ns)          Edge
  tdqss_clk        tdqss_clk            2.604            0.543           (R-R)
  tdqss_clk        core_clk             2.604            2.124           (R-R)
  core_clk         tdqss_clk            2.604            1.676           (R-R)
  core_clk         core_clk             5.208            0.961           (R-R)
  core_clk         tac_clk              2.604            2.249           (R-R)
  core_clk         twd_clk              0.541            0.317           (R-R)
  tac_clk          core_clk             2.604            2.100           (R-R)
  tac_clk          tac_clk              2.604            0.411           (R-R)
  twd_clk          twd_clk              2.604            0.471           (R-R)
  clk_sys          clk_sys             10.417            4.489           (R-R)
  clk_sys          clk_lvds_1x          0.001           -0.846           (R-R)
  clk_pixel        clk_lvds_1x          0.001           -0.717           (R-R)
  clk_lvds_1x      clk_sys              0.001           -0.929           (R-R)
  clk_lvds_1x      clk_lvds_1x         20.833           17.778           (R-R)
  csi_rxc_i        csi_rxc_i            6.000            2.270           (R-R)

Hold (Min) Clock Relationship
  Launch Clock    Capture Clock   Constraint (ns)     Slack (ns)          Edge
  tdqss_clk        tdqss_clk            0.000           0.101            (R-R)
  tdqss_clk        core_clk             0.000           0.151            (R-R)
  core_clk         tdqss_clk            0.000           0.077            (R-R)
  core_clk         core_clk             0.000           0.027            (R-R)
  core_clk         tac_clk              0.000           0.067            (R-R)
  core_clk         twd_clk             -1.883           2.026            (R-R)
  tac_clk          core_clk             0.000           0.159            (R-R)
  tac_clk          tac_clk              0.000           0.027            (R-R)
  twd_clk          twd_clk              0.000           0.071            (R-R)
  clk_sys          clk_sys              0.000           0.028            (R-R)
  clk_sys          clk_lvds_1x          0.000           0.140            (R-R)
  clk_pixel        clk_lvds_1x          0.000           0.123            (R-R)
  clk_lvds_1x      clk_sys              0.000           0.086            (R-R)
  clk_lvds_1x      clk_lvds_1x          0.000           0.103            (R-R)
  csi_rxc_i        csi_rxc_i            0.000           0.047            (R-R)

WARNING  : Clock domain between clk_sys (rising) and clk_lvds_1x (rising) may not meet (slack: -0.846 ns) the setup (max) timing requirement
WARNING  : Clock domain between clk_pixel (rising) and clk_lvds_1x (rising) may not meet (slack: -0.717 ns) the setup (max) timing requirement
WARNING  : Clock domain between clk_lvds_1x (rising) and clk_sys (rising) may not meet (slack: -0.929 ns) the setup (max) timing requirement
INFO     : Write Timing Report to "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow\Ti60_Demo.timing.rpt" ...
INFO     : final timing analysis took 15.0665 seconds.
INFO     : 	final timing analysis took 15.4844 seconds (approximately) in total CPU time.
INFO     : final timing analysis virtual memory usage: begin = 329.292 MB, end = 369.3 MB, delta = 40.008 MB
INFO     : 	final timing analysis peak virtual memory usage = 410.02 MB
INFO     : final timing analysis resident set memory usage: begin = 341.308 MB, end = 377.976 MB, delta = 36.668 MB
INFO     : 	final timing analysis peak resident set memory usage = 389.472 MB
           ***** Ending stage final timing analysis *****
           
           ***** Beginning stage netlist pre-processing ... *****
INFO     : Successfully Read in Verific binary Netlist dump file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.vdb".
INFO     : ***** Beginning VDB Netlist Checker ... *****
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(11): Input/inout port clk_24m is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(12): Input/inout port clk_25m is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(21): Input/inout port clk_pixel_2x is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(22): Input/inout port clk_pixel_10x is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(30): Input/inout port dsi_refclk_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(31): Input/inout port dsi_byteclk_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(32): Input/inout port dsi_serclk_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(33): Input/inout port dsi_txcclk_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(61): Input/inout port clk_lvds_7x is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(62): Input/inout port clk_27m is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(63): Input/inout port clk_54m is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(97): Input/inout port i_dqs_n_hi[1] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(98): Input/inout port i_dqs_n_lo[1] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(121): Input/inout port csi_ctl0_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(125): Input/inout port csi_ctl1_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(129): Input/inout port csi_scl_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(133): Input/inout port csi_sda_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(156): Input/inout port csi_rxd1_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(157): Input/inout port csi_rxd1_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(158): Input/inout port csi_rxd1_hs_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(165): Input/inout port csi_rxd2_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(166): Input/inout port csi_rxd2_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(167): Input/inout port csi_rxd2_hs_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(174): Input/inout port csi_rxd3_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(175): Input/inout port csi_rxd3_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(176): Input/inout port csi_rxd3_hs_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(235): Input/inout port dsi_txd0_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(236): Input/inout port dsi_txd0_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(237): Input/inout port dsi_txd1_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(238): Input/inout port dsi_txd1_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(239): Input/inout port dsi_txd2_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(240): Input/inout port dsi_txd2_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(241): Input/inout port dsi_txd3_lp_p_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(242): Input/inout port dsi_txd3_lp_n_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(247): Input/inout port uart_rx_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(257): Input/inout port cmos_sdat_IN is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(262): Input/inout port cmos_pclk is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(263): Input/inout port cmos_vsync is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(264): Input/inout port cmos_href is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(265): Input/inout port cmos_data[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(266): Input/inout port cmos_ctl1 is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(316): Input/inout port lcd_tp_sda_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(320): Input/inout port lcd_tp_scl_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(324): Input/inout port lcd_tp_int_i is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(344): Input/inout port lcd_b7_0_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(345): Input/inout port lcd_g7_0_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : D:\temp_colorbar\Ti60F225_AD2020_LVDS_Display\example_top.v(346): Input/inout port lcd_r7_0_i[7] is unconnected and will be removed. [VDB-8003]
WARNING  : CE port of EFX_DSP48 instance mult_184 is permanently disabled [VDB-8002]
WARNING  : Found 99 warnings in the post-synthesis netlist.
INFO     : VDB Netlist Checker took 0.0266479 seconds.
INFO     : 	VDB Netlist Checker took 0.0625 seconds (approximately) in total CPU time.
INFO     : VDB Netlist Checker virtual memory usage: begin = 352.572 MB, end = 352.588 MB, delta = 0.016 MB
INFO     : 	VDB Netlist Checker peak virtual memory usage = 410.02 MB
INFO     : VDB Netlist Checker resident set memory usage: begin = 366.496 MB, end = 366.512 MB, delta = 0.016 MB
INFO     : 	VDB Netlist Checker peak resident set memory usage = 389.472 MB
INFO     : ***** Ending VDB Netlist Checker ... *****
INFO     : Reading core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Successfully read core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Net cmos_pclk promoted to global due to interface constraints.
INFO     : Net clk_pixel_10x promoted to global due to interface constraints.
INFO     : Net clk_lvds_7x promoted to global due to interface constraints.
INFO     : Net clk_27m promoted to global due to interface constraints.
INFO     : Net dsi_serclk_i promoted to global due to interface constraints.
INFO     : Net dsi_byteclk_i promoted to global due to interface constraints.
INFO     : Net dsi_txcclk_i promoted to global due to interface constraints.
INFO     : Found 0 constant generator nets.
INFO     : Pass 0: Swept away 0 nets with no fanout.
INFO     : logical_block #0(clk_24m) has no fanout.
INFO     : Removing input.
INFO     : logical_block #1(clk_25m) has no fanout.
INFO     : Removing input.
INFO     : logical_block #5(clk_pixel_2x) has no fanout.
INFO     : Removing input.
INFO     : logical_block #9(dsi_refclk_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #33(clk_54m) has no fanout.
INFO     : Removing input.
INFO     : logical_block #149(i_dqs_n_hi[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #150(i_dqs_n_hi[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #151(i_dqs_n_lo[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #152(i_dqs_n_lo[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #171(csi_ctl0_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #174(csi_ctl1_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #177(csi_scl_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #180(csi_sda_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #202(csi_rxd1_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #203(csi_rxd1_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #204(csi_rxd1_hs_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #205(csi_rxd1_hs_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #206(csi_rxd1_hs_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #207(csi_rxd1_hs_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #208(csi_rxd1_hs_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #209(csi_rxd1_hs_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #210(csi_rxd1_hs_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #211(csi_rxd1_hs_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #215(csi_rxd2_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #216(csi_rxd2_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #217(csi_rxd2_hs_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #218(csi_rxd2_hs_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #219(csi_rxd2_hs_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #220(csi_rxd2_hs_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #221(csi_rxd2_hs_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #222(csi_rxd2_hs_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #223(csi_rxd2_hs_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #224(csi_rxd2_hs_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #228(csi_rxd3_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #229(csi_rxd3_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #230(csi_rxd3_hs_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #231(csi_rxd3_hs_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #232(csi_rxd3_hs_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #233(csi_rxd3_hs_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #234(csi_rxd3_hs_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #235(csi_rxd3_hs_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #236(csi_rxd3_hs_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #237(csi_rxd3_hs_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #310(dsi_txd0_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #311(dsi_txd0_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #312(dsi_txd1_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #313(dsi_txd1_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #314(dsi_txd2_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #315(dsi_txd2_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #316(dsi_txd3_lp_p_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #317(dsi_txd3_lp_n_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #318(uart_rx_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #327(cmos_sdat_IN) has no fanout.
INFO     : Removing input.
INFO     : logical_block #331(cmos_vsync) has no fanout.
INFO     : Removing input.
INFO     : logical_block #332(cmos_href) has no fanout.
INFO     : Removing input.
INFO     : logical_block #333(cmos_data[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #334(cmos_data[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #335(cmos_data[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #336(cmos_data[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #337(cmos_data[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #338(cmos_data[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #339(cmos_data[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #340(cmos_data[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #341(cmos_ctl1) has no fanout.
INFO     : Removing input.
INFO     : logical_block #439(lcd_tp_sda_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #442(lcd_tp_scl_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #445(lcd_tp_int_i) has no fanout.
INFO     : Removing input.
INFO     : logical_block #500(lcd_b7_0_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #501(lcd_b7_0_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #502(lcd_b7_0_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #503(lcd_b7_0_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #504(lcd_b7_0_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #505(lcd_b7_0_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #506(lcd_b7_0_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #507(lcd_b7_0_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #508(lcd_g7_0_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #509(lcd_g7_0_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #510(lcd_g7_0_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #511(lcd_g7_0_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #512(lcd_g7_0_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #513(lcd_g7_0_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #514(lcd_g7_0_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #515(lcd_g7_0_i[0]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #516(lcd_r7_0_i[7]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #517(lcd_r7_0_i[6]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #518(lcd_r7_0_i[5]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #519(lcd_r7_0_i[4]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #520(lcd_r7_0_i[3]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #521(lcd_r7_0_i[2]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #522(lcd_r7_0_i[1]) has no fanout.
INFO     : Removing input.
INFO     : logical_block #523(lcd_r7_0_i[0]) has no fanout.
INFO     : Removing input.
INFO     : Pass 0: Swept away 91 blocks with no fanout.
INFO     : Pass 1: Swept away 0 nets with no fanout.
INFO     : Pass 1: Swept away 0 blocks with no fanout.
INFO     : Swept away 0 nets and 91 blocks in total.
INFO     : Removed 0 LUT buffers
INFO     : Sweept away 91 nodes
INFO     : Successfully created VPR logical netlist from Verific binary DataBase file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.vdb".
INFO     : Netlist pre-processing took 0.254547 seconds.
INFO     : 	Netlist pre-processing took 0.28125 seconds (approximately) in total CPU time.
INFO     : Netlist pre-processing virtual memory usage: begin = 337.284 MB, end = 361.364 MB, delta = 24.08 MB
INFO     : 	Netlist pre-processing peak virtual memory usage = 410.02 MB
INFO     : Netlist pre-processing resident set memory usage: begin = 351.152 MB, end = 374.972 MB, delta = 23.82 MB
INFO     : 	Netlist pre-processing peak resident set memory usage = 389.472 MB
           ***** Ending stage netlist pre-processing *****
           
           ***** Beginning stage packed netlist loading ... *****
INFO     : Load Global Network took 1.11401 seconds.
INFO     : 	Load Global Network took 0.3125 seconds (approximately) in total CPU time.
INFO     : Load Global Network virtual memory usage: begin = 361.364 MB, end = 375.62 MB, delta = 14.256 MB
INFO     : 	Load Global Network peak virtual memory usage = 410.02 MB
INFO     : Load Global Network resident set memory usage: begin = 375.004 MB, end = 389.44 MB, delta = 14.436 MB
INFO     : 	Load Global Network peak resident set memory usage = 389.472 MB
INFO     : Reading C:/Efinity/2025.1/arch/.\rr_pb_hier_mapping.xml
INFO     : Read proto netlist file D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.net_proto
INFO     : Read proto netlist for file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.net_proto" took 0.014 seconds
INFO     : Setup net and block data structure took 0.14 seconds
INFO     : Reading core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Successfully read core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Packed netlist loading took 1.5591 seconds.
INFO     : 	Packed netlist loading took 0.75 seconds (approximately) in total CPU time.
INFO     : Packed netlist loading virtual memory usage: begin = 361.364 MB, end = 406.692 MB, delta = 45.328 MB
INFO     : 	Packed netlist loading peak virtual memory usage = 410.02 MB
INFO     : Packed netlist loading resident set memory usage: begin = 374.972 MB, end = 417.668 MB, delta = 42.696 MB
INFO     : 	Packed netlist loading peak resident set memory usage = 417.672 MB
           ***** Ending stage packed netlist loading *****
           
INFO     : Run checks after load placement.
INFO     : Reading core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Successfully read core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
WARNING  : Clock net led_o[5] should not drive a normal pad, led_o[5]. Clock should only drive clkout pads defined in Interface Designer.
INFO     : Successfully loaded placement data from "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.place".
           ***** Beginning stage bitstream generation ... *****
INFO     : Reading core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Successfully read core interface constraints file "D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv"
INFO     : Finished writing bitstream file D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/work_pnr\Ti60_Demo.lbf.
INFO     : Bitstream generation took 2.2761 seconds.
INFO     : 	Bitstream generation took 2.17188 seconds (approximately) in total CPU time.
INFO     : Bitstream generation virtual memory usage: begin = 371.18 MB, end = 450.932 MB, delta = 79.752 MB
INFO     : 	Bitstream generation peak virtual memory usage = 450.932 MB
INFO     : Bitstream generation resident set memory usage: begin = 382.504 MB, end = 463.432 MB, delta = 80.928 MB
INFO     : 	Bitstream generation peak resident set memory usage = 463.436 MB
           ***** Ending stage bitstream generation *****
           
INFO     : The entire flow of EFX_PNR took 58.6875 seconds.
INFO     : 	The entire flow of EFX_PNR took 99.9531 seconds (approximately) in total CPU time.
INFO     : The entire flow of EFX_PNR virtual memory usage: begin = 19.688 MB, end = 450.932 MB, delta = 431.244 MB
INFO     : 	The entire flow of EFX_PNR peak virtual memory usage = 450.932 MB
INFO     : The entire flow of EFX_PNR resident set memory usage: begin = 19.604 MB, end = 463.456 MB, delta = 443.852 MB
INFO     : 	The entire flow of EFX_PNR peak resident set memory usage = 463.46 MB
