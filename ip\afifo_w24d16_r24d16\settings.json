{"args": ["-o", "afifo_w24d16_r24d16", "--base_path", "E:\\fpga\\yilingsi\\A2020_demo\\Ti60F225_A2020_LVDS_Display_30FPS\\Ti60F225_AD2020_LVDS_Display_30FPS\\ip", "--vlnv", {"vendor": "efinixinc.com", "library": "memory", "name": "efx_fifo_top", "version": "8.0"}], "conf": {"SYNC_CLK": "0", "SYNC_STAGE": "2", "DEPTH_2": "4", "DATA_WIDTH": "24", "MODE": "\"FWFT\"", "OUTPUT_REG": "0", "PROG_FULL_ASSERT": "13", "PROGRAMMABLE_FULL": "\"NONE\"", "PFN_INTERNAL": "3", "PEA_INTERNAL": "0", "PEN_INTERNAL": "0", "PROGRAMMABLE_EMPTY": "\"NONE\"", "OPTIONAL_FLAGS": "0", "PIPELINE_REG": "1", "ASYM_WIDTH_RATIO": "4", "BYPASS_RESET_SYNC": "0", "ENDIANESS": "0", "RAM_STYLE": "\"block_ram\"", "OVERFLOW_PROTECT": "0", "UNDERFLOW_PROTECT": "0"}, "output": {"external_source_source": ["afifo_w24d16_r24d16\\afifo_w24d16_r24d16_tmpl.sv", "afifo_w24d16_r24d16\\afifo_w24d16_r24d16_tmpl.vhd", "afifo_w24d16_r24d16\\afifo_w24d16_r24d16.sv", "afifo_w24d16_r24d16\\afifo_w24d16_r24d16_define.svh"], "external_example_example": ["afifo_w24d16_r24d16\\T20F256_devkit\\fifo_demo_top.v", "afifo_w24d16_r24d16\\T20F256_devkit\\fifo_demo_T20.sdc", "afifo_w24d16_r24d16\\T20F256_devkit\\efx_symmetric_width_fifo_top.sv", "afifo_w24d16_r24d16\\T20F256_devkit\\afifo_w24d16_r24d16.sv", "afifo_w24d16_r24d16\\T20F256_devkit\\afifo_w24d16_r24d16_define.svh", "afifo_w24d16_r24d16\\T20F256_devkit\\fifo_demo.peri.xml", "afifo_w24d16_r24d16\\T20F256_devkit\\fifo_demo.xml"], "external_example_2": ["afifo_w24d16_r24d16\\Ti60F225_devkit\\fifo_demo_top.v", "afifo_w24d16_r24d16\\Ti60F225_devkit\\fifo_demo_Ti60.sdc", "afifo_w24d16_r24d16\\Ti60F225_devkit\\efx_symmetric_width_fifo_top.sv", "afifo_w24d16_r24d16\\Ti60F225_devkit\\afifo_w24d16_r24d16.sv", "afifo_w24d16_r24d16\\Ti60F225_devkit\\afifo_w24d16_r24d16_define.svh", "afifo_w24d16_r24d16\\Ti60F225_devkit\\fifo_demo.peri.xml", "afifo_w24d16_r24d16\\Ti60F225_devkit\\fifo_demo.xml"], "external_testbench_testbench": ["afifo_w24d16_r24d16\\Testbench\\fifo_tb.sv", "afifo_w24d16_r24d16\\Testbench\\xrun.sh", "afifo_w24d16_r24d16\\Testbench\\msim.sh", "afifo_w24d16_r24d16\\Testbench\\flist", "afifo_w24d16_r24d16\\Testbench\\modelsim.do", "afifo_w24d16_r24d16\\Testbench\\afifo_w24d16_r24d16.sv", "afifo_w24d16_r24d16\\Testbench\\afifo_w24d16_r24d16_define.svh"]}, "ooc_synthesis": {}, "sw_version": "2025.1.110.2.15", "generated_date": "2025-10-06T09:30:29.129144+00:00"}