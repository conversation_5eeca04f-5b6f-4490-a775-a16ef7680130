
// Efinity Top-level template
// Version: 2025.1.110.2.15
// Date: 2025-07-22 18:24

// Copyright (C) 2013 - 2025 Efinix Inc. All rights reserved.

// This file may be used as a starting point for Efinity synthesis top-level target.
// The port list here matches what is expected by Efinity constraint files generated
// by the Efinity Interface Designer.

// To use this:
//     #1)  Save this file with a different name to a different directory, where source files are kept.
//              Example: you may wish to save as Ti60_Demo.v
//     #2)  Add the newly saved file into Efinity project as design file
//     #3)  Edit the top level entity in Efinity project to:  Ti60_Demo
//     #4)  Insert design content.


module Ti60_Demo
(
  (* syn_peri_port = 0 *) input clk_24m,
  (* syn_peri_port = 0 *) input clk_25m,
  (* syn_peri_port = 0 *) input cmos_ctl1,
  (* syn_peri_port = 0 *) input [7:0] cmos_data,
  (* syn_peri_port = 0 *) input cmos_href,
  (* syn_peri_port = 0 *) input cmos_sdat_IN,
  (* syn_peri_port = 0 *) input cmos_vsync,
  (* syn_peri_port = 0 *) input sys_pll_lock,
  (* syn_peri_port = 0 *) input ddr_pll_lock,
  (* syn_peri_port = 0 *) input dsi_pll_lock,
  (* syn_peri_port = 0 *) input lvds_pll_lock,
  (* syn_peri_port = 0 *) input dsi_serclk_i,
  (* syn_peri_port = 0 *) input dsi_txcclk_i,
  (* syn_peri_port = 0 *) input dsi_refclk_i,
  (* syn_peri_port = 0 *) input tdqss_clk,
  (* syn_peri_port = 0 *) input tac_clk,
  (* syn_peri_port = 0 *) input dsi_byteclk_i,
  (* syn_peri_port = 0 *) input clk_pixel,
  (* syn_peri_port = 0 *) input clk_sys,
  (* syn_peri_port = 0 *) input clk_pixel_2x,
  (* syn_peri_port = 0 *) input clk_pixel_10x,
  (* syn_peri_port = 0 *) input clk_lvds_7x,
  (* syn_peri_port = 0 *) input twd_clk,
  (* syn_peri_port = 0 *) input csi_rxc_i,
  (* syn_peri_port = 0 *) input clk_lvds_1x,
  (* syn_peri_port = 0 *) input clk_27m,
  (* syn_peri_port = 0 *) input core_clk,
  (* syn_peri_port = 0 *) input cmos_pclk,
  (* syn_peri_port = 0 *) input clk_54m,
  (* syn_peri_port = 0 *) input csi_ctl0_i,
  (* syn_peri_port = 0 *) input csi_ctl1_i,
  (* syn_peri_port = 0 *) input csi_scl_i,
  (* syn_peri_port = 0 *) input csi_sda_i,
  (* syn_peri_port = 0 *) input [15:0] i_dq_hi,
  (* syn_peri_port = 0 *) input [15:0] i_dq_lo,
  (* syn_peri_port = 0 *) input [1:0] i_dqs_hi,
  (* syn_peri_port = 0 *) input [1:0] i_dqs_lo,
  (* syn_peri_port = 0 *) input [1:0] i_dqs_n_hi,
  (* syn_peri_port = 0 *) input [1:0] i_dqs_n_lo,
  (* syn_peri_port = 0 *) input [7:0] lcd_b7_0_i,
  (* syn_peri_port = 0 *) input [7:0] lcd_g7_0_i,
  (* syn_peri_port = 0 *) input [7:0] lcd_r7_0_i,
  (* syn_peri_port = 0 *) input lcd_tp_int_i,
  (* syn_peri_port = 0 *) input lcd_tp_scl_i,
  (* syn_peri_port = 0 *) input lcd_tp_sda_i,
  (* syn_peri_port = 0 *) input uart_rx_i,
  (* syn_peri_port = 0 *) input csi_rxc_lp_n_i,
  (* syn_peri_port = 0 *) input csi_rxc_lp_p_i,
  (* syn_peri_port = 0 *) input [7:0] csi_rxd0_hs_i,
  (* syn_peri_port = 0 *) input csi_rxd0_lp_n_i,
  (* syn_peri_port = 0 *) input csi_rxd0_lp_p_i,
  (* syn_peri_port = 0 *) input [7:0] csi_rxd1_hs_i,
  (* syn_peri_port = 0 *) input csi_rxd1_lp_n_i,
  (* syn_peri_port = 0 *) input csi_rxd1_lp_p_i,
  (* syn_peri_port = 0 *) input [7:0] csi_rxd2_hs_i,
  (* syn_peri_port = 0 *) input csi_rxd2_lp_n_i,
  (* syn_peri_port = 0 *) input csi_rxd2_lp_p_i,
  (* syn_peri_port = 0 *) input [7:0] csi_rxd3_hs_i,
  (* syn_peri_port = 0 *) input csi_rxd3_lp_n_i,
  (* syn_peri_port = 0 *) input csi_rxd3_lp_p_i,
  (* syn_peri_port = 0 *) input dsi_txd0_lp_n_i,
  (* syn_peri_port = 0 *) input dsi_txd0_lp_p_i,
  (* syn_peri_port = 0 *) input dsi_txd1_lp_n_i,
  (* syn_peri_port = 0 *) input dsi_txd1_lp_p_i,
  (* syn_peri_port = 0 *) input dsi_txd2_lp_n_i,
  (* syn_peri_port = 0 *) input dsi_txd2_lp_p_i,
  (* syn_peri_port = 0 *) input dsi_txd3_lp_n_i,
  (* syn_peri_port = 0 *) input dsi_txd3_lp_p_i,
  (* syn_peri_port = 0 *) output cmos_ctl2,
  (* syn_peri_port = 0 *) output cmos_sclk,
  (* syn_peri_port = 0 *) output cmos_sdat_OUT,
  (* syn_peri_port = 0 *) output cmos_sdat_OE,
  (* syn_peri_port = 0 *) output [5:0] led_o,
  (* syn_peri_port = 0 *) output sys_pll_rstn_o,
  (* syn_peri_port = 0 *) output ddr_pll_rstn_o,
  (* syn_peri_port = 0 *) output [2:0] shift,
  (* syn_peri_port = 0 *) output shift_ena,
  (* syn_peri_port = 0 *) output [4:0] shift_sel,
  (* syn_peri_port = 0 *) output dsi_pll_rstn_o,
  (* syn_peri_port = 0 *) output lvds_pll_rstn_o,
  (* syn_peri_port = 0 *) output hdmi_txc_oe,
  (* syn_peri_port = 0 *) output [9:0] hdmi_txc_o,
  (* syn_peri_port = 0 *) output hdmi_txc_rst_o,
  (* syn_peri_port = 0 *) output hdmi_txd0_oe,
  (* syn_peri_port = 0 *) output [9:0] hdmi_txd0_o,
  (* syn_peri_port = 0 *) output hdmi_txd0_rst_o,
  (* syn_peri_port = 0 *) output hdmi_txd1_oe,
  (* syn_peri_port = 0 *) output [9:0] hdmi_txd1_o,
  (* syn_peri_port = 0 *) output hdmi_txd1_rst_o,
  (* syn_peri_port = 0 *) output hdmi_txd2_oe,
  (* syn_peri_port = 0 *) output [9:0] hdmi_txd2_o,
  (* syn_peri_port = 0 *) output hdmi_txd2_rst_o,
  (* syn_peri_port = 0 *) output lvds_txc_oe,
  (* syn_peri_port = 0 *) output [6:0] lvds_txc_o,
  (* syn_peri_port = 0 *) output lvds_txc_rst_o,
  (* syn_peri_port = 0 *) output lvds_txd0_oe,
  (* syn_peri_port = 0 *) output [6:0] lvds_txd0_o,
  (* syn_peri_port = 0 *) output lvds_txd0_rst_o,
  (* syn_peri_port = 0 *) output lvds_txd1_oe,
  (* syn_peri_port = 0 *) output [6:0] lvds_txd1_o,
  (* syn_peri_port = 0 *) output lvds_txd1_rst_o,
  (* syn_peri_port = 0 *) output lvds_txd2_oe,
  (* syn_peri_port = 0 *) output [6:0] lvds_txd2_o,
  (* syn_peri_port = 0 *) output lvds_txd2_rst_o,
  (* syn_peri_port = 0 *) output lvds_txd3_oe,
  (* syn_peri_port = 0 *) output [6:0] lvds_txd3_o,
  (* syn_peri_port = 0 *) output lvds_txd3_rst_o,
  (* syn_peri_port = 0 *) output [15:0] addr,
  (* syn_peri_port = 0 *) output [2:0] ba,
  (* syn_peri_port = 0 *) output cas,
  (* syn_peri_port = 0 *) output cke,
  (* syn_peri_port = 0 *) output clk_n_hi,
  (* syn_peri_port = 0 *) output clk_n_lo,
  (* syn_peri_port = 0 *) output clk_p_hi,
  (* syn_peri_port = 0 *) output clk_p_lo,
  (* syn_peri_port = 0 *) output cmos_ctl3,
  (* syn_peri_port = 0 *) output cs,
  (* syn_peri_port = 0 *) output csi_ctl0_o,
  (* syn_peri_port = 0 *) output csi_ctl0_oe,
  (* syn_peri_port = 0 *) output csi_ctl1_o,
  (* syn_peri_port = 0 *) output csi_ctl1_oe,
  (* syn_peri_port = 0 *) output csi_scl_o,
  (* syn_peri_port = 0 *) output csi_scl_oe,
  (* syn_peri_port = 0 *) output csi_sda_o,
  (* syn_peri_port = 0 *) output csi_sda_oe,
  (* syn_peri_port = 0 *) output [1:0] o_dm_hi,
  (* syn_peri_port = 0 *) output [1:0] o_dm_lo,
  (* syn_peri_port = 0 *) output [15:0] o_dq_hi,
  (* syn_peri_port = 0 *) output [15:0] o_dq_lo,
  (* syn_peri_port = 0 *) output [15:0] o_dq_oe,
  (* syn_peri_port = 0 *) output [1:0] o_dqs_hi,
  (* syn_peri_port = 0 *) output [1:0] o_dqs_lo,
  (* syn_peri_port = 0 *) output [1:0] o_dqs_oe,
  (* syn_peri_port = 0 *) output [1:0] o_dqs_n_hi,
  (* syn_peri_port = 0 *) output [1:0] o_dqs_n_lo,
  (* syn_peri_port = 0 *) output [1:0] o_dqs_n_oe,
  (* syn_peri_port = 0 *) output dsi_pwm_o,
  (* syn_peri_port = 0 *) output dsi_resetn_o,
  (* syn_peri_port = 0 *) output [7:0] lcd_b7_0_o,
  (* syn_peri_port = 0 *) output [7:0] lcd_b7_0_oe,
  (* syn_peri_port = 0 *) output lcd_blen_o,
  (* syn_peri_port = 0 *) output lcd_de_o,
  (* syn_peri_port = 0 *) output [7:0] lcd_g7_0_o,
  (* syn_peri_port = 0 *) output [7:0] lcd_g7_0_oe,
  (* syn_peri_port = 0 *) output lcd_hs_o,
  (* syn_peri_port = 0 *) output lcd_pwm_o,
  (* syn_peri_port = 0 *) output [7:0] lcd_r7_0_o,
  (* syn_peri_port = 0 *) output [7:0] lcd_r7_0_oe,
  (* syn_peri_port = 0 *) output lcd_tp_int_o,
  (* syn_peri_port = 0 *) output lcd_tp_int_oe,
  (* syn_peri_port = 0 *) output lcd_tp_rst_o,
  (* syn_peri_port = 0 *) output lcd_tp_scl_o,
  (* syn_peri_port = 0 *) output lcd_tp_scl_oe,
  (* syn_peri_port = 0 *) output lcd_tp_sda_o,
  (* syn_peri_port = 0 *) output lcd_tp_sda_oe,
  (* syn_peri_port = 0 *) output lcd_vs_o,
  (* syn_peri_port = 0 *) output odt,
  (* syn_peri_port = 0 *) output ras,
  (* syn_peri_port = 0 *) output reset,
  (* syn_peri_port = 0 *) output spi_sck_o,
  (* syn_peri_port = 0 *) output spi_ssn_o,
  (* syn_peri_port = 0 *) output uart_tx_o,
  (* syn_peri_port = 0 *) output we,
  (* syn_peri_port = 0 *) output csi_rxc_hs_en_o,
  (* syn_peri_port = 0 *) output csi_rxc_hs_term_en_o,
  (* syn_peri_port = 0 *) output csi_rxd0_hs_en_o,
  (* syn_peri_port = 0 *) output csi_rxd0_hs_term_en_o,
  (* syn_peri_port = 0 *) output csi_rxd0_rst_o,
  (* syn_peri_port = 0 *) output csi_rxd1_hs_en_o,
  (* syn_peri_port = 0 *) output csi_rxd1_hs_term_en_o,
  (* syn_peri_port = 0 *) output csi_rxd1_rst_o,
  (* syn_peri_port = 0 *) output csi_rxd2_hs_en_o,
  (* syn_peri_port = 0 *) output csi_rxd2_hs_term_en_o,
  (* syn_peri_port = 0 *) output csi_rxd2_rst_o,
  (* syn_peri_port = 0 *) output csi_rxd3_hs_en_o,
  (* syn_peri_port = 0 *) output csi_rxd3_hs_term_en_o,
  (* syn_peri_port = 0 *) output csi_rxd3_rst_o,
  (* syn_peri_port = 0 *) output dsi_txc_hs_oe,
  (* syn_peri_port = 0 *) output [7:0] dsi_txc_hs_o,
  (* syn_peri_port = 0 *) output dsi_txc_lp_n_oe,
  (* syn_peri_port = 0 *) output dsi_txc_lp_n_o,
  (* syn_peri_port = 0 *) output dsi_txc_lp_p_oe,
  (* syn_peri_port = 0 *) output dsi_txc_lp_p_o,
  (* syn_peri_port = 0 *) output dsi_txc_rst_o,
  (* syn_peri_port = 0 *) output dsi_txd0_hs_oe,
  (* syn_peri_port = 0 *) output [7:0] dsi_txd0_hs_o,
  (* syn_peri_port = 0 *) output dsi_txd0_lp_n_oe,
  (* syn_peri_port = 0 *) output dsi_txd0_lp_n_o,
  (* syn_peri_port = 0 *) output dsi_txd0_lp_p_oe,
  (* syn_peri_port = 0 *) output dsi_txd0_lp_p_o,
  (* syn_peri_port = 0 *) output dsi_txd0_rst_o,
  (* syn_peri_port = 0 *) output dsi_txd1_hs_oe,
  (* syn_peri_port = 0 *) output [7:0] dsi_txd1_hs_o,
  (* syn_peri_port = 0 *) output dsi_txd1_lp_n_oe,
  (* syn_peri_port = 0 *) output dsi_txd1_lp_n_o,
  (* syn_peri_port = 0 *) output dsi_txd1_lp_p_oe,
  (* syn_peri_port = 0 *) output dsi_txd1_lp_p_o,
  (* syn_peri_port = 0 *) output dsi_txd1_rst_o,
  (* syn_peri_port = 0 *) output dsi_txd2_hs_oe,
  (* syn_peri_port = 0 *) output [7:0] dsi_txd2_hs_o,
  (* syn_peri_port = 0 *) output dsi_txd2_lp_n_oe,
  (* syn_peri_port = 0 *) output dsi_txd2_lp_n_o,
  (* syn_peri_port = 0 *) output dsi_txd2_lp_p_oe,
  (* syn_peri_port = 0 *) output dsi_txd2_lp_p_o,
  (* syn_peri_port = 0 *) output dsi_txd2_rst_o,
  (* syn_peri_port = 0 *) output dsi_txd3_hs_oe,
  (* syn_peri_port = 0 *) output [7:0] dsi_txd3_hs_o,
  (* syn_peri_port = 0 *) output dsi_txd3_lp_n_oe,
  (* syn_peri_port = 0 *) output dsi_txd3_lp_n_o,
  (* syn_peri_port = 0 *) output dsi_txd3_lp_p_oe,
  (* syn_peri_port = 0 *) output dsi_txd3_lp_p_o,
  (* syn_peri_port = 0 *) output dsi_txd3_rst_o
);


endmodule

