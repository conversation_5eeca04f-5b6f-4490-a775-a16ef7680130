<?xml version="1.0" encoding="UTF-8"?>
<efxpt:design_db name="fifo_demo" device_def="T20F256" location="/projects/SWIP/syng/efinity/2021.M.165/project/t20_fifo_demo" version="2021.M.165" db_version="********" last_change_date="Tue Aug 10 01:04:53 2021" xmlns:efxpt="http://www.efinixinc.com/peri_design_db" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.efinixinc.com/peri_design_db peri_design_db.xsd ">
    <efxpt:device_info>
        <efxpt:iobank_info>
            <efxpt:iobank name="1A" iostd="3.3 V LVTTL / LVCMOS"/>
            <efxpt:iobank name="1B_1C" iostd="3.3 V LVTTL / LVCMOS"/>
            <efxpt:iobank name="1D_1E" iostd="3.3 V LVTTL / LVCMOS"/>
            <efxpt:iobank name="3A_3B_3C" iostd="3.3 V LVTTL / LVCMOS"/>
            <efxpt:iobank name="3D_3E" iostd="3.3 V LVTTL / LVCMOS"/>
            <efxpt:iobank name="4A" iostd="3.3 V LVTTL / LVCMOS"/>
            <efxpt:iobank name="4B" iostd="3.3 V LVTTL / LVCMOS"/>
            <efxpt:iobank name="BR" iostd="1.2 V"/>
            <efxpt:iobank name="TL" iostd="1.2 V"/>
            <efxpt:iobank name="TR" iostd="1.2 V"/>
        </efxpt:iobank_info>
        <efxpt:ctrl_info>
            <efxpt:ctrl name="cfg" ctrl_def="CONFIG_CTRL0" clock_name="" is_clk_invert="false" cbsel_bus_name="cfg_CBSEL" config_ctrl_name="cfg_CONFIG" ena_capture_name="cfg_ENA" error_status_name="cfg_ERROR" um_signal_status_name="cfg_USR_STATUS" is_remote_update_enable="false" is_user_mode_enable="false"/>
        </efxpt:ctrl_info>
    </efxpt:device_info>
    <efxpt:gpio_info device_def="T20F256">
        <efxpt:gpio name="led_blink" gpio_def="GPIOR_104" mode="output" bus_name="" is_lvds_gpio="false" io_standard="3.3 V LVTTL / LVCMOS">
            <efxpt:output_config name="led_blink" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" drive_strength="1"/>
        </efxpt:gpio>
        <efxpt:gpio name="led_fifo_empty" gpio_def="GPIOR_118" mode="output" bus_name="" is_lvds_gpio="false" io_standard="3.3 V LVTTL / LVCMOS">
            <efxpt:output_config name="led_fifo_empty" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" drive_strength="1"/>
        </efxpt:gpio>
        <efxpt:gpio name="led_fifo_full" gpio_def="GPIOR_117" mode="output" bus_name="" is_lvds_gpio="false" io_standard="3.3 V LVTTL / LVCMOS">
            <efxpt:output_config name="led_fifo_full" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" drive_strength="1"/>
        </efxpt:gpio>
        <efxpt:gpio name="led_rdata_error" gpio_def="GPIOR_105" mode="output" bus_name="" is_lvds_gpio="false" io_standard="3.3 V LVTTL / LVCMOS">
            <efxpt:output_config name="led_rdata_error" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" drive_strength="1"/>
        </efxpt:gpio>
        <efxpt:gpio name="ref_clk" gpio_def="GPIOL_75" mode="input" bus_name="" is_lvds_gpio="false" io_standard="3.3 V LVTTL / LVCMOS">
            <efxpt:input_config name="ref_clk" name_ddio_lo="" conn_type="pll_clkin" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none"/>
        </efxpt:gpio>
        <efxpt:gpio name="stop_fifo_rd" gpio_def="GPIOL_05" mode="input" bus_name="" is_lvds_gpio="false" io_standard="3.3 V LVTTL / LVCMOS">
            <efxpt:input_config name="stop_fifo_rd" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none"/>
        </efxpt:gpio>
        <efxpt:gpio name="stop_fifo_wr" gpio_def="GPIOL_04" mode="input" bus_name="" is_lvds_gpio="false" io_standard="3.3 V LVTTL / LVCMOS">
            <efxpt:input_config name="stop_fifo_wr" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none"/>
        </efxpt:gpio>
        <efxpt:gpio name="sys_rst_n" gpio_def="GPIOL_02" mode="input" bus_name="" is_lvds_gpio="false" io_standard="3.3 V LVTTL / LVCMOS">
            <efxpt:input_config name="sys_rst_n" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none"/>
        </efxpt:gpio>
        <efxpt:global_unused_config state="input with weak pullup"/>
    </efxpt:gpio_info>
    <efxpt:pll_info>
        <efxpt:pll name="pll_inst1" pll_def="PLL_TL0" ref_clock_name="" ref_clock_freq="74.50" multiplier="141" pre_divider="7" post_divider="2" reset_name="pll_reset" locked_name="pll_lock" is_ipfrz="false" is_bypass_lock="true">
            <efxpt:output_clock name="pll_clkout_0" number="0" out_divider="6" adv_out_phase_shift="0"/>
            <efxpt:output_clock name="pll_clkout_1" number="1" out_divider="12" adv_out_phase_shift="0"/>
            <efxpt:output_clock name="led_clk" number="2" out_divider="251" adv_out_phase_shift="0"/>
            <efxpt:adv_prop ref_clock_mode="external" ref_clock1_name="" ext_ref_clock_id="3" clksel_name="" feedback_clock_name="" feedback_mode="internal"/>
        </efxpt:pll>
    </efxpt:pll_info>
    <efxpt:lvds_info/>
    <efxpt:jtag_info/>
</efxpt:design_db>
