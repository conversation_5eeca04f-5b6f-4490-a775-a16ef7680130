
///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:23:32
///////////////////////////////////

[EFX-0011 VERI-WARNING] port 'probe1' remains unconnected for this instance (VERI-1927) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:108)
[EFX-0011 VERI-WARNING] input port 'probe1[0]' remains unconnected for this instance (VDB-1053) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0200 WARNING] Removing redundant signal : compared_in[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5478)
[EFX-0200 WARNING] Removing redundant signal : mask_in[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5479)
[EFX-0200 WARNING] Removing redundant signal : trigger_in. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5689)
[EFX-0200 WARNING] Removing redundant signal : din[1]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4581)
[EFX-0200 WARNING] Removing redundant signal : trig_out. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4860)
[EFX-0200 WARNING] Removing redundant signal : trig_out_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4861)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1014)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1016)
[EFX-0200 WARNING] Removing redundant signal : bscan_SEL. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1017)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1021)
[EFX-0200 WARNING] Removing redundant signal : trig_in_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1032)
[EFX-0200 WARNING] Removing redundant signal : mux_capture_cmp[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:3444)
[EFX-0200 WARNING] Removing redundant signal : bscan_CAPTURE. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:265)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:266)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:268)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:273)
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_in=0).
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_out_ack=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe1[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe2[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe3[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe4[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe5[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe6[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe7[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe8[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe9[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe10[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_DRCK is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_RUNTEST is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_TMS is unconnected and will be removed 

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:23:46
///////////////////////////////////

[EFX-0256 WARNING] The primary output port 'csi_ctl0_o' wire 'csi_ctl0_o' is not driven.
[EFX-0256 WARNING] The primary output port 'csi_ctl1_o' wire 'csi_ctl1_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_rst_o' wire 'dsi_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_oe' wire 'dsi_txc_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_o' wire 'dsi_txc_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_oe' wire 'dsi_txc_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_o' wire 'dsi_txc_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_oe' wire 'dsi_txc_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[7]' wire 'dsi_txc_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[6]' wire 'dsi_txc_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[5]' wire 'dsi_txc_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[4]' wire 'dsi_txc_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[3]' wire 'dsi_txc_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[2]' wire 'dsi_txc_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[1]' wire 'dsi_txc_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[0]' wire 'dsi_txc_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_rst_o' wire 'dsi_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_oe' wire 'dsi_txd0_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[7]' wire 'dsi_txd0_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[6]' wire 'dsi_txd0_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[5]' wire 'dsi_txd0_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[4]' wire 'dsi_txd0_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[3]' wire 'dsi_txd0_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[2]' wire 'dsi_txd0_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[1]' wire 'dsi_txd0_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[0]' wire 'dsi_txd0_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_oe' wire 'dsi_txd0_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_o' wire 'dsi_txd0_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_oe' wire 'dsi_txd0_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_o' wire 'dsi_txd0_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_rst_o' wire 'dsi_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_oe' wire 'dsi_txd1_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_o' wire 'dsi_txd1_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_oe' wire 'dsi_txd1_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_o' wire 'dsi_txd1_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_oe' wire 'dsi_txd1_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[7]' wire 'dsi_txd1_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[6]' wire 'dsi_txd1_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[5]' wire 'dsi_txd1_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[4]' wire 'dsi_txd1_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[3]' wire 'dsi_txd1_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[2]' wire 'dsi_txd1_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[1]' wire 'dsi_txd1_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[0]' wire 'dsi_txd1_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_rst_o' wire 'dsi_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_oe' wire 'dsi_txd2_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_o' wire 'dsi_txd2_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_oe' wire 'dsi_txd2_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_o' wire 'dsi_txd2_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_oe' wire 'dsi_txd2_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[7]' wire 'dsi_txd2_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[6]' wire 'dsi_txd2_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[5]' wire 'dsi_txd2_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[4]' wire 'dsi_txd2_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[3]' wire 'dsi_txd2_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[2]' wire 'dsi_txd2_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[1]' wire 'dsi_txd2_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[0]' wire 'dsi_txd2_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_rst_o' wire 'dsi_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_oe' wire 'dsi_txd3_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_o' wire 'dsi_txd3_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_oe' wire 'dsi_txd3_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_o' wire 'dsi_txd3_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_oe' wire 'dsi_txd3_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[7]' wire 'dsi_txd3_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[6]' wire 'dsi_txd3_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[5]' wire 'dsi_txd3_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[4]' wire 'dsi_txd3_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[3]' wire 'dsi_txd3_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[2]' wire 'dsi_txd3_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[1]' wire 'dsi_txd3_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[0]' wire 'dsi_txd3_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'uart_tx_o' wire 'uart_tx_o' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sclk' wire 'cmos_sclk' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OUT' wire 'cmos_sdat_OUT' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OE' wire 'cmos_sdat_OE' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl2' wire 'cmos_ctl2' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl3' wire 'cmos_ctl3' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_oe' wire 'lvds_txc_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[6]' wire 'lvds_txc_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[5]' wire 'lvds_txc_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[4]' wire 'lvds_txc_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[3]' wire 'lvds_txc_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[2]' wire 'lvds_txc_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[1]' wire 'lvds_txc_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[0]' wire 'lvds_txc_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_rst_o' wire 'lvds_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_oe' wire 'lvds_txd0_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[6]' wire 'lvds_txd0_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[5]' wire 'lvds_txd0_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[4]' wire 'lvds_txd0_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[3]' wire 'lvds_txd0_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[2]' wire 'lvds_txd0_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[1]' wire 'lvds_txd0_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[0]' wire 'lvds_txd0_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_rst_o' wire 'lvds_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_oe' wire 'lvds_txd1_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[6]' wire 'lvds_txd1_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[5]' wire 'lvds_txd1_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[4]' wire 'lvds_txd1_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[3]' wire 'lvds_txd1_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[2]' wire 'lvds_txd1_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[1]' wire 'lvds_txd1_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[0]' wire 'lvds_txd1_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_rst_o' wire 'lvds_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_oe' wire 'lvds_txd2_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[6]' wire 'lvds_txd2_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[5]' wire 'lvds_txd2_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[4]' wire 'lvds_txd2_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[3]' wire 'lvds_txd2_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[2]' wire 'lvds_txd2_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[1]' wire 'lvds_txd2_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[0]' wire 'lvds_txd2_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_rst_o' wire 'lvds_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_oe' wire 'lvds_txd3_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[6]' wire 'lvds_txd3_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[5]' wire 'lvds_txd3_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[4]' wire 'lvds_txd3_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[3]' wire 'lvds_txd3_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[2]' wire 'lvds_txd3_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[1]' wire 'lvds_txd3_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[0]' wire 'lvds_txd3_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_rst_o' wire 'lvds_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_o' wire 'lcd_tp_sda_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_oe' wire 'lcd_tp_sda_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_o' wire 'lcd_tp_scl_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_oe' wire 'lcd_tp_scl_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_o' wire 'lcd_tp_int_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_oe' wire 'lcd_tp_int_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_rst_o' wire 'lcd_tp_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_pwm_o' wire 'lcd_pwm_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_blen_o' wire 'lcd_blen_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_vs_o' wire 'lcd_vs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_hs_o' wire 'lcd_hs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_de_o' wire 'lcd_de_o' is not driven.
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][31]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][30]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][29]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][28]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wclk=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[9]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[10]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[11]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[12]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[13]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[14]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[15]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[16]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[17]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[18]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[19]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[20]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[21]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[22]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[23]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[24]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[25]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[26]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[27]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[28]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[29]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[30]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[31]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[32]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[33]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[34]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[35]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[36]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[37]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[38]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[39]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[40]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[41]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[42]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[43]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[44]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[45]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[46]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[47]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[48]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[49]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[50]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[51]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[52]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[53]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[54]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[55]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[56]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[57]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[58]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[59]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[60]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[61]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[62]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[63]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[64]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[65]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[66]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[67]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[68]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[69]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[70]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[71]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (we=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (re=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_push_payload[0]=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.a_rst_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.clk_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'fifo_ack' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_ar_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_aw_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'mux0' input pin tied to constant (b_rd_en=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[0]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[1]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[2]=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[3]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[2]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[3]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[5]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[6]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[7]=0).
[EFX-0266 WARNING] Module Instance 'u_wr_addr_fifo' input pin tied to constant (I_Wr_Data[42]=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[127]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[126]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[125]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[124]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[123]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[122]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:32:49
///////////////////////////////////

[EFX-0011 VERI-WARNING] port 'probe1' remains unconnected for this instance (VERI-1927) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:108)
[EFX-0011 VERI-WARNING] input port 'probe1[0]' remains unconnected for this instance (VDB-1053) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0200 WARNING] Removing redundant signal : compared_in[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5478)
[EFX-0200 WARNING] Removing redundant signal : mask_in[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5479)
[EFX-0200 WARNING] Removing redundant signal : trigger_in. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5689)
[EFX-0200 WARNING] Removing redundant signal : din[1]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4581)
[EFX-0200 WARNING] Removing redundant signal : trig_out. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4860)
[EFX-0200 WARNING] Removing redundant signal : trig_out_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4861)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1014)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1016)
[EFX-0200 WARNING] Removing redundant signal : bscan_SEL. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1017)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1021)
[EFX-0200 WARNING] Removing redundant signal : trig_in_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1032)
[EFX-0200 WARNING] Removing redundant signal : mux_capture_cmp[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:3444)
[EFX-0200 WARNING] Removing redundant signal : bscan_CAPTURE. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:265)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:266)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:268)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:273)
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_in=0).
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_out_ack=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe1[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe2[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe3[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe4[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe5[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe6[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe7[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe8[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe9[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe10[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_DRCK is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_RUNTEST is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_TMS is unconnected and will be removed 

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:33:03
///////////////////////////////////


///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:34:46
///////////////////////////////////

[EFX-0011 VERI-WARNING] port 'probe1' remains unconnected for this instance (VERI-1927) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:108)
[EFX-0011 VERI-WARNING] input port 'probe1[0]' remains unconnected for this instance (VDB-1053) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0200 WARNING] Removing redundant signal : trigger_in. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5689)
[EFX-0200 WARNING] Removing redundant signal : din[8]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4581)
[EFX-0200 WARNING] Removing redundant signal : trig_out. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4860)
[EFX-0200 WARNING] Removing redundant signal : trig_out_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4861)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1014)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1016)
[EFX-0200 WARNING] Removing redundant signal : bscan_SEL. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1017)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1021)
[EFX-0200 WARNING] Removing redundant signal : trig_in_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1032)
[EFX-0200 WARNING] Removing redundant signal : mux_capture_cmp[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:3444)
[EFX-0200 WARNING] Removing redundant signal : bscan_CAPTURE. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:265)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:266)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:268)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:273)
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_in=0).
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_out_ack=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe1[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe2[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe3[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe4[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe5[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe6[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe7[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe8[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe9[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe10[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_DRCK is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_RUNTEST is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_TMS is unconnected and will be removed 

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:35:00
///////////////////////////////////

[EFX-0256 WARNING] The primary output port 'csi_ctl0_o' wire 'csi_ctl0_o' is not driven.
[EFX-0256 WARNING] The primary output port 'csi_ctl1_o' wire 'csi_ctl1_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_rst_o' wire 'dsi_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_oe' wire 'dsi_txc_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_o' wire 'dsi_txc_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_oe' wire 'dsi_txc_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_o' wire 'dsi_txc_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_oe' wire 'dsi_txc_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[7]' wire 'dsi_txc_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[6]' wire 'dsi_txc_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[5]' wire 'dsi_txc_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[4]' wire 'dsi_txc_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[3]' wire 'dsi_txc_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[2]' wire 'dsi_txc_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[1]' wire 'dsi_txc_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[0]' wire 'dsi_txc_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_rst_o' wire 'dsi_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_oe' wire 'dsi_txd0_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[7]' wire 'dsi_txd0_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[6]' wire 'dsi_txd0_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[5]' wire 'dsi_txd0_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[4]' wire 'dsi_txd0_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[3]' wire 'dsi_txd0_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[2]' wire 'dsi_txd0_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[1]' wire 'dsi_txd0_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[0]' wire 'dsi_txd0_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_oe' wire 'dsi_txd0_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_o' wire 'dsi_txd0_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_oe' wire 'dsi_txd0_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_o' wire 'dsi_txd0_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_rst_o' wire 'dsi_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_oe' wire 'dsi_txd1_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_o' wire 'dsi_txd1_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_oe' wire 'dsi_txd1_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_o' wire 'dsi_txd1_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_oe' wire 'dsi_txd1_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[7]' wire 'dsi_txd1_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[6]' wire 'dsi_txd1_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[5]' wire 'dsi_txd1_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[4]' wire 'dsi_txd1_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[3]' wire 'dsi_txd1_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[2]' wire 'dsi_txd1_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[1]' wire 'dsi_txd1_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[0]' wire 'dsi_txd1_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_rst_o' wire 'dsi_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_oe' wire 'dsi_txd2_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_o' wire 'dsi_txd2_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_oe' wire 'dsi_txd2_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_o' wire 'dsi_txd2_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_oe' wire 'dsi_txd2_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[7]' wire 'dsi_txd2_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[6]' wire 'dsi_txd2_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[5]' wire 'dsi_txd2_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[4]' wire 'dsi_txd2_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[3]' wire 'dsi_txd2_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[2]' wire 'dsi_txd2_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[1]' wire 'dsi_txd2_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[0]' wire 'dsi_txd2_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_rst_o' wire 'dsi_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_oe' wire 'dsi_txd3_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_o' wire 'dsi_txd3_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_oe' wire 'dsi_txd3_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_o' wire 'dsi_txd3_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_oe' wire 'dsi_txd3_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[7]' wire 'dsi_txd3_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[6]' wire 'dsi_txd3_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[5]' wire 'dsi_txd3_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[4]' wire 'dsi_txd3_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[3]' wire 'dsi_txd3_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[2]' wire 'dsi_txd3_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[1]' wire 'dsi_txd3_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[0]' wire 'dsi_txd3_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'uart_tx_o' wire 'uart_tx_o' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sclk' wire 'cmos_sclk' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OUT' wire 'cmos_sdat_OUT' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OE' wire 'cmos_sdat_OE' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl2' wire 'cmos_ctl2' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl3' wire 'cmos_ctl3' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_oe' wire 'lvds_txc_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[6]' wire 'lvds_txc_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[5]' wire 'lvds_txc_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[4]' wire 'lvds_txc_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[3]' wire 'lvds_txc_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[2]' wire 'lvds_txc_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[1]' wire 'lvds_txc_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[0]' wire 'lvds_txc_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_rst_o' wire 'lvds_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_oe' wire 'lvds_txd0_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[6]' wire 'lvds_txd0_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[5]' wire 'lvds_txd0_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[4]' wire 'lvds_txd0_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[3]' wire 'lvds_txd0_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[2]' wire 'lvds_txd0_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[1]' wire 'lvds_txd0_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[0]' wire 'lvds_txd0_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_rst_o' wire 'lvds_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_oe' wire 'lvds_txd1_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[6]' wire 'lvds_txd1_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[5]' wire 'lvds_txd1_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[4]' wire 'lvds_txd1_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[3]' wire 'lvds_txd1_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[2]' wire 'lvds_txd1_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[1]' wire 'lvds_txd1_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[0]' wire 'lvds_txd1_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_rst_o' wire 'lvds_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_oe' wire 'lvds_txd2_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[6]' wire 'lvds_txd2_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[5]' wire 'lvds_txd2_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[4]' wire 'lvds_txd2_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[3]' wire 'lvds_txd2_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[2]' wire 'lvds_txd2_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[1]' wire 'lvds_txd2_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[0]' wire 'lvds_txd2_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_rst_o' wire 'lvds_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_oe' wire 'lvds_txd3_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[6]' wire 'lvds_txd3_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[5]' wire 'lvds_txd3_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[4]' wire 'lvds_txd3_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[3]' wire 'lvds_txd3_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[2]' wire 'lvds_txd3_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[1]' wire 'lvds_txd3_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[0]' wire 'lvds_txd3_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_rst_o' wire 'lvds_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_o' wire 'lcd_tp_sda_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_oe' wire 'lcd_tp_sda_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_o' wire 'lcd_tp_scl_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_oe' wire 'lcd_tp_scl_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_o' wire 'lcd_tp_int_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_oe' wire 'lcd_tp_int_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_rst_o' wire 'lcd_tp_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_pwm_o' wire 'lcd_pwm_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_blen_o' wire 'lcd_blen_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_vs_o' wire 'lcd_vs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_hs_o' wire 'lcd_hs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_de_o' wire 'lcd_de_o' is not driven.
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][31]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][30]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][29]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][28]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wclk=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[9]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[10]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[11]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[12]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[13]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[14]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[15]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[16]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[17]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[18]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[19]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[20]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[21]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[22]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[23]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[24]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[25]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[26]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[27]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[28]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[29]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[30]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[31]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[32]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[33]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[34]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[35]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[36]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[37]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[38]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[39]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[40]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[41]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[42]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[43]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[44]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[45]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[46]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[47]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[48]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[49]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[50]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[51]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[52]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[53]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[54]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[55]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[56]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[57]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[58]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[59]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[60]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[61]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[62]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[63]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[64]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[65]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[66]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[67]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[68]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[69]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[70]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[71]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (we=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (re=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_push_payload[0]=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.a_rst_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.clk_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'fifo_ack' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_ar_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_aw_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'mux0' input pin tied to constant (b_rd_en=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[0]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[1]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[2]=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[3]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[2]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[3]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[5]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[6]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[7]=0).
[EFX-0266 WARNING] Module Instance 'u_wr_addr_fifo' input pin tied to constant (I_Wr_Data[42]=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[127]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[126]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[125]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[124]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[123]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[122]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:36:21
///////////////////////////////////

[EFX-0011 VERI-WARNING] port 'probe1' remains unconnected for this instance (VERI-1927) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:108)
[EFX-0011 VERI-WARNING] input port 'probe1[0]' remains unconnected for this instance (VDB-1053) (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0200 WARNING] Removing redundant signal : trigger_in. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:5689)
[EFX-0200 WARNING] Removing redundant signal : din[8]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4581)
[EFX-0200 WARNING] Removing redundant signal : trig_out. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4860)
[EFX-0200 WARNING] Removing redundant signal : trig_out_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:4861)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1014)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1016)
[EFX-0200 WARNING] Removing redundant signal : bscan_SEL. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1017)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1021)
[EFX-0200 WARNING] Removing redundant signal : trig_in_ack. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:1032)
[EFX-0200 WARNING] Removing redundant signal : mux_capture_cmp[0]. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:3444)
[EFX-0200 WARNING] Removing redundant signal : bscan_CAPTURE. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:265)
[EFX-0200 WARNING] Removing redundant signal : bscan_DRCK. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:266)
[EFX-0200 WARNING] Removing redundant signal : bscan_RUNTEST. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:268)
[EFX-0200 WARNING] Removing redundant signal : bscan_TMS. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:273)
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_in=0).
[EFX-0266 WARNING] Module Instance 'la0' input pin tied to constant (trig_out_ack=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe1[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe2[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe3[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe4[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe5[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe6[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe7[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe8[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe9[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'la0.probe10[0]'. (D:/Efinity_Project/temp3/03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720/work_dbg/debug_top.v:87)
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_DRCK is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_RUNTEST is unconnected and will be removed 
[EFX-0011 VERI-WARNING] Input/Inout Port bscan_TMS is unconnected and will be removed 

///////////////////////////////////
// Efinity Synthesis Started 
// Mar 20, 2025 16:36:32
///////////////////////////////////

[EFX-0256 WARNING] The primary output port 'csi_ctl0_o' wire 'csi_ctl0_o' is not driven.
[EFX-0256 WARNING] The primary output port 'csi_ctl1_o' wire 'csi_ctl1_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_rst_o' wire 'dsi_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_oe' wire 'dsi_txc_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_p_o' wire 'dsi_txc_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_oe' wire 'dsi_txc_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_lp_n_o' wire 'dsi_txc_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_oe' wire 'dsi_txc_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[7]' wire 'dsi_txc_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[6]' wire 'dsi_txc_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[5]' wire 'dsi_txc_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[4]' wire 'dsi_txc_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[3]' wire 'dsi_txc_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[2]' wire 'dsi_txc_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[1]' wire 'dsi_txc_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txc_hs_o[0]' wire 'dsi_txc_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_rst_o' wire 'dsi_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_oe' wire 'dsi_txd0_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[7]' wire 'dsi_txd0_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[6]' wire 'dsi_txd0_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[5]' wire 'dsi_txd0_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[4]' wire 'dsi_txd0_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[3]' wire 'dsi_txd0_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[2]' wire 'dsi_txd0_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[1]' wire 'dsi_txd0_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_hs_o[0]' wire 'dsi_txd0_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_oe' wire 'dsi_txd0_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_p_o' wire 'dsi_txd0_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_oe' wire 'dsi_txd0_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd0_lp_n_o' wire 'dsi_txd0_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_rst_o' wire 'dsi_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_oe' wire 'dsi_txd1_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_p_o' wire 'dsi_txd1_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_oe' wire 'dsi_txd1_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_lp_n_o' wire 'dsi_txd1_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_oe' wire 'dsi_txd1_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[7]' wire 'dsi_txd1_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[6]' wire 'dsi_txd1_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[5]' wire 'dsi_txd1_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[4]' wire 'dsi_txd1_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[3]' wire 'dsi_txd1_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[2]' wire 'dsi_txd1_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[1]' wire 'dsi_txd1_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd1_hs_o[0]' wire 'dsi_txd1_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_rst_o' wire 'dsi_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_oe' wire 'dsi_txd2_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_p_o' wire 'dsi_txd2_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_oe' wire 'dsi_txd2_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_lp_n_o' wire 'dsi_txd2_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_oe' wire 'dsi_txd2_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[7]' wire 'dsi_txd2_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[6]' wire 'dsi_txd2_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[5]' wire 'dsi_txd2_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[4]' wire 'dsi_txd2_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[3]' wire 'dsi_txd2_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[2]' wire 'dsi_txd2_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[1]' wire 'dsi_txd2_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd2_hs_o[0]' wire 'dsi_txd2_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_rst_o' wire 'dsi_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_oe' wire 'dsi_txd3_lp_p_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_p_o' wire 'dsi_txd3_lp_p_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_oe' wire 'dsi_txd3_lp_n_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_lp_n_o' wire 'dsi_txd3_lp_n_o' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_oe' wire 'dsi_txd3_hs_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[7]' wire 'dsi_txd3_hs_o[7]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[6]' wire 'dsi_txd3_hs_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[5]' wire 'dsi_txd3_hs_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[4]' wire 'dsi_txd3_hs_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[3]' wire 'dsi_txd3_hs_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[2]' wire 'dsi_txd3_hs_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[1]' wire 'dsi_txd3_hs_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'dsi_txd3_hs_o[0]' wire 'dsi_txd3_hs_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'uart_tx_o' wire 'uart_tx_o' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sclk' wire 'cmos_sclk' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OUT' wire 'cmos_sdat_OUT' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_sdat_OE' wire 'cmos_sdat_OE' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl2' wire 'cmos_ctl2' is not driven.
[EFX-0256 WARNING] The primary output port 'cmos_ctl3' wire 'cmos_ctl3' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_oe' wire 'lvds_txc_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[6]' wire 'lvds_txc_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[5]' wire 'lvds_txc_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[4]' wire 'lvds_txc_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[3]' wire 'lvds_txc_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[2]' wire 'lvds_txc_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[1]' wire 'lvds_txc_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_o[0]' wire 'lvds_txc_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txc_rst_o' wire 'lvds_txc_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_oe' wire 'lvds_txd0_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[6]' wire 'lvds_txd0_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[5]' wire 'lvds_txd0_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[4]' wire 'lvds_txd0_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[3]' wire 'lvds_txd0_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[2]' wire 'lvds_txd0_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[1]' wire 'lvds_txd0_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_o[0]' wire 'lvds_txd0_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd0_rst_o' wire 'lvds_txd0_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_oe' wire 'lvds_txd1_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[6]' wire 'lvds_txd1_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[5]' wire 'lvds_txd1_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[4]' wire 'lvds_txd1_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[3]' wire 'lvds_txd1_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[2]' wire 'lvds_txd1_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[1]' wire 'lvds_txd1_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_o[0]' wire 'lvds_txd1_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd1_rst_o' wire 'lvds_txd1_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_oe' wire 'lvds_txd2_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[6]' wire 'lvds_txd2_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[5]' wire 'lvds_txd2_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[4]' wire 'lvds_txd2_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[3]' wire 'lvds_txd2_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[2]' wire 'lvds_txd2_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[1]' wire 'lvds_txd2_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_o[0]' wire 'lvds_txd2_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd2_rst_o' wire 'lvds_txd2_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_oe' wire 'lvds_txd3_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[6]' wire 'lvds_txd3_o[6]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[5]' wire 'lvds_txd3_o[5]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[4]' wire 'lvds_txd3_o[4]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[3]' wire 'lvds_txd3_o[3]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[2]' wire 'lvds_txd3_o[2]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[1]' wire 'lvds_txd3_o[1]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_o[0]' wire 'lvds_txd3_o[0]' is not driven.
[EFX-0256 WARNING] The primary output port 'lvds_txd3_rst_o' wire 'lvds_txd3_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_o' wire 'lcd_tp_sda_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_sda_oe' wire 'lcd_tp_sda_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_o' wire 'lcd_tp_scl_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_scl_oe' wire 'lcd_tp_scl_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_o' wire 'lcd_tp_int_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_int_oe' wire 'lcd_tp_int_oe' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_tp_rst_o' wire 'lcd_tp_rst_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_pwm_o' wire 'lcd_pwm_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_blen_o' wire 'lcd_blen_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_vs_o' wire 'lcd_vs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_hs_o' wire 'lcd_hs_o' is not driven.
[EFX-0256 WARNING] The primary output port 'lcd_de_o' wire 'lcd_de_o' is not driven.
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[7]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[6]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[5]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[4]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[3]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[2]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[1]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Out[0]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][31]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][30]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][29]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0200 WARNING] Removing redundant signal : Shift_Q7_Out[4][28]. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wclk=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[9]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[10]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[11]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[12]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[13]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[14]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[15]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[16]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[17]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[18]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[19]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[20]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[21]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[22]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[23]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[24]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[25]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[26]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[27]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[28]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[29]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[30]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[31]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[32]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[33]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[34]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[35]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[36]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[37]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[38]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[39]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[40]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[41]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[42]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[43]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[44]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[45]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[46]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[47]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[48]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[49]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[50]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[51]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[52]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[53]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[54]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[55]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[56]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[57]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[58]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[59]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[60]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[61]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[62]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[63]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[64]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[65]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[66]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[67]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[68]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[69]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[70]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (wdata[71]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[0]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[1]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[2]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[3]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[4]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[5]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[6]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[7]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (waddr[8]=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (we=0).
[EFX-0266 WARNING] Module Instance 'map_ram' input pin tied to constant (re=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_push_payload[0]=1).
[EFX-0266 WARNING] Module Instance 'fifo_rdpush' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.a_rst_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'fifo_rd.clk_i'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'fifo_ack' input pin tied to constant (io_pop_ready=1).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_ar_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'w_aw_fifo_rd_vaild'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:0)
[EFX-0266 WARNING] Module Instance 'mux0' input pin tied to constant (b_rd_en=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[0]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[1]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[2]=1).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[3]=0).
[EFX-0266 WARNING] Module Instance 'phase_u0' input pin tied to constant (pll_sel[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[2]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[3]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[4]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[5]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[6]=0).
[EFX-0266 WARNING] Module Instance 'inst_arburst_fifo' input pin tied to constant (I_Wr_Data[7]=0).
[EFX-0266 WARNING] Module Instance 'u_wr_addr_fifo' input pin tied to constant (I_Wr_Data[42]=0).
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[127]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[126]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[125]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[124]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[123]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
[EFX-0201 WARNING] Re-wiring to GND non-driven net 'u_efx_ddr3_soft_controller.wr_data[122]'. (D:\Efinity_Project\temp3\03-1_Ti60_SC130GS_MIPIx4_HDMI_1280720\ip\DdrCtrl\DdrCtrl.v:116)
