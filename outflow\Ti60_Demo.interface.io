# Efinity IO Placement 
# Interface File: D:/temp_colorbar/Ti60F225_AD2020_LVDS_Display/outflow/Ti60_Demo.interface.csv
# Version:        2025.1.110.2.15
# Date:           May  7 2025

# Copyright (C) 2013 - 2025  All rights reserved.


# pin name           	x	y	z
# --------           	----	----	---
cmos_data[0]         	0	3	3
cmos_data[1]         	0	4	3
dsi_pll_lock         	0	5	1
dsi_pll_rstn_o       	0	6	2
cmos_ctl3            	0	17	0
spi_ssn_o            	0	30	0
spi_sck_o            	0	32	2
lcd_de_o             	0	46	2
clk_lvds_1x~CLKOUT~1~48	0	48	6
lcd_g7_0_o[0]        	0	60	0
lcd_g7_0_o[1]        	0	62	2
lcd_g7_0_oe[0]       	0	63	0
lcd_g7_0_oe[1]       	0	63	2
lcd_g7_0_i[0]        	0	64	3
lcd_g7_0_i[1]        	0	65	1
lcd_b7_0_o[2]        	0	71	0
lcd_b7_0_o[3]        	0	73	2
lcd_b7_0_oe[2]       	0	74	0
lcd_b7_0_oe[3]       	0	74	2
lcd_b7_0_i[2]        	0	75	1
lcd_b7_0_i[3]        	0	75	3
lcd_b7_0_o[4]        	0	96	2
lcd_b7_0_o[5]        	0	99	0
lcd_b7_0_oe[4]       	0	99	2
lcd_b7_0_oe[5]       	0	100	0
lcd_b7_0_i[4]        	0	100	3
lcd_b7_0_i[5]        	0	101	1
lcd_hs_o             	0	112	2
lcd_vs_o             	0	115	0
lcd_r7_0_o[6]        	0	130	0
lcd_r7_0_o[7]        	0	132	2
lcd_r7_0_oe[6]       	0	133	0
lcd_r7_0_oe[7]       	0	133	2
lcd_r7_0_i[6]        	0	134	1
lcd_r7_0_i[7]        	0	134	3
lcd_b7_0_o[6]        	0	146	0
lcd_b7_0_o[7]        	0	148	2
lcd_b7_0_oe[6]       	0	149	0
lcd_b7_0_oe[7]       	0	149	2
lcd_b7_0_i[6]        	0	150	1
lcd_b7_0_i[7]        	0	150	3
clk_pixel            	0	159	1
clk_sys              	0	162	1
clk_pixel_2x         	0	163	1
clk_pixel_10x        	0	164	1
lcd_r7_0_o[4]        	0	169	0
lcd_r7_0_o[5]        	0	171	2
lcd_r7_0_oe[4]       	0	172	0
lcd_r7_0_oe[5]       	0	172	2
lcd_r7_0_i[4]        	0	173	1
lcd_r7_0_i[5]        	0	173	3
lcd_g7_0_o[4]        	0	180	2
lcd_g7_0_o[5]        	0	183	0
lcd_g7_0_oe[4]       	0	183	2
lcd_g7_0_oe[5]       	0	184	0
lcd_g7_0_i[4]        	0	184	3
lcd_g7_0_i[5]        	0	185	1
lcd_g7_0_o[2]        	0	192	2
lcd_g7_0_o[3]        	0	195	0
lcd_g7_0_oe[2]       	0	195	2
lcd_g7_0_oe[3]       	0	196	0
lcd_g7_0_i[2]        	0	196	3
lcd_g7_0_i[3]        	0	197	1
lcd_b7_0_o[0]        	0	205	2
lcd_b7_0_o[1]        	0	208	0
lcd_b7_0_oe[0]       	0	208	2
lcd_b7_0_oe[1]       	0	209	0
lcd_b7_0_i[0]        	0	209	3
lcd_b7_0_i[1]        	0	210	1
lcd_g7_0_o[6]        	0	223	0
lcd_g7_0_o[7]        	0	225	2
lcd_g7_0_oe[6]       	0	226	0
lcd_g7_0_oe[7]       	0	226	2
lcd_g7_0_i[6]        	0	227	1
lcd_g7_0_i[7]        	0	227	3
lcd_r7_0_o[2]        	0	244	0
lcd_r7_0_o[3]        	0	246	2
lcd_r7_0_oe[2]       	0	247	0
lcd_r7_0_oe[3]       	0	247	2
lcd_r7_0_i[2]        	0	248	1
lcd_r7_0_i[3]        	0	248	3
lcd_r7_0_o[0]        	0	256	0
lcd_r7_0_o[1]        	0	258	2
lcd_r7_0_oe[0]       	0	259	0
lcd_r7_0_oe[1]       	0	259	2
lcd_r7_0_i[0]        	0	260	1
lcd_r7_0_i[1]        	0	260	3
lcd_blen_o           	0	267	0
lcd_pwm_o            	0	269	2
lcd_tp_scl_o         	0	278	0
lcd_tp_sda_o         	0	280	2
lcd_tp_scl_oe        	0	283	0
lcd_tp_sda_oe        	0	283	2
lcd_tp_scl_i         	0	284	1
lcd_tp_sda_i         	0	284	3
lcd_tp_int_o         	0	292	0
lcd_tp_rst_o         	0	294	2
lcd_tp_int_oe        	0	295	0
lcd_tp_int_i         	0	296	3
sys_pll_rstn_o       	0	302	2
clk_sys~CLKOUT~1~304 	0	304	6
cmos_sclk            	0	312	0
sys_pll_lock         	0	312	1
clk_24m              	0	313	3
cmos_data[4]         	0	314	3
cmos_data[6]         	0	317	3
cmos_data[2]         	0	318	3
cmos_ctl2            	0	319	2
cmos_vsync           	0	321	3
hdmi_txd1_o[0]       	2	323	0
hdmi_txd1_o[1]       	2	323	2
o_dq_lo[14]          	3	0	2
hdmi_txd1_o[2]       	3	323	0
hdmi_txd1_o[3]       	3	323	2
o_dq_hi[14]          	4	0	0
cmos_pclk~CLKOUT~4~1 	4	0	6
hdmi_txd1_o[4]       	4	323	0
hdmi_txd1_o[5]       	4	323	2
cmos_pclk~CLKOUT~4~322	4	323	6
o_dq_lo[15]          	5	0	2
hdmi_txd1_o[6]       	5	323	0
hdmi_txd1_o[7]       	5	323	2
o_dq_hi[15]          	6	0	2
cmos_pclk~CLKOUT~6~1 	6	0	6
hdmi_txd1_o[8]       	6	323	0
hdmi_txd1_o[9]       	6	323	2
o_dq_oe[14]          	7	0	0
i_dq_lo[14]          	7	0	1
o_dq_oe[15]          	7	0	2
i_dq_lo[15]          	7	0	3
i_dq_hi[14]          	8	0	1
i_dq_hi[15]          	8	0	3
hdmi_txd1_oe         	8	323	2
cmos_pclk~CLKOUT~10~322	10	323	6
tac_clk~CLKOUT~11~1  	11	0	6
tac_clk~CLKOUT~12~1  	12	0	6
hdmi_txd1_rst_o      	12	323	0
cmos_pclk~CLKOUT~12~322	12	323	6
twd_clk~CLKOUT~13~1  	13	0	6
lvds_txc_o[0]        	13	323	0
lvds_txc_o[1]        	13	323	2
o_dq_lo[10]          	14	0	2
twd_clk~CLKOUT~14~1  	14	0	6
lvds_txc_o[2]        	14	323	0
lvds_txc_o[3]        	14	323	2
o_dq_hi[10]          	15	0	0
lvds_txc_o[4]        	15	323	0
lvds_txc_o[5]        	15	323	2
o_dq_lo[12]          	16	0	2
lvds_txc_o[6]        	16	323	0
o_dq_hi[12]          	17	0	2
o_dq_oe[10]          	18	0	0
i_dq_lo[10]          	18	0	1
o_dq_oe[12]          	18	0	2
i_dq_lo[12]          	18	0	3
clk_sys~CLKOUT~18~1  	18	0	6
cmos_pclk~CLKOUT~18~322	18	323	6
i_dq_hi[10]          	19	0	1
i_dq_hi[12]          	19	0	3
lvds_txc_oe          	19	323	2
tac_clk~CLKOUT~22~1  	22	0	6
tac_clk~CLKOUT~23~1  	23	0	6
lvds_txc_rst_o       	23	323	0
twd_clk~CLKOUT~24~1  	24	0	6
twd_clk~CLKOUT~25~1  	25	0	6
o_dqs_lo[1]          	26	0	0
o_dqs_hi[1]          	26	0	2
clk_pixel_10x~CLKOUT~27~322	27	323	6
o_dqs_n_lo[1]        	28	0	0
o_dqs_n_hi[1]        	29	0	0
o_dqs_oe[1]          	29	0	2
i_dqs_lo[1]          	29	0	3
o_dqs_n_oe[1]        	30	0	0
i_dqs_n_lo[1]        	30	0	1
i_dqs_hi[1]          	30	0	3
tac_clk~CLKOUT~30~1  	30	0	6
i_dqs_n_hi[1]        	31	0	1
tac_clk~CLKOUT~31~1  	31	0	6
clk_pixel~CLKOUT~31~322	31	323	6
tdqss_clk~CLKOUT~32~1	32	0	6
tdqss_clk~CLKOUT~33~1	33	0	6
clk_lvds_7x~CLKOUT~35~322	35	323	6
hdmi_txd2_o[0]       	36	323	0
hdmi_txd2_o[1]       	36	323	2
o_dq_lo[13]          	37	0	2
hdmi_txd2_o[2]       	37	323	0
hdmi_txd2_o[3]       	37	323	2
o_dq_hi[13]          	38	0	0
tac_clk~CLKOUT~38~1  	38	0	6
hdmi_txd2_o[4]       	38	323	0
hdmi_txd2_o[5]       	38	323	2
o_dq_lo[9]           	39	0	2
tac_clk~CLKOUT~39~1  	39	0	6
hdmi_txd2_o[6]       	39	323	0
hdmi_txd2_o[7]       	39	323	2
clk_lvds_1x~CLKOUT~39~322	39	323	6
o_dq_hi[9]           	40	0	2
twd_clk~CLKOUT~40~1  	40	0	6
hdmi_txd2_o[8]       	40	323	0
hdmi_txd2_o[9]       	40	323	2
o_dq_oe[13]          	41	0	0
i_dq_lo[13]          	41	0	1
o_dq_oe[9]           	41	0	2
i_dq_lo[9]           	41	0	3
twd_clk~CLKOUT~41~1  	41	0	6
i_dq_hi[13]          	42	0	1
i_dq_hi[9]           	42	0	3
hdmi_txd2_oe         	42	323	2
hdmi_txd2_rst_o      	46	323	0
hdmi_txc_o[0]        	47	323	0
hdmi_txc_o[1]        	47	323	2
hdmi_txc_o[2]        	48	323	0
hdmi_txc_o[3]        	48	323	2
hdmi_txc_o[4]        	49	323	0
hdmi_txc_o[5]        	49	323	2
tdqss_clk~CLKOUT~50~1	50	0	6
hdmi_txc_o[6]        	50	323	0
hdmi_txc_o[7]        	50	323	2
addr[3]              	51	0	2
hdmi_txc_o[8]        	51	323	0
hdmi_txc_o[9]        	51	323	2
clk_pixel_10x~CLKOUT~51~322	51	323	6
hdmi_txc_oe          	53	323	2
clk_pixel~CLKOUT~55~322	55	323	6
hdmi_txc_rst_o       	57	323	0
lvds_txd2_o[0]       	58	323	0
lvds_txd2_o[1]       	58	323	2
lvds_txd2_o[2]       	59	323	0
lvds_txd2_o[3]       	59	323	2
clk_pixel_10x~CLKOUT~59~322	59	323	6
lvds_txd2_o[4]       	60	323	0
lvds_txd2_o[5]       	60	323	2
lvds_txd2_o[6]       	61	323	0
clk_pixel~CLKOUT~63~322	63	323	6
lvds_txd2_oe         	64	323	2
clk_lvds_7x~CLKOUT~67~322	67	323	6
lvds_txd2_rst_o      	68	323	0
odt                  	71	0	0
csi_sda_o            	71	323	0
clk_lvds_1x~CLKOUT~71~322	71	323	6
ba[0]                	73	0	2
tdqss_clk~CLKOUT~73~1	73	0	6
dsi_pwm_o            	73	323	2
tdqss_clk~CLKOUT~74~1	74	0	6
csi_sda_oe           	74	323	0
csi_sda_i            	75	323	1
o_dm_lo[1]           	81	0	2
o_dm_hi[1]           	82	0	0
tac_clk~CLKOUT~82~1  	82	0	6
o_dq_lo[8]           	83	0	2
twd_clk~CLKOUT~83~1  	83	0	6
o_dq_hi[8]           	84	0	2
twd_clk~CLKOUT~84~1  	84	0	6
o_dq_oe[8]           	85	0	2
i_dq_lo[8]           	85	0	3
i_dq_hi[8]           	86	0	3
o_dq_lo[11]          	92	0	0
o_dq_hi[11]          	92	0	2
tac_clk~CLKOUT~92~1  	92	0	6
twd_clk~CLKOUT~94~1  	94	0	6
cs                   	95	0	0
o_dq_oe[11]          	95	0	2
tdqss_clk~CLKOUT~95~1	95	0	6
i_dq_lo[11]          	96	0	1
i_dq_hi[11]          	97	0	1
dsi_serclk_i         	107	0	1
dsi_txcclk_i         	108	0	1
dsi_refclk_i         	109	0	1
tdqss_clk            	110	0	1
tac_clk              	111	0	1
cmos_pclk            	111	323	1
dsi_byteclk_i        	112	0	1
clk_54m              	112	323	1
csi_scl_o            	121	323	0
cke                  	123	0	2
tdqss_clk~CLKOUT~123~1	123	0	6
dsi_resetn_o         	123	323	2
csi_scl_oe           	124	323	0
csi_scl_i            	124	323	1
o_dm_lo[0]           	131	0	0
o_dm_hi[0]           	131	0	2
twd_clk~CLKOUT~131~1 	131	0	6
csi_ctl0_o           	131	323	2
tdqss_clk~CLKOUT~132~1	132	0	6
ba[1]                	134	0	0
csi_ctl1_o           	134	323	0
csi_ctl0_oe          	134	323	2
csi_ctl1_oe          	135	323	0
csi_ctl0_i           	135	323	1
csi_ctl1_i           	135	323	3
addr[5]              	142	0	0
tdqss_clk~CLKOUT~142~1	142	0	6
tdqss_clk~CLKOUT~143~1	143	0	6
addr[15]             	144	0	2
tac_clk~CLKOUT~148~1 	148	0	6
tac_clk~CLKOUT~149~1 	149	0	6
twd_clk~CLKOUT~150~1 	150	0	6
twd_clk~CLKOUT~151~1 	151	0	6
lvds_txd1_o[0]       	151	323	0
lvds_txd1_o[1]       	151	323	2
clk_lvds_7x~CLKOUT~151~322	151	323	6
o_dq_lo[0]           	152	0	2
lvds_txd1_o[2]       	152	323	0
lvds_txd1_o[3]       	152	323	2
o_dq_hi[0]           	153	0	0
lvds_txd1_o[4]       	153	323	0
lvds_txd1_o[5]       	153	323	2
o_dq_lo[3]           	154	0	2
lvds_txd1_o[6]       	154	323	0
o_dq_hi[3]           	155	0	2
clk_lvds_1x~CLKOUT~155~322	155	323	6
o_dq_oe[0]           	156	0	0
i_dq_lo[0]           	156	0	1
o_dq_oe[3]           	156	0	2
i_dq_lo[3]           	156	0	3
tac_clk~CLKOUT~156~1 	156	0	6
i_dq_hi[0]           	157	0	1
i_dq_hi[3]           	157	0	3
tac_clk~CLKOUT~157~1 	157	0	6
lvds_txd1_oe         	157	323	2
twd_clk~CLKOUT~158~1 	158	0	6
twd_clk~CLKOUT~159~1 	159	0	6
clk_lvds_7x~CLKOUT~159~322	159	323	6
lvds_txd1_rst_o      	161	323	0
lvds_txd0_o[0]       	162	323	0
lvds_txd0_o[1]       	162	323	2
o_dq_lo[5]           	163	0	2
lvds_txd0_o[2]       	163	323	0
lvds_txd0_o[3]       	163	323	2
clk_lvds_1x~CLKOUT~163~322	163	323	6
o_dq_hi[5]           	164	0	0
tac_clk~CLKOUT~164~1 	164	0	6
lvds_txd0_o[4]       	164	323	0
lvds_txd0_o[5]       	164	323	2
o_dq_lo[1]           	165	0	2
tac_clk~CLKOUT~165~1 	165	0	6
lvds_txd0_o[6]       	165	323	0
o_dq_hi[1]           	166	0	2
twd_clk~CLKOUT~166~1 	166	0	6
o_dq_oe[5]           	167	0	0
i_dq_lo[5]           	167	0	1
o_dq_oe[1]           	167	0	2
i_dq_lo[1]           	167	0	3
twd_clk~CLKOUT~167~1 	167	0	6
clk_lvds_7x~CLKOUT~167~322	167	323	6
i_dq_hi[5]           	168	0	1
i_dq_hi[1]           	168	0	3
lvds_txd0_oe         	168	323	2
clk_lvds_1x~CLKOUT~171~322	171	323	6
tac_clk~CLKOUT~172~1 	172	0	6
lvds_txd0_rst_o      	172	323	0
tac_clk~CLKOUT~173~1 	173	0	6
lvds_txd3_o[0]       	173	323	0
lvds_txd3_o[1]       	173	323	2
o_dq_lo[2]           	174	0	2
tdqss_clk~CLKOUT~174~1	174	0	6
lvds_txd3_o[2]       	174	323	0
lvds_txd3_o[3]       	174	323	2
o_dq_hi[2]           	175	0	0
tdqss_clk~CLKOUT~175~1	175	0	6
lvds_txd3_o[4]       	175	323	0
lvds_txd3_o[5]       	175	323	2
o_dq_lo[6]           	176	0	2
lvds_txd3_o[6]       	176	323	0
o_dq_hi[6]           	177	0	2
o_dq_oe[2]           	178	0	0
i_dq_lo[2]           	178	0	1
o_dq_oe[6]           	178	0	2
i_dq_lo[6]           	178	0	3
i_dq_hi[2]           	179	0	1
i_dq_hi[6]           	179	0	3
lvds_txd3_oe         	179	323	2
lvds_txd3_rst_o      	183	323	0
o_dqs_lo[0]          	186	0	0
o_dqs_hi[0]          	186	0	2
o_dqs_n_lo[0]        	188	0	0
tac_clk~CLKOUT~188~1 	188	0	6
o_dqs_n_hi[0]        	189	0	0
o_dqs_oe[0]          	189	0	2
i_dqs_lo[0]          	189	0	3
tac_clk~CLKOUT~189~1 	189	0	6
o_dqs_n_oe[0]        	190	0	0
i_dqs_n_lo[0]        	190	0	1
i_dqs_hi[0]          	190	0	3
twd_clk~CLKOUT~190~1 	190	0	6
i_dqs_n_hi[0]        	191	0	1
twd_clk~CLKOUT~191~1 	191	0	6
clk_pixel_10x~CLKOUT~191~322	191	323	6
clk_pixel~CLKOUT~195~322	195	323	6
cmos_pclk~CLKOUT~197~322	197	323	6
uart_tx_o            	198	323	0
clk_sys~CLKOUT~200~322	200	323	6
clk_sys~CLKOUT~202~1 	202	0	6
uart_rx_i            	202	323	3
core_clk~CLKOUT~203~1	203	0	6
hdmi_txd0_o[0]       	207	323	2
hdmi_txd0_o[1]       	208	323	0
hdmi_txd0_o[2]       	208	323	2
o_dq_lo[7]           	209	0	0
o_dq_hi[7]           	209	0	2
hdmi_txd0_o[3]       	209	323	0
hdmi_txd0_o[4]       	209	323	2
clk_27m~CLKOUT~209~322	209	323	6
hdmi_txd0_o[5]       	210	323	0
hdmi_txd0_o[6]       	210	323	2
o_dq_lo[4]           	211	0	0
hdmi_txd0_o[7]       	211	323	0
hdmi_txd0_o[8]       	211	323	2
o_dq_hi[4]           	212	0	0
o_dq_oe[7]           	212	0	2
i_dq_lo[7]           	212	0	3
hdmi_txd0_o[9]       	212	323	0
cmos_pclk~CLKOUT~212~322	212	323	6
o_dq_oe[4]           	213	0	0
i_dq_lo[4]           	213	0	1
i_dq_hi[7]           	213	0	3
i_dq_hi[4]           	214	0	1
hdmi_txd0_oe         	214	323	0
cmos_pclk~CLKOUT~214~322	214	323	6
cmos_pclk~CLKOUT~216~322	216	323	6
hdmi_txd0_rst_o      	217	323	2
clk_25m              	219	2	3
led_o[2]             	219	4	0
led_o[5]             	219	5	2
led_o[0]             	219	8	2
led_o[4]             	219	10	0
ddr_pll_lock         	219	11	1
led_o[1]             	219	13	0
led_o[3]             	219	14	2
ddr_pll_rstn_o       	219	15	2
shift[0]             	219	16	0
shift[1]             	219	16	2
shift[2]             	219	17	0
shift_sel[0]         	219	17	2
shift_sel[1]         	219	18	0
shift_sel[2]         	219	18	2
shift_sel[3]         	219	19	0
shift_sel[4]         	219	19	2
shift_ena            	219	20	0
addr[4]              	219	23	2
tdqss_clk~CLKOUT~218~25	219	25	6
addr[8]              	219	26	0
tdqss_clk~CLKOUT~218~26	219	26	6
addr[12]             	219	34	2
tdqss_clk~CLKOUT~218~35	219	35	6
tdqss_clk~CLKOUT~218~36	219	36	6
addr[1]              	219	37	0
ras                  	219	47	2
tdqss_clk~CLKOUT~218~48	219	48	6
tdqss_clk~CLKOUT~218~49	219	49	6
cas                  	219	50	0
addr[6]              	219	58	2
tdqss_clk~CLKOUT~218~59	219	59	6
tdqss_clk~CLKOUT~218~60	219	60	6
addr[11]             	219	61	0
addr[2]              	219	69	2
tdqss_clk~CLKOUT~218~70	219	70	6
tdqss_clk~CLKOUT~218~71	219	71	6
addr[13]             	219	72	0
we                   	219	93	0
tdqss_clk~CLKOUT~218~94	219	94	6
addr[0]              	219	95	2
tdqss_clk~CLKOUT~218~95	219	95	6
addr[14]             	219	107	2
tdqss_clk~CLKOUT~218~109	219	109	6
addr[7]              	219	110	0
tdqss_clk~CLKOUT~218~110	219	110	6
ba[2]                	219	125	0
tdqss_clk~CLKOUT~218~126	219	126	6
addr[9]              	219	127	2
tdqss_clk~CLKOUT~218~127	219	127	6
addr[10]             	219	137	0
tdqss_clk~CLKOUT~218~138	219	138	6
reset                	219	139	2
tdqss_clk~CLKOUT~218~139	219	139	6
clk_p_lo             	219	148	0
clk_p_hi             	219	148	2
tdqss_clk~CLKOUT~218~149	219	149	6
clk_n_lo             	219	150	0
tdqss_clk~CLKOUT~218~150	219	150	6
clk_n_hi             	219	151	0
clk_lvds_7x          	219	158	1
twd_clk              	219	159	1
csi_rxc_i            	219	161	1
clk_lvds_1x          	219	162	1
clk_27m              	219	163	1
core_clk             	219	164	1
dsi_txd3_hs_o[0]     	219	167	0
dsi_txd3_hs_o[2]     	219	167	2
dsi_txd3_hs_o[4]     	219	168	0
dsi_txd3_hs_o[6]     	219	168	2
dsi_txd3_lp_p_o      	219	169	0
dsi_txd3_hs_o[1]     	219	169	2
dsi_txd3_hs_o[3]     	219	170	0
dsi_txd3_hs_o[5]     	219	170	2
dsi_txd3_hs_o[7]     	219	171	0
dsi_txd3_lp_n_o      	219	171	2
dsi_serclk_i~CLKOUT~218~171	219	171	6
dsi_txd3_lp_p_oe     	219	172	0
dsi_txd3_lp_n_oe     	219	172	2
dsi_txd3_lp_p_i      	219	173	1
dsi_txd3_hs_oe       	219	173	2
dsi_txd3_lp_n_i      	219	173	3
dsi_byteclk_i~CLKOUT~218~175	219	175	6
dsi_txd3_rst_o       	219	177	0
dsi_txd0_hs_o[0]     	219	179	0
dsi_txd0_hs_o[2]     	219	179	2
dsi_txd0_hs_o[4]     	219	180	0
dsi_txd0_hs_o[6]     	219	180	2
dsi_txd0_lp_p_o      	219	181	0
dsi_txd0_hs_o[1]     	219	181	2
dsi_txd0_hs_o[3]     	219	182	0
dsi_txd0_hs_o[5]     	219	182	2
dsi_txd0_hs_o[7]     	219	183	0
dsi_txd0_lp_n_o      	219	183	2
dsi_serclk_i~CLKOUT~218~183	219	183	6
dsi_txd0_lp_p_oe     	219	184	0
dsi_txd0_lp_n_oe     	219	184	2
dsi_txd0_lp_p_i      	219	185	1
dsi_txd0_hs_oe       	219	185	2
dsi_txd0_lp_n_i      	219	185	3
dsi_byteclk_i~CLKOUT~218~187	219	187	6
dsi_txd0_rst_o       	219	189	0
dsi_txc_hs_o[0]      	219	192	2
dsi_txc_hs_o[2]      	219	193	0
dsi_txc_hs_o[4]      	219	193	2
dsi_txc_hs_o[6]      	219	194	0
dsi_txc_lp_p_o       	219	194	2
dsi_txc_hs_o[1]      	219	195	0
dsi_txc_hs_o[3]      	219	195	2
dsi_txc_hs_o[5]      	219	196	0
dsi_txc_hs_o[7]      	219	196	2
dsi_txcclk_i~CLKOUT~218~196	219	196	6
dsi_txc_lp_n_o       	219	197	0
dsi_txc_lp_p_oe      	219	197	2
dsi_txc_lp_n_oe      	219	198	0
dsi_txc_hs_oe        	219	199	0
dsi_byteclk_i~CLKOUT~218~200	219	200	6
dsi_txc_rst_o        	219	204	2
dsi_txd1_hs_o[0]     	219	207	0
dsi_txd1_hs_o[2]     	219	207	2
dsi_txd1_hs_o[4]     	219	208	0
dsi_txd1_hs_o[6]     	219	208	2
dsi_txd1_lp_p_o      	219	209	0
dsi_txd1_hs_o[1]     	219	209	2
dsi_txd1_hs_o[3]     	219	210	0
dsi_txd1_hs_o[5]     	219	210	2
dsi_txd1_hs_o[7]     	219	211	0
dsi_txd1_lp_n_o      	219	211	2
dsi_serclk_i~CLKOUT~218~211	219	211	6
dsi_txd1_lp_p_oe     	219	212	0
dsi_txd1_lp_n_oe     	219	212	2
dsi_txd1_lp_p_i      	219	213	1
dsi_txd1_hs_oe       	219	213	2
dsi_txd1_lp_n_i      	219	213	3
dsi_byteclk_i~CLKOUT~218~215	219	215	6
dsi_txd1_rst_o       	219	217	0
dsi_txd2_hs_o[0]     	219	221	2
dsi_txd2_hs_o[2]     	219	222	0
dsi_txd2_hs_o[4]     	219	222	2
dsi_txd2_hs_o[6]     	219	223	0
dsi_txd2_lp_p_o      	219	223	2
dsi_txd2_hs_o[1]     	219	224	0
dsi_txd2_hs_o[3]     	219	224	2
dsi_txd2_hs_o[5]     	219	225	0
dsi_txd2_hs_o[7]     	219	225	2
dsi_txd2_lp_n_o      	219	226	0
dsi_txd2_lp_p_oe     	219	226	2
dsi_serclk_i~CLKOUT~218~226	219	226	6
dsi_txd2_lp_n_oe     	219	227	0
dsi_txd2_lp_p_i      	219	227	3
dsi_txd2_hs_oe       	219	228	0
dsi_txd2_lp_n_i      	219	228	1
dsi_byteclk_i~CLKOUT~218~230	219	230	6
dsi_txd2_rst_o       	219	231	2
csi_rxd2_hs_i[0]     	219	244	1
csi_rxd2_hs_i[1]     	219	244	3
csi_rxd2_hs_i[2]     	219	245	1
csi_rxd2_hs_i[3]     	219	245	3
csi_rxd2_hs_i[4]     	219	246	1
csi_rxd2_hs_i[5]     	219	246	3
csi_rxd2_hs_i[6]     	219	247	1
csi_rxd2_hs_i[7]     	219	247	3
csi_rxd2_hs_en_o     	219	249	0
csi_rxd2_lp_p_i      	219	249	1
csi_rxd2_lp_n_i      	219	249	3
csi_rxd2_hs_term_en_o	219	250	0
csi_rxd2_rst_o       	219	252	0
csi_rxd3_hs_i[0]     	219	255	1
csi_rxd3_hs_i[1]     	219	255	3
csi_rxd3_hs_i[2]     	219	256	1
csi_rxd3_hs_i[3]     	219	256	3
csi_rxd3_hs_i[4]     	219	257	1
csi_rxd3_hs_i[5]     	219	257	3
csi_rxd3_hs_i[6]     	219	258	1
csi_rxd3_hs_i[7]     	219	258	3
csi_rxd3_hs_en_o     	219	260	0
csi_rxd3_lp_p_i      	219	260	1
csi_rxd3_lp_n_i      	219	260	3
csi_rxd3_hs_term_en_o	219	261	0
csi_rxd3_rst_o       	219	263	0
csi_rxc_hs_en_o      	219	271	2
csi_rxc_lp_p_i       	219	271	3
csi_rxc_lp_n_i       	219	272	1
csi_rxc_hs_term_en_o 	219	272	2
csi_rxd0_hs_i[0]     	219	277	1
csi_rxd0_hs_i[1]     	219	277	3
csi_rxd0_hs_i[2]     	219	278	1
csi_rxd0_hs_i[3]     	219	278	3
csi_rxd0_hs_i[4]     	219	279	1
csi_rxd0_hs_i[5]     	219	279	3
csi_rxd0_hs_i[6]     	219	280	1
csi_rxd0_hs_i[7]     	219	280	3
csi_rxd0_hs_en_o     	219	284	0
csi_rxd0_lp_p_i      	219	284	1
csi_rxd0_lp_n_i      	219	284	3
csi_rxd0_hs_term_en_o	219	285	0
csi_rxd0_rst_o       	219	287	0
csi_rxd1_hs_i[0]     	219	293	1
csi_rxd1_hs_i[1]     	219	293	3
csi_rxd1_hs_i[2]     	219	294	1
csi_rxd1_hs_i[3]     	219	294	3
csi_rxd1_hs_i[4]     	219	295	1
csi_rxd1_hs_i[5]     	219	295	3
csi_rxd1_hs_i[6]     	219	296	1
csi_rxd1_hs_i[7]     	219	296	3
csi_rxd1_hs_en_o     	219	298	0
csi_rxd1_lp_p_i      	219	298	1
csi_rxd1_lp_n_i      	219	298	3
csi_rxd1_hs_term_en_o	219	299	0
csi_rxd1_rst_o       	219	301	0
lvds_pll_rstn_o      	219	302	2
cmos_sdat_OUT        	219	310	2
cmos_sdat_OE         	219	311	0
lvds_pll_lock        	219	312	1
cmos_data[7]         	219	313	3
cmos_sdat_IN         	219	314	3
cmos_ctl1            	219	316	3
cmos_data[5]         	219	319	3
cmos_data[3]         	219	320	3
cmos_href            	219	321	3
