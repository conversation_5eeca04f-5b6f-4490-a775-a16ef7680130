<?xml version="1.0" encoding="UTF-8"?>
<efxpt:design_db name="Ti60_Demo" device_def="Ti60F225" version="2025.1.110.2.15" db_version="********" last_change_date="Mon Jul 14 18:43:15 2025" xmlns:efxpt="http://www.efinixinc.com/peri_design_db" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.efinixinc.com/peri_design_db peri_design_db.xsd ">
    <efxpt:device_info>
        <efxpt:iobank_info>
            <efxpt:iobank name="1A" iostd="1.8 V LVCMOS" is_dyn_voltage="false" mode_sel_name="1A_MODE_SEL"/>
            <efxpt:iobank name="1B" iostd="1.8 V LVCMOS" is_dyn_voltage="false" mode_sel_name="1B_MODE_SEL"/>
            <efxpt:iobank name="2A" iostd="1.8 V LVCMOS" is_dyn_voltage="false" mode_sel_name="2A_MODE_SEL"/>
            <efxpt:iobank name="2B" iostd="1.8 V LVCMOS" is_dyn_voltage="false" mode_sel_name="2B_MODE_SEL"/>
            <efxpt:iobank name="3A" iostd="1.2 V LVCMOS" is_dyn_voltage="false" mode_sel_name="3A_MODE_SEL"/>
            <efxpt:iobank name="3B" iostd="1.5 V LVCMOS" is_dyn_voltage="false" mode_sel_name="3B_MODE_SEL"/>
            <efxpt:iobank name="4A" iostd="1.5 V LVCMOS" is_dyn_voltage="false" mode_sel_name="4A_MODE_SEL"/>
            <efxpt:iobank name="4B" iostd="1.5 V LVCMOS" is_dyn_voltage="false" mode_sel_name="4B_MODE_SEL"/>
            <efxpt:iobank name="BL" iostd="3.3 V LVCMOS" is_dyn_voltage="false" mode_sel_name="BL_MODE_SEL"/>
            <efxpt:iobank name="BR" iostd="3.3 V LVCMOS" is_dyn_voltage="false" mode_sel_name="BR_MODE_SEL"/>
            <efxpt:iobank name="TL" iostd="3.3 V LVCMOS" is_dyn_voltage="false" mode_sel_name="TL_MODE_SEL"/>
            <efxpt:iobank name="TR" iostd="3.3 V LVCMOS" is_dyn_voltage="false" mode_sel_name="TR_MODE_SEL"/>
        </efxpt:iobank_info>
        <efxpt:ctrl_info>
            <efxpt:ctrl name="cfg" ctrl_def="CONFIG_CTRL0" clock_name="" is_clk_invert="false" cbsel_bus_name="cfg_CBSEL" config_ctrl_name="cfg_CONFIG" ena_capture_name="cfg_ENA" error_status_name="cfg_ERROR" um_signal_status_name="cfg_USR_STATUS" is_remote_update_enable="false" is_user_mode_enable="false"/>
        </efxpt:ctrl_info>
        <efxpt:seu_info>
            <efxpt:seu name="seu" block_def="CONFIG_SEU0" mode="auto" ena_detect="false" wait_interval="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="seu_START" type_name="START" is_bus="false"/>
                    <efxpt:pin name="seu_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="seu_INJECT_ERROR" type_name="INJECT_ERROR" is_bus="false"/>
                    <efxpt:pin name="seu_CONFIG" type_name="CONFIG" is_bus="false"/>
                    <efxpt:pin name="seu_ERROR" type_name="ERROR" is_bus="false"/>
                    <efxpt:pin name="seu_DONE" type_name="DONE" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:seu>
        </efxpt:seu_info>
        <efxpt:clkmux_info>
            <efxpt:clkmux name="CLKMUX_B" block_def="CLKMUX_B" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
            <efxpt:clkmux name="CLKMUX_L" block_def="CLKMUX_L" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
            <efxpt:clkmux name="CLKMUX_R" block_def="CLKMUX_R" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
            <efxpt:clkmux name="CLKMUX_T" block_def="CLKMUX_T" is_mux_bot0_dyn="false" is_mux_bot7_dyn="false">
                <efxpt:gen_pin>
                    <efxpt:pin name="" type_name="ROUTE0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_7" is_bus="true"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_7" is_bus="false"/>
                    <efxpt:pin name="" type_name="ROUTE1" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE2" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="ROUTE3" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_OUT_0" is_bus="false"/>
                    <efxpt:pin name="" type_name="DYN_MUX_SEL_0" is_bus="true"/>
                </efxpt:gen_pin>
            </efxpt:clkmux>
        </efxpt:clkmux_info>
    </efxpt:device_info>
    <efxpt:gpio_info>
        <efxpt:comp_gpio name="addr[0]" gpio_def="GPIOR_N_05" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[0]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[10]" gpio_def="GPIOR_P_08" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[10]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[11]" gpio_def="GPIOR_N_03" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[11]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[12]" gpio_def="GPIOR_P_01" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[12]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[13]" gpio_def="GPIOR_N_04" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[13]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[14]" gpio_def="GPIOR_P_06" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[14]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[15]" gpio_def="GPIOB_N_11" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[15]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[1]" gpio_def="GPIOR_N_01" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[1]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[2]" gpio_def="GPIOR_P_04" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[2]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[3]" gpio_def="GPIOB_N_04" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[3]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[4]" gpio_def="GPIOR_P_00" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[4]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[5]" gpio_def="GPIOB_P_11" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[5]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[6]" gpio_def="GPIOR_P_03" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[6]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[7]" gpio_def="GPIOR_N_06" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[7]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[8]" gpio_def="GPIOR_N_00" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[8]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="addr[9]" gpio_def="GPIOR_N_07" mode="output" bus_name="addr" io_standard="1.5 V SSTL">
            <efxpt:output_config name="addr[9]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="ba[0]" gpio_def="GPIOB_N_06" mode="output" bus_name="ba" io_standard="1.5 V SSTL">
            <efxpt:output_config name="ba[0]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="ba[1]" gpio_def="GPIOB_N_10" mode="output" bus_name="ba" io_standard="1.5 V SSTL">
            <efxpt:output_config name="ba[1]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="ba[2]" gpio_def="GPIOR_P_07" mode="output" bus_name="ba" io_standard="1.5 V SSTL">
            <efxpt:output_config name="ba[2]" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cas" gpio_def="GPIOR_N_02" mode="output" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:output_config name="cas" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cke" gpio_def="GPIOB_N_09" mode="output" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:output_config name="cke" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="clk_24m" gpio_def="GPIOL_11" mode="input" bus_name="" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="clk_24m" name_ddio_lo="" conn_type="pll_clkin" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="clk_24m_PULL_UP_ENA" dyn_delay_en_name="clk_24m_DLY_ENA" dyn_delay_reset_name="clk_24m_DLY_RST" dyn_delay_ctrl_name="clk_24m_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="clk_25m" gpio_def="GPIOR_29" mode="input" bus_name="" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="clk_25m" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="clk_25m_PULL_UP_ENA" dyn_delay_en_name="clk_25m_DLY_ENA" dyn_delay_reset_name="clk_25m_DLY_RST" dyn_delay_ctrl_name="clk_25m_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="clk_n" gpio_def="GPIOR_N_09" mode="output" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:output_config name="clk_n_hi" name_ddio_lo="clk_n_lo" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="clk_p" gpio_def="GPIOR_P_09" mode="output" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:output_config name="clk_p_hi" name_ddio_lo="clk_p_lo" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_ctl1" gpio_def="GPIOR_15" mode="input" bus_name="" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_ctl1" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_ctl1_PULL_UP_ENA" dyn_delay_en_name="cmos_ctl1_DLY_ENA" dyn_delay_reset_name="cmos_ctl1_DLY_RST" dyn_delay_ctrl_name="cmos_ctl1_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_ctl2" gpio_def="GPIOL_04" mode="output" bus_name="" io_standard="3.3 V LVCMOS">
            <efxpt:output_config name="cmos_ctl2" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="2" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_ctl3" gpio_def="GPIOL_N_00" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="cmos_ctl3" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_data[0]" gpio_def="GPIOL_01" mode="input" bus_name="cmos_data" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_data[0]" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_data_PULL_UP_ENA[0]" dyn_delay_en_name="cmos_data_DLY_ENA[0]" dyn_delay_reset_name="cmos_data_DLY_RST[0]" dyn_delay_ctrl_name="cmos_data_DLY_CTRL[0]" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_data[1]" gpio_def="GPIOL_02" mode="input" bus_name="cmos_data" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_data[1]" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_data_PULL_UP_ENA[1]" dyn_delay_en_name="cmos_data_DLY_ENA[1]" dyn_delay_reset_name="cmos_data_DLY_RST[1]" dyn_delay_ctrl_name="cmos_data_DLY_CTRL[1]" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_data[2]" gpio_def="GPIOL_06" mode="input" bus_name="cmos_data" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_data[2]" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_data_PULL_UP_ENA[2]" dyn_delay_en_name="cmos_data_DLY_ENA[2]" dyn_delay_reset_name="cmos_data_DLY_RST[2]" dyn_delay_ctrl_name="cmos_data_DLY_CTRL[2]" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_data[3]" gpio_def="GPIOR_19" mode="input" bus_name="cmos_data" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_data[3]" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_data_PULL_UP_ENA[3]" dyn_delay_en_name="cmos_data_DLY_ENA[3]" dyn_delay_reset_name="cmos_data_DLY_RST[3]" dyn_delay_ctrl_name="cmos_data_DLY_CTRL[3]" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_data[4]" gpio_def="GPIOL_10" mode="input" bus_name="cmos_data" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_data[4]" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_data_PULL_UP_ENA[4]" dyn_delay_en_name="cmos_data_DLY_ENA[4]" dyn_delay_reset_name="cmos_data_DLY_RST[4]" dyn_delay_ctrl_name="cmos_data_DLY_CTRL[4]" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_data[5]" gpio_def="GPIOR_18" mode="input" bus_name="cmos_data" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_data[5]" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_data_PULL_UP_ENA[5]" dyn_delay_en_name="cmos_data_DLY_ENA[5]" dyn_delay_reset_name="cmos_data_DLY_RST[5]" dyn_delay_ctrl_name="cmos_data_DLY_CTRL[5]" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_data[6]" gpio_def="GPIOL_07" mode="input" bus_name="cmos_data" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_data[6]" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_data_PULL_UP_ENA[6]" dyn_delay_en_name="cmos_data_DLY_ENA[6]" dyn_delay_reset_name="cmos_data_DLY_RST[6]" dyn_delay_ctrl_name="cmos_data_DLY_CTRL[6]" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_data[7]" gpio_def="GPIOR_12" mode="input" bus_name="cmos_data" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_data[7]" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_data_PULL_UP_ENA[7]" dyn_delay_en_name="cmos_data_DLY_ENA[7]" dyn_delay_reset_name="cmos_data_DLY_RST[7]" dyn_delay_ctrl_name="cmos_data_DLY_CTRL[7]" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_href" gpio_def="GPIOR_20" mode="input" bus_name="" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_href" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_href_PULL_UP_ENA" dyn_delay_en_name="cmos_href_DLY_ENA" dyn_delay_reset_name="cmos_href_DLY_RST" dyn_delay_ctrl_name="cmos_href_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_pclk" gpio_def="GPIOT_P_07" mode="input" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="cmos_pclk" name_ddio_lo="" conn_type="gclk" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="15" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_pclk_PULL_UP_ENA" dyn_delay_en_name="cmos_pclk_DLY_ENA" dyn_delay_reset_name="cmos_pclk_DLY_RST" dyn_delay_ctrl_name="cmos_pclk_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_sclk" gpio_def="GPIOL_09" mode="output" bus_name="" io_standard="3.3 V LVCMOS">
            <efxpt:output_config name="cmos_sclk" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="2" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_sdat" gpio_def="GPIOR_13" mode="inout" bus_name="" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_sdat_IN" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_sdat_PULL_UP_ENA" dyn_delay_en_name="cmos_sdat_DLY_ENA" dyn_delay_reset_name="cmos_sdat_DLY_RST" dyn_delay_ctrl_name="cmos_sdat_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="cmos_sdat_OUT" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="2" fastclk_name=""/>
            <efxpt:output_enable_config name="cmos_sdat_OE" is_register="false" clock_name="" is_clock_inverted="false" name_oen="cmos_sdat_OEN"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_vsync" gpio_def="GPIOL_03" mode="input" bus_name="" io_standard="3.3 V LVCMOS">
            <efxpt:input_config name="cmos_vsync" name_ddio_lo="" conn_type="normal" is_register="true" clock_name="cmos_pclk" is_clock_inverted="true" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="cmos_vsync_PULL_UP_ENA" dyn_delay_en_name="cmos_vsync_DLY_ENA" dyn_delay_reset_name="cmos_vsync_DLY_RST" dyn_delay_ctrl_name="cmos_vsync_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cmos_xclk" gpio_def="GPIOR_16" mode="clkout" bus_name="" io_standard="3.3 V LVCMOS">
            <efxpt:output_config name="" name_ddio_lo="" register_option="none" clock_name="clk_27m" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="cs" gpio_def="GPIOB_N_08" mode="output" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:output_config name="cs" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="csi_ctl0_io" gpio_def="GPIOT_P_10" mode="inout" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="csi_ctl0_i" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="csi_ctl0_io_PULL_UP_ENA" dyn_delay_en_name="csi_ctl0_io_DLY_ENA" dyn_delay_reset_name="csi_ctl0_io_DLY_RST" dyn_delay_ctrl_name="csi_ctl0_io_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="csi_ctl0_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="csi_ctl0_oe" is_register="false" clock_name="" is_clock_inverted="false" name_oen="csi_ctl0_io_OEN"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="csi_ctl1_io" gpio_def="GPIOT_N_10" mode="inout" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="csi_ctl1_i" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="csi_ctl1_io_PULL_UP_ENA" dyn_delay_en_name="csi_ctl1_io_DLY_ENA" dyn_delay_reset_name="csi_ctl1_io_DLY_RST" dyn_delay_ctrl_name="csi_ctl1_io_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="csi_ctl1_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="csi_ctl1_oe" is_register="false" clock_name="" is_clock_inverted="false" name_oen="csi_ctl1_io_OEN"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="csi_scl_io" gpio_def="GPIOT_P_09" mode="inout" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="csi_scl_i" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="csi_scl_io_PULL_UP_ENA" dyn_delay_en_name="csi_scl_io_DLY_ENA" dyn_delay_reset_name="csi_scl_io_DLY_RST" dyn_delay_ctrl_name="csi_scl_io_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="csi_scl_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="csi_scl_oe" is_register="false" clock_name="" is_clock_inverted="false" name_oen="csi_scl_io_OEN"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="csi_sda_io" gpio_def="GPIOT_P_06" mode="inout" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="csi_sda_i" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="csi_sda_io_PULL_UP_ENA" dyn_delay_en_name="csi_sda_io_DLY_ENA" dyn_delay_reset_name="csi_sda_io_DLY_RST" dyn_delay_ctrl_name="csi_sda_io_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="csi_sda_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="csi_sda_oe" is_register="false" clock_name="" is_clock_inverted="false" name_oen="csi_sda_io_OEN"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dm[0]" gpio_def="GPIOB_P_10" mode="output" bus_name="dm" io_standard="1.5 V SSTL">
            <efxpt:output_config name="o_dm_hi[0]" name_ddio_lo="o_dm_lo[0]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dm[1]" gpio_def="GPIOB_P_07" mode="output" bus_name="dm" io_standard="1.5 V SSTL">
            <efxpt:output_config name="o_dm_hi[1]" name_ddio_lo="o_dm_lo[1]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[0]" gpio_def="GPIOB_P_12" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[0]" name_ddio_lo="i_dq_lo[0]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[0]" dyn_delay_en_name="dq[0]_DLY_ENA" dyn_delay_reset_name="dq[0]_DLY_RST" dyn_delay_ctrl_name="dq[0]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[0]" name_ddio_lo="o_dq_lo[0]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[0]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[10]" gpio_def="GPIOB_P_01" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[10]" name_ddio_lo="i_dq_lo[10]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[10]" dyn_delay_en_name="dq[10]_DLY_ENA" dyn_delay_reset_name="dq[10]_DLY_RST" dyn_delay_ctrl_name="dq[10]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[10]" name_ddio_lo="o_dq_lo[10]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[10]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[11]" gpio_def="GPIOB_P_08" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[11]" name_ddio_lo="i_dq_lo[11]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[11]" dyn_delay_en_name="dq[11]_DLY_ENA" dyn_delay_reset_name="dq[11]_DLY_RST" dyn_delay_ctrl_name="dq[11]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[11]" name_ddio_lo="o_dq_lo[11]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[11]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[12]" gpio_def="GPIOB_N_01" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[12]" name_ddio_lo="i_dq_lo[12]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[12]" dyn_delay_en_name="dq[12]_DLY_ENA" dyn_delay_reset_name="dq[12]_DLY_RST" dyn_delay_ctrl_name="dq[12]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[12]" name_ddio_lo="o_dq_lo[12]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[12]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[13]" gpio_def="GPIOB_P_03" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[13]" name_ddio_lo="i_dq_lo[13]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[13]" dyn_delay_en_name="dq[13]_DLY_ENA" dyn_delay_reset_name="dq[13]_DLY_RST" dyn_delay_ctrl_name="dq[13]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[13]" name_ddio_lo="o_dq_lo[13]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[13]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[14]" gpio_def="GPIOB_P_00" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[14]" name_ddio_lo="i_dq_lo[14]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[14]" dyn_delay_en_name="dq[14]_DLY_ENA" dyn_delay_reset_name="dq[14]_DLY_RST" dyn_delay_ctrl_name="dq[14]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[14]" name_ddio_lo="o_dq_lo[14]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[14]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[15]" gpio_def="GPIOB_N_00" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[15]" name_ddio_lo="i_dq_lo[15]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[15]" dyn_delay_en_name="dq[15]_DLY_ENA" dyn_delay_reset_name="dq[15]_DLY_RST" dyn_delay_ctrl_name="dq[15]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[15]" name_ddio_lo="o_dq_lo[15]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[15]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[1]" gpio_def="GPIOB_N_13" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[1]" name_ddio_lo="i_dq_lo[1]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[1]" dyn_delay_en_name="dq[1]_DLY_ENA" dyn_delay_reset_name="dq[1]_DLY_RST" dyn_delay_ctrl_name="dq[1]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[1]" name_ddio_lo="o_dq_lo[1]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[1]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[2]" gpio_def="GPIOB_P_14" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[2]" name_ddio_lo="i_dq_lo[2]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[2]" dyn_delay_en_name="dq[2]_DLY_ENA" dyn_delay_reset_name="dq[2]_DLY_RST" dyn_delay_ctrl_name="dq[2]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[2]" name_ddio_lo="o_dq_lo[2]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[2]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[3]" gpio_def="GPIOB_N_12" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[3]" name_ddio_lo="i_dq_lo[3]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[3]" dyn_delay_en_name="dq[3]_DLY_ENA" dyn_delay_reset_name="dq[3]_DLY_RST" dyn_delay_ctrl_name="dq[3]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[3]" name_ddio_lo="o_dq_lo[3]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[3]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[4]" gpio_def="GPIOB_N_17" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[4]" name_ddio_lo="i_dq_lo[4]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[4]" dyn_delay_en_name="dq[4]_DLY_ENA" dyn_delay_reset_name="dq[4]_DLY_RST" dyn_delay_ctrl_name="dq[4]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[4]" name_ddio_lo="o_dq_lo[4]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[4]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[5]" gpio_def="GPIOB_P_13" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[5]" name_ddio_lo="i_dq_lo[5]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[5]" dyn_delay_en_name="dq[5]_DLY_ENA" dyn_delay_reset_name="dq[5]_DLY_RST" dyn_delay_ctrl_name="dq[5]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[5]" name_ddio_lo="o_dq_lo[5]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[5]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[6]" gpio_def="GPIOB_N_14" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[6]" name_ddio_lo="i_dq_lo[6]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[6]" dyn_delay_en_name="dq[6]_DLY_ENA" dyn_delay_reset_name="dq[6]_DLY_RST" dyn_delay_ctrl_name="dq[6]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[6]" name_ddio_lo="o_dq_lo[6]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[6]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[7]" gpio_def="GPIOB_P_17" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[7]" name_ddio_lo="i_dq_lo[7]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[7]" dyn_delay_en_name="dq[7]_DLY_ENA" dyn_delay_reset_name="dq[7]_DLY_RST" dyn_delay_ctrl_name="dq[7]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[7]" name_ddio_lo="o_dq_lo[7]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[7]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[8]" gpio_def="GPIOB_N_07" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[8]" name_ddio_lo="i_dq_lo[8]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[8]" dyn_delay_en_name="dq[8]_DLY_ENA" dyn_delay_reset_name="dq[8]_DLY_RST" dyn_delay_ctrl_name="dq[8]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[8]" name_ddio_lo="o_dq_lo[8]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[8]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dq[9]" gpio_def="GPIOB_N_03" mode="inout" bus_name="dq" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dq_hi[9]" name_ddio_lo="i_dq_lo[9]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="ddr_dq_PULL_UP_ENA[9]" dyn_delay_en_name="dq[9]_DLY_ENA" dyn_delay_reset_name="dq[9]_DLY_RST" dyn_delay_ctrl_name="dq[9]_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dq_hi[9]" name_ddio_lo="o_dq_lo[9]" register_option="register" clock_name="twd_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dq_oe[9]" is_register="true" clock_name="twd_clk" is_clock_inverted="false" name_oen=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dqs[0]" gpio_def="GPIOB_P_15" mode="inout" bus_name="dqs" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dqs_hi[0]" name_ddio_lo="i_dqs_lo[0]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="dqs_PULL_UP_ENA[0]" dyn_delay_en_name="dqs_DLY_ENA[0]" dyn_delay_reset_name="dqs_DLY_RST[0]" dyn_delay_ctrl_name="dqs_DLY_CTRL[0]" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dqs_hi[0]" name_ddio_lo="o_dqs_lo[0]" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dqs_oe[0]" is_register="true" clock_name="tdqss_clk" is_clock_inverted="false" name_oen="dqs_OEN[0]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dqs[1]" gpio_def="GPIOB_P_02" mode="inout" bus_name="dqs" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dqs_hi[1]" name_ddio_lo="i_dqs_lo[1]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="dqs_PULL_UP_ENA[1]" dyn_delay_en_name="dqs_DLY_ENA[1]" dyn_delay_reset_name="dqs_DLY_RST[1]" dyn_delay_ctrl_name="dqs_DLY_CTRL[1]" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dqs_hi[1]" name_ddio_lo="o_dqs_lo[1]" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dqs_oe[1]" is_register="true" clock_name="tdqss_clk" is_clock_inverted="false" name_oen="dqs_OEN[1]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dqs_n[0]" gpio_def="GPIOB_N_15" mode="inout" bus_name="dqs_n" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dqs_n_hi[0]" name_ddio_lo="i_dqs_n_lo[0]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="dqs_n_PULL_UP_ENA[0]" dyn_delay_en_name="dqs_n_DLY_ENA[0]" dyn_delay_reset_name="dqs_n_DLY_RST[0]" dyn_delay_ctrl_name="dqs_n_DLY_CTRL[0]" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dqs_n_hi[0]" name_ddio_lo="o_dqs_n_lo[0]" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dqs_n_oe[0]" is_register="true" clock_name="tdqss_clk" is_clock_inverted="false" name_oen="dqs_n_OEN[0]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dqs_n[1]" gpio_def="GPIOB_N_02" mode="inout" bus_name="dqs_n" io_standard="1.5 V SSTL">
            <efxpt:input_config name="i_dqs_n_hi[1]" name_ddio_lo="i_dqs_n_lo[1]" conn_type="normal" is_register="true" clock_name="tac_clk" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="resync" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="dqs_n_PULL_UP_ENA[1]" dyn_delay_en_name="dqs_n_DLY_ENA[1]" dyn_delay_reset_name="dqs_n_DLY_RST[1]" dyn_delay_ctrl_name="dqs_n_DLY_CTRL[1]" clkmux_buf_name=""/>
            <efxpt:output_config name="o_dqs_n_hi[1]" name_ddio_lo="o_dqs_n_lo[1]" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="resync" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
            <efxpt:output_enable_config name="o_dqs_n_oe[1]" is_register="true" clock_name="tdqss_clk" is_clock_inverted="false" name_oen="dqs_n_OEN[1]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dsi_pwm_o" gpio_def="GPIOT_N_06" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="dsi_pwm_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="dsi_resetn_o" gpio_def="GPIOT_N_09" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="dsi_resetn_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_b7_0_io[0]" gpio_def="GPIOL_P_12" mode="inout" bus_name="lcd_b7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_b7_0_i[0]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_b7_0_o_PULL_UP_ENA[0]" dyn_delay_en_name="lcd_b7_0_io_DLY_ENA[0]" dyn_delay_reset_name="lcd_b7_0_io_DLY_RST[0]" dyn_delay_ctrl_name="lcd_b7_0_io_DLY_CTRL[0]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_b7_0_o[0]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_b7_0_oe[0]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_b7_0_o_OEN[0]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_b7_0_io[1]" gpio_def="GPIOL_N_12" mode="inout" bus_name="lcd_b7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_b7_0_i[1]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_b7_0_o_PULL_UP_ENA[1]" dyn_delay_en_name="lcd_b7_0_io_DLY_ENA[1]" dyn_delay_reset_name="lcd_b7_0_io_DLY_RST[1]" dyn_delay_ctrl_name="lcd_b7_0_io_DLY_CTRL[1]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_b7_0_o[1]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_b7_0_oe[1]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_b7_0_o_OEN[1]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_b7_0_io[2]" gpio_def="GPIOL_P_04" mode="inout" bus_name="lcd_b7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_b7_0_i[2]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_b7_0_o_PULL_UP_ENA[2]" dyn_delay_en_name="lcd_b7_0_io_DLY_ENA[2]" dyn_delay_reset_name="lcd_b7_0_io_DLY_RST[2]" dyn_delay_ctrl_name="lcd_b7_0_io_DLY_CTRL[2]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_b7_0_o[2]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_b7_0_oe[2]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_b7_0_o_OEN[2]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_b7_0_io[3]" gpio_def="GPIOL_N_04" mode="inout" bus_name="lcd_b7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_b7_0_i[3]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_b7_0_o_PULL_UP_ENA[3]" dyn_delay_en_name="lcd_b7_0_io_DLY_ENA[3]" dyn_delay_reset_name="lcd_b7_0_io_DLY_RST[3]" dyn_delay_ctrl_name="lcd_b7_0_io_DLY_CTRL[3]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_b7_0_o[3]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_b7_0_oe[3]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_b7_0_o_OEN[3]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_b7_0_io[4]" gpio_def="GPIOL_P_05" mode="inout" bus_name="lcd_b7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_b7_0_i[4]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_b7_0_o_PULL_UP_ENA[4]" dyn_delay_en_name="lcd_b7_0_io_DLY_ENA[4]" dyn_delay_reset_name="lcd_b7_0_io_DLY_RST[4]" dyn_delay_ctrl_name="lcd_b7_0_io_DLY_CTRL[4]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_b7_0_o[4]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_b7_0_oe[4]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_b7_0_o_OEN[4]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_b7_0_io[5]" gpio_def="GPIOL_N_05" mode="inout" bus_name="lcd_b7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_b7_0_i[5]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_b7_0_o_PULL_UP_ENA[5]" dyn_delay_en_name="lcd_b7_0_io_DLY_ENA[5]" dyn_delay_reset_name="lcd_b7_0_io_DLY_RST[5]" dyn_delay_ctrl_name="lcd_b7_0_io_DLY_CTRL[5]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_b7_0_o[5]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_b7_0_oe[5]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_b7_0_o_OEN[5]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_b7_0_io[6]" gpio_def="GPIOL_P_08" mode="inout" bus_name="lcd_b7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_b7_0_i[6]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_b7_0_o_PULL_UP_ENA[6]" dyn_delay_en_name="lcd_b7_0_io_DLY_ENA[6]" dyn_delay_reset_name="lcd_b7_0_io_DLY_RST[6]" dyn_delay_ctrl_name="lcd_b7_0_io_DLY_CTRL[6]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_b7_0_o[6]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_b7_0_oe[6]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_b7_0_o_OEN[6]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_b7_0_io[7]" gpio_def="GPIOL_N_08" mode="inout" bus_name="lcd_b7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_b7_0_i[7]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_b7_0_o_PULL_UP_ENA[7]" dyn_delay_en_name="lcd_b7_0_io_DLY_ENA[7]" dyn_delay_reset_name="lcd_b7_0_io_DLY_RST[7]" dyn_delay_ctrl_name="lcd_b7_0_io_DLY_CTRL[7]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_b7_0_o[7]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_b7_0_oe[7]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_b7_0_o_OEN[7]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_blen_o" gpio_def="GPIOL_P_16" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="lcd_blen_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_de_o" gpio_def="GPIOL_P_02" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="lcd_de_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_g7_0_io[0]" gpio_def="GPIOL_P_03" mode="inout" bus_name="lcd_g7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_g7_0_i[0]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_g7_0_o_PULL_UP_ENA[0]" dyn_delay_en_name="lcd_g7_0_io_DLY_ENA[0]" dyn_delay_reset_name="lcd_g7_0_io_DLY_RST[0]" dyn_delay_ctrl_name="lcd_g7_0_io_DLY_CTRL[0]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_g7_0_o[0]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_g7_0_oe[0]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_g7_0_o_OEN[0]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_g7_0_io[1]" gpio_def="GPIOL_N_03" mode="inout" bus_name="lcd_g7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_g7_0_i[1]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_g7_0_o_PULL_UP_ENA[1]" dyn_delay_en_name="lcd_g7_0_io_DLY_ENA[1]" dyn_delay_reset_name="lcd_g7_0_io_DLY_RST[1]" dyn_delay_ctrl_name="lcd_g7_0_io_DLY_CTRL[1]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_g7_0_o[1]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_g7_0_oe[1]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_g7_0_o_OEN[1]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_g7_0_io[2]" gpio_def="GPIOL_P_11" mode="inout" bus_name="lcd_g7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_g7_0_i[2]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_g7_0_o_PULL_UP_ENA[2]" dyn_delay_en_name="lcd_g7_0_io_DLY_ENA[2]" dyn_delay_reset_name="lcd_g7_0_io_DLY_RST[2]" dyn_delay_ctrl_name="lcd_g7_0_io_DLY_CTRL[2]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_g7_0_o[2]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_g7_0_oe[2]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_g7_0_o_OEN[2]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_g7_0_io[3]" gpio_def="GPIOL_N_11" mode="inout" bus_name="lcd_g7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_g7_0_i[3]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_g7_0_o_PULL_UP_ENA[3]" dyn_delay_en_name="lcd_g7_0_io_DLY_ENA[3]" dyn_delay_reset_name="lcd_g7_0_io_DLY_RST[3]" dyn_delay_ctrl_name="lcd_g7_0_io_DLY_CTRL[3]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_g7_0_o[3]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_g7_0_oe[3]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_g7_0_o_OEN[3]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_g7_0_io[4]" gpio_def="GPIOL_P_10" mode="inout" bus_name="lcd_g7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_g7_0_i[4]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_g7_0_o_PULL_UP_ENA[4]" dyn_delay_en_name="lcd_g7_0_io_DLY_ENA[4]" dyn_delay_reset_name="lcd_g7_0_io_DLY_RST[4]" dyn_delay_ctrl_name="lcd_g7_0_io_DLY_CTRL[4]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_g7_0_o[4]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_g7_0_oe[4]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_g7_0_o_OEN[4]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_g7_0_io[5]" gpio_def="GPIOL_N_10" mode="inout" bus_name="lcd_g7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_g7_0_i[5]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_g7_0_o_PULL_UP_ENA[5]" dyn_delay_en_name="lcd_g7_0_io_DLY_ENA[5]" dyn_delay_reset_name="lcd_g7_0_io_DLY_RST[5]" dyn_delay_ctrl_name="lcd_g7_0_io_DLY_CTRL[5]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_g7_0_o[5]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_g7_0_oe[5]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_g7_0_o_OEN[5]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_g7_0_io[6]" gpio_def="GPIOL_P_13" mode="inout" bus_name="lcd_g7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_g7_0_i[6]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_g7_0_o_PULL_UP_ENA[6]" dyn_delay_en_name="lcd_g7_0_io_DLY_ENA[6]" dyn_delay_reset_name="lcd_g7_0_io_DLY_RST[6]" dyn_delay_ctrl_name="lcd_g7_0_io_DLY_CTRL[6]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_g7_0_o[6]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_g7_0_oe[6]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_g7_0_o_OEN[6]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_g7_0_io[7]" gpio_def="GPIOL_N_13" mode="inout" bus_name="lcd_g7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_g7_0_i[7]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_g7_0_o_PULL_UP_ENA[7]" dyn_delay_en_name="lcd_g7_0_io_DLY_ENA[7]" dyn_delay_reset_name="lcd_g7_0_io_DLY_RST[7]" dyn_delay_ctrl_name="lcd_g7_0_io_DLY_CTRL[7]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_g7_0_o[7]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_g7_0_oe[7]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_g7_0_o_OEN[7]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_hs_o" gpio_def="GPIOL_P_06" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="lcd_hs_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_pclk_o" gpio_def="GPIOL_N_02" mode="clkout" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="" name_ddio_lo="" register_option="none" clock_name="clk_lvds_1x" is_clock_inverted="true" is_slew_rate="false" tied_option="none" ddio_type="none" delay="15" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_pwm_o" gpio_def="GPIOL_N_16" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="lcd_pwm_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_r7_0_io[0]" gpio_def="GPIOL_P_15" mode="inout" bus_name="lcd_r7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_r7_0_i[0]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_r7_0_o_PULL_UP_ENA[0]" dyn_delay_en_name="lcd_r7_0_io_DLY_ENA[0]" dyn_delay_reset_name="lcd_r7_0_io_DLY_RST[0]" dyn_delay_ctrl_name="lcd_r7_0_io_DLY_CTRL[0]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_r7_0_o[0]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_r7_0_oe[0]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_r7_0_o_OEN[0]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_r7_0_io[1]" gpio_def="GPIOL_N_15" mode="inout" bus_name="lcd_r7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_r7_0_i[1]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_r7_0_o_PULL_UP_ENA[1]" dyn_delay_en_name="lcd_r7_0_io_DLY_ENA[1]" dyn_delay_reset_name="lcd_r7_0_io_DLY_RST[1]" dyn_delay_ctrl_name="lcd_r7_0_io_DLY_CTRL[1]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_r7_0_o[1]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_r7_0_oe[1]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_r7_0_o_OEN[1]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_r7_0_io[2]" gpio_def="GPIOL_P_14" mode="inout" bus_name="lcd_r7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_r7_0_i[2]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_r7_0_o_PULL_UP_ENA[2]" dyn_delay_en_name="lcd_r7_0_io_DLY_ENA[2]" dyn_delay_reset_name="lcd_r7_0_io_DLY_RST[2]" dyn_delay_ctrl_name="lcd_r7_0_io_DLY_CTRL[2]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_r7_0_o[2]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_r7_0_oe[2]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_r7_0_o_OEN[2]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_r7_0_io[3]" gpio_def="GPIOL_N_14" mode="inout" bus_name="lcd_r7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_r7_0_i[3]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_r7_0_o_PULL_UP_ENA[3]" dyn_delay_en_name="lcd_r7_0_io_DLY_ENA[3]" dyn_delay_reset_name="lcd_r7_0_io_DLY_RST[3]" dyn_delay_ctrl_name="lcd_r7_0_io_DLY_CTRL[3]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_r7_0_o[3]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_r7_0_oe[3]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_r7_0_o_OEN[3]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_r7_0_io[4]" gpio_def="GPIOL_P_09" mode="inout" bus_name="lcd_r7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_r7_0_i[4]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_r7_0_o_PULL_UP_ENA[4]" dyn_delay_en_name="lcd_r7_0_io_DLY_ENA[4]" dyn_delay_reset_name="lcd_r7_0_io_DLY_RST[4]" dyn_delay_ctrl_name="lcd_r7_0_io_DLY_CTRL[4]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_r7_0_o[4]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_r7_0_oe[4]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_r7_0_o_OEN[4]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_r7_0_io[5]" gpio_def="GPIOL_N_09" mode="inout" bus_name="lcd_r7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_r7_0_i[5]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_r7_0_o_PULL_UP_ENA[5]" dyn_delay_en_name="lcd_r7_0_io_DLY_ENA[5]" dyn_delay_reset_name="lcd_r7_0_io_DLY_RST[5]" dyn_delay_ctrl_name="lcd_r7_0_io_DLY_CTRL[5]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_r7_0_o[5]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_r7_0_oe[5]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_r7_0_o_OEN[5]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_r7_0_io[6]" gpio_def="GPIOL_P_07" mode="inout" bus_name="lcd_r7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_r7_0_i[6]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_r7_0_o_PULL_UP_ENA[6]" dyn_delay_en_name="lcd_r7_0_io_DLY_ENA[6]" dyn_delay_reset_name="lcd_r7_0_io_DLY_RST[6]" dyn_delay_ctrl_name="lcd_r7_0_io_DLY_CTRL[6]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_r7_0_o[6]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_r7_0_oe[6]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_r7_0_o_OEN[6]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_r7_0_io[7]" gpio_def="GPIOL_N_07" mode="inout" bus_name="lcd_r7_0_io" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_r7_0_i[7]" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_r7_0_o_PULL_UP_ENA[7]" dyn_delay_en_name="lcd_r7_0_io_DLY_ENA[7]" dyn_delay_reset_name="lcd_r7_0_io_DLY_RST[7]" dyn_delay_ctrl_name="lcd_r7_0_io_DLY_CTRL[7]" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_r7_0_o[7]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_r7_0_oe[7]" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_r7_0_o_OEN[7]"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_tp_int_io" gpio_def="GPIOL_P_18" mode="inout" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_tp_int_i" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_tp_int_io_PULL_UP_ENA" dyn_delay_en_name="lcd_tp_int_io_DLY_ENA" dyn_delay_reset_name="lcd_tp_int_io_DLY_RST" dyn_delay_ctrl_name="lcd_tp_int_io_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_tp_int_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_tp_int_oe" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_tp_int_io_OEN"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_tp_rst_o" gpio_def="GPIOL_N_18" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="lcd_tp_rst_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_tp_scl_io" gpio_def="GPIOL_P_17" mode="inout" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_tp_scl_i" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_tp_scl_io_PULL_UP_ENA" dyn_delay_en_name="lcd_tp_scl_io_DLY_ENA" dyn_delay_reset_name="lcd_tp_scl_io_DLY_RST" dyn_delay_ctrl_name="lcd_tp_scl_io_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_tp_scl_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_tp_scl_oe" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_tp_scl_io_OEN"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_tp_sda_io" gpio_def="GPIOL_N_17" mode="inout" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="lcd_tp_sda_i" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="lcd_tp_sda_io_PULL_UP_ENA" dyn_delay_en_name="lcd_tp_sda_io_DLY_ENA" dyn_delay_reset_name="lcd_tp_sda_io_DLY_RST" dyn_delay_ctrl_name="lcd_tp_sda_io_DLY_CTRL" clkmux_buf_name=""/>
            <efxpt:output_config name="lcd_tp_sda_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
            <efxpt:output_enable_config name="lcd_tp_sda_oe" is_register="false" clock_name="" is_clock_inverted="false" name_oen="lcd_tp_sda_io_OEN"/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="lcd_vs_o" gpio_def="GPIOL_N_06" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="lcd_vs_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_o[0]" gpio_def="GPIOR_25" mode="output" bus_name="led_o" io_standard="3.3 V LVCMOS">
            <efxpt:output_config name="led_o[0]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_o[1]" gpio_def="GPIOR_22" mode="output" bus_name="led_o" io_standard="3.3 V LVCMOS">
            <efxpt:output_config name="led_o[1]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_o[2]" gpio_def="GPIOR_28" mode="output" bus_name="led_o" io_standard="3.3 V LVCMOS">
            <efxpt:output_config name="led_o[2]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_o[3]" gpio_def="GPIOR_21" mode="output" bus_name="led_o" io_standard="3.3 V LVCMOS">
            <efxpt:output_config name="led_o[3]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_o[4]" gpio_def="GPIOR_24" mode="output" bus_name="led_o" io_standard="3.3 V LVCMOS">
            <efxpt:output_config name="led_o[4]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="led_o[5]" gpio_def="GPIOR_27" mode="output" bus_name="led_o" io_standard="3.3 V LVCMOS">
            <efxpt:output_config name="led_o[5]" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="odt" gpio_def="GPIOB_P_06" mode="output" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:output_config name="odt" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="ras" gpio_def="GPIOR_P_02" mode="output" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:output_config name="ras" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="reset" gpio_def="GPIOR_N_08" mode="output" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:output_config name="reset" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="spi_sck_o" gpio_def="GPIOL_N_01" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="spi_sck_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="spi_ssn_o" gpio_def="GPIOL_P_01" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="spi_ssn_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="uart_rx_i" gpio_def="GPIOT_N_16" mode="input" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:input_config name="uart_rx_i" name_ddio_lo="" conn_type="normal" is_register="false" clock_name="" is_clock_inverted="false" pull_option="weak pullup" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="uart_rx_i_PULL_UP_ENA" dyn_delay_en_name="uart_rx_i_DLY_ENA" dyn_delay_reset_name="uart_rx_i_DLY_RST" dyn_delay_ctrl_name="uart_rx_i_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="uart_tx_o" gpio_def="GPIOT_P_16" mode="output" bus_name="" io_standard="1.8 V LVCMOS">
            <efxpt:output_config name="uart_tx_o" name_ddio_lo="" register_option="none" clock_name="" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="4" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="verf0" gpio_def="GPIOB_P_09" mode="input" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:input_config name="verf0" name_ddio_lo="" conn_type="vref" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="verf0_PULL_UP_ENA" dyn_delay_en_name="verf0_DLY_ENA" dyn_delay_reset_name="verf0_DLY_RST" dyn_delay_ctrl_name="verf0_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="vref1" gpio_def="GPIOB_P_04" mode="input" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:input_config name="vref1" name_ddio_lo="" conn_type="vref" is_register="false" clock_name="" is_clock_inverted="false" pull_option="none" is_schmitt_trigger="false" ddio_type="none" is_bus_hold="false" delay="0" is_serial="false" is_dyn_delay="false" fastclk_name="" pullup_ena_name="vref1_PULL_UP_ENA" dyn_delay_en_name="vref1_DLY_ENA" dyn_delay_reset_name="vref1_DLY_RST" dyn_delay_ctrl_name="vref1_DLY_CTRL" clkmux_buf_name=""/>
        </efxpt:comp_gpio>
        <efxpt:comp_gpio name="we" gpio_def="GPIOR_P_05" mode="output" bus_name="" io_standard="1.5 V SSTL">
            <efxpt:output_config name="we" name_ddio_lo="" register_option="register" clock_name="tdqss_clk" is_clock_inverted="false" is_slew_rate="false" tied_option="none" ddio_type="none" delay="0" is_serial="false" drive_strength="8" fastclk_name=""/>
        </efxpt:comp_gpio>
        <efxpt:global_unused_config state="input with weak pullup"/>
        <efxpt:bus name="dqs" mode="inout" msb="1" lsb="0"/>
        <efxpt:bus name="dm" mode="output" msb="1" lsb="0"/>
        <efxpt:bus name="ba" mode="output" msb="2" lsb="0"/>
        <efxpt:bus name="addr" mode="output" msb="15" lsb="0"/>
        <efxpt:bus name="dq" mode="inout" msb="15" lsb="0"/>
        <efxpt:bus name="cmos_data" mode="input" msb="7" lsb="0"/>
        <efxpt:bus name="dqs_n" mode="inout" msb="1" lsb="0"/>
        <efxpt:bus name="led_o" mode="output" msb="5" lsb="0"/>
        <efxpt:bus name="lcd_r7_0_io" mode="inout" msb="7" lsb="0"/>
        <efxpt:bus name="lcd_g7_0_io" mode="inout" msb="7" lsb="0"/>
        <efxpt:bus name="lcd_b7_0_io" mode="inout" msb="7" lsb="0"/>
    </efxpt:gpio_info>
    <efxpt:pll_info>
        <efxpt:pll name="sys_pll" pll_def="PLL_TL0" ref_clock_name="" ref_clock_freq="24.0000" multiplier="4" pre_divider="1" post_divider="1" reset_name="sys_pll_rstn_o" locked_name="sys_pll_lock" is_ipfrz="false" is_bypass_lock="true">
            <efxpt:adv_prop ref_clock_mode="external" ref_clock1_name="" ext_ref_clock_id="4" clksel_name="" feedback_clock_name="clk_sys" feedback_mode="core"/>
            <efxpt:gen_pin>
                <efxpt:pin name="" type_name="DESKEWED" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT_ENA" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT" is_bus="true"/>
                <efxpt:pin name="" type_name="SHIFT_SEL" is_bus="true"/>
            </efxpt:gen_pin>
            <efxpt:comp_output_clock name="clk_sys" number="0" out_divider="31" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="clk_pixel" number="1" out_divider="40" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="clk_pixel_2x" number="2" out_divider="20" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="clk_pixel_10x" number="3" out_divider="4" is_dyn_phase="false" phase_setting="2" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_prop/>
        </efxpt:pll>
        <efxpt:pll name="ddr_pll" pll_def="PLL_BR0" ref_clock_name="clk_sys" ref_clock_freq="96.0000" multiplier="2" pre_divider="1" post_divider="2" reset_name="ddr_pll_rstn_o" locked_name="ddr_pll_lock" is_ipfrz="false" is_bypass_lock="true">
            <efxpt:adv_prop ref_clock_mode="core" ref_clock1_name="" ext_ref_clock_id="4" clksel_name="" feedback_clock_name="core_clk" feedback_mode="core"/>
            <efxpt:gen_pin>
                <efxpt:pin name="" type_name="DESKEWED" is_bus="false"/>
                <efxpt:pin name="shift_ena" type_name="SHIFT_ENA" is_bus="false"/>
                <efxpt:pin name="shift" type_name="SHIFT" is_bus="false"/>
                <efxpt:pin name="shift_sel" type_name="SHIFT_SEL" is_bus="false"/>
            </efxpt:gen_pin>
            <efxpt:comp_output_clock name="tdqss_clk" number="0" out_divider="6" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="core_clk" number="1" out_divider="12" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="tac_clk" number="2" out_divider="6" is_dyn_phase="true" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="twd_clk" number="3" out_divider="6" is_dyn_phase="false" phase_setting="3" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_prop/>
        </efxpt:pll>
        <efxpt:pll name="dsi_pll" pll_def="PLL_BL0" ref_clock_name="clk_sys" ref_clock_freq="96.0000" multiplier="1" pre_divider="2" post_divider="4" reset_name="dsi_pll_rstn_o" locked_name="dsi_pll_lock" is_ipfrz="false" is_bypass_lock="true">
            <efxpt:adv_prop ref_clock_mode="core" ref_clock1_name="" ext_ref_clock_id="2" clksel_name="" feedback_clock_name="dsi_refclk_i" feedback_mode="local"/>
            <efxpt:gen_pin>
                <efxpt:pin name="" type_name="SHIFT_ENA" is_bus="false"/>
                <efxpt:pin name="" type_name="DESKEWED" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT" is_bus="true"/>
                <efxpt:pin name="" type_name="SHIFT_SEL" is_bus="true"/>
            </efxpt:gen_pin>
            <efxpt:comp_output_clock name="dsi_refclk_i" number="0" out_divider="28" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="dsi_byteclk_i" number="1" out_divider="32" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="dsi_txcclk_i" number="2" out_divider="8" is_dyn_phase="false" phase_setting="6" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="dsi_serclk_i" number="3" out_divider="8" is_dyn_phase="false" phase_setting="2" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_prop/>
        </efxpt:pll>
        <efxpt:pll name="lvds_pll" pll_def="PLL_TR0" ref_clock_name="clk_sys" ref_clock_freq="96.0000" multiplier="1" pre_divider="2" post_divider="4" reset_name="lvds_pll_rstn_o" locked_name="lvds_pll_lock" is_ipfrz="false" is_bypass_lock="true">
            <efxpt:adv_prop ref_clock_mode="core" ref_clock1_name="" ext_ref_clock_id="2" clksel_name="" feedback_clock_name="clk_lvds_1x" feedback_mode="local"/>
            <efxpt:gen_pin>
                <efxpt:pin name="" type_name="SHIFT_ENA" is_bus="false"/>
                <efxpt:pin name="" type_name="DESKEWED" is_bus="false"/>
                <efxpt:pin name="" type_name="SHIFT" is_bus="true"/>
                <efxpt:pin name="" type_name="SHIFT_SEL" is_bus="true"/>
            </efxpt:gen_pin>
            <efxpt:comp_output_clock name="clk_lvds_1x" number="0" out_divider="28" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="clk_lvds_7x" number="1" out_divider="4" is_dyn_phase="false" phase_setting="2" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="clk_27m" number="2" out_divider="84" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_output_clock name="clk_54m" number="3" out_divider="42" is_dyn_phase="false" phase_setting="0" is_inverted="false" conn_type="gclk">
                <efxpt:gen_pin/>
            </efxpt:comp_output_clock>
            <efxpt:comp_prop/>
        </efxpt:pll>
    </efxpt:pll_info>
    <efxpt:osc_info/>
    <efxpt:lvds_info>
        <efxpt:lvds name="hdmi_txc" lvds_def="GPIOT_PN_04" ops_type="tx">
            <efxpt:adv_ltx_info pll_instance="" fast_clock_name="clk_pixel_10x" slow_clock_name="clk_pixel" reset_name="hdmi_txc_rst_o" out_bname="hdmi_txc_o" oe_name="hdmi_txc_oe" delay="0" is_half_rate="false" is_serial="true" serial_width="10" mode="out" diff_type="lvds" vod="typical" pre_emphasis="medium low">
                <efxpt:gen_pin>
                    <efxpt:pin name="hdmi_txc_o" type_name="OUT" is_bus="true"/>
                    <efxpt:pin name="hdmi_txc_oe" type_name="OE" is_bus="false"/>
                    <efxpt:pin name="hdmi_txc_rst_o" type_name="OUTRST" is_bus="false"/>
                    <efxpt:pin name="clk_pixel_10x" type_name="OUTFASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="clk_pixel" type_name="OUTSLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:adv_ltx_info>
        </efxpt:lvds>
        <efxpt:lvds name="hdmi_txd0" lvds_def="GPIOT_PN_17" ops_type="tx">
            <efxpt:adv_ltx_info pll_instance="" fast_clock_name="clk_pixel_10x" slow_clock_name="clk_pixel" reset_name="hdmi_txd0_rst_o" out_bname="hdmi_txd0_o" oe_name="hdmi_txd0_oe" delay="0" is_half_rate="false" is_serial="true" serial_width="10" mode="out" diff_type="lvds" vod="typical" pre_emphasis="medium low">
                <efxpt:gen_pin>
                    <efxpt:pin name="hdmi_txd0_o" type_name="OUT" is_bus="true"/>
                    <efxpt:pin name="hdmi_txd0_oe" type_name="OE" is_bus="false"/>
                    <efxpt:pin name="hdmi_txd0_rst_o" type_name="OUTRST" is_bus="false"/>
                    <efxpt:pin name="clk_pixel_10x" type_name="OUTFASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="clk_pixel" type_name="OUTSLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:adv_ltx_info>
        </efxpt:lvds>
        <efxpt:lvds name="hdmi_txd1" lvds_def="GPIOT_PN_00" ops_type="tx">
            <efxpt:adv_ltx_info pll_instance="" fast_clock_name="clk_pixel_10x" slow_clock_name="clk_pixel" reset_name="hdmi_txd1_rst_o" out_bname="hdmi_txd1_o" oe_name="hdmi_txd1_oe" delay="0" is_half_rate="false" is_serial="true" serial_width="10" mode="out" diff_type="lvds" vod="typical" pre_emphasis="medium low">
                <efxpt:gen_pin>
                    <efxpt:pin name="hdmi_txd1_o" type_name="OUT" is_bus="true"/>
                    <efxpt:pin name="hdmi_txd1_oe" type_name="OE" is_bus="false"/>
                    <efxpt:pin name="hdmi_txd1_rst_o" type_name="OUTRST" is_bus="false"/>
                    <efxpt:pin name="clk_pixel_10x" type_name="OUTFASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="clk_pixel" type_name="OUTSLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:adv_ltx_info>
        </efxpt:lvds>
        <efxpt:lvds name="hdmi_txd2" lvds_def="GPIOT_PN_03" ops_type="tx">
            <efxpt:adv_ltx_info pll_instance="" fast_clock_name="clk_pixel_10x" slow_clock_name="clk_pixel" reset_name="hdmi_txd2_rst_o" out_bname="hdmi_txd2_o" oe_name="hdmi_txd2_oe" delay="0" is_half_rate="false" is_serial="true" serial_width="10" mode="out" diff_type="lvds" vod="typical" pre_emphasis="medium low">
                <efxpt:gen_pin>
                    <efxpt:pin name="hdmi_txd2_o" type_name="OUT" is_bus="true"/>
                    <efxpt:pin name="hdmi_txd2_oe" type_name="OE" is_bus="false"/>
                    <efxpt:pin name="hdmi_txd2_rst_o" type_name="OUTRST" is_bus="false"/>
                    <efxpt:pin name="clk_pixel_10x" type_name="OUTFASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="clk_pixel" type_name="OUTSLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:adv_ltx_info>
        </efxpt:lvds>
        <efxpt:lvds name="lvds_txc" lvds_def="GPIOT_PN_01" ops_type="tx">
            <efxpt:adv_ltx_info pll_instance="" fast_clock_name="clk_lvds_7x" slow_clock_name="clk_lvds_1x" reset_name="lvds_txc_rst_o" out_bname="lvds_txc_o" oe_name="lvds_txc_oe" delay="0" is_half_rate="false" is_serial="true" serial_width="7" mode="out" diff_type="lvds" vod="typical" pre_emphasis="medium low">
                <efxpt:gen_pin>
                    <efxpt:pin name="lvds_txc_o" type_name="OUT" is_bus="true"/>
                    <efxpt:pin name="lvds_txc_oe" type_name="OE" is_bus="false"/>
                    <efxpt:pin name="lvds_txc_rst_o" type_name="OUTRST" is_bus="false"/>
                    <efxpt:pin name="clk_lvds_7x" type_name="OUTFASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="clk_lvds_1x" type_name="OUTSLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:adv_ltx_info>
        </efxpt:lvds>
        <efxpt:lvds name="lvds_txd0" lvds_def="GPIOT_PN_13" ops_type="tx">
            <efxpt:adv_ltx_info pll_instance="" fast_clock_name="clk_lvds_7x" slow_clock_name="clk_lvds_1x" reset_name="lvds_txd0_rst_o" out_bname="lvds_txd0_o" oe_name="lvds_txd0_oe" delay="0" is_half_rate="false" is_serial="true" serial_width="7" mode="out" diff_type="lvds" vod="typical" pre_emphasis="medium low">
                <efxpt:gen_pin>
                    <efxpt:pin name="lvds_txd0_o" type_name="OUT" is_bus="true"/>
                    <efxpt:pin name="lvds_txd0_oe" type_name="OE" is_bus="false"/>
                    <efxpt:pin name="lvds_txd0_rst_o" type_name="OUTRST" is_bus="false"/>
                    <efxpt:pin name="clk_lvds_7x" type_name="OUTFASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="clk_lvds_1x" type_name="OUTSLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:adv_ltx_info>
        </efxpt:lvds>
        <efxpt:lvds name="lvds_txd1" lvds_def="GPIOT_PN_12" ops_type="tx">
            <efxpt:adv_ltx_info pll_instance="" fast_clock_name="clk_lvds_7x" slow_clock_name="clk_lvds_1x" reset_name="lvds_txd1_rst_o" out_bname="lvds_txd1_o" oe_name="lvds_txd1_oe" delay="0" is_half_rate="false" is_serial="true" serial_width="7" mode="out" diff_type="lvds" vod="typical" pre_emphasis="medium low">
                <efxpt:gen_pin>
                    <efxpt:pin name="lvds_txd1_o" type_name="OUT" is_bus="true"/>
                    <efxpt:pin name="lvds_txd1_oe" type_name="OE" is_bus="false"/>
                    <efxpt:pin name="lvds_txd1_rst_o" type_name="OUTRST" is_bus="false"/>
                    <efxpt:pin name="clk_lvds_7x" type_name="OUTFASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="clk_lvds_1x" type_name="OUTSLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:adv_ltx_info>
        </efxpt:lvds>
        <efxpt:lvds name="lvds_txd2" lvds_def="GPIOT_PN_05" ops_type="tx">
            <efxpt:adv_ltx_info pll_instance="" fast_clock_name="clk_lvds_7x" slow_clock_name="clk_lvds_1x" reset_name="lvds_txd2_rst_o" out_bname="lvds_txd2_o" oe_name="lvds_txd2_oe" delay="0" is_half_rate="false" is_serial="true" serial_width="7" mode="out" diff_type="lvds" vod="typical" pre_emphasis="medium low">
                <efxpt:gen_pin>
                    <efxpt:pin name="lvds_txd2_o" type_name="OUT" is_bus="true"/>
                    <efxpt:pin name="lvds_txd2_oe" type_name="OE" is_bus="false"/>
                    <efxpt:pin name="lvds_txd2_rst_o" type_name="OUTRST" is_bus="false"/>
                    <efxpt:pin name="clk_lvds_7x" type_name="OUTFASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="clk_lvds_1x" type_name="OUTSLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:adv_ltx_info>
        </efxpt:lvds>
        <efxpt:lvds name="lvds_txd3" lvds_def="GPIOT_PN_14" ops_type="tx">
            <efxpt:adv_ltx_info pll_instance="" fast_clock_name="clk_lvds_7x" slow_clock_name="clk_lvds_1x" reset_name="lvds_txd3_rst_o" out_bname="lvds_txd3_o" oe_name="lvds_txd3_oe" delay="0" is_half_rate="false" is_serial="true" serial_width="7" mode="out" diff_type="lvds" vod="typical" pre_emphasis="medium low">
                <efxpt:gen_pin>
                    <efxpt:pin name="lvds_txd3_o" type_name="OUT" is_bus="true"/>
                    <efxpt:pin name="lvds_txd3_oe" type_name="OE" is_bus="false"/>
                    <efxpt:pin name="lvds_txd3_rst_o" type_name="OUTRST" is_bus="false"/>
                    <efxpt:pin name="clk_lvds_7x" type_name="OUTFASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="clk_lvds_1x" type_name="OUTSLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:adv_ltx_info>
        </efxpt:lvds>
    </efxpt:lvds_info>
    <efxpt:jtag_info/>
    <efxpt:mipi_dphy_info>
        <efxpt:mipi_dphy name="csi_rxc" block_def="GPIOR_PN_17" ops_type="rx">
            <efxpt:rx_info mode="clock lane" is_reversible="false" delay="0" delay_mode="static" is_fifo="false" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="csi_rxc_HS_IN" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="csi_rxc_lp_p_i" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_lp_n_i" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_FIFO_EMPTY" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_RST" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_FIFO_RD" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_hs_term_en_o" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_hs_en_o" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxc_i" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="csi_rxd0" block_def="GPIOR_PN_18" ops_type="rx">
            <efxpt:rx_info mode="data lane" is_reversible="false" delay="17" delay_mode="static" is_fifo="false" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="csi_rxd0_hs_i" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="csi_rxd0_lp_p_i" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_lp_n_i" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_fifo_empty_i" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_rst_o" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_fifo_rd_o" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_hs_term_en_o" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_hs_en_o" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxd0_CLKOUT" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="csi_rxd1" block_def="GPIOR_PN_19" ops_type="rx">
            <efxpt:rx_info mode="data lane" is_reversible="false" delay="17" delay_mode="static" is_fifo="false" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="csi_rxd1_hs_i" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="csi_rxd1_lp_p_i" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_lp_n_i" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_fifo_empty_i" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_rst_o" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_fifo_rd_o" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_hs_term_en_o" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_hs_en_o" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxd1_CLKOUT" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="csi_rxd2" block_def="GPIOR_PN_15" ops_type="rx">
            <efxpt:rx_info mode="data lane" is_reversible="false" delay="17" delay_mode="static" is_fifo="false" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="csi_rxd2_hs_i" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="csi_rxd2_lp_p_i" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_lp_n_i" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_fifo_empty_i" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_rst_o" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_fifo_rd_o" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_hs_term_en_o" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_hs_en_o" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxd2_CLKOUT" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="csi_rxd3" block_def="GPIOR_PN_16" ops_type="rx">
            <efxpt:rx_info mode="data lane" is_reversible="false" delay="17" delay_mode="static" is_fifo="false" conn_type="gclk" clkmux_buf_name="">
                <efxpt:gen_pin>
                    <efxpt:pin name="csi_rxd3_hs_i" type_name="HS_IN" is_bus="true"/>
                    <efxpt:pin name="csi_rxd3_lp_p_i" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_lp_n_i" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_LP_N_OUT" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_LP_P_OUT" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_LP_P_OE" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_LP_N_OE" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_fifo_empty_i" type_name="FIFO_EMPTY" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_rst_o" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_fifo_rd_o" type_name="FIFO_RD" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_hs_term_en_o" type_name="HS_TERM" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_hs_en_o" type_name="HS_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_DLY_RST" type_name="DLY_RST" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_DLY_INC" type_name="DLY_INC" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_DLY_ENA" type_name="DLY_ENA" is_bus="false"/>
                    <efxpt:pin name="csi_rxd3_CLKOUT" type_name="CLKOUT" is_bus="false"/>
                </efxpt:gen_pin>
            </efxpt:rx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="dsi_txc" block_def="GPIOR_PN_12" ops_type="tx">
            <efxpt:tx_info mode="clock lane" is_reversible="false" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="dsi_txc_hs_o" type_name="HS_OUT" is_bus="true"/>
                    <efxpt:pin name="dsi_txc_lp_n_o" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txc_lp_p_o" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txc_LP_P_IN" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txc_LP_N_IN" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txc_rst_o" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="dsi_txc_hs_oe" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txc_lp_p_oe" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txc_lp_n_oe" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txcclk_i" type_name="FASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="dsi_byteclk_i" type_name="SLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="dsi_txd0" block_def="GPIOR_PN_11" ops_type="tx">
            <efxpt:tx_info mode="data lane" is_reversible="true" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="dsi_txd0_hs_o" type_name="HS_OUT" is_bus="true"/>
                    <efxpt:pin name="dsi_txd0_lp_n_o" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txd0_lp_p_o" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txd0_lp_p_i" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txd0_lp_n_i" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txd0_rst_o" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="dsi_txd0_hs_oe" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txd0_lp_p_oe" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txd0_lp_n_oe" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_serclk_i" type_name="FASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="dsi_byteclk_i" type_name="SLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="dsi_txd1" block_def="GPIOR_PN_13" ops_type="tx">
            <efxpt:tx_info mode="data lane" is_reversible="true" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="dsi_txd1_hs_o" type_name="HS_OUT" is_bus="true"/>
                    <efxpt:pin name="dsi_txd1_lp_n_o" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txd1_lp_p_o" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txd1_lp_p_i" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txd1_lp_n_i" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txd1_rst_o" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="dsi_txd1_hs_oe" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txd1_lp_p_oe" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txd1_lp_n_oe" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_serclk_i" type_name="FASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="dsi_byteclk_i" type_name="SLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="dsi_txd2" block_def="GPIOR_PN_14" ops_type="tx">
            <efxpt:tx_info mode="data lane" is_reversible="true" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="dsi_txd2_hs_o" type_name="HS_OUT" is_bus="true"/>
                    <efxpt:pin name="dsi_txd2_lp_n_o" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txd2_lp_p_o" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txd2_lp_p_i" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txd2_lp_n_i" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txd2_rst_o" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="dsi_txd2_hs_oe" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txd2_lp_p_oe" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txd2_lp_n_oe" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_serclk_i" type_name="FASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="dsi_byteclk_i" type_name="SLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
        <efxpt:mipi_dphy name="dsi_txd3" block_def="GPIOR_PN_10" ops_type="tx">
            <efxpt:tx_info mode="data lane" is_reversible="true" delay="0">
                <efxpt:gen_pin>
                    <efxpt:pin name="dsi_txd3_hs_o" type_name="HS_OUT" is_bus="true"/>
                    <efxpt:pin name="dsi_txd3_lp_n_o" type_name="LP_N_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txd3_lp_p_o" type_name="LP_P_OUT" is_bus="false"/>
                    <efxpt:pin name="dsi_txd3_lp_p_i" type_name="LP_P_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txd3_lp_n_i" type_name="LP_N_IN" is_bus="false"/>
                    <efxpt:pin name="dsi_txd3_rst_o" type_name="RST" is_bus="false"/>
                    <efxpt:pin name="dsi_txd3_hs_oe" type_name="HS_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txd3_lp_p_oe" type_name="LP_P_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_txd3_lp_n_oe" type_name="LP_N_OE" is_bus="false"/>
                    <efxpt:pin name="dsi_serclk_i" type_name="FASTCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                    <efxpt:pin name="dsi_byteclk_i" type_name="SLOWCLK" is_bus="false" is_clk="true" is_clk_invert="false"/>
                </efxpt:gen_pin>
            </efxpt:tx_info>
        </efxpt:mipi_dphy>
    </efxpt:mipi_dphy_info>
</efxpt:design_db>
