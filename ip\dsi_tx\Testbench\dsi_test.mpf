; vsim modelsim.ini file, version 10.4
[Version]
INIVersion = "10.6d"

; Copyright 1991-2018 Mentor Graphics Corporation
;
; All Rights Reserved.
;
; THIS WORK CONTAINS TRADE SECRET AND PROPRIETARY INFORMATION WHICH IS THE PROPERTY OF 
; MENTOR GRAPHICS CORPORATION OR ITS LICENSORS AND IS SUBJECT TO LICENSE TERMS.
;   

[Library]
std = $MODEL_TECH/../std
ieee = $MODEL_TECH/../ieee
vital2000 = $MODEL_TECH/../vital2000
;
; VITAL concerns:
;
; The library ieee contains (among other packages) the packages of the
; VITAL 2000 standard.  When a design uses VITAL 2000 exclusively, it should use
; the physical library ieee (recommended), or use the physical library
; vital2000, but not both.  The design can use logical library ieee and/or
; vital2000 as long as each of these maps to the same physical library, either
; ieee or vital2000.
;
; A design using the 1995 version of the VITAL packages, whether or not
; it also uses the 2000 version of the VITAL packages, must have logical library
; name ieee mapped to physical library vital1995.  (A design cannot use library
; vital1995 directly because some packages in this library use logical name ieee
; when referring to the other packages in the library.)  The design source
; should use logical name ieee when referring to any packages there except the
; VITAL 2000 packages.  Any VITAL 2000 present in the design must use logical
; name vital2000 (mapped to physical library vital2000) to refer to those
; packages.
; ieee = $MODEL_TECH/../vital1995
;
; For compatiblity with previous releases, logical library name vital2000 maps
; to library vital2000 (a different library than library ieee, containing the
; same packages).
; A design should not reference VITAL from both the ieee library and the
; vital2000 library because the vital packages are effectively different.
; A design that references both the ieee and vital2000 libraries must have
; both logical names ieee and vital2000 mapped to the same library, either of
; these:
;   $MODEL_TECH/../ieee
;   $MODEL_TECH/../vital2000
;
verilog = $MODEL_TECH/../verilog
std_developerskit = $MODEL_TECH/../std_developerskit
synopsys = $MODEL_TECH/../synopsys
modelsim_lib = $MODEL_TECH/../modelsim_lib
sv_std = $MODEL_TECH/../sv_std
mtiAvm = $MODEL_TECH/../avm
mtiRnm = $MODEL_TECH/../rnm
mtiOvm = $MODEL_TECH/../ovm-2.1.2
mtiUvm = $MODEL_TECH/../uvm-1.1d
mtiUPF = $MODEL_TECH/../upf_lib
mtiPA  = $MODEL_TECH/../pa_lib
floatfixlib = $MODEL_TECH/../floatfixlib
mc2_lib = $MODEL_TECH/../mc2_lib
osvvm = $MODEL_TECH/../osvvm

; added mapping for ADMS
mgc_ams = $MODEL_TECH/../mgc_ams
ieee_env = $MODEL_TECH/../ieee_env

;vhdl_psl_checkers = $MODEL_TECH/../vhdl_psl_checkers       // Source files only for this release
;verilog_psl_checkers = $MODEL_TECH/../verilog_psl_checkers // Source files only for this release
;mvc_lib = $MODEL_TECH/../mvc_lib
infact = $MODEL_TECH/../infact
vhdlopt_lib = $MODEL_TECH/../vhdlopt_lib

; Automatically perform logical->physical mapping for physical libraries that
; appear in -L/-Lf options with filesystem path delimiters (e.g. '.' or '/').
; The tail of the filesystem path name is chosen as the logical library name.
; For example, in the command "vopt -L ./path/to/lib1 -o opttop top",
; vopt automatically performs the mapping "lib1 -> ./path/to/lib1".
; See the User Manual for more details.
;
; AutoLibMapping = 0

work = work
[DefineOptionset]
; Define optionset entries for the various compilers, vmake, and vsim.
; These option sets can be used with the "-optionset <optionsetname>" syntax.
; i.e.
;  vlog -optionset COMPILEDEBUG top.sv
;  vsim -optionset UVMDEBUG my_top
;
; Following are some useful examples.

; define a vsim optionset for uvm debugging
UVMDEBUG = -uvmcontrol=all -msgmode both -displaymsgmode both -classdebug -onfinish stop

; define a vopt optionset for debugging
VOPTDEBUG = +acc -debugdb


[vcom]
; VHDL93 variable selects language version as the default. 
; Default is VHDL-2002.
; Value of 0 or 1987 for VHDL-1987.
; Value of 1 or 1993 for VHDL-1993.
; Default or value of 2 or 2002 for VHDL-2002.
; Value of 3 or 2008 for VHDL-2008
; Value of 4 or ams99 for VHDL-AMS-1999
; Value of 5 or ams07 for VHDL-AMS-2007
VHDL93 = 2002

; Ignore VHDL-2008 declaration of REAL_VECTOR in package STANDARD. Default is off.
; ignoreStandardRealVector = 1

; Show source line containing error. Default is off.
; Show_source = 1

; Turn off unbound-component warnings. Default is on.
; Show_Warning1 = 0

; Turn off process-without-a-wait-statement warnings. Default is on.
; Show_Warning2 = 0

; Turn off null-range warnings. Default is on.
; Show_Warning3 = 0

; Turn off no-space-in-time-literal warnings. Default is on.
; Show_Warning4 = 0

; Turn off multiple-drivers-on-unresolved-signal warnings. Default is on.
; Show_Warning5 = 0

; Turn off optimization for IEEE std_logic_1164 package. Default is on.
; Optimize_1164 = 0

; Enable compiler statistics. Specify one or more arguments: 
;                   [all,none,time,cmd,msg,perf,verbose,list]
; Add '-' to disable specific statistics. Default is [time,cmd,msg].
; Stats = time,cmd,msg

; Turn on resolving of ambiguous function overloading in favor of the
; "explicit" function declaration (not the one automatically created by
; the compiler for each type declaration). Default is off.
; The .ini file has Explicit enabled so that std_logic_signed/unsigned
; will match the behavior of synthesis tools.
Explicit = 1

; Turn off acceleration of the VITAL packages. Default is to accelerate.
; NoVital = 1

; Turn off VITAL compliance checking. Default is checking on.
; NoVitalCheck = 1

; Ignore VITAL compliance checking errors. Default is to not ignore.
; IgnoreVitalErrors = 1

; Turn off VITAL compliance checking warnings. Default is to show warnings.
; Show_VitalChecksWarnings = 0

; Turn off PSL assertion warning messages. Default is to show warnings.
; Show_PslChecksWarnings = 0

; Enable parsing of embedded PSL assertions. Default is enabled.
; EmbeddedPsl = 0

; Keep silent about case statement static warnings.
; Default is to give a warning.
; NoCaseStaticError = 1

; Keep silent about warnings caused by aggregates that are not locally static.
; Default is to give a warning.
; NoOthersStaticError = 1

; Treat as errors:
;   case statement static warnings
;   warnings caused by aggregates that are not locally static
; Overrides NoCaseStaticError, NoOthersStaticError settings.
; PedanticErrors = 1

; Turn off inclusion of debugging info within design units.
; Default is to include debugging info.
; NoDebug = 1

; Turn off "Loading..." messages. Default is messages on.
; Quiet = 1

; Turn on some limited synthesis rule compliance checking. Checks only:
;    -- signals used (read) by a process must be in the sensitivity list
; CheckSynthesis = 1

; Activate optimizations on expressions that do not involve signals,
; waits, or function/procedure/task invocations. Default is off.
; ScalarOpts = 1

; Turns on lint-style checking.
; Show_Lint = 1

; Require the user to specify a configuration for all bindings,
; and do not generate a compile time default binding for the
; component. This will result in an elaboration error of
; 'component not bound' if the user fails to do so. Avoids the rare
; issue of a false dependency upon the unused default binding.
; RequireConfigForAllDefaultBinding = 1

; Perform default binding at compile time.
; Default is to do default binding at load time.
; BindAtCompile = 1;

; Inhibit range checking on subscripts of arrays. Range checking on
; scalars defined with subtypes is inhibited by default.
; NoIndexCheck = 1

; Inhibit range checks on all (implicit and explicit) assignments to
; scalar objects defined with subtypes.
; NoRangeCheck = 1

; Set the prefix to be honored for synthesis/coverage pragma recognition.
; Default is "".
; AddPragmaPrefix = ""

; Ignore synthesis and coverage pragmas with this prefix.
; Default is "".
; IgnorePragmaPrefix = ""

; Turn on code coverage in VHDL design units. Default is off.
; Coverage = sbceft

; Turn off code coverage in VHDL subprograms. Default is on.
; CoverSub = 0

; Automatically exclude VHDL case statement OTHERS choice branches.
; This includes OTHERS choices in selected signal assigment statements.
; Default is to not exclude.
; CoverExcludeDefault = 1

; Control compiler and VOPT optimizations that are allowed when
; code coverage is on.  Refer to the comment for this in the [vlog] area. 
; CoverOpt = 3

; Turn on or off clkOpt optimization for code coverage. Default is on.
; CoverClkOpt = 1

; Turn on or off clkOpt optimization builtins for code coverage. Default is on.
; CoverClkOptBuiltins = 0

; Inform code coverage optimizations to respect VHDL 'H' and 'L'
; values on signals in conditions and expressions, and to not automatically
; convert them to '1' and '0'. Default is to not convert.
; CoverRespectHandL = 0

; Increase or decrease the maximum number of rows allowed in a UDP table
; implementing a VHDL condition coverage or expression coverage expression.
; More rows leads to a longer compile time, but more expressions covered.
; CoverMaxUDPRows = 192

; Increase or decrease the maximum number of input patterns that are present
; in FEC table. This leads to a longer compile time with more expressions
; covered with FEC metric.
; CoverMaxFECRows = 192

; Increase or decrease the limit on the size of expressions and conditions
; considered for expression and condition coverages. Higher FecUdpEffort leads 
; to higher compile, optimize and simulation time, but more expressions and 
; conditions are considered for coverage in the design. FecUdpEffort can
; be set to a number ranging from 1 (low) to 3 (high), defined as:
;   1 - (low) Only small expressions and conditions considered for coverage.
;   2 - (medium) Bigger expressions and conditions considered for coverage.
;   3 - (high) Very large expressions and conditions considered for coverage.
; The default setting is 1 (low).
; FecUdpEffort = 1

; Enable or disable Focused Expression Coverage analysis for conditions and
; expressions. Focused Expression Coverage data is provided by default when
; expression and/or condition coverage is active.
; CoverFEC = 0

; Enable or disable UDP Coverage analysis for conditions and expressions.
; UDP Coverage data is disabled by default when expression and/or condition
; coverage is active.
; CoverUDP = 1

; Enable or disable Rapid Expression Coverage mode for conditions and expressions.
; Disabling this would convert non-masking conditions in FEC tables to matching
; input patterns. 
; CoverREC = 1

; Enable or disable bit-blasting multi-bit operands of reduction prefix expressions
; for expression/condition coverage.
; NOTE: Enabling this may have a negative impact on simulation performance.
; CoverExpandReductionPrefix = 0

; Enable or disable short circuit evaluation of conditions and expressions when
; condition or expression coverage is active. Short circuit evaluation is enabled
; by default.
; CoverShortCircuit = 0

; Enable code coverage reporting of code that has been optimized away.
; The default is not to report.
; CoverReportCancelled = 1

; Enable deglitching of code coverage in combinatorial, non-clocked, processes.
; Default is no deglitching.
; CoverDeglitchOn = 1

; Control the code coverage deglitching period. A period of 0, eliminates delta
; cycle glitches. The value of CoverDeglitchPeriod needs to be either be 0 or a
; time string that includes time units. Examples: 0 or 10.0ps or "10.0 ps".
; CoverDeglitchPeriod = 0

; Use this directory for compiler temporary files instead of "work/_temp"
; CompilerTempDir = /tmp

; Set this to cause the compilers to force data to be committed to disk
; when the files are closed.
; SyncCompilerFiles = 1

; Add VHDL-AMS declarations to package STANDARD
; Default is not to add
; AmsStandard = 1

; Range and length checking will be performed on array indices and discrete
; ranges, and when violations are found within subprograms, errors will be
; reported. Default is to issue warnings for violations, because subprograms
; may not be invoked.
; NoDeferSubpgmCheck = 0

; Turn ON detection of FSMs having single bit current state variable.
; FsmSingle = 1

; Turn off reset state transitions in FSM.
; FsmResetTrans = 0

; Turn ON detection of FSM Implicit Transitions.
; FsmImplicitTrans = 1

; Controls whether or not to show immediate assertions with constant expressions
; in GUI/report/UCDB etc. By default, immediate assertions with constant
; expressions are shown in GUI/report/UCDB etc. This does not affect
; evaluation of immediate assertions.
; ShowConstantImmediateAsserts = 0

; Controls how VHDL basic identifiers are stored with the design unit.
; Does not make the language case-sensitive, affects only how declarations
; declared with basic identifiers have their names stored and printed
; (in the GUI, examine, etc.).
; Default is to preserve the case as originally depicted in the VHDL source.
; Value of 0 indicates to change all basic identifiers to lower case.
; PreserveCase = 0

; For Configuration Declarations, controls the effect that USE clauses have
; on visibility inside the configuration items being configured.  If 1
; (the default), then use pre-10.0 behavior. If 0, then for stricter LRM-compliance,
; extend the visibility of objects made visible through USE clauses into nested
; component configurations.
; OldVHDLConfigurationVisibility = 0

; Allows VHDL configuration declarations to be in a different library from
; the corresponding configured entity. Default is to not allow this for
; stricter LRM-compliance.
; SeparateConfigLibrary = 1;

; Determine how mode OUT subprogram parameters of type array and record are treated.
; If 0 (the default), then only VHDL 2008 will do this initialization.
; If 1, always initialize the mode OUT parameter to its default value.
; If 2, do not initialize the mode OUT out parameter.
; Note that prior to release 10.1, all language versions did not initialize mode
; OUT array and record type parameters, unless overridden here via this mechanism.
; In release 10.1 and later, only files compiled with VHDL 2008 will cause this
; initialization, unless overridden here.
; InitOutCompositeParam = 0

; Generate symbols debugging database in only some special cases to save on
; the number of files in the library. For other design-units, this database is
; generated on-demand in vsim. 
; Default is to to generate debugging database for all design-units.
; SmartDbgSym = 1

; Enable or disable automatic creation of missing libraries.
; Default is 1 (enabled)  
; CreateLib = 1

[vlog]
; Turn off inclusion of debugging info within design units.
; Default is to include debugging info.
; NoDebug = 1

; Turn on `protect compiler directive processing.
; Default is to ignore `protect directives.
; Protect = 1

; Turn off "Loading..." messages. Default is messages on.
; Quiet = 1

; Turn on Verilog hazard checking (order-dependent accessing of global vars).
; Default is off.
; Hazard = 1

; Turn on converting regular Verilog identifiers to uppercase. Allows case
; insensitivity for module names. Default is no conversion.
; UpCase = 1

; Activate optimizations on expressions that do not involve signals,
; waits, or function/procedure/task invocations. Default is off.
; ScalarOpts = 1

; Turns on lint-style checking.
; Show_Lint = 1

; Show source line containing error. Default is off.
; Show_source = 1

; Turn on bad option warning. Default is off.
; Show_BadOptionWarning = 1

; Revert back to IEEE 1364-1995 syntax, default is 0 (off).
; vlog95compat = 1

; Turn off PSL warning messages. Default is to show warnings.
; Show_PslChecksWarnings = 0

; Enable parsing of embedded PSL assertions. Default is enabled.
; EmbeddedPsl = 0

; Enable compiler statistics. Specify one or more arguments: 
;                   [all,none,time,cmd,msg,perf,verbose,list,kb]
; Add '-' to disable specific statistics. Default is [time,cmd,msg].
; Stats = time,cmd,msg

; Set the threshold for automatically identifying sparse Verilog memories.
; A memory with total size in bytes equal to or more than the sparse memory
; threshold gets marked as sparse automatically, unless specified otherwise
; in source code or by the +nosparse commandline option of vlog or vopt.
; The default is 1M.  (i.e. memories with total size equal
; to or greater than 1Mb are marked as sparse)
; SparseMemThreshold = 1048576 

; Set the prefix to be honored for synthesis and coverage pragma recognition.
; Default is "".
; AddPragmaPrefix = ""

; Ignore synthesis and coverage pragmas with this prefix.
; Default is "".
; IgnorePragmaPrefix = ""

; Set the option to treat all files specified in a vlog invocation as a
; single compilation unit. The default value is set to 0 which will treat
; each file as a separate compilation unit as specified in the P1800 draft standard.
; MultiFileCompilationUnit = 1

; Turn on code coverage in Verilog design units. Default is off.
; Coverage = sbceft

; Automatically exclude Verilog case statement default branches. 
; Default is to not automatically exclude defaults.
; CoverExcludeDefault = 1

; Increase or decrease the maximum number of rows allowed in a UDP table
; implementing a VHDL condition coverage or expression coverage expression.
; More rows leads to a longer compile time, but more expressions covered.
; CoverMaxUDPRows = 192

; Increase or decrease the maximum number of input patterns that are present
; in FEC table. This leads to a longer compile time with more expressions
; covered with FEC metric.
; CoverMaxFECRows = 192

; Increase or decrease the limit on the size of expressions and conditions
; considered for expression and condition coverages. Higher FecUdpEffort leads 
; to higher compile, optimize and simulation time, but more expressions and 
; conditions are considered for coverage in the design. FecUdpEffort can
; be set to a number ranging from 1 (low) to 3 (high), defined as:
;   1 - (low) Only small expressions and conditions considered for coverage.
;   2 - (medium) Bigger expressions and conditions considered for coverage.
;   3 - (high) Very large expressions and conditions considered for coverage.
; The default setting is 1 (low).
; FecUdpEffort = 1

; Enable or disable Focused Expression Coverage analysis for conditions and
; expressions. Focused Expression Coverage data is provided by default when
; expression and/or condition coverage is active.
; CoverFEC = 0

; Enable or disable UDP Coverage analysis for conditions and expressions.
; UDP Coverage data is disabled by default when expression and/or condition
; coverage is active.
; CoverUDP = 1

; Enable or disable Rapid Expression Coverage mode for conditions and expressions.
; Disabling this would convert non-masking conditions in FEC tables to matching
; input patterns. 
; CoverREC = 1

; Enable or disable bit-blasting multi-bit operands of reduction prefix expressions
; for expression/condition coverage.
; NOTE: Enabling this may have a negative impact on simulation performance.
; CoverExpandReductionPrefix = 0

; Enable or disable short circuit evaluation of conditions and expressions when
; condition or expression coverage is active. Short circuit evaluation is enabled
; by default.
; CoverShortCircuit = 0

; Enable deglitching of code coverage in combinatorial, non-clocked, processes.
; Default is no deglitching.
; CoverDeglitchOn = 1

; Control the code coverage deglitching period. A period of 0, eliminates delta
; cycle glitches. The value of CoverDeglitchPeriod needs to be either be 0 or a
; time string that includes time units. Examples: 0 or 10.0ps or "10.0 ps".
; CoverDeglitchPeriod = 0

; Turn on code coverage in VLOG `celldefine modules, modules containing
; specify blocks, and modules included using vlog -v and -y. Default is off.
; CoverCells = 1

; Enable code coverage reporting of code that has been optimized away.
; The default is not to report.
; CoverReportCancelled = 1

; Control compiler and VOPT optimizations that are allowed when
; code coverage is on. This is a number from 0 to 5, with the following
; meanings (the default is 3):
;    5 -- All allowable optimizations are on.
;    4 -- Turn off removing unreferenced code.
;    3 -- Turn off process, always block and if statement merging.
;    2 -- Turn off expression optimization, converting primitives
;         to continuous assignments, VHDL subprogram inlining.
;         and VHDL clkOpt (converting FF's to builtins).
;    1 -- Turn off continuous assignment optimizations and clock suppression.
;    0 -- Turn off Verilog module inlining and VHDL arch inlining.
; HOWEVER, if fsm coverage is turned on, optimizations will be forced to
; level 3, with also turning off converting primitives to continuous assigns.
; CoverOpt = 3

; Specify the override for the default value of "cross_num_print_missing"
; option for the Cross in Covergroups. If not specified then LRM default
; value of 0 (zero) is used. This is a compile time option.
; SVCrossNumPrintMissingDefault = 0

; Setting following to 1 would cause creation of variables which
; would represent the value of Coverpoint expressions. This is used
; in conjunction with "SVCoverpointExprVariablePrefix" option
; in the modelsim.ini
; EnableSVCoverpointExprVariable = 0

; Specify the override for the prefix used in forming the variable names
; which represent the Coverpoint expressions. This is used in conjunction with 
; "EnableSVCoverpointExprVariable" option of the modelsim.ini
; The default prefix is "expr".
; The variable name is
;    variable name => <prefix>_<coverpoint name>
; SVCoverpointExprVariablePrefix = expr

; Override for the default value of the SystemVerilog covergroup,
; coverpoint, and cross option.goal (defined to be 100 in the LRM).
; NOTE: It does not override specific assignments in SystemVerilog
; source code. NOTE: The modelsim.ini variable "SVCovergroupGoal"
; in the [vsim] section can override this value.
; SVCovergroupGoalDefault = 100

; Override for the default value of the SystemVerilog covergroup,
; coverpoint, and cross type_option.goal (defined to be 100 in the LRM)
; NOTE: It does not override specific assignments in SystemVerilog
; source code. NOTE: The modelsim.ini variable "SVCovergroupTypeGoal"
; in the [vsim] section can override this value.
; SVCovergroupTypeGoalDefault = 100

; Specify the override for the default value of "strobe" option for the
; Covergroup Type. This is a compile time option which forces "strobe" to
; a user specified default value and supersedes SystemVerilog specified
; default value of '0'(zero). NOTE: This can be overriden by a runtime
; modelsim.ini variable "SVCovergroupStrobe" in the [vsim] section.
; SVCovergroupStrobeDefault = 0

; Specify the override for the default value of "per_instance" option for the
; Covergroup variables. This is a compile time option which forces "per_instance"
; to a user specified default value and supersedes SystemVerilog specified
; default value of '0'(zero).
; SVCovergroupPerInstanceDefault = 0

; Specify the override for the default value of "get_inst_coverage" option for the
; Covergroup variables. This is a compile time option which forces 
; "get_inst_coverage" to a user specified default value and supersedes 
; SystemVerilog specified default value of '0'(zero).
; SVCovergroupGetInstCoverageDefault = 0

;
; A space separated list of resource libraries that contain precompiled
; packages.  The behavior is identical to using the "-L" switch.
; 
; LibrarySearchPath = <path/lib> [<path/lib> ...]
LibrarySearchPath = mtiAvm mtiRnm mtiOvm mtiUvm mtiUPF infact

; The behavior is identical to the "-mixedansiports" switch.  Default is off.
; MixedAnsiPorts = 1

; Enable SystemVerilog 3.1a $typeof() function. Default is off.
; EnableTypeOf = 1

; Only allow lower case pragmas. Default is disabled.
; AcceptLowerCasePragmaOnly = 1

; Set the maximum depth permitted for a recursive include file nesting.
; IncludeRecursionDepthMax = 5

; Turn ON detection of FSMs having single bit current state variable.
; FsmSingle = 1

; Turn off reset state transitions in FSM.
; FsmResetTrans = 0

; Turn off detections of FSMs having x-assignment.
; FsmXAssign = 0

; Turn ON detection of FSM Implicit Transitions.
; FsmImplicitTrans = 1

; List of file suffixes which will be read as SystemVerilog.  White space
; in extensions can be specified with a back-slash: "\ ".  Back-slashes
; can be specified with two consecutive back-slashes: "\\";
; SvFileSuffixes = sv svp svh

; This setting is the same as the vlog -sv command line switch.
; Enables SystemVerilog features and keywords when true (1).
; When false (0), the rules of IEEE Std 1364-2001 are followed and 
; SystemVerilog keywords are ignored. 
; Svlog = 0

; Prints attribute placed upon SV packages during package import
; when true (1).  The attribute will be ignored when this
; entry is false (0). The attribute name is "package_load_message".
; The value of this attribute is a string literal.
; Default is true (1).
; PrintSVPackageLoadingAttribute = 1

; Do not show immediate assertions with constant expressions in 
; GUI/reports/UCDB etc. By default immediate assertions with constant 
; expressions are shown in GUI/reports/UCDB etc. This does not affect 
; evaluation of immediate assertions.
; ShowConstantImmediateAsserts = 0

; Controls if untyped parameters that are initialized with values greater
; than 2147483647 are mapped to generics of type INTEGER or ignored.
; If mapped to VHDL Integers, values greater than 2147483647
; are mapped to negative values.
; Default is to map these parameter to generic of type INTEGER
; ForceUnsignedToVHDLInteger = 1

; Enable AMS wreal (wired real) extensions.  Default is 0.
; WrealType = 1

; Controls SystemVerilog Language Extensions.  These options enable
; some non-LRM compliant behavior.
; SvExtensions = [+|-]<extension>[,[+|-]<extension>*]

; Generate symbols debugging database in only some special cases to save on
; the number of files in the library. For other design-units, this database is
; generated on-demand in vsim. 
; Default is to to generate debugging database for all design-units.
; SmartDbgSym = 1

; Controls how $unit library entries are named.  Valid options are:
; "file" (generate name based on the first file on the command line)
; "du" (generate name based on first design unit following an item
; found in $unit scope)
; CUAutoName = file

; Enable or disable automatic creation of missing libraries.
; Default is 1 (enabled)  
; CreateLib = 1

[sccom]
; Enable use of SCV include files and library.  Default is off.
; UseScv = 1

; Add C++ compiler options to the sccom command line by using this variable.
; CppOptions = -g

; Use custom C++ compiler located at this path rather than the default path.
; The path should point directly at a compiler executable.
; CppPath = /usr/bin/g++

; Specify the compiler version from the list of support GNU compilers.
; examples 4.3.3, 4.5.0
; CppInstall = 4.5.0

; Enable verbose messages from sccom.  Default is off.
; SccomVerbose = 1

; sccom logfile.  Default is no logfile.
; SccomLogfile = sccom.log

; Enable use of SC_MS include files and library.  Default is off.
; UseScMs = 1

; Use SystemC-2.2 instead of the default SystemC-2.3. Default is off.
; Sc22Mode = 1

; Enable compiler statistics. Specify one or more arguments: 
;                   [all,none,time,cmd,msg,perf,verbose,list,kb]
; Add '-' to disable specific statistics. Default is [time,cmd,msg].
; Stats = time,cmd,msg

; Enable or disable automatic creation of missing libraries.
; Default is 1 (enabled)  
; CreateLib = 1

; Enable use of UVMC library.  Default is off.
; UseUvmc = 1

[vopt]
; Turn on code coverage in vopt.  Default is off. 
; Coverage = sbceft

; Control compiler optimizations that are allowed when
; code coverage is on.  Refer to the comment for this in the [vlog] area. 
; CoverOpt = 3

; Controls set of CoverConstructs that are being considered for Coverage 
; Collection.
; Some of Valid options are: default,set1,set2
; Covermode = default

; Controls set of HDL cover constructs that would be considered(or not considered)
; for Coverage Collection. (Default corresponds to covermode default). 
; Some of Valid options are: "ca", "citf", "cifl", "tcint", "fsmqs".
; Coverconstruct = noca,nocitf,nofsmtf,nofsmds,noctes,nocicl,nocprc,nocfl,nofsmup,nocifl,nocpm,notcint,nocpkg,nocsva

; Increase or decrease the maximum number of rows allowed in a UDP table
; implementing a VHDL condition coverage or expression coverage expression.
; More rows leads to a longer compile time, but more expressions covered.
; CoverMaxUDPRows = 192

; Increase or decrease the maximum number of input patterns that are present
; in FEC table. This leads to a longer compile time with more expressions
; covered with FEC metric.
; CoverMaxFECRows = 192

; Increase or decrease the limit on the size of expressions and conditions
; considered for expression and condition coverages. Higher FecUdpEffort leads 
; to higher compile, optimize and simulation time, but more expressions and 
; conditions are considered for coverage in the design. FecUdpEffort can
; be set to a number ranging from 1 (low) to 3 (high), defined as:
;   1 - (low) Only small expressions and conditions considered for coverage.
;   2 - (medium) Bigger expressions and conditions considered for coverage.
;   3 - (high) Very large expressions and conditions considered for coverage.
; The default setting is 1 (low).
; FecUdpEffort = 1

; Enable code coverage reporting of code that has been optimized away.
; The default is not to report.
; CoverReportCancelled = 1

; Enable deglitching of code coverage in combinatorial, non-clocked, processes.
; Default is no deglitching.
; CoverDeglitchOn = 1

; Enable compiler statistics. Specify one or more arguments: 
;                   [all,none,time,cmd,msg,perf,verbose,list,kb]
; Add '-' to disable specific statistics. Default is [time,cmd,msg].
; Stats = time,cmd,msg

; Control the code coverage deglitching period. A period of 0, eliminates delta
; cycle glitches. The value of CoverDeglitchPeriod needs to be either be 0 or a
; time string that includes time units. Examples: 0 or 10.0ps or "10.0 ps".
; CoverDeglitchPeriod = 0

; Do not show immediate assertions with constant expressions in 
; GUI/reports/UCDB etc. By default immediate assertions with constant 
; expressions are shown in GUI/reports/UCDB etc. This does not affect 
; evaluation of immediate assertions.
; ShowConstantImmediateAsserts = 0

; Set the maximum number of iterations permitted for a generate loop.
; Restricting this permits the implementation to recognize infinite
; generate loops.
; GenerateLoopIterationMax = 100000

; Set the maximum depth permitted for a recursive generate instantiation.
; Restricting this permits the implementation to recognize infinite
; recursions.
; GenerateRecursionDepthMax = 200

; Set the number of processes created during the code generation phase.
; By default a heuristic is used to set this value.  This may be set to 0
; to disable this feature completely.
; ParallelJobs = 0 

; Controls SystemVerilog Language Extensions.  These options enable
; some non-LRM compliant behavior.
; SvExtensions = [+|-]<extension>[,[+|-]<extension>*]

; Load the specified shared objects with the RTLD_GLOBAL flag.
; This gives global visibility to all symbols in the shared objects,
; meaning that subsequently loaded shared objects can bind to symbols
; in the global shared objects.  The list of shared objects should
; be whitespace delimited.  This option is not supported on the
; Windows or AIX platforms.
; GlobalSharedObjectList = example1.so example2.so example3.so

; Disable SystemVerilog elaboration system task messages
; IgnoreSVAInfo = 1 
; IgnoreSVAWarning = 1
; IgnoreSVAError = 1
; IgnoreSVAFatal = 1

; Enable or disable automatic creation of missing libraries.
; Default is 1 (enabled)  
; CreateLib = 1


[vsim]
; vopt flow
; Set to turn on automatic optimization of a design.
; Default is on
VoptFlow = 1

; Simulator resolution
; Set to fs, ps, ns, us, ms, or sec with optional prefix of 1, 10, or 100.
Resolution = ns

; Disable certain code coverage exclusions automatically. 
; Assertions and FSM are exluded from the code coverage by default
; Set AutoExclusionsDisable = fsm to enable code coverage for fsm
; Set AutoExclusionsDisable = assertions to enable code coverage for assertions
; Set AutoExclusionsDisable = all to enable code coverage for all the automatic exclusions
; Or specify comma or space separated list
;AutoExclusionsDisable = fsm,assertions

; User time unit for run commands
; Set to default, fs, ps, ns, us, ms, or sec. The default is to use the
; unit specified for Resolution. For example, if Resolution is 100ps,
; then UserTimeUnit defaults to ps.
; Should generally be set to default.
UserTimeUnit = default

; Default run length
RunLength = 100 ms

; Maximum iterations that can be run without advancing simulation time
IterationLimit = 10000000

; Specify libraries to be searched for precompiled modules
; LibrarySearchPath = <path/lib> [<path/lib> ...]

; Set XPROP assertion fail limit. Default is 5.
; Any positive integer, -1 for infinity.
; XpropAssertionLimit = 5

; Control PSL and Verilog Assume directives during simulation
; Set SimulateAssumeDirectives = 0 to disable assume being simulated as asserts
; Set SimulateAssumeDirectives = 1 to enable assume simulation as asserts
; SimulateAssumeDirectives = 1 

; Control the simulation of PSL and SVA
; These switches can be overridden by the vsim command line switches:
;    -psl, -nopsl, -sva, -nosva.
; Set SimulatePSL = 0 to disable PSL simulation
; Set SimulatePSL = 1 to enable PSL simulation (default)
; SimulatePSL = 1 
; Set SimulateSVA = 0 to disable SVA simulation
; Set SimulateSVA = 1 to enable concurrent SVA simulation (default)
; SimulateSVA = 1 

; Control SVA and VHDL immediate assertion directives during simulation
; Set SimulateImmedAsserts = 0 to disable simulation of immediate asserts 
; Set SimulateImmedAsserts = 1 to enable simulation of immediate asserts
; SimulateImmedAsserts = 1 

; License feature mappings for Verilog and VHDL
; qhsimvh       Single language VHDL license
; qhsimvl       Single language Verilog license
; msimhdlsim    Language neutral license for either Verilog or VHDL
; msimhdlmix    Second language only, language neutral license for either 
;               Verilog or VHDL
;
; Directives to license manager can be set either as single value or as
; space separated multi-values:
; vhdl          Immediately checkout and hold a VHDL license (i.e., one of
;               qhsimvh, msimhdlsim, or msimhdlmix)
; vlog          Immediately checkout and hold a Verilog license (i.e., one of
;               qhsimvl, msimhdlsim, or msimhdlmix)
; plus          Immediately checkout and hold a VHDL license and a Verilog license
; noqueue       Do not wait in the license queue when a license is not available
; viewsim       Try for viewer license but accept simulator license(s) instead
;               of queuing for viewer license (PE ONLY)
; noviewer      Disable checkout of msimviewer license feature (PE ONLY)
; noslvhdl      Disable checkout of qhsimvh license feature
; noslvlog      Disable checkout of qhsimvl license feature
; nomix         Disable checkout of msimhdlmix license feature
; nolnl         Disable checkout of msimhdlsim license feature
; mixedonly     Disable checkout of qhsimvh and qhsimvl license features
; lnlonly       Disable checkout of qhsimvh,qhsimvl, and msimhdlmix license features
;
; Examples (remove ";" comment character to activate licensing directives):
; Single directive:
; License = plus
; Multi-directive (Note: space delimited directives):
; License = noqueue plus

; Severity level of a VHDL assertion message or of a SystemVerilog severity system task
; which will cause a running simulation to stop.
; VHDL assertions and SystemVerilog severity system task that occur with the
; given severity or higher will cause a running simulation to stop.
; This value is ignored during elaboration.
; 0 = Note  1 = Warning  2 = Error  3 = Failure  4 = Fatal
BreakOnAssertion = 3

; Severity level of a tool message which will cause a running simulation to 
; stop. This value is ignored during elaboration. Default is to not break.
; 0 = Note  1 = Warning  2 = Error  3 = Fatal
;BreakOnMessage = 2

; The class debug feature enables more visibility and tracking of class instances
; during simulation.  By default this feature is disabled (0).  To enable this 
; feature set ClassDebug to 1.
; ClassDebug = 1

; Message Format conversion specifications:
; %S - Severity Level of message/assertion
; %R - Text of message
; %T - Time of message
; %D - Delta value (iteration number) of Time
; %K - Kind of path: Instance/Region/Signal/Process/Foreign Process/Unknown/Protected
; %i - Instance/Region/Signal pathname with Process name (if available)
; %I - shorthand for one of these:
;      "  %K: %i"
;      "  %K: %i File: %F" (when path is not Process or Signal)
;      except that the %i in this case does not report the Process name
; %O - Process name
; %P - Instance/Region path without leaf process
; %F - File name
; %L - Line number; if assertion message, then line number of assertion or, if
;      assertion is in a subprogram, line from which the call is made
; %u - Design unit name in form library.primary
; %U - Design unit name in form library.primary(secondary)
; %% - The '%' character itself
;
; If specific format for Severity Level is defined, use that format.
; Else, for a message that occurs during elaboration:
;   -- Failure/Fatal message in VHDL region that is not a Process, and in
;      certain non-VHDL regions, uses MessageFormatBreakLine;
;   -- Failure/Fatal message otherwise uses MessageFormatBreak;
;   -- Note/Warning/Error message uses MessageFormat.
; Else, for a message that occurs during runtime and triggers a breakpoint because
; of the BreakOnAssertion setting:
;   -- if in a VHDL region that is not a Process, uses MessageFormatBreakLine;
;   -- otherwise uses MessageFormatBreak.
; Else (a runtime message that does not trigger a breakpoint) uses MessageFormat.
;
; MessageFormatNote      = "** %S: %R\n   Time: %T  Iteration: %D%I\n"
; MessageFormatWarning   = "** %S: %R\n   Time: %T  Iteration: %D%I\n"
; MessageFormatError     = "** %S: %R\n   Time: %T  Iteration: %D  %K: %i File: %F\n"
; MessageFormatFail      = "** %S: %R\n   Time: %T  Iteration: %D  %K: %i File: %F\n"
; MessageFormatFatal     = "** %S: %R\n   Time: %T  Iteration: %D  %K: %i File: %F\n"
; MessageFormatBreakLine = "** %S: %R\n   Time: %T  Iteration: %D  %K: %i File: %F Line: %L\n"
; MessageFormatBreak     = "** %S: %R\n   Time: %T  Iteration: %D  %K: %i File: %F\n"
; MessageFormat          = "** %S: %R\n   Time: %T  Iteration: %D%I\n"

; Error File - alternate file for storing error messages
; ErrorFile = error.log

; Simulation Breakpoint messages
; This flag controls the display of function names when reporting the location
; where the simulator stops because of a breakpoint or fatal error.
; Example with function name:    # Break in Process ctr at counter.vhd line 44
; Example without function name: # Break at counter.vhd line 44
; Default value is 1.
ShowFunctions = 1

; Default radix for all windows and commands.
; Radix may be one of: symbolic, ascii, binary, octal, decimal, hex, unsigned
; Flags may be one of: enumnumeric, showbase, wreal
DefaultRadix = hexadecimal
DefaultRadixFlags = showbase
; Set to 1 for make the signal_force VHDL and Verilog functions use the 
; default radix when processing the force value. Prior to 10.2 signal_force
; used the default radix, now it always uses symbolic unless value explicitly indicates base
;SignalForceFunctionUseDefaultRadix = 0

; VSIM Startup command
; Startup = do startup.do

; VSIM Shutdown file
; Filename to save u/i formats and configurations.
; ShutdownFile = restart.do
; To explicitly disable auto save:
; ShutdownFile = --disable-auto-save

; Run simulator in batch mode as if -batch were specified on the command line if none of -c, -gui, or -i specified.
; Simulator runs in interactive mode as if -i were specified if this option is 0. Default is 0.
; BatchMode = 1

; File for saving command transcript when -batch option used
; This option is ignored when -c, -gui, or -i options are used or if BatchMode above is zero
; default is unset so command transcript only goes to stdout for better performance
; BatchTranscriptFile = transcript

; File for saving command transcript, this option is ignored when -batch option is used
TranscriptFile = transcript

; Transcript file long line wrapping mode(s)
;   mode == 0 :: no wrapping, line recorded as is
;   mode == 1 :: wrap at first whitespace after WSColumn
;                or at Column.
;   mode == 2 :: wrap as above, but add continuation 
;                character ('\') at end of each wrapped line
;
; WrapMode = 0
; WrapColumn = 30000
; WrapWSColumn = 27000

; File for saving command history
; CommandHistory = cmdhist.log

; Specify whether paths in simulator commands should be described
; in VHDL or Verilog format.
; For VHDL, PathSeparator = /
; For Verilog, PathSeparator = .
; Must not be the same character as DatasetSeparator.
PathSeparator = /

; Specify the dataset separator for fully rooted contexts.
; The default is ':'. For example: sim:/top
; Must not be the same character as PathSeparator.
DatasetSeparator = :

; Specify a unique path separator for the Signal Spy set of functions. 
; The default will be to use the PathSeparator variable.
; Must not be the same character as DatasetSeparator.
; SignalSpyPathSeparator = /

; Used to control parsing of HDL identifiers input to the tool.
; This includes CLI commands, vsim/vopt/vlog/vcom options,
; string arguments to FLI/VPI/DPI calls, etc.
; If set to 1, accept either Verilog escaped Id syntax or
; VHDL extended id syntax, regardless of source language.
; If set to 0, the syntax of the source language must be used.
; Each identifier in a hierarchical name may need different syntax,
; e.g. "/top/\vhdl*ext*id\/middle/\vlog*ext*id /bottom" or
;       "top.\vhdl*ext*id\.middle.\vlog*ext*id .bottom"
; GenerousIdentifierParsing = 1

; Disable VHDL assertion messages
; IgnoreNote = 1
; IgnoreWarning = 1
; IgnoreError = 1
; IgnoreFailure = 1

; Disable SystemVerilog assertion messages
; IgnoreSVAInfo = 1 
; IgnoreSVAWarning = 1
; IgnoreSVAError = 1
; IgnoreSVAFatal = 1

; Do not print any additional information from Severity System tasks.
; Only the message provided by the user is printed along with severity
; information.
; SVAPrintOnlyUserMessage = 1;

; Default force kind. May be freeze, drive, deposit, or default
; or in other terms, fixed, wired, or charged.
; A value of "default" will use the signal kind to determine the
; force kind, drive for resolved signals, freeze for unresolved signals
; DefaultForceKind = freeze

; Control the iteration of events when a VHDL signal is forced to a value
; This flag can be set to honour the signal update event in next iteration,
; the default is to update and propagate in the same iteration.
; ForceSigNextIter = 1

; Enable simulation statistics. Specify one or more arguments: 
;                   [all,none,time,cmd,msg,perf,verbose,list,kb,eor]
; Add '-' to disable specific statistics. Default is [time,cmd,msg].
; Stats = time,cmd,msg

; If zero, open files when elaborated; otherwise, open files on
; first read or write.  Default is 0.
; DelayFileOpen = 1

; Control VHDL files opened for write.
;   0 = Buffered, 1 = Unbuffered
UnbufferedOutput = 0

; Control the number of VHDL files open concurrently.
; This number should always be less than the current ulimit
; setting for max file descriptors.
;   0 = unlimited
ConcurrentFileLimit = 40

; If nonzero, close files as soon as there is either an explicit call to
; file_close, or when the file variable's scope is closed. When zero, a
; file opened in append mode is not closed in case it is immediately
; reopened in append mode; otherwise, the file will be closed at the
; point it is reopened.
; AppendClose = 1

; Control the number of hierarchical regions displayed as
; part of a signal name shown in the Wave window.
; A value of zero tells VSIM to display the full name.
; The default is 0.
; WaveSignalNameWidth = 0

; Turn off warnings when changing VHDL constants and generics
; Default is 1 to generate warning messages
; WarnConstantChange = 0

; Turn off warnings from accelerated versions of the std_logic_arith,
; std_logic_unsigned, and std_logic_signed packages.
; StdArithNoWarnings = 1

; Turn off warnings from accelerated versions of the IEEE numeric_std
; and numeric_bit packages.
; NumericStdNoWarnings = 1

; Use old-style (pre-6.6) VHDL FOR GENERATE statement iteration names
; in the design hierarchy.
; This style is controlled by the value of the GenerateFormat
; value described next.  Default is to use new-style names, which
; comprise the generate statement label, '(', the value of the generate
; parameter, and a closing ')'.
; Set this to 1 to use old-style names.
; OldVhdlForGenNames = 1

; Control the format of the old-style VHDL FOR generate statement region
; name for each iteration.  Do not quote the value.
; The format string here must contain the conversion codes %s and %d,
; in that order, and no other conversion codes.  The %s represents
; the generate statement label; the %d represents the generate parameter value
; at a particular iteration (this is the position number if the generate parameter
; is of an enumeration type).  Embedded whitespace is allowed (but discouraged);
; leading and trailing whitespace is ignored.
; Application of the format must result in a unique region name over all
; loop iterations for a particular immediately enclosing scope so that name
; lookup can function properly.  The default is %s__%d.
; GenerateFormat = %s__%d

; Enable more efficient logging of VHDL Variables.
; Logging VHDL variables without this enabled, while possible, is very
; inefficient.  Enabling this will provide a more efficient logging methodology
; at the expense of more memory usage.  By default this feature is disabled (0).
; To enabled this feature, set this variable to 1.
; VhdlVariableLogging = 1

; Enable logging of VHDL access type variables and their designated objects.
; This setting will allow both variables of an access type ("access variables")
; and their designated objects ("access objects") to be logged.  Logging a
; variable of an access type will automatically also cause the designated
; object(s) of that variable to be logged as the simulation progresses.
; Further, enabling this allows access objects to be logged by name.  By default
; this feature is disabled (0).  To enable this feature, set this variable to 1.
; Enabling this will automatically enable the VhdlVariableLogging feature also.
; AccessObjDebug = 1

; Make each VHDL package in a PDU has its own separate copy of the package instead
; of sharing the package between PDUs. The default is to share packages.
; To ensure that each PDU has its own set of packages, set this variable to 1.
; VhdlSeparatePduPackage = 1

; Specify whether checkpoint files should be compressed.
; The default is 1 (compressed).
; CheckpointCompressMode = 0

; Specify gcc compiler used in the compilation of automatically generated DPI exportwrapper.
; Use custom gcc compiler located at this path rather than the default path.
; The path should point directly at a compiler executable.
; DpiCppPath = <your-gcc-installation>/bin/gcc
;
; Specify the compiler version from the list of support GNU compilers.
; examples 4.5.0, 4.7.4
; DpiCppInstall = 4.7.4

; Specify whether to enable SystemVerilog DPI "out-of-the-blue" calls.
; The term "out-of-the-blue" refers to SystemVerilog export function calls
; made from C functions that don't have the proper context setup
; (as is the case when running under "DPI-C" import functions).
; When this is enabled, one can call a DPI export function
; (but not task) from any C code.
; the setting of this variable can be one of the following values:
; 0 : dpioutoftheblue call is disabled (default)
; 1 : dpioutoftheblue call is enabled, but export call debug support is not available.
; 2 : dpioutoftheblue call is enabled, and limited export call debug support is available.
; DpiOutOfTheBlue = 1

; Specify whether continuous assignments are run before other normal priority
; processes scheduled in the same iteration. This event ordering minimizes race
; differences between optimized and non-optimized designs, and is the default
; behavior beginning with the 6.5 release. For pre-6.5 event ordering, set
; ImmediateContinuousAssign to 0.
; The default is 1 (enabled).
; ImmediateContinuousAssign = 0

; List of dynamically loaded objects for Verilog PLI applications
; Veriuser = veriuser.sl

; Which default VPI object model should the tool conform to?
; The 1364 modes are Verilog-only, for backwards compatibility with older
; libraries, and SystemVerilog objects are not available in these modes.
; 
; In the absence of a user-specified default, the tool default is the
; latest available LRM behavior.
; Options for PliCompatDefault are:
;  VPI_COMPATIBILITY_VERSION_1364v1995
;  VPI_COMPATIBILITY_VERSION_1364v2001
;  VPI_COMPATIBILITY_VERSION_1364v2005
;  VPI_COMPATIBILITY_VERSION_1800v2005
;  VPI_COMPATIBILITY_VERSION_1800v2008
;
; Synonyms for each string are also recognized:
;  VPI_COMPATIBILITY_VERSION_1364v1995 (1995, 95, 1364v1995, 1364V1995, VL1995)
;  VPI_COMPATIBILITY_VERSION_1364v2001 (2001, 01, 1364v2001, 1364V2001, VL2001)
;  VPI_COMPATIBILITY_VERSION_1364v2005 (1364v2005, 1364V2005, VL2005)
;  VPI_COMPATIBILITY_VERSION_1800v2005 (2005, 05, 1800v2005, 1800V2005, SV2005)
;  VPI_COMPATIBILITY_VERSION_1800v2008 (2008, 08, 1800v2008, 1800V2008, SV2008)


; PliCompatDefault = VPI_COMPATIBILITY_VERSION_1800v2005

; Specify whether the Verilog system task $fopen or vpi_mcd_open()
; will create directories that do not exist when opening the file
; in "a" or "w" mode.
; The default is 0 (do not create non-existent directories)
; CreateDirForFileAccess = 1

; Specify default options for the restart command. Options can be one
; or more of: -force -nobreakpoint -nolist -nolog -nowave -noassertions
; DefaultRestartOptions = -force


; Specify default UVM-aware debug options if the vsim -uvmcontrol switch is not used.
; Valid options include: all, none, verbose, disable, struct, reseed, msglog, trlog, certe.
; Options can be enabled by just adding the name, or disabled by prefixing the option with a "-".
; The list of options must be delimited by commas, without spaces or tabs.
;
; Some examples
; To turn on all available UVM-aware debug features:
; UVMControl = all
; To turn on the struct window, mesage logging, and transaction logging:
; UVMControl = struct,msglog,trlog
; To turn on all options except certe:
; UVMControl = all,-certe
; To completely disable all UVM-aware debug functionality:
; UVMControl = disable

; Specify the WildcardFilter setting.
; A space separated list of object types to be excluded when performing
; wildcard matches with log, wave, etc commands.  The default value for this variable is:
;   "Variable Constant Generic Parameter SpecParam Memory Assertion Cover Endpoint ScVariable CellInternal ImmediateAssert VHDLFile"
; See "Using the WildcardFilter Preference Variable" in the documentation for
; details on how to use this variable and for descriptions of the filter types.
WildcardFilter = Variable Constant Generic Parameter SpecParam Memory Assertion Cover Endpoint ScVariable CellInternal ImmediateAssert VHDLFile

; Specify the WildcardSizeThreshold setting.
; This integer setting specifies the size at which objects will be excluded when 
; performing wildcard matches with log, wave, etc commands.  Objects of size equal
; to or greater than the WildcardSizeThreshold will be filtered out from the wildcard
; matches.  The size is a simple calculation of number of bits or items in the object.  
; The default value is 8k (8192).  Setting this value to 0 will disable the checking 
; of object size against this threshold and allow all objects of any size to be logged.
WildcardSizeThreshold = 8192

; Specify whether warning messages are output when objects are filtered out due to the
; WildcardSizeThreshold.  The default is 0 (no messages generated).
WildcardSizeThresholdVerbose = 0

; Turn on (1) or off (0) WLF file compression.
; The default is 1 (compress WLF file).
; WLFCompress = 0

; Specify whether to save all design hierarchy (1) in the WLF file
; or only regions containing logged signals (0).
; The default is 0 (save only regions with logged signals).
; WLFSaveAllRegions = 1

; WLF file time limit.  Limit WLF file by time, as closely as possible,
; to the specified amount of simulation time.  When the limit is exceeded
; the earliest times get truncated from the file.
; If both time and size limits are specified the most restrictive is used.
; UserTimeUnits are used if time units are not specified.
; The default is 0 (no limit).  Example: WLFTimeLimit = {100 ms}
; WLFTimeLimit = 0

; WLF file size limit.  Limit WLF file size, as closely as possible,
; to the specified number of megabytes.  If both time and size limits
; are specified then the most restrictive is used.
; The default is 0 (no limit).
; WLFSizeLimit = 1000

; Specify whether or not a WLF file should be deleted when the
; simulation ends.  A value of 1 will cause the WLF file to be deleted.
; The default is 0 (do not delete WLF file when simulation ends).
; WLFDeleteOnQuit = 1

; Specify whether or not a WLF file should be optimized during 
; simulation.  If set to 0, the WLF file will not be optimized.
; The default is 1, optimize the WLF file.
; WLFOptimize = 0

; Specify the name of the WLF file.
; The default is vsim.wlf
; WLFFilename = vsim.wlf

; Specify whether to lock the WLF file.
; Locking the file prevents other invocations of ModelSim/Questa tools from
; inadvertently overwriting the WLF file.
; The default is 1, lock the WLF file.
; WLFFileLock = 0

; Specify the update interval for the WLF file in live simulation.
; The interval is given in seconds.
; The value is the smallest interval between WLF file updates.  The WLF file
; will be flushed (updated) after (at least) the interval has elapsed, ensuring
; that the data is correct when viewed from a separate viewer.
; A value of 0 means that no updating will occur.
; The default value is 10 seconds.
; WLFUpdateInterval = 10

; Specify the WLF cache size limit for WLF files.
; The value is given in megabytes.  A value of 0 turns off the cache.
; On non-Windows platforms the default WLFCacheSize setting is 2000 (megabytes).
; On Windows, the default value is 1000 (megabytes) to help to avoid filling
; process memory.
; WLFSimCacheSize allows a different cache size to be set for a live simulation
; WLF file, independent of post-simulation WLF file viewing.  If WLFSimCacheSize
; is not set, it defaults to the WLFCacheSize value.
; WLFCacheSize = 2000
; WLFSimCacheSize = 500

; Specify the WLF file event collapse mode.
; 0 = Preserve all events and event order. (same as -wlfnocollapse)
; 1 = Only record values of logged objects at the end of a simulator iteration. 
;     (same as -wlfcollapsedelta)
; 2 = Only record values of logged objects at the end of a simulator time step. 
;     (same as -wlfcollapsetime)
; The default is 1.
; WLFCollapseMode = 0

; Specify whether WLF file logging can use threads on multi-processor machines.
; If 0, no threads will be used; if 1, threads will be used if the system has
; more than one processor.
; WLFUseThreads = 1

; Specify the size of objects that will trigger "large object" messages
; at log/wave/list time.  The size calculation of the object is the same as that
; used by the WildcardSizeThreshold. The default LargeObjectSize size is 500,000.
; Setting LargeObjectSize to 0 will disable these messages.
; LargeObjectSize = 500000

; Specify the depth of stack frames returned by $stacktrace([level]).
; This depth will be picked up when the optional 'level' argument
; is not specified or its value is not a positive integer. 
; StackTraceDepth = 100

; Turn on/off undebuggable SystemC type warnings. Default is on.
; ShowUndebuggableScTypeWarning = 0

; Turn on/off unassociated SystemC name warnings. Default is off.
; ShowUnassociatedScNameWarning = 1

; Turn on/off SystemC IEEE 1666 deprecation warnings. Default is off.
; ScShowIeeeDeprecationWarnings = 1

; Turn on/off the check for multiple drivers on a SystemC sc_signal. Default is off.
; ScEnableScSignalWriteCheck = 1

; Set SystemC default time unit.
; Set to fs, ps, ns, us, ms, or sec with optional 
; prefix of 1, 10, or 100.  The default is 1 ns.
; The ScTimeUnit value is honored if it is coarser than Resolution.
; If ScTimeUnit is finer than Resolution, it is set to the value
; of Resolution. For example, if Resolution is 100ps and ScTimeUnit is ns,
; then the default time unit will be 1 ns.  However if Resolution 
; is 10 ns and ScTimeUnit is ns, then the default time unit will be 10 ns.
ScTimeUnit = ns

; Set SystemC sc_main stack size. The stack size is set as an integer
; number followed by the unit which can be Kb(Kilo-byte), Mb(Mega-byte) or
; Gb(Giga-byte). Default is 10 Mb. The stack size for sc_main depends
; on the amount of data on the sc_main() stack and the memory required
; to succesfully execute the longest function call chain of sc_main().
ScMainStackSize = 10 Mb

; Set SystemC thread stack size. The stack size is set as an integer
; number followed by the unit which can be Kb(Kilo-byte), Mb(Mega-byte) or
; Gb(Giga-byte). The stack size for sc_thread depends
; on the amount of data on the sc_thread stack and the memory required
; to succesfully execute the thread.
; ScStackSize = 1 Mb

; Turn on/off execution of remainder of sc_main upon quitting the current
; simulation session. If the cumulative length of sc_main() in terms of 
; simulation time units is less than the length of the current simulation
; run upon quit or restart, sc_main() will be in the middle of execution.
; This switch gives the option to execute the remainder of sc_main upon
; quitting simulation. The drawback of not running sc_main till the end
; is memory leaks for objects created by sc_main. If on, the remainder of
; sc_main will be executed ignoring all delays. This may cause the simulator
; to crash if the code in sc_main is dependent on some simulation state.
; Default is on.
ScMainFinishOnQuit = 1

; Enable calling of the DPI export taks/functions from the
; SystemC start_of_simulation() callback.
; The default is off.
; EnableDpiSosCb = 1


; Set the SCV relationship name that will be used to identify phase
; relations.  If the name given to a transactor relation matches this
; name, the transactions involved will be treated as phase transactions
ScvPhaseRelationName = mti_phase

; Customize the vsim kernel shutdown behavior at the end of the simulation.
; Some common causes of the end of simulation are $finish (implicit or explicit), 
; sc_stop(), tf_dofinish(), and assertion failures. 
; This should be set to "ask", "exit", or "stop". The default is "ask".
; "ask"   -- In batch mode, the vsim kernel will abruptly exit.  
;            In GUI mode, a dialog box will pop up and ask for user confirmation 
;            whether or not to quit the simulation.
; "stop"  -- Cause the simulation to stay loaded in memory. This can make some 
;            post-simulation tasks easier.
; "exit"  -- The simulation will abruptly exit without asking for any confirmation.
; "final" -- Run SystemVerilog final blocks then behave as "stop".
; Note: This variable can be overridden with the vsim "-onfinish" command line switch.
OnFinish = ask

; Print pending deferred assertion messages. 
; Deferred assertion messages may be scheduled after the $finish in the same 
; time step. Deferred assertions scheduled to print after the $finish are 
; printed before exiting with severity level NOTE since it's not known whether
; the assertion is still valid due to being printed in the active region
; instead of the reactive region where they are normally printed.
; OnFinishPendingAssert = 1;

; Print "simstats" result. Default is 0.
; 0 == do not print simstats
; 1 == print at end of simulation
; 2 == print at end of each run command and end of simulation
; PrintSimStats = 1

; Assertion File - alternate file for storing VHDL/PSL/Verilog assertion messages
; AssertFile = assert.log

; Enable assertion counts. Default is off.
; AssertionCover = 1

; Run simulator in assertion debug mode. Default is off.
; AssertionDebug = 1

; Turn on/off PSL/SVA/VHDL assertion enable. Default is on.
; AssertionEnable = 0

; Set PSL/SVA/VHDL concurrent assertion fail limit. Default is -1.
; Any positive integer, -1 for infinity.
; AssertionLimit = 1

; Turn on/off concurrent assertion pass log. Default is off. 
; Assertion pass logging is only enabled when assertion is browseable 
; and assertion debug is enabled.
; AssertionPassLog = 1

; Turn on/off PSL concurrent assertion fail log. Default is on.
; The flag does not affect SVA
; AssertionFailLog = 0

; Turn on/off SVA concurrent assertion local var printing in -assertdebug mode.  Default is on.
; AssertionFailLocalVarLog = 0

; Set action type for PSL/SVA concurrent assertion fail action. Default is continue.
; 0 = Continue  1 = Break  2 = Exit
; AssertionFailAction = 1

; Enable the active thread monitor in the waveform display when assertion debug is enabled.
; AssertionActiveThreadMonitor = 1

; Control how many waveform rows will be used for displaying the active threads.  Default is 5.
; AssertionActiveThreadMonitorLimit = 5

; Assertion thread limit after which assertion would be killed/switched off. 
; The default is -1 (unlimited). If the number of threads for an assertion go 
; beyond this limit, the assertion would be either switched off or killed. This
; limit applies to only assert directives.
;AssertionThreadLimit = -1

; Action to be taken once the assertion thread limit is reached. Default 
; is kill. It can have a value of off or kill. In case of kill, all the existing
; threads are terminated and no new attempts are started. In case of off, the 
; existing attempts keep on evaluating but no new attempts are started. This 
; variable applies to only assert directives.
;AssertionThreadLimitAction = kill

; Cover thread limit after which cover would be killed/switched off. 
; The default is -1 (unlimited). If the number of threads for a cover go 
; beyond this limit, the cover would be either switched off or killed. This
; limit applies to only cover directives.
;CoverThreadLimit = -1

; Action to be taken once the cover thread limit is reached. Default 
; is kill. It can have a value of off or kill. In case of kill, all the existing
; threads are terminated and no new attempts are started. In case of off, the 
; existing attempts keep on evaluating but no new attempts are started. This 
; variable applies to only cover directives.
;CoverThreadLimitAction = kill


; By default immediate assertions do not participate in Assertion Coverage calculations
; unless they are executed.  This switch causes all immediate assertions in the design
; to participate in Assertion Coverage calculations, whether attempted or not.
; UnattemptedImmediateAssertions = 0

; By default immediate covers participate in Coverage calculations 
; whether they are attempted or not. This switch causes all unattempted 
; immediate covers in the design to stop participating in Coverage 
; calculations.
; UnattemptedImmediateCovers = 0

; By default pass action block is not executed for assertions on vacuous 
; success. The following variable is provided to enable execution of 
; pass action block on vacuous success. The following variable is only effective
; if the user does not disable pass action block execution by using either 
; system tasks or CLI. Also there is a performance penalty for enabling 
; the following variable. 
;AssertionEnableVacuousPassActionBlock = 1

; As per strict 1850-2005 PSL LRM, an always property can either pass
; or fail. However, by default, Questa reports multiple passes and
; multiple fails on top always/never property (always/never operator
; is the top operator under Verification Directive). The reason
; being that Questa reports passes and fails on per attempt of the
; top always/never property. Use the following flag to instruct
; Questa to strictly follow LRM. With this flag, all assert/never
; directives will start an attempt once at start of simulation.
; The attempt can either fail, match or match vacuously.
; For e.g. if always is the top operator under assert, the always will
; keep on checking the property at every clock. If the property under
; always fails, the directive will be considered failed and no more 
; checking will be done for that directive. A top always property,
; if it does not fail, will show a pass at end of simulation.
; The default value is '0' (i.e. zero is off). For example:
; PslOneAttempt = 1

; Specify the number of clock ticks to represent infinite clock ticks.
; This affects eventually!, until! and until_!. If at End of Simulation
; (EOS) an active strong-property has not clocked this number of
; clock ticks then neither pass or fail (vacuous match) is returned
; else respective fail/pass is returned. The default value is '0' (zero)
; which effectively does not check for clock tick condition. For example:
; PslInfinityThreshold = 5000

; Control how many thread start times will be preserved for ATV viewing for a given assertion
; instance.  Default is -1 (ALL).
; ATVStartTimeKeepCount = -1

; Turn on/off code coverage
; CodeCoverage = 0

; This option applies to condition and expression coverage UDP tables. It
; has no effect unless UDP is enabled for coverage with vcom/vlog/vopt -coverudp.
; If this option is used and a match occurs in more than one row in the UDP table,
; none of the counts for all matching rows is incremented. By default, counts are
; incremented for all matching rows.
; CoverCountAll = 1

; Turn off automatic inclusion of VHDL integers in toggle coverage. Default
; is to include them.
; ToggleNoIntegers = 1

; Set the maximum number of values that are collected for toggle coverage of
; VHDL integers. Default is 100;
; ToggleMaxIntValues = 100

; Set the maximum number of values that are collected for toggle coverage of
; Verilog real. Default is 100;
; ToggleMaxRealValues = 100

; Turn on automatic inclusion of Verilog integers in toggle coverage, except
; for enumeration types. Default is to include them.
; ToggleVlogIntegers = 0

; Turn on automatic inclusion of Verilog real type in toggle coverage, except
; for shortreal types. Default is to not include them.
; ToggleVlogReal = 1

; Turn on automatic inclusion of Verilog fixed-size unpacked arrays, VHDL multi-d arrays
; and VHDL arrays-of-arrays in toggle coverage.
; Default is to not include them.
; ToggleFixedSizeArray = 1

; Increase or decrease the maximum size of Verilog unpacked fixed-size arrays,
; VHDL multi-d arrays and VHDL arrays-of-arrays that are included for toggle coverage.
; This leads to a longer simulation time with bigger arrays covered with toggle coverage.
; Default is 1024.
; ToggleMaxFixedSizeArray = 1024

; Treat Verilog multi-dimensional packed vectors and packed structures as equivalently sized
; one-dimensional packed vectors for toggle coverage. Default is 0.
; TogglePackedAsVec = 0

; Treat Verilog enumerated types as equivalently sized one-dimensional packed vectors for
; toggle coverage. Default is 0.
; ToggleVlogEnumBits = 0

; Turn off automatic inclusion of VHDL records in toggle coverage.
; Default is to include them.
; ToggleVHDLRecords = 0

; Limit the widths of registers automatically tracked for toggle coverage. Default is 128.
; For unlimited width, set to 0.
; ToggleWidthLimit = 128

; Limit the counts that are tracked for toggle coverage. When all edges for a bit have
; reached this count, further activity on the bit is ignored. Default is 1.
; For unlimited counts, set to 0.
; ToggleCountLimit = 1

; Change the mode of extended toggle coverage. Default is 3. Valid modes are 1, 2 and 3.
; Following is the toggle coverage calculation criteria based on extended toggle mode:
; Mode 1: 0L->1H & 1H->0L & any one 'Z' transition (to/from 'Z').
; Mode 2: 0L->1H & 1H->0L & one transition to 'Z' & one transition from 'Z'.
; Mode 3: 0L->1H & 1H->0L & all 'Z' transitions.
; ExtendedToggleMode = 3

; Enable toggle statistics collection only for ports. Default is 0.
; TogglePortsOnly = 1

; Limit the counts that are tracked for Focussed Expression Coverage. When a bin has
; reached this count, further tracking of the input patterns linked to it is ignored.
; Default is 1. For unlimited counts, set to 0.
; NOTE: Changing this value from its default value may affect simulation performance.
; FecCountLimit = 1

; Limit the counts that are tracked for UDP Coverage. When a bin has
; reached this count, further tracking of the input patterns linked to it is ignored.
; Default is 1. For unlimited counts, set to 0.
; NOTE: Changing this value from its default value may affect simulation performance.
; UdpCountLimit = 1

; Control toggle coverage deglitching period. A period of 0, eliminates delta
; cycle glitches. This is the default. The value of ToggleDeglitchPeriod needs to be either 
; 0 or a time string that includes time units. Examples: 0 or 10.0ps or "10.0 ps".
; ToggleDeglitchPeriod = 10.0ps

; Turn on/off all PSL/SVA cover directive enables.  Default is on.
; CoverEnable = 0

; Turn on/off PSL/SVA cover log.  Default is off "0".
; CoverLog = 1

; Set "at_least" value for all PSL/SVA cover directives.  Default is 1.
; CoverAtLeast = 2

; Set "limit" value for all PSL/SVA cover directives.  Default is -1.
; Any positive integer, -1 for infinity.
; CoverLimit = 1

; Specify the coverage database filename.
; Default is "" (i.e. database is NOT automatically saved on close). 
; UCDBFilename = vsim.ucdb

; Specify the maximum limit for the number of Cross (bin) products reported
; in XML and UCDB report against a Cross. A warning is issued if the limit
; is crossed. Default is zero. vsim switch -cvgmaxrptrhscross can override this
; setting.
; MaxReportRhsSVCrossProducts = 1000

; Specify the override for the "auto_bin_max" option for the Covergroups.
; If not specified then value from Covergroup "option" is used.
; SVCoverpointAutoBinMax = 64

; Specify the override for the value of "cross_num_print_missing"
; option for the Cross in Covergroups. If not specified then value
; specified in the "option.cross_num_print_missing" is used. This
; is a runtime option. NOTE: This overrides any "cross_num_print_missing"
; value specified by user in source file and any SVCrossNumPrintMissingDefault
; specified in modelsim.ini.
; SVCrossNumPrintMissing = 0

; Specify whether to use the value of "cross_num_print_missing"
; option in report and GUI for the Cross in Covergroups. If not specified then 
; cross_num_print_missing is ignored for creating reports and displaying 
; covergroups in GUI. Default is 0, which means ignore "cross_num_print_missing".
; UseSVCrossNumPrintMissing = 0

; Specify the threshold of Coverpoint wildcard bin value range size, above which 
; a warning will be triggered. The default is 4K -- 12 wildcard bits.
; SVCoverpointWildCardBinValueSizeWarn = 4096

; Specify the override for the value of "strobe" option for the
; Covergroup Type. If not specified then value in "type_option.strobe"
; will be used. This is runtime option which forces "strobe" to
; user specified value and supersedes user specified values in the
; SystemVerilog Code. NOTE: This also overrides the compile time
; default value override specified using "SVCovergroupStrobeDefault"
; SVCovergroupStrobe = 0

; Override for explicit assignments in source code to "option.goal" of
; SystemVerilog covergroup, coverpoint, and cross. It also overrides the
; default value of "option.goal" (defined to be 100 in the SystemVerilog
; LRM) and the value of modelsim.ini variable "SVCovergroupGoalDefault".
; SVCovergroupGoal = 100

; Override for explicit assignments in source code to "type_option.goal" of
; SystemVerilog covergroup, coverpoint, and cross. It also overrides the
; default value of "type_option.goal" (defined to be 100 in the SystemVerilog
; LRM) and the value of modelsim.ini variable "SVCovergroupTypeGoalDefault".
; SVCovergroupTypeGoal = 100

; Enforce the 6.3 behavior of covergroup get_coverage() and get_inst_coverage()
; builtin functions, and report. This setting changes the default values of
; option.get_inst_coverage and type_option.merge_instances to ensure the 6.3
; behavior if explicit assignments are not made on option.get_inst_coverage and
; type_option.merge_instances by the user. There are two vsim command line
; options, -cvg63 and -nocvg63 to override this setting from vsim command line.
; The default value of this variable from release 6.6 onwards is 0. This default
; drives compliance with the clarified behavior in the IEEE 1800-2009 standard.
; SVCovergroup63Compatibility = 0

; Enforce the default behavior of covergroup get_coverage() builtin function, GUI
; and report. This variable sets the default value of type_option.merge_instances.
; There are two vsim command line options, -cvgmergeinstances and 
; -nocvgmergeinstances to override this setting from vsim command line.
; The default value of this variable, -1 (don't care), allows the tool to determine
; the effective value, based on factors related to capacity and optimization. 
; The type_option.merge_instances appears in the GUI and coverage reports as either 
; auto(1) or auto(0), depending on whether the effective value was determined to 
; be a 1 or a 0.
; SVCovergroupMergeInstancesDefault = -1

; Enable or disable generation of more detailed information about the sampling
; of covergroup, cross, and coverpoints. It provides the details of the number
; of times the covergroup instance and type were sampled, as well as details
; about why covergroup, cross and coverpoint were not covered. A non-zero value
; is to enable this feature. 0 is to disable this feature. Default is 0
; SVCovergroupSampleInfo = 0

; Specify the maximum number of Coverpoint bins in whole design for
; all Covergroups.
; MaxSVCoverpointBinsDesign = 2147483648 

; Specify maximum number of Coverpoint bins in any instance of a Covergroup, default is 2^10 bins
; MaxSVCoverpointBinsInst = 1048576

; Specify the maximum number of Cross bins in whole design for
; all Covergroups.
; MaxSVCrossBinsDesign = 2147483648 

; Specify maximum number of Cross bins in any instance of a Covergroup, default is 2^16 bins
; MaxSVCrossBinsInst = 67108864

; Specify whether vsim will collect the coverage data of zero-weight coverage items or not.
; By default, this variable is set 0, in which case option.no_collect setting will take effect.
; If this variable is set to 1, all zero-weight coverage items will not be saved.
; Note that the usage of vsim switch -cvgzwnocollect, if present, will override the setting 
; of this variable.
; CvgZWNoCollect = 1

; Specify a space delimited list of double quoted TCL style
; regular expressions which will be matched against the text of all messages.
; If any regular expression is found to be contained within any message, the 
; status for that message will not be propagated to the UCDB TESTSTATUS. 
; If no match is detected, then the status will be propagated to the
; UCDB TESTSTATUS. More than one such regular expression text is allowed, 
; and each message text is compared for each regular expression in the list.
; UCDBTestStatusMessageFilter = "Done with Test Bench" "Ignore .* message" 

; Set weight for all PSL/SVA cover directives.  Default is 1.
; CoverWeight = 2

; Check vsim plusargs.  Default is 0 (off).
; 0 = Don't check plusargs
; 1 = Warning on unrecognized plusarg
; 2 = Error and exit on unrecognized plusarg
; CheckPlusargs = 1

; Load the specified shared objects with the RTLD_GLOBAL flag.
; This gives global visibility to all symbols in the shared objects,
; meaning that subsequently loaded shared objects can bind to symbols
; in the global shared objects.  The list of shared objects should
; be whitespace delimited.  This option is not supported on the
; Windows or AIX platforms.
; GlobalSharedObjectList = example1.so example2.so example3.so

; Generate the stub definitions for the undefined symbols in the shared libraries being
; loaded in the simulation. When this flow is turned on, the undefined symbols will not
; prevent vsim from loading. Calling undefined symbols at runtime will cause fatal error.
; The valid arguments are: on, off, verbose. 
;     on : turn on the automatic generation of stub definitions.
;     off: turn off the flow. The undefined symbols will trigger an immediate load failure.
;     verbose: Turn on the flow and report the undefined symbols for each shared library.
; NOTE: This variable can be overriden with vsim switch "-undefsyms".
; The default is on.
;
; UndefSyms = off

; Enable the support for checkpointing foreign C++ libraries.
; The valid arguments are: 1 and 0. 
;     1 : turn on the support
;     0 : turn off the support (default)
; This option is not supported on the Windows platforms.
;
; AllowCheckpointCpp = 1

; Initial seed for the random number generator of the root thread (SystemVerilog).
; NOTE: This variable can be overridden with the vsim "-sv_seed" command line switch.
; The default value is 0.
; Sv_Seed = 0

; Specify the solver "engine" that vsim will select for constrained random
; generation.
; Valid values are:
;    "auto" - automatically select the best engine for the current
;             constraint scenario
;    "bdd"  - evaluate all constraint scenarios using the BDD solver engine
;    "act"  - evaluate all constraint scenarios using the ACT solver engine
; While the BDD solver engine is generally efficient with constraint scenarios
; involving bitwise logical relationships, the ACT solver engine can exhibit
; superior performance with constraint scenarios involving large numbers of
; random variables related via arithmetic operators (+, *, etc).
; NOTE: This variable can be overridden with the vsim "-solveengine" command
; line switch.
; The default value is "auto".
; SolveEngine = auto

; Specify if the solver should attempt to ignore overflow/underflow semantics
; for arithmetic constraints (multiply, addition, subtraction) in order to
; improve performance. The "solveignoreoverflow" attribute can be specified on
; a per-call basis to randomize() to override this setting.
; The default value is 0 (overflow/underflow is not ignored). Set to 1 to
; ignore overflow/underflow.
; SolveIgnoreOverflow = 0

; Specifies the maximum size that a dynamic array may be resized to by the
; solver. If the solver attempts to resize a dynamic array to a size greater
; than the specified limit, the solver will abort with an error.
; The default value is 10000. A value of 0 indicates no limit.
; SolveArrayResizeMax = 10000

; Specifies the maximum size that a dynamic array may be resized to by the
; solver. If the solver attempts to resize a dynamic array to a size greater
; than the specified limit, the solver will abort with an error.
; The default value is 10000. A value of 0 indicates no limit.
; SolveArrayResizeMax = 10000

; Error message severity when normal randomize() and randomize(null) failures are detected.
; Integer value up to two digits are allowed with each digit having the following legal values: 
; 0 = No error  1 = Warning  2 = Error  3 = Failure  4 = Fatal
;
; 1) When a value with two digits is used, the digit at tenth place (leftmost digit) represents
;    the severtity setting for normal randomize() calls. The digit at ones place (rightmost digit)
;    represents the setting for randomize(null) calls.
;
; 2) When a single digit value is used, the setting is applied to both normal randomize() call 
;    and randomize(null) call.
;
; Example: -solvefailseverity=40 means:
;   fatal error for failed normal randomize() calls and NO error for failed randomize(null) calls.
;
; The default is 0 (no error).
; SolveFailSeverity = 0

; Error message severity for suppressible errors that are detected in a
; solve/before constraint.
; NOTE: This variable can be overridden with the vsim "-solvebeforeerrorseverity"
; command line switch.
; 0 = No error  1 = Warning  2 = Error  3 = Failure  4 = Fatal
; The default is 3 (failure).
; SolveBeforeErrorSeverity = 3

; Error message severity for suppressible errors that are related to
; solve engine capacity limits
; NOTE: This variable can be overridden with the vsim "-solveengineerrorseverity"
; command line switch.
; 0 = No error  1 = Warning  2 = Error  3 = Failure  4 = Fatal
; The default is 3 (failure).
; SolveEngineErrorSeverity = 3

; Enable/disable debug information for randomize() failures.
; NOTE: This variable can be overridden with the vsim "-solvefaildebug" command
; line switch.
; The default is 0 (disabled). Set to 1 to enable basic debug (with no
; performance penalty). Set to 2 for enhanced debug (will result in slower
; runtime performance).
; SolveFailDebug = 0

; Upon encountering a randomize() failure, generate a simplified testcase that
; will reproduce the failure. Optionally output the testcase to a file.
; Testcases for 'no-solution' failures will only be produced if SolveFailDebug
; is enabled (see above).
; NOTE: This variable can be overridden with the vsim "-solvefailtestcase"
; command line switch.
; The default is OFF (do not generate a testcase). To enable testcase
; generation, uncomment this variable. To redirect testcase generation to a
; file, specify the name of the output file.
; SolveFailTestcase = 

; Specify solver timeout threshold (in seconds). randomize() will fail if the
; CPU time required to evaluate any randset exceeds the specified timeout.
; The default value is 500. A value of 0 will disable timeout failures. 
; SolveTimeout = 500

; Specify the maximum size of the solution graph generated by the BDD solver.
; This value can be used to force the BDD solver to abort the evaluation of a
; complex constraint scenario that cannot be evaluated with finite memory.
; This value is specified in 1000s of nodes.
; The default value is 10000. A value of 0 indicates no limit.
; SolveGraphMaxSize = 10000

; Specify the maximum number of evaluations that may be performed on the
; solution graph by the BDD solver. This value can be used to force the BDD
; solver to abort the evaluation of a complex constraint scenario that cannot
; be evaluated in finite time. This value is specified in 10000s of evaluations.
; The default value is 10000. A value of 0 indicates no limit.
; SolveGraphMaxEval = 10000

; Specify the maximum number of tests that the ACT solver may evaluate before
; abandoning an attempt to solve a particular constraint scenario.
; The default value is 2000000.  A value of 0 indicates no limit.
; SolveACTMaxTests = 2000000

; Specify the maximum number of operations that the ACT solver may perform 
; before abandoning an attempt to solve a particular constraint scenario.  The 
; value is specified in 1000000s of operations.
; The default value is 10000. A value of 0 indicates no limit.
; SolveACTMaxOps = 10000

; Specify the number of times the ACT solver will retry to evaluate a constraint
; scenario that fails due to the SolveACTMax[Tests|Ops] threshold.
; The default value is 0 (no retry).
; SolveACTRetryCount = 0

; Specify random sequence compatiblity with a prior letter release. This 
; option is used to get the same random sequences during simulation as
; as a prior letter release. Only prior letter releases (of the current
; number release) are allowed.
; NOTE: Only those random sequence changes due to solver optimizations are
; reverted by this variable. Random sequence changes due to solver bugfixes
; cannot be un-done.
; NOTE: This variable can be overridden with the vsim "-solverev" command
; line switch.
; Default value set to "" (no compatibility).
; SolveRev =

; Environment variable expansion of command line arguments has been depricated 
; in favor shell level expansion.  Universal environment variable expansion 
; inside -f files is support and continued support for MGC Location Maps provide
; alternative methods for handling flexible pathnames.
; The following line may be uncommented and the value set to 1 to re-enable this 
; deprecated behavior.  The default value is 0.
; DeprecatedEnvironmentVariableExpansion = 0

; Specify the memory threshold for the System Verilog garbage collector.
; The value is the number of megabytes of class objects that must accumulate
; before the garbage collector is run.
; The GCThreshold setting is used when class debug mode is disabled to allow
; less frequent garbage collection and better simulation performance.
; The GCThresholdClassDebug setting is used when class debug mode is enabled
; to allow for more frequent garbage collection.
; GCThreshold = 100
; GCThresholdClassDebug = 5

; Turn on/off collapsing of bus ports in VCD dumpports output
DumpportsCollapse = 1

; Location of Multi-Level Verification Component (MVC) installation. 
; The default location is the product installation directory.
MvcHome = $MODEL_TECH/..

; Location of InFact installation. The default is $MODEL_TECH/../../infact
;
; InFactHome = $MODEL_TECH/../../infact

; Initialize SystemVerilog enums using the base type's default value
; instead of the leftmost value.
; EnumBaseInit = 1

; Suppress file type registration.  
; SuppressFileTypeReg = 1

; Enable/disable non-LRM compliant SystemVerilog language extensions.
; Valid extensions are:
;   cfce        - generate an error if $cast fails as a function
;   dfsp        - sets default format specifier as %p, if no format specifier is given for unpacked array in $display and related systasks
;   expdfmt     - enable format string extensions for $display/$sformatf
;   extscan     - support values greater than 32 bit for string builtin methods (atohex, atobin, atooct, atoi)
;   fmtcap      - prints capital hex digits with %X/%H in display calls
;   iddp        - ignore DPI disable protocol check
;   noexptc     - ignore DPI export type name overloading check
;   lfmt        - zero-pad data if '0' prefixes width in format specifier (e.g. "%04h")
;   realrand    - support randomize() with real variables and constraints (Default)
; SvExtensions = [+|-]<extension>[,[+|-]<extension>*]

; Enable/disable non-LRM compliant SystemVerilog constrained-random language extensions.
; Valid extensions are:
;   arraymode    - consider rand_mode of unpacked array field independently from its elements
;   deepcheck    - allow randomize(null) to recursively consider constraints from member rand class handles  
;   forkjoinstab - preserve parent thread random stability when seeding fork/join sub-threads (Default)
;   funcback     - enable function backtracking (ACT only)
;   nodist       - interpret 'dist' constraint as 'inside' (ACT only)
;   nonrandstab  - disable seeding of "non-random" class instances (Default)
;   noorder      - ignore solve/before ordering constraints (ACT only)
;   promotedist  - promote priority of 'dist' constraint if LHS has no solve/before
;   randindex    - allow random index in constraint (Default)
;   randstruct   - consider all fields of unpacked structs as 'rand'
;   skew         - skew randomize results (ACT only)
; SvRandExtensions = [+|-]<extension>[,[+|-]<extension>*]

; Controls the formatting of '%p' and '%P' conversion specification, used in $display
; and similar system tasks.
; 1. SVPrettyPrintFlags=I<n><S|T> use <n> spaces(S) or tabs(T) per indentation level. 
;    The 'I' flag when present causes relevant data types to be expanded and indented into
;    a more readable format.
;    (e.g. SVPrettyPrintFlags=I4S will cause 4 spaces to be used per indentation level).
; 2. SVPrettyPrintFlags=L<numLines> limits the output to <numLines> lines.
;    (e.g. SVPrettyPrintFlags=L20 will limit the output to 20 lines).
; 3. SVPrettyPrintFlags=C<numChars> limits the output to <numChars> characters.
;    (e.g. SVPrettyPrintFlags=C256 will limit the output to 256 characters).
; 4. SVPrettyPrintFlags=F<numFields> limits the output to <numFields> of relevant datatypes
;    (e.g. SVPrettyPrintFlags=F4 will limit the output to 4 fields of a structure).
; 5. SVPrettyPrintFlags=E<numElements> limits the output to <numElements> of relevant datatypes
;    (e.g. SVPrettyPrintFlags=E50 will limit the output to 50 elements of an array).
; 6. SVPrettyPrintFlags=D<depth> suppresses the output of sub-elements below <depth>.
;    (e.g. SVPrettyPrintFlags=D5 will suppresses the output of sub elements below a depth of 5).
; 7. SVPrettyPrintFlags=R<specifier> shows the output of specifier %p as per the specifed radix.
;    It changes the output in $display and similar systasks. It does not affect formatted output functions ($displayh etc)).
;    (e.g. SVPrettyPrintFlags=Rb will show the output of %p specifier in binary format.
; 8. Items 1-7 above can be combined as a comma separated list.
;    (e.g. SVPrettyPrintFlags=I4S,L20,C256,F4,E50,D5,Rb)
; SVPrettyPrintFlags=I4S

[lmc]
; The simulator's interface to Logic Modeling's SmartModel SWIFT software
libsm = $MODEL_TECH/libsm.sl
; The simulator's interface to Logic Modeling's SmartModel SWIFT software (Windows NT)
; libsm = $MODEL_TECH/libsm.dll
;  Logic Modeling's SmartModel SWIFT software (HP 9000 Series 700)
; libswift = $LMC_HOME/lib/hp700.lib/libswift.sl
;  Logic Modeling's SmartModel SWIFT software (IBM RISC System/6000)
; libswift = $LMC_HOME/lib/ibmrs.lib/swift.o
;  Logic Modeling's SmartModel SWIFT software (Sun4 Solaris)
; libswift = $LMC_HOME/lib/sun4Solaris.lib/libswift.so
;  Logic Modeling's SmartModel SWIFT software (Windows NT)
; libswift = $LMC_HOME/lib/pcnt.lib/libswift.dll
;  Logic Modeling's SmartModel SWIFT software (non-Enterprise versions of Linux)
; libswift = $LMC_HOME/lib/x86_linux.lib/libswift.so
;  Logic Modeling's SmartModel SWIFT software (Enterprise versions of Linux)
; libswift = $LMC_HOME/lib/linux.lib/libswift.so

; The simulator's interface to Logic Modeling's hardware modeler SFI software
libhm = $MODEL_TECH/libhm.sl
; The simulator's interface to Logic Modeling's hardware modeler SFI software (Windows NT)
; libhm = $MODEL_TECH/libhm.dll
;  Logic Modeling's hardware modeler SFI software (HP 9000 Series 700)
; libsfi = <sfi_dir>/lib/hp700/libsfi.sl
;  Logic Modeling's hardware modeler SFI software (IBM RISC System/6000)
; libsfi = <sfi_dir>/lib/rs6000/libsfi.a
;  Logic Modeling's hardware modeler SFI software (Sun4 Solaris)
; libsfi = <sfi_dir>/lib/sun4.solaris/libsfi.so
;  Logic Modeling's hardware modeler SFI software (Windows NT)
; libsfi = <sfi_dir>/lib/pcnt/lm_sfi.dll
;  Logic Modeling's hardware modeler SFI software (Linux)
; libsfi = <sfi_dir>/lib/linux/libsfi.so

[msg_system]
; Change a message severity or suppress a message.
; The format is: <msg directive> = <msg number>[,<msg number>...]
; suppress can be used to achieve +nowarn<CODE> functionality
; The format is: suppress = <CODE>,<msg number>,[<CODE>,<msg number>,...]
; Examples:
suppress = 8780 ;an explanation can be had by running: verror 8780 
;   note = 3009
;   warning = 3033
;   error = 3010,3016
;   fatal = 3016,3033
;   suppress = 3009,3016,3601
;   suppress = 3009,CNNODP,3601,TFMPC
;   suppress = 8683,8684
; The command verror <msg number> can be used to get the complete
; description of a message.

; Control transcripting of Verilog display system task messages and
; PLI/FLI print function call messages.  The system tasks include
; $display[bho], $strobe[bho], $monitor[bho], and $write[bho].  They
; also include the analogous file I/O tasks that write to STDOUT 
; (i.e. $fwrite or $fdisplay).  The PLI/FLI calls include io_printf,
; vpi_printf, mti_PrintMessage, and mti_PrintFormatted.  The default
; is to have messages appear only in the transcript.  The other 
; settings are to send messages to the wlf file only (messages that
; are recorded in the wlf file can be viewed in the MsgViewer) or 
; to both the transcript and the wlf file.  The valid values are
;    tran  {transcript only (default)}
;    wlf   {wlf file only}
;    both  {transcript and wlf file}
; displaymsgmode = tran

; Control transcripting of elaboration/runtime messages not
; addressed by the displaymsgmode setting.  The default is to 
; have messages appear only in the transcript.  The other settings
; are to send messages to the wlf file only (messages that are
; recorded in the wlf file can be viewed in the MsgViewer) or to both
; the transcript and the wlf file. The valid values are
;    tran  {transcript only (default)}
;    wlf   {wlf file only}
;    both  {transcript and wlf file}
; msgmode = tran

; Controls number of displays of a particluar message
; default value is 5
; MsgLimitCount = 5

[utils]
; Default Library Type (while creating a library with "vlib")
;  0 - legacy library using subdirectories for design units
;  2 - flat library
; DefaultLibType = 2

; Flat Library Page Size (while creating a library with "vlib")
; Set the size in bytes for flat library file pages.  Libraries containing
; very large files may benefit from a larger value.
; FlatLibPageSize = 8192

; Flat Library Page Cleanup Percentage (while creating a library with "vlib")
; Set the percentage of total pages deleted before library cleanup can occur.
; This setting is applied together with FlatLibPageDeleteThreshold.
; FlatLibPageDeletePercentage = 50

; Flat Library Page Cleanup Threshold (while creating a library with "vlib")
; Set the number of pages deleted before library cleanup can occur.
; This setting is applied together with FlatLibPageDeletePercentage.
; FlatLibPageDeleteThreshold = 1000

[Project]
; Warning -- Do not edit the project properties directly.
;            Property names are dynamic in nature and property
;            values have special syntax.  Changing property data directly
;            can result in a corrupt MPF file.  All project properties
;            can be modified through project window dialogs.
Project_Version = 6
Project_DefaultLib = work
Project_SortMethod = unused
Project_Files_Count = 9
Project_File_0 = D:/SVN_Path/DevKitProjects/Ti60/Dev/Ti60_SC130GS_DDR_DSI/ip/dsi_tx/Testbench/modelsim/dsi_tx.sv
Project_File_P_0 = cover_toggle 0 vlog_protect 0 file_type systemverilog group_id 0 cover_exttoggle 0 cover_nofec 0 cover_cond 0 vlog_1995compat SV vlog_nodebug 0 cover_fsm 0 last_compile 1684145320 vlog_noload 0 cover_branch 0 folder {Top Level} vlog_enable0In 0 cover_excludedefault 0 vlog_disableopt 0 cover_covercells 0 vlog_hazard 0 vlog_showsource 0 cover_optlevel 3 voptflow 1 ood 0 vlog_0InOptions {} toggle - vlog_options {} compile_to work vlog_upper 0 cover_noshort 0 compile_order 8 dont_compile 0 cover_expr 0 cover_stmt 0
Project_File_1 = D:/SVN_Path/DevKitProjects/Ti60/Dev/Ti60_SC130GS_DDR_DSI/ip/dsi_tx/Testbench/vga_gen.v
Project_File_P_1 = cover_toggle 0 vlog_protect 0 file_type verilog group_id 0 cover_exttoggle 0 cover_nofec 0 cover_cond 0 vlog_1995compat 0 vlog_nodebug 0 folder {Top Level} cover_branch 0 cover_fsm 0 last_compile 1684145320 vlog_noload 0 cover_excludedefault 0 vlog_enable0In 0 vlog_disableopt 0 cover_covercells 0 voptflow 1 cover_optlevel 3 vlog_showsource 0 vlog_hazard 0 toggle - vlog_0InOptions {} ood 0 cover_noshort 0 vlog_upper 0 compile_to work vlog_options {} compile_order 7 cover_expr 0 dont_compile 0 cover_stmt 0
Project_File_2 = D:/SVN_Path/DevKitProjects/Ti60/Dev/Ti60_SC130GS_DDR_DSI/ip/dsi_tx/Testbench/shift_reg.v
Project_File_P_2 = cover_toggle 0 vlog_protect 0 file_type verilog group_id 0 cover_exttoggle 0 cover_nofec 0 cover_cond 0 vlog_1995compat 0 vlog_nodebug 0 folder {Top Level} cover_branch 0 cover_fsm 0 last_compile 1684145320 vlog_noload 0 cover_excludedefault 0 vlog_enable0In 0 vlog_disableopt 0 cover_covercells 0 voptflow 1 cover_optlevel 3 vlog_showsource 0 vlog_hazard 0 toggle - vlog_0InOptions {} ood 0 cover_noshort 0 vlog_upper 0 compile_to work vlog_options {} compile_order 3 cover_expr 0 dont_compile 0 cover_stmt 0
Project_File_3 = D:/SVN_Path/DevKitProjects/Ti60/Dev/Ti60_SC130GS_DDR_DSI/ip/dsi_tx/Testbench/true_dual_port_ram.v
Project_File_P_3 = cover_toggle 0 vlog_protect 0 file_type verilog group_id 0 cover_exttoggle 0 cover_nofec 0 cover_cond 0 vlog_1995compat 0 vlog_nodebug 0 folder {Top Level} cover_branch 0 cover_fsm 0 last_compile 1684145320 vlog_noload 0 cover_excludedefault 0 vlog_enable0In 0 vlog_disableopt 0 cover_covercells 0 voptflow 1 cover_optlevel 3 vlog_showsource 0 vlog_hazard 0 toggle - vlog_0InOptions {} ood 0 cover_noshort 0 vlog_upper 0 compile_to work vlog_options {} compile_order 6 cover_expr 0 dont_compile 0 cover_stmt 0
Project_File_4 = D:/SVN_Path/DevKitProjects/Ti60/Dev/Ti60_SC130GS_DDR_DSI/ip/dsi_tx/Testbench/TI60F225_MIPI_dsi_tb.v
Project_File_P_4 = cover_toggle 0 vlog_protect 0 file_type verilog group_id 0 cover_exttoggle 0 cover_nofec 0 cover_cond 0 vlog_1995compat 0 vlog_nodebug 0 folder {Top Level} cover_branch 0 cover_fsm 0 last_compile 1684146216 vlog_noload 0 cover_excludedefault 0 vlog_enable0In 0 vlog_disableopt 0 cover_covercells 0 voptflow 1 cover_optlevel 3 vlog_showsource 0 vlog_hazard 0 toggle - vlog_0InOptions {} ood 0 cover_noshort 0 vlog_upper 0 compile_to work vlog_options {} compile_order 5 cover_expr 0 dont_compile 0 cover_stmt 0
Project_File_5 = D:/SVN_Path/DevKitProjects/Ti60/Dev/Ti60_SC130GS_DDR_DSI/ip/dsi_tx/Testbench/dual_clock_fifo.v
Project_File_P_5 = cover_toggle 0 vlog_protect 0 file_type verilog group_id 0 cover_exttoggle 0 cover_nofec 0 cover_cond 0 vlog_1995compat 0 vlog_nodebug 0 folder {Top Level} cover_branch 0 cover_fsm 0 last_compile 1684145320 vlog_noload 0 cover_excludedefault 0 vlog_enable0In 0 vlog_disableopt 0 cover_covercells 0 voptflow 1 cover_optlevel 3 vlog_showsource 0 vlog_hazard 0 toggle - vlog_0InOptions {} ood 0 cover_noshort 0 vlog_upper 0 compile_to work vlog_options {} compile_order 1 cover_expr 0 dont_compile 0 cover_stmt 0
Project_File_6 = D:/SVN_Path/DevKitProjects/Ti60/Dev/Ti60_SC130GS_DDR_DSI/ip/dsi_tx/Testbench/data_pack.v
Project_File_P_6 = cover_toggle 0 vlog_protect 0 file_type verilog group_id 0 cover_exttoggle 0 cover_nofec 0 cover_cond 0 vlog_1995compat 0 vlog_nodebug 0 folder {Top Level} cover_branch 0 cover_fsm 0 last_compile 1684145320 vlog_noload 0 cover_excludedefault 0 vlog_enable0In 0 vlog_disableopt 0 cover_covercells 0 voptflow 1 cover_optlevel 3 vlog_showsource 0 vlog_hazard 0 toggle - vlog_0InOptions {} ood 0 cover_noshort 0 vlog_upper 0 compile_to work vlog_options {} compile_order 0 cover_expr 0 dont_compile 0 cover_stmt 0
Project_File_7 = D:/SVN_Path/DevKitProjects/Ti60/Dev/Ti60_SC130GS_DDR_DSI/ip/dsi_tx/Testbench/panel_config.v
Project_File_P_7 = cover_toggle 0 vlog_protect 0 file_type verilog group_id 0 cover_exttoggle 0 cover_nofec 0 cover_cond 0 vlog_1995compat 0 vlog_nodebug 0 folder {Top Level} cover_branch 0 cover_fsm 0 last_compile 1684145320 vlog_noload 0 cover_excludedefault 0 vlog_enable0In 0 vlog_disableopt 0 cover_covercells 0 voptflow 1 cover_optlevel 3 vlog_showsource 0 vlog_hazard 0 toggle - vlog_0InOptions {} ood 0 cover_noshort 0 vlog_upper 0 compile_to work vlog_options {} compile_order 2 cover_expr 0 dont_compile 0 cover_stmt 0
Project_File_8 = D:/SVN_Path/DevKitProjects/Ti60/Dev/Ti60_SC130GS_DDR_DSI/ip/dsi_tx/Testbench/simple_dual_port_ram.v
Project_File_P_8 = cover_toggle 0 vlog_protect 0 file_type verilog group_id 0 cover_exttoggle 0 cover_nofec 0 cover_cond 0 vlog_1995compat 0 vlog_nodebug 0 folder {Top Level} cover_branch 0 cover_fsm 0 last_compile 1684145320 vlog_noload 0 cover_excludedefault 0 vlog_enable0In 0 vlog_disableopt 0 cover_covercells 0 voptflow 1 cover_optlevel 3 vlog_showsource 0 vlog_hazard 0 toggle - vlog_0InOptions {} ood 0 cover_noshort 0 vlog_upper 0 compile_to work vlog_options {} compile_order 4 cover_expr 0 dont_compile 0 cover_stmt 0
Project_Sim_Count = 0
Project_Folder_Count = 0
Echo_Compile_Output = 0
Save_Compile_Report = 1
Project_Opt_Count = 0
ForceSoftPaths = 0
ProjectStatusDelay = 5000
VERILOG_DoubleClick = Edit
VERILOG_CustomDoubleClick = 
SYSTEMVERILOG_DoubleClick = Edit
SYSTEMVERILOG_CustomDoubleClick = 
VHDL_DoubleClick = Edit
VHDL_CustomDoubleClick = 
PSL_DoubleClick = Edit
PSL_CustomDoubleClick = 
TEXT_DoubleClick = Edit
TEXT_CustomDoubleClick = 
SYSTEMC_DoubleClick = Edit
SYSTEMC_CustomDoubleClick = 
TCL_DoubleClick = Edit
TCL_CustomDoubleClick = 
MACRO_DoubleClick = Edit
MACRO_CustomDoubleClick = 
VCD_DoubleClick = Edit
VCD_CustomDoubleClick = 
SDF_DoubleClick = Edit
SDF_CustomDoubleClick = 
XML_DoubleClick = Edit
XML_CustomDoubleClick = 
LOGFILE_DoubleClick = Edit
LOGFILE_CustomDoubleClick = 
UCDB_DoubleClick = Edit
UCDB_CustomDoubleClick = 
TDB_DoubleClick = Edit
TDB_CustomDoubleClick = 
UPF_DoubleClick = Edit
UPF_CustomDoubleClick = 
PCF_DoubleClick = Edit
PCF_CustomDoubleClick = 
PROJECT_DoubleClick = Edit
PROJECT_CustomDoubleClick = 
VRM_DoubleClick = Edit
VRM_CustomDoubleClick = 
DEBUGDATABASE_DoubleClick = Edit
DEBUGDATABASE_CustomDoubleClick = 
DEBUGARCHIVE_DoubleClick = Edit
DEBUGARCHIVE_CustomDoubleClick = 
Project_Major_Version = 10
Project_Minor_Version = 6
