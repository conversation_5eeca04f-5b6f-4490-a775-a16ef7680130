// =============================================================================
// Generated by efx_ipmgr
// Version: 2025.**********
// IP Version: 5.12
// =============================================================================

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2025 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//                                                                       
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//                                                                       
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.                                                
//
////////////////////////////////////////////////////////////////////////////////

`define IP_UUID _449714fdae91449eb701c8d91e94b26b
`define IP_NAME_CONCAT(a,b) a``b
`define IP_MODULE_NAME(name) `IP_NAME_CONCAT(name,`IP_UUID)
module csi_rx
(
    input reset_n,
    input clk,
    input reset_byte_HS_n,
    input clk_byte_HS,
    input reset_pixel_n,
    input clk_pixel,
    input Rx_LP_CLK_P,
    input Rx_LP_CLK_N,
    output Rx_HS_enable_C,
    output LVDS_termen_C,
    input [0:0] Rx_LP_D_P,
    input [0:0] Rx_LP_D_N,
    input [7:0] Rx_HS_D_0,
    input [7:0] Rx_HS_D_1,
    input [7:0] Rx_HS_D_2,
    input [7:0] Rx_HS_D_3,
    input [7:0] Rx_HS_D_4,
    input [7:0] Rx_HS_D_5,
    input [7:0] Rx_HS_D_6,
    input [7:0] Rx_HS_D_7,
    output [0:0] Rx_HS_enable_D,
    output [0:0] LVDS_termen_D,
    output [0:0] fifo_rd_enable,
    input [0:0] fifo_rd_empty,
    output [0:0] DLY_enable_D,
    output [0:0] DLY_inc_D,
    input [0:0] u_dly_enable_D,
    output vsync_vc1,
    output vsync_vc15,
    output vsync_vc12,
    output vsync_vc9,
    output vsync_vc7,
    output vsync_vc14,
    output vsync_vc13,
    output vsync_vc11,
    output vsync_vc10,
    output vsync_vc8,
    output vsync_vc6,
    output vsync_vc4,
    output vsync_vc0,
    output vsync_vc5,
    output irq,
    output pixel_data_valid,
    output [63:0] pixel_data,
    output [3:0] pixel_per_clk,
    output [5:0] datatype,
    output [15:0] shortpkt_data_field,
    output [15:0] word_count,
    output [1:0] vcx,
    output [1:0] vc,
    output hsync_vc3,
    output hsync_vc2,
    output hsync_vc8,
    output hsync_vc12,
    output hsync_vc7,
    output hsync_vc10,
    output hsync_vc1,
    output hsync_vc0,
    output hsync_vc13,
    output hsync_vc4,
    output hsync_vc11,
    output hsync_vc6,
    output hsync_vc9,
    output hsync_vc15,
    output hsync_vc14,
    output hsync_vc5,
    input axi_rready,
    output axi_rvalid,
    output [31:0] axi_rdata,
    output axi_arready,
    input axi_arvalid,
    input [5:0] axi_araddr,
    input axi_bready,
    output axi_bvalid,
    output axi_wready,
    input axi_wvalid,
    input [31:0] axi_wdata,
    output vsync_vc3,
    output vsync_vc2,
    output axi_awready,
    input [0:0] u_dly_inc_D,
    input axi_clk,
    input axi_reset_n,
    input [5:0] axi_awaddr,
    input axi_awvalid
);
`IP_MODULE_NAME(efx_csi2_rx)
#(
    .PACK_TYPE (15),
    .tLPX_NS (50),
    .tINIT_NS (1000),
    .tCLK_TERM_EN_NS (38),
    .tD_TERM_EN_NS (35),
    .tHS_SETTLE_NS (85),
    .tHS_PREPARE_ZERO_NS (145),
    .NUM_DATA_LANE (1),
    .ASYNC_STAGE (2),
    .HS_BYTECLK_MHZ (187),
    .CLOCK_FREQ_MHZ (50),
    .DPHY_CLOCK_MODE ("Discontinuous"),
    .PIXEL_FIFO_DEPTH (1024),
    .AREGISTER (8),
    .ENABLE_USER_DESKEWCAL (1'b0),
    .FRAME_MODE ("GENERIC"),
    .ENABLE_VCX (1'b0)
)
u_efx_csi2_rx
(
    .reset_n ( reset_n ),
    .clk ( clk ),
    .reset_byte_HS_n ( reset_byte_HS_n ),
    .clk_byte_HS ( clk_byte_HS ),
    .reset_pixel_n ( reset_pixel_n ),
    .clk_pixel ( clk_pixel ),
    .Rx_LP_CLK_P ( Rx_LP_CLK_P ),
    .Rx_LP_CLK_N ( Rx_LP_CLK_N ),
    .Rx_HS_enable_C ( Rx_HS_enable_C ),
    .LVDS_termen_C ( LVDS_termen_C ),
    .Rx_LP_D_P ( Rx_LP_D_P ),
    .Rx_LP_D_N ( Rx_LP_D_N ),
    .Rx_HS_D_0 ( Rx_HS_D_0 ),
    .Rx_HS_D_1 ( Rx_HS_D_1 ),
    .Rx_HS_D_2 ( Rx_HS_D_2 ),
    .Rx_HS_D_3 ( Rx_HS_D_3 ),
    .Rx_HS_D_4 ( Rx_HS_D_4 ),
    .Rx_HS_D_5 ( Rx_HS_D_5 ),
    .Rx_HS_D_6 ( Rx_HS_D_6 ),
    .Rx_HS_D_7 ( Rx_HS_D_7 ),
    .Rx_HS_enable_D ( Rx_HS_enable_D ),
    .LVDS_termen_D ( LVDS_termen_D ),
    .fifo_rd_enable ( fifo_rd_enable ),
    .fifo_rd_empty ( fifo_rd_empty ),
    .DLY_enable_D ( DLY_enable_D ),
    .DLY_inc_D ( DLY_inc_D ),
    .u_dly_enable_D ( u_dly_enable_D ),
    .vsync_vc1 ( vsync_vc1 ),
    .vsync_vc15 ( vsync_vc15 ),
    .vsync_vc12 ( vsync_vc12 ),
    .vsync_vc9 ( vsync_vc9 ),
    .vsync_vc7 ( vsync_vc7 ),
    .vsync_vc14 ( vsync_vc14 ),
    .vsync_vc13 ( vsync_vc13 ),
    .vsync_vc11 ( vsync_vc11 ),
    .vsync_vc10 ( vsync_vc10 ),
    .vsync_vc8 ( vsync_vc8 ),
    .vsync_vc6 ( vsync_vc6 ),
    .vsync_vc4 ( vsync_vc4 ),
    .vsync_vc0 ( vsync_vc0 ),
    .vsync_vc5 ( vsync_vc5 ),
    .irq ( irq ),
    .pixel_data_valid ( pixel_data_valid ),
    .pixel_data ( pixel_data ),
    .pixel_per_clk ( pixel_per_clk ),
    .datatype ( datatype ),
    .shortpkt_data_field ( shortpkt_data_field ),
    .word_count ( word_count ),
    .vcx ( vcx ),
    .vc ( vc ),
    .hsync_vc3 ( hsync_vc3 ),
    .hsync_vc2 ( hsync_vc2 ),
    .hsync_vc8 ( hsync_vc8 ),
    .hsync_vc12 ( hsync_vc12 ),
    .hsync_vc7 ( hsync_vc7 ),
    .hsync_vc10 ( hsync_vc10 ),
    .hsync_vc1 ( hsync_vc1 ),
    .hsync_vc0 ( hsync_vc0 ),
    .hsync_vc13 ( hsync_vc13 ),
    .hsync_vc4 ( hsync_vc4 ),
    .hsync_vc11 ( hsync_vc11 ),
    .hsync_vc6 ( hsync_vc6 ),
    .hsync_vc9 ( hsync_vc9 ),
    .hsync_vc15 ( hsync_vc15 ),
    .hsync_vc14 ( hsync_vc14 ),
    .hsync_vc5 ( hsync_vc5 ),
    .axi_rready ( axi_rready ),
    .axi_rvalid ( axi_rvalid ),
    .axi_rdata ( axi_rdata ),
    .axi_arready ( axi_arready ),
    .axi_arvalid ( axi_arvalid ),
    .axi_araddr ( axi_araddr ),
    .axi_bready ( axi_bready ),
    .axi_bvalid ( axi_bvalid ),
    .axi_wready ( axi_wready ),
    .axi_wvalid ( axi_wvalid ),
    .axi_wdata ( axi_wdata ),
    .vsync_vc3 ( vsync_vc3 ),
    .vsync_vc2 ( vsync_vc2 ),
    .axi_awready ( axi_awready ),
    .u_dly_inc_D ( u_dly_inc_D ),
    .axi_clk ( axi_clk ),
    .axi_reset_n ( axi_reset_n ),
    .axi_awaddr ( axi_awaddr ),
    .axi_awvalid ( axi_awvalid )
);
endmodule


// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_asyncreg) #(
    parameter ASYNC_STAGE = 2,
    parameter WIDTH = 4,
    parameter ACTIVE_LOW = 1, // 0 - Active high reset, 1 - Active low reset
    parameter RST_VALUE = 0 
) (
    input  wire             clk,
    input  wire             reset_n,
    input  wire [WIDTH-1:0] d_i,
    output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] async_reg[ASYNC_STAGE-1:0];

assign d_o = async_reg[ASYNC_STAGE-1];

genvar i;
generate
   if (ACTIVE_LOW == 1) begin: active_low
      if (RST_VALUE == 0) begin: low_reset_0
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: low_reset_1
         always @ (posedge clk or negedge reset_n) begin
             if(!reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or negedge reset_n) begin
                 if(!reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
   else begin: active_high
      if (RST_VALUE == 0) begin: high_reset_0
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b0}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b0}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
      else begin: high_reset_1
         always @ (posedge clk or posedge reset_n) begin
             if(reset_n) begin
                 async_reg[0]         <= {WIDTH{1'b1}};
             end
             else begin 
                 async_reg[0]         <= d_i;
             end
         end
             
         for (i=1; i<ASYNC_STAGE; i=i+1) begin
             always @ (posedge clk or posedge reset_n) begin
                 if(reset_n) begin
                     async_reg[i]         <= {WIDTH{1'b1}};
                 end
                 else begin 
                     async_reg[i]         <= async_reg[i-1];
                 end
             end
         end
      end
   end
endgenerate

endmodule


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
Viq0kOOwfB2r3ajDKVBNbQxpBpBDhXl6LJMYpWMCmyuPo80Gb8IHgd9LucmnO0rW
vKayGIyST6jD4CrKwVwSNgJbFQo8vKHKT/3FVKx0Tg4cyGIIh2IKF/AlHVKPKCx1
h51a0lRQbVZTuspSdTLya08LqD+LvWtZb5RQzKTEkjFE/svDVzuQWxcBpyEc3ONf
cewxx3H4vOwZ/ljupQYhIZXlR4CoI85hk0LP8sRstykSfJmy29+TkW3R5cs6v4cW
AMUoI3Nz0n/Xg5xgOFzgEKdH1pAZtnyEz9e9zjXNsbXp8ofZmrVjhqvyvLUYwcNX
ongmQIQa5YNCFVFwJ3jBWg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 37024 )
`pragma protect data_block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***********************************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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
oQRLfe6IiUBBD5YgJBYoxKiFS9jKxvBpA/RQ/z1PEypnpc2lKG2jetnPZyYqszeX
Ct+sY5Fk/ijnd2QIvUk2xKit/yNs3nbuMfrIfFm529bDlxdf5+UrpNKhNfIdsg66
G6+IQM+CgslzwMnJvK3oxE+U2ugD22XS57Oyu2gppkgRqoHNXi9L/ryxf5R4eEXf
ocDISZ9qva0GCmmA4/nm3+xWGxQ+lUtupDFLtxY/oNRFCNObwoy0/4x98px10+s0
SDzj1foAbfE0rT0pRM9wz0qMKNLk2IcP9FwmsXMnIRbNsP4DaZstxcsHDRvMzF94
PmHHqPhx/fUDz1yRsF4aRg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 2384 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
YDkZcRrsqeu+rBMp2ENTe/6UvbS6bYn8H7uDU4NfnLVJROXqSYnadRddBy6U41L2
4ey/wocogZGQYPx5b6kcrfPKfQe+iblfQ22++61+hQ65sNG3bmL4nKQou/Q3N9Uq
xGz8P9ME41iaSfHlEUdDWcl/tQGV6NIpVDaHA1ZdnwHXd/e+v1SaesX3XprpXdXi
AnuRK/ZJbGtm22tEO4kAL/4KBIxwICKdqqq9UTgqusmsguVYdCCJmhZSM3iv0EGQ
ttboaSwxRkoZWJ8y4chjlTkHT8bKhyjQz3+XOdO+vNdl6i2inKEAPLAs5/MbCBoJ
tBtFox/vE22VZcrG57hiZA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 4672 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
iBvWgv1dZFBQZyxl7hZLTW1Jvwt5RBvQc0x0dc7S1HU4738Z19cIUz4WKjeMAhNW
836Opmi6anwI7CavN65u05ldktNp2JOzFTNfwS3sKHx51uB4uLMDY2pJVeZQnoe2
u0gZ6LMlE5X5te5GOm/Z5BnsX+C2+2BUbJWRHVG2zw+qIPUctV6egW64HlBZdZ01
XPfXQlf3bN7cmNncCBMLgIeS+TTk/6hoZiuVCUhnOdIXY0oq+RCmwXD4IzfPXbqQ
Tt31q6ZIKIox0fKVIQtzqIQSuZRfkNd6hsSAdutlIGG5+DRmGJJGvv3ZVVsnLgMo
Zyw/kSxAcTT+ej6oFW35Eg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 11552 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
wJyX+5GIM2Zp/fn39rPVzvDWJQQ3pLBYeqFZmValZ3BdGIdeBCZY2EmptrHMT68G
fxkR3KqBr/RDDYb2aqd5o2HqM1+0bvDmir7CZ+QBTezPLt0SKnuYnIJOxtLq0mnA
nKWPjLZetqsiA4c58fZBn/zUrHryzUGu9o6DuOrZyRumHI8qebNxh5GloCV5d8tm
pPo+4TqVKUC9bv+YrmBLJqX2qFN06WsjNTHtF3b6HLyFEghxCeQHclLoWzM2hzKD
RjrfyBQlql0nqvP8SZMXhftYypYhxUZq21mjNITHh1K7QKgrtnG82rQLo4HoHpoO
nSIXbZaCM4+o7ceW4GiMIw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 31328 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
E+2/LAX8TPyM8jnJjX3z2WcSL7YhXwlTdqPkPjXTb9ksCt+lfXbw+7eWsREM9mER
EVVcHqeRVsdjZeYljywr++OZCLM+oear3HpzGeM6797JkQ0XfI6s/GgaaqD/qYAl
3lLPf4pPPW58V/9zIT6NUydhyKuSMuQII/7B0PF+pW9T1EPRJIDu5roWYiM0Spvr
YPgqjyHDEMh3K05OXFyEMIAmSVU1FGDVlQPa4kj/fhckU8zOW+NCLcKI8ThnHWbc
Ev/Ys8FJo5p+3xdtFoEXEFGbUEu/Re448t6iMhveW6szD9aV4ui2Z3jeEgkBVA6j
TjzKVTlfo7X+cAqCqGdRyA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 6640 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
XfESszFQV64UP2q/kLm/BvZMnWXueKipbOY2k2zYxRkTmVcU3Gu1tGpudaqvZsaE
Y3sZomonzhW5XRz4D3Fp4fNJaLf7GNyuIJMFF7+0f4Bx1VybdBrFhktbve9QMLvL
B5Nc1r0I1qSN7OocqswLcvZsJn59NbgtQsEGECLXPvNXvboy5Pw5ooS64xvw3MAS
y+l2XZ9t5SMogqJOflLFz+43u+IfELCICtUE8HkhjgcaxUjU15SmGp/n5Q51HxS3
zvRHlfGlqTNdYeDD6+g8vSq0XIh5q7bKPLzKVageZ7Zi6m1S9IkIQ5hoYo8PQc/u
ViFtFGpzMJVXfzJUwZynMg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 4960 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
uqpPN2xSc6d/bg4z6D2sB09ekPUp5eZBThVKIaU70Zy11r1PqvRR5S1i58wy9XQg
6icfPgnZYa9ywB9/hgXrgrKeoisSxZZq1FJAXUOwjYsL7nJ//clcwBKAjL4LRmbV
hfzQYaxuI2mnt7UKvykXBaWC7jPvP4/Pvvw4Gd4+boRyP2hZPqqhc2HVDByReZNT
14NYC54dO0lBGt6rlg7Jj2w96fKo5kwTmr+4WIZwO5wSgEDmqtcJlByFO8i7A0Uw
***********************************************/jswYo0uI5xuMPzLw
+r8jssGVvd8+Zb3kDmzDEA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 6288 )
`pragma protect data_block
mXvOGfu6wyxSZGAVUxl4JuX+ID/IAvFRZRSrJo7eHzlP1kTg3+Y+K7JMmvIO2czh
VD/ykL+w1MeexzTEKA64hYXIYpr9dpHQriUKpWBXY0M8WMre0O5oJIl9OYtGzYTD
0HgQS4lIcYM9NtzZjj6bmIu6vQ+oKnx7qj+pCsVaGWg70KZ9LXKtuCu6cjBK9gHN
FHjc+XolDxijB6xNf2e4ILqNfrUxKDnllbCI5u192lGC8DZUPvM0JBtcsvj5UfK2
aaQ0gPPnIc/HkTKRB4xpW8M0bJXwHyouii9BDcsjUk17YCLRHjVvQejQuvmjRVk4
MIukjmWwiN7KnV6lfLqE78fNH3a+5raCSl683tZ3rprGkKUL9YlmaqcjUD2fGkGu
3QnH775BFsZwB+snNy6fXtVG/AK3TkG6mZmqbQTJ+SxmmMSrgYdXOYXJoRsRTut6
gVM1hTrF9/QlAA23NmMK0lKrjEevb+7vRS4IbpAAfBd6FGDJTezK3NwGmXz70GBI
9f3MEM2t2vw/sBBj7We63STbqNwPRFQroWDluEYdfD/NL0F9tlVKsn0y8fmMNZ7S
OhsEgfYYV6ZJEPcgrJTasffQFvYoi/Co1B9KszJ2HSkgywyYxVqS1T8WGDXA3b0S
Oc19w++PwEdbxYeor4wZ/PQAxL7+vXfNN5m7TIZqJlRaf1SF5ZmokLy3NpNTAKHX
MrO80geZsQADzBGrdO50F+oW7P23bHkTwzY5FDI/cRAsdHVWXbvT/89HEIs22H5C
zI4y0V5u9WOaiFAjkLXKOF3txVR6wsXgz4R4SZVL4bu2/W1stUzqCqRcEXwU9s/P
yGK+W9r38pgtekYXlANKosX28ep9/eTckKEgqc0Qc5gF2CMibhQm6RUeHAwLwcHa
JRZ6lopWezjGO9rt9GhejP9BxDJ/YnXASKH1jYiIRw3gcSw63z64ZkSvY/wQYo7o
ZkEqhQ/+3CuQMzWiXf4fwLFTVGDs1KqGLqOOeyqeZSVZ9UOZxOWGPTg3g0BgOeGj
Jkr7i+XLpy3f4R7NaUh+9QKTm2oybuw7F2GuVFLoLm4d5C2Qtaq0udYE6W+tk0Bg
J6Wmhe3nA5dCQhMDjK+nDUbhXO/XfDO/66lquUBrLFyHtB8dhQ2rMKjzwgrfdaQV
Oa50VXqLQeviijoL84eSIYXsT/lxM4tY4L5rxj8AnnUFhY62vMhidqkNmMRHvck8
vthNotsyoORurplv8lPGYFcRT6fNqfYtDAjUxQOgu61jxBYsmn/h5kXotJwcnjo5
4SDxxCtzTSdFw3gJvR1eLafX9JtrYRPtuIgLnkRDrhH1/z1g73BqCj3BP5n/UZIW
/Zh/P0rx9GJ3pmdTlCBIN5ywo3qln2BpPBgaUO0o3v8erG7TdpvEEhQ75wE3N+Pw
pz1HtR7xo8Pp3KUdqyyGBEqQhCZvlxw1h0x9A2TkDDW6huzdY3QB9bvJxpQiFYd6
uW+jT02N1n9gx8/QBq2OMpFJC2LT1xcI5NiaKtpjOXyPdBS4l1iUc+gfx9InbRrB
KbGopogrSypphrbY5vZ+DJMc6zY+3ltPM6XGh49ujuHqw6zZ9Cv0NL0nIM/aQYsG
qGBaR5Gf3IkrZMXl+ushd1T9SMTkeyr2moxqz3PEJcTfr6VfWEIyQ0rcga2YJTFe
X/QQsrrvhdE7tUlHOGbghXwiaIgjX69LRwtzV0y7EDcROKldWVM32yC2YyapIAYs
6MrewKlpCrY7jv5Q0R+zWwMA7wy/2oQBOmH6dGMLGaBmyDv0DVk3h8/GxIUOXHOY
iLcz3iy+DJS2RckBfZtRY8MWXJRfrcYPuF2EjGPgNkxctOAkqmeXol70/ByO/3Gd
WWxfzMC9sa+9Qy+jjdKb7HAvWSaw52TLLe0CNTtnEyuIklBR3bq5xlmtQHsF6MKe
naRjG+FVWW5Ac/teUAYIQJ3KD/kx4V/5pe1Svs1wO2HMgJKSPig6YBUX/Q/1EAAu
MwWv5s89pPJAsx+B8VBZWuHRNi2grZWUEYDa2nHcqZmIFcrj51+OJRn4kfing848
QeDanqXmMcLUTv9levHv1nPPX7gwrbRtFS18gSEE6gXtWMUhhb+++2ZV4sLTMTVa
4ZVIb8k4050HzLFqL7RVwU7H6ijO88pO35ZAR0reXSDaBhNAcj1+QIFSg2G1YZYv
vDIryG6OLxlkHy4rGCtcpWuKFKmJweBf23v5vUr6UycSU5trHd4di2HQOrf4wOJR
+O8yd7OmrMi2JXrnESEfdJG93NNIG+TS69SKTy5W7ocuDS6HSa9XAG5Nghj6lc16
qejd3oGvNrjqC6dI+CEa7ypQWg/jKRRdtMNjU5p5o0Fa45R4+5mThzHbiG5ia3rm
dSx1RmYB8m+3Agf6VQ5NAEG0HYy7vO/Lt8OdaPUJuJUPjuW4TJ3NzjE/mR5d8ZEe
SgXesgehtzdDHwHI1lmGGI8UfgpMdSpVrxAsQzJKYFSUoRtrZgolduPRrrG5krAJ
/QNPFwueq11KmINCf7X3wKuw6HKny6T2k13Cdu7C1ALo5sImMebDbl5NQVy+qV+R
pTyT1MfiS6hzM49cILwRz9SLp760+fOVbb1Pu4gXdzyA22KyGZUvpJfA95+dN8KB
LTPwPeIU9QzxxF/ITF5cNdsZgYeKbHKEw2rN6Palgnf2uNBNM2zS4mHTEs70xRT8
7avDsKf/qy+MRajOsbhHNTI6hAq5fyefmss+WIMejctWYDifZwomNyFF9HDbL5Tq
x9Pw6NK3vaqqrLHw2kdKfGfEIAQRQHi4BSQPFtj3jtWmeJZy6mpm0yv8u8tcqEEr
hj/3CC8BaYs9esvCM8cH7gWDaAMvf5l9C6fD7vyzcIevzZSyg0HIGeftcwWJGMNE
li6Q1MaxfubPn/WfQNH/PEi6S+F+j0VEMz13u4sgD+zladQ3LG/6dImAlmGszfPU
k90MCgPGQDEgy/RIhZG1YppE8cDLYfDVImWfOCZV8FeNoErgI/HwqBzM8jKEYK9q
mKx7ZyvSCoEeVdXpjcp0URXwe14n9I9u4mwlNCWtGRZTYQ2dZkaMgABtdwc7/Ys1
KXWe/NMECOZev2OilMxQFmOzxs9feSZ161KooiIhFbU6648Nti2hdWZSar9KMOxU
umz/4/LNXigVjZse+Hk+1GEe3ZGrJBD+RnBcakpaBeuJBxcvftecDVgi6YOT555T
/aWOLqH0CafDD9kCKRSrLnvvFc4G8PqBfIHAypdS+mh7wmi8ic5sg11CbIB+V9aQ
S18XAMe4f6Y/WyP0UXwK/k1eJCtq7JgD8MFB0FSHdh8QWI0AS8bxBRfZRNOmJQ7G
wCF8jWgxKleTDU9I5dvHJFXPJg4wW1KaNfHjZ4CZBCpgNwp8gQerNrTqaeKUfe1q
GJaoSZQsf1qdWNCxUwCGuxag+2MhlZIIuLG+puzSmNk9nTqiPKHrkgCF0dEQBP4m
aQAzsByBoi30MoCfsR5p8Arq7/7kkOI17sOy8+1yK5yIkYamjgGnmuK2rf55a1++
Tdgy6ZeB5N9+olnQKuvpMWg9VO8LnEx3tYLRe4t7Y7xJVP5phptAWkKz6LCnvKOa
ShFPpeuzQCT/0YbhZjUXZiELHzPvv9fhtZIfJs3sfImTmkedjtJiEZgHn1L1cvUa
fXXYct2eiwmNmOubP8lz15gvjCe1qpHSZ8wkvBJ5PnVZt7rVvVOexBS7uSTbUabj
B7nWrgSnzYxbfbApsuB2ZxkTcQI0d0ugSe228r/TJNcPkWNl0oDC5WnOgG0UBRXP
EXAAVzgVBK77nskASK+0gviO7Fn8vUaSCAxokdGDynNurDWlxCcMCQ1f0nfhrF3J
1v45x5OOg/ofP2OqMK30eU9A+kK4P1GKMUbJfoTA8Nyspy5pPtOpMhwRIj0rnSUL
yodZcfwDofsrfxr9b7ZHM6D5ihyL1hgQQlPqF8mntglKZto3bVpjpW2cIbV9dl96
a3jSUNESY1RqyhkAh9nSk+32OAn/VpZtjpaJTz/H0p0h5NmwoL4q6c6fcvYPCYBC
8EedwWRhRSy70+zmUBA5bNwCM8Z6fXMvhUXQf6LSBd0LP6Wdc49FaJi9/MijdsTG
TOjSsBWPLsnVlcTLkX4hm9z1ez46PCwoI625qFZK1trjhlgKJ+wCKAqfKbiwFJZ8
C+yvbVRmeEgBich7dHFqbY18DUvKqJGV7slN5aZdAtxiuczUBRr41UWW5Lqrg3pV
2sTx/wSd93CQ6AI6t05Fpo/yjv/wZOGDGPLNQYUP2/NieLaY+4675i2vMhGIC6Bo
2DVkS7Zfu9PDlIh0Ln4E20VctRjfs6gPga2tfQteOq2+duQJfAmm72CW8+i+imYA
9zK/5v7OdZF6fRecX5ow0UU5v/lGpTf5YiIOFhCZbG/hTkbtlMY7fRnQOQFUhnFC
/ppd0Jhi9e4kiu/EdlH+bgxcW/mPAZOaGExNSrdnwogw5f9rn4KXEznXpqhG4NCL
GiUhgTS2qD2d4AMJLneAKsddpO+AnIatV5nu2T37lJNoIbLh/HjOfA4NUzkxBwE8
/DNM0SOPFT7Uo5Ao6961fOF2B0MZL6RnwtvWPmmStr8jXQ3DZ1V9eOOo4ns4UQvM
uN8ODpxncY2SQHUTBh0kbKUYtvoqDfKknm0z6tV/2dvemfu81UVwoPcS++0e9UQu
uuDsHX+4WSyHiiyU2UmwOTlDJqsxP0z1IfdRrx/R6VPxX89UK3V94NhbG3gPh5vw
7V4L300bYr/xaCxUDeVkjXdLZjCN4caNJSWRNH/2JvyzPrJRH18pWtoDadEKm0dI
jr+/urUfw4TwhdI940MRpcvZ9Xv7Vtuzvv8spqyC75tzTjzV1xMG4qVHC8Z+v2fQ
UVjs6ztv6KfZyORxCk93Yu8oku+mGQ1scjcxWwZc0Qjz0Tr9jni0wAHaBmfy6Cf2
nq97SBautaiUU9jrc9JdXGJDp44pqwUXYTxGuhCW8LXZeCRwGLPrRHi5/1EJFzcY
YdrfRrVShyQUqrV6vGX8T/YYCnNtrLl6zKKuy5058sWYFJdLkB6M1K52sVN3FCTb
i1vlE7tTtKDEv829ed35OEixossIvMjDl4A7aP9Q3XwwstMftKjm5hNY7ntPwe4n
IqpYhIL5Cec/nm/N599YYdsG5Wsaa2LnZr/bG5ifMnCYHzqhK8XbHXFSWxj/OQq2
Fa83PiXy/rbvKxkdZYZRrR5gCQWXs7mAEJHKzPLfo41yZPvAXdtXsEAprr9neQtg
G9k4EILQO5nLF6ilhm6BrSeioxkJGJfm7mgMZfvIdFdcqYXSFoVYCW34eO4+R6wh
KcFLt4wZBK6pjSE69jNSzvVTverA/u4lHHZgVWAPAxFzS3p87ASA1vz0TpRoTKEc
CSaCZS9np4mfOAZPZ8OIU7sO2xAs1YMQethW4pjDO6UV4B7Icn7yuX1vrcpruQJb
dhMUgYSk79DxFyhBC5TmC+Yd/6/oOGZm2o2axfcrgxTgHo1Ua903hMG0Ci4o9s1A
H/3h8BO5CT+0io2bta7fK2ezUGnVIAKQrOL41mYxOL9BIrnP4Xs4JCdmzrT7L7eY
Ksd9LIwlcYLIs82vbxU+aEWfxJIyp+ciP0a+9UEpTIKjRfNxAy1+A+JCAjQCR0Bx
thzJ75H72le/1ER8/bu8jXpjOQps++7pS1sk3GzHGIiw1jw2G4UfPKKXN2Q19u2W
GcNjxH6vqhhtMXb+5vFM4RhvmnFXl7/xn83x4cZOlOKkcQxzeatUy6hRPnbrPUHT
KaY1QfxVztXb0Em6L+3TWN/cFlfhLObndig4fWFmemUwSLl4pQNkezY15u0Vw7Yo
bUnkLXZghn/+FnDyuRIcL9lC05nqjB6r92EWWHzAKrVioQrqPjqv88Gr8zamO6EG
AbnHN+yh0NVY1eN7/5K0Xs0/630tOQj/yKtdI2pMXOe397klCPzr38FiV7oaOZKT
FDrfh8L0L354MhPxPAD9kKd/Rbacnn4qHxI9pqr7lK3lb7sChXB+SB+ZxX+oCpma
VNpFmyMs2WAdleDUF8tePdxO5tAVvCtKkyJ1i4e2p1/Z00PV2aR1mxqZijGXalIA
rFzqX/s2YYW04Bqzk/1PdhU4F9NIRuECz+YY0s2QMDa1MzbLqN13YxD6bI0pbgYd
2Se8RMlDtilLTnjsUi+9vQqqFDpPmMoTExrM1rfIvRTdUO7GgTHUkeDf5ZYHjBld
0C5mwxE5Kl9U5CVT8frNAOiapttj2oQfJziDVaHQ50ns5yV9Tek7CAZL0Qx/nd4K
scNwXMLiscsPGzL6eLZhdoNdCItYS/1ad6w0eiM0RGjM/OsWOAzzQ7z/1HvYL/6g
nvYqcJk3Sz86U2g/oZhh4CsGpnR9NNwJcWxVSi2pCQ6vQtrM4qIyRAjJBbK0TG2I
juSHyjtTeSXCeZouMfeeDe5w97u7m0tzm3s2/IEHygoLKPjW/F4nRwXRr45P7i3u
BoWbjBgPV+vK7j/OFC8rhQcMqzk/d0lU1Gy8kt5At4Y65p0SoGXEdzi6xNrU3XLF
9nZvybLBqrrDdhdxTeVxPQv6CG2r0AcryrosD4CsWHNx62J16LlEGq97boeXYUR6
QHKSycoiFT76pU+H5g4R0bMqgx9dyCXZe3mTNgOrZ+qX7o6ev1t+IBCkrIhICCbg
RIJSJ7t3RdWoMabFpYmi1a5hqmoPF0IuHkpHSRjf3KFDX5sifN3zVETROGrTrHAg
GnxnkVDJhN8Fiq1RqXLnp3g81Rh9TDCyJFCXJfwfOWuuRtmWwIc+NMFSQtaXROwo
AD6fsfk3PhjSMQn5vd5UwOnCRagGutY3AcQQgBmYMNpSu8Kq5et4fo8KLA5uiX82
t7LXa5AWPZFVpNEc7J915m2GUXVD0qEdZjJSZWRgt3/4MtYGD9VBHhvEh3N4+cpZ
u3ZJA9dayVjDneWcrXJT3MFy0cYtc9FPSnO/ehHYl0Np5qOpV4yePk/EXaeMEtXd
m9/2y3TZI6s8obrowHSwM9Iu+XZrPhkDjnRuHWnX5eluUbgqrX07Jr2LD0fcD7fj
pOkRprbacZRu6sr4NzM3JQDmlhot1bC/HSInYvbGobNylOKevfCPW90fGdiQHF3A
Lsc5a0OVD3S/F6pEJmqNkswHblo9HFFH8/JX6D9q/ZKBPIh9hsrV5nfI6vCdB8gY
HJhPLkL2kwnm9aYF2reKuispTKDk+aPYoJ+6rRrFbx1+ax0rzKUKpwxFzMOODlnD
lFd+K4htvCdK8JpOh1kUFwwESsizzXF49Jkp3UumQk1FmycymqzcHtOARTLyI2L7
bnVuDcN1L3NctUP5rfj3oSEsnFd4/UYU4SAL+DL4nmNOzq+gAnt4TMYwM43rmwcp
8Ba1JHpnZ89Pb0HmSR6EePdtxrlJTygnaSyw2a+DW19O+k4dLp+cUkciDv0R/cjC
HG2TQDKBdUcF0HPV+Kc+Uyxczpb3N8dxX2qsF00weRl/3dEGuGfl2HnqLwEcQOE/
C8zmDdY8YGylir5xVxf9LiG4cOnSs+RMFOlvGC7iWF5EUviQ/CLQbp0oXf7LBKYt
MPZsh6lvcKbBzvQeRtQplZJ/T6n+WrpHbGhjvJFFJCQZRbxeSuSAFJ2+MN+JCo7O
WtWt5tHF+6EuAIxt2N4D3IWIMSFKpG9C4chf6o80/sgv7zN7n5lvJZyguCC8m881
XRdnhHdPKQg2TFo0OccafK5rF05kT6wFZ0wCAzIcLvRjVuQqHPyRfO/9D4O90G4S
fYao/+db4rRuBH86D+6yii2CnMlRP9NwFjdau1P/Alr/Vjh7mgsJUc1+L6ozVqEA
RKjLzIUZUmxbmyp4KnbDGbLgxVcfUlXGUroSVEOuTkfyznl+ODYELEklvyGOSIfx
KAgdTbPr/Qtocfi46iS6OUqeRa5hdEVSHk6O8+VnMzOdtXIFC4xLiwG8tmKz7Wju
EIqKMBhTUIwnCmoacr1RWL68GXcnlekukXhswZzA7QMa405ImivqaESaISzBrgl2
TD+tJ0E2Sog+4VpGLcHevCJv5hiE9bfmA+nFnZbEVOLQBdxBkfYGFmYpXOWT8Tt2
WCHwYz8I8pnEUlgpQspKis+XeQpDboMcHHEaX849RaL/cojUAr7Nwq7U4yH5Qbvd
4WszEvCgaBtBlJpLg0rr9DsJnMxqXLcVGa5AlGJbHXub/tIH8a5qaOmwiw/1HODS
p1dKsM+VyvpNM7kJgd0b+qAQUXopGPvlLte2ruBIvHFSsPmChIa5vc+w+suZQ6gw
Shzk2b+kc54dKBfiEx3T+gFfzVPUEpCFTnvFWRf3gG1AP3eLZZmcVaUWSaIq6Y5d
K+bDecw61naNAGz2xrvkOk24Y6IVDYr6NYADJRKi4h8ITz0MKlhlBzELRUE8dGMz
`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
D7rLO2oEmeyq1YifAHIAqiodsCbS6hcrR6LGNhRvEUFccMuzuN/KkvY38UD3e7Nd
xKJvmqLyrD82mN71rmsPiJc1CfwDQDEfLVDblLmRI2I1/wke+/QbRhgvBfmjH3Jk
pvF+ttEif6BjIbZXnLtqbr6DdGNHmDzN1CQaacIDeGHBA90OCkzVEQm1TaVRPXtg
CAv1nCPxbo00ujGcZpRv+4jOsrJpBT8YtyzmZnAjLA9g4K24/2oM3WP2bPBsLhcU
gILZoRYkCb6v538dnQRllNLjpDKzyxH9AYr7hTdOSfXNMMGfoCVJqgJmt8c8dIWw
OYYLLPulBngLa80LZJDkXg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 2496 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
kteYjvoRun2+Ff0h83ldQ32uzKHwyNHnVZO0ij0pEowiAjblttHVCt7gkdhWV+pA
UrRmOjKHnYBN+BITMKXwtQrXeZYf4+s4SV9y2r40MgjgjmkwqTNstzGr9VrAa3+E
w0YofkEzZc7jkZaSN3GSJDYdw+KU7VceKdEuhFiCUoV9Yw6GuamyVHwwOIfN/K4X
/cDO8N5tFrdL4yG+6f0X3D/Ofxx/M1UGjHNTvuDffyDfHxM1B7P5fyeZxEwojhwa
6Cv1LleykWFQNjY6afEvdR7OkXtnQmAlmfYbbqHmkKqRana6U8Yu2Gy+pSOKdh/C
b2OH6TVf2HR7RKUCrQ2izA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 54720 )
`pragma protect data_block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*************************************************************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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
xPBnQOmBsaT9ulKTFz7xtjKtiCEsQVdpU434PB7/PmwjTkwjnO5QoZvpz1yXEyES
E6QHOaKWpn1/n/Rl1Gllz56ohW8JccaRxM6pKOfg12Ktcro4H28DK5XUGuy9Z4D4
qgIITyshHnIGbZPmRRsvewcNUqm434SYzxg36Z0uE9mHRDdnQDbWJ5ClMGD/O0Vt
EJCr/qBPo2sf5ut2GmfogZm/QtqgTWcDUIWQK8zVrXLL9c1A+LYQzWIo4/n4iE+C
BDbGm+3dSlaVQwyGMphlp75D5RvD7n6vnLmg5VvoQgFOaooa18yTR3rfBC/c9Tee
BoV7ytJhfrYI6HJEVoCTcA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 9568 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
YgIngQgYLNGQuEJvONMN7sPXEyh1n/230uNWHk9Hslq1ddtbxim3zYmDEkShqz0u
W75sZQ3UcOvaqUlA+NvH7rzAD6khf5lvLXlrickSeq5P/M5Ncxrg4Zup2I3rW171
/7djxJOxBsWLU+bMyhrnGuML3yMMb4EDz6K4NIEntnRz4tjoz9U/BFvB/Gm81sTB
j6E3H3VMT+j3fqbpulnZWy/MoO0DP6DI/xODfLvpFNWGGDqqYDkA25iqvTLXa20M
dMZ6Ufyg2aRCHgmIXyhNJoisWlqCPh9Vk2l0M5q0r1zeWg+5dZY1Uc68SpLLdlU+
qROZMQP15JNckGkbXT8KCw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 26016 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
zgRHGof3G63tQGS90Lm/yUokeBcjlzwUJBROHOrGrDOxj6uzeV7M1AlfAZXDdQKZ
Igl0GfoVQgXp2Tkp1MI4T2lnp8Sl7ZS+o+JEamcj/5YoPTPsSMHqYCEtMMruaia3
1p+BDKiUF15YmKOzbDJfzBRvR3BiIWN6Yqj3N/NvTICiCVqpv7A8P6hoLijAi0HD
ioxlQgQuvmTu+dE5RHKbxlCrVNS2jt1crqDWL5y6/8h9exnHcUGCNO7ZNkyFAS1E
JnHxhsAT9XKkODwAwBgxfmD12CX3qKjPrLpyEu/FjgFN0aWgP89qtFSZhosrcx6L
8tLgdz+RoUSVyWYGkLjNHw==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 28624 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_csi2_rx) #(
    parameter tLPX_NS = 50,
    parameter tINIT_NS = 100000,
    parameter tCLK_TERM_EN_NS = 38,
    parameter tD_TERM_EN_NS = 35,
    parameter tHS_SETTLE_NS = 85,
    parameter tHS_PREPARE_ZERO_NS = 145,
    parameter NUM_DATA_LANE = 4,
    parameter HS_BYTECLK_MHZ = 187,
    parameter CLOCK_FREQ_MHZ = 100,
    parameter DPHY_CLOCK_MODE = "Continuous",  //"Continuous", "Discontinuous"
    parameter PIXEL_FIFO_DEPTH = 1024,
    parameter AREGISTER = 8,
    parameter ENABLE_USER_DESKEWCAL = 0,
    parameter ENABLE_VCX = 0,
    parameter FRAME_MODE = "GENERIC",    //1-ACCURATE, 0-GENERIC
    parameter ASYNC_STAGE = 2,
    parameter PACK_TYPE = 4'b1111
)(
    input logic           reset_n,
    input logic           clk,				//100Mhz
    input logic           reset_byte_HS_n,
    input logic           clk_byte_HS,
    input logic           reset_pixel_n,
    input logic           clk_pixel,
    // LVDS clock lane   
    input logic           Rx_LP_CLK_P, 
	input logic           Rx_LP_CLK_N,
    output logic          Rx_HS_enable_C, 
	output logic          LVDS_termen_C,
	
    // LVDS RX data lane
    input logic  [NUM_DATA_LANE-1:0]      Rx_LP_D_P, 
	input logic  [NUM_DATA_LANE-1:0]      Rx_LP_D_N,
    input logic  [7:0]                    Rx_HS_D_0,
    input logic  [7:0]                    Rx_HS_D_1,
    input logic  [7:0]                    Rx_HS_D_2,
    input logic  [7:0]                    Rx_HS_D_3,
    input logic  [7:0]                    Rx_HS_D_4,
    input logic  [7:0]                    Rx_HS_D_5,
    input logic  [7:0]                    Rx_HS_D_6,
    input logic  [7:0]                    Rx_HS_D_7,
    // control signal to LVDS IO
    output logic [NUM_DATA_LANE-1:0]      Rx_HS_enable_D, 
	output logic [NUM_DATA_LANE-1:0]      LVDS_termen_D,
	output logic [NUM_DATA_LANE-1:0]      fifo_rd_enable,                            
	input  logic [NUM_DATA_LANE-1:0]      fifo_rd_empty,
    output logic [NUM_DATA_LANE-1:0]      DLY_enable_D,
	output logic [NUM_DATA_LANE-1:0]      DLY_inc_D,
	input  logic [NUM_DATA_LANE-1:0]      u_dly_enable_D, //user control the IO delay
	input  logic [NUM_DATA_LANE-1:0]      u_dly_inc_D, //user control the IO delay

    //AXI4-Lite Interface
    input                 axi_clk,
    input                 axi_reset_n,
    input          [5:0]  axi_awaddr,//Write Address. byte address.
    input                 axi_awvalid,//Write address valid.
    output logic          axi_awready,//Write address ready.
    input          [31:0] axi_wdata,//Write data bus.
    input                 axi_wvalid,//Write valid.
    output logic          axi_wready,//Write ready.
                          
    output logic          axi_bvalid,//Write response valid.
    input                 axi_bready,//Response ready.      
    input          [5:0]  axi_araddr,//Read address. byte address.
    input                 axi_arvalid,//Read address valid.
    output logic          axi_arready,//Read address ready.
    output logic   [31:0] axi_rdata,//Read data.
    output logic          axi_rvalid,//Read valid.
    input                 axi_rready,//Read ready.
	
    output logic          hsync_vc0,
    output logic          hsync_vc1,
    output logic          hsync_vc2,
    output logic          hsync_vc3,
    output logic          vsync_vc0,
    output logic          vsync_vc1,
    output logic          vsync_vc2,
    output logic          vsync_vc3,
    
    output logic          hsync_vc4,
    output logic          hsync_vc5,
    output logic          hsync_vc6,
    output logic          hsync_vc7,
    output logic          hsync_vc8,
    output logic          hsync_vc9,
    output logic          hsync_vc10,
    output logic          hsync_vc11,
    output logic          hsync_vc12,
    output logic          hsync_vc13,
    output logic          hsync_vc14,
    output logic          hsync_vc15,
    output logic          vsync_vc4,
    output logic          vsync_vc5,
    output logic          vsync_vc6,
    output logic          vsync_vc7,
    output logic          vsync_vc8,
    output logic          vsync_vc9,
    output logic          vsync_vc10,
    output logic          vsync_vc11,
    output logic          vsync_vc12,
    output logic          vsync_vc13,
    output logic          vsync_vc14,
    output logic          vsync_vc15,
    
    output logic [1:0]    vc,
    output logic [1:0]    vcx,
    output logic [15:0]   word_count,
    output logic [15:0]   shortpkt_data_field,
    output logic [5:0]    datatype,
    output logic [3:0]    pixel_per_clk,
    output logic [63:0]   pixel_data,
    output logic          pixel_data_valid,
`ifdef MIPI_CSI2_RX_DEBUG
    input  logic [31:0]   mipi_debug_in,
    output logic [31:0]   mipi_debug_out,
`endif
`ifdef MIPI_CSI2_RX_PIXEL_SIDEBAND
    output logic [15:0]   pixel_line_num,
    output logic [15:0]   pixel_frame_num,
    output logic [5:0]    pixel_datatype,
    output logic [15:0]   pixel_wordcount,
    output logic [1:0]    pixel_vc,
    output logic [1:0]    pixel_vcx,
`endif
    output logic          irq
    
);

logic [7:0] RxDataHS_0, RxDataHS_1, RxDataHS_2, RxDataHS_3, RxDataHS_4, RxDataHS_5, RxDataHS_6, RxDataHS_7;
logic RxValidHS_0, RxValidHS_1, RxValidHS_2, RxValidHS_3, RxValidHS_4, RxValidHS_5, RxValidHS_6, RxValidHS_7;
// logic [NUM_DATA_LANE-1:0][7:0] RxDataHS;
logic [NUM_DATA_LANE-1:0] RxValidHS, RxSyncHS;
logic RxUlpsClkNot, RxUlpsActiveClkNot;
logic [NUM_DATA_LANE-1:0] RxErrEsc, RxErrControl, RxErrSotSyncHS;
logic [NUM_DATA_LANE-1:0] RxUlpsEsc, RxUlpsActiveNot, RxSkewCalHS, RxStopState; 

generate
if (NUM_DATA_LANE == 1) begin
// assign RxDataHS[0] = RxDataHS_0;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = 1'b0;
assign RxValidHS_2 = 1'b0;
assign RxValidHS_3 = 1'b0;
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end             
else if (NUM_DATA_LANE == 2) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = 1'b0;
assign RxValidHS_3 = 1'b0;
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end
else if (NUM_DATA_LANE == 4) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
// assign RxDataHS[2] = RxDataHS_2;
// assign RxDataHS[3] = RxDataHS_3;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = RxValidHS[2];
assign RxValidHS_3 = RxValidHS[3];
assign RxValidHS_4 = 1'b0;
assign RxValidHS_5 = 1'b0;
assign RxValidHS_6 = 1'b0;
assign RxValidHS_7 = 1'b0;
end
else if (NUM_DATA_LANE == 8) begin
// assign RxDataHS[0] = RxDataHS_0;
// assign RxDataHS[1] = RxDataHS_1;
// assign RxDataHS[2] = RxDataHS_2;
// assign RxDataHS[3] = RxDataHS_3;
// assign RxDataHS[4] = RxDataHS_4;
// assign RxDataHS[5] = RxDataHS_5;
// assign RxDataHS[6] = RxDataHS_6;
// assign RxDataHS[7] = RxDataHS_7;
assign RxValidHS_0 = RxValidHS[0];
assign RxValidHS_1 = RxValidHS[1];
assign RxValidHS_2 = RxValidHS[2];
assign RxValidHS_3 = RxValidHS[3];
assign RxValidHS_4 = RxValidHS[4];
assign RxValidHS_5 = RxValidHS[5];
assign RxValidHS_6 = RxValidHS[6];
assign RxValidHS_7 = RxValidHS[7];
end                              
endgenerate

`IP_MODULE_NAME(efx_dphy_rx) #(
    .tLPX_NS              (tLPX_NS),
    .tCLK_TERM_EN_NS      (tCLK_TERM_EN_NS),
    .tD_TERM_EN_NS        (tD_TERM_EN_NS),
    .tHS_SETTLE_NS        (tHS_SETTLE_NS),
    .tHS_PREPARE_ZERO_NS  (tHS_PREPARE_ZERO_NS),
    .HS_BYTECLK_MHZ       (HS_BYTECLK_MHZ),
    .CLOCK_FREQ_MHZ       (CLOCK_FREQ_MHZ),
    .NUM_DATA_LANE        (NUM_DATA_LANE),
    .ENABLE_USER_DESKEWCAL(ENABLE_USER_DESKEWCAL),
    .DPHY_CLOCK_MODE      (DPHY_CLOCK_MODE)
) dphy_rx_inst (
    .reset_n              (reset_n),
    .clk                  (clk),				
    .reset_byte_HS_n      (reset_byte_HS_n),
    .clk_byte_HS          (clk_byte_HS),
    //To LVDS clock lane   
    .Rx_LP_CLK_P          (Rx_LP_CLK_P), 
	.Rx_LP_CLK_N          (Rx_LP_CLK_N),
    .Rx_HS_enable_C       (Rx_HS_enable_C), 
	.LVDS_termen_C        (LVDS_termen_C), 
	
	//ULPS clock
	.RxUlpsClkNot         (RxUlpsClkNot),
	.RxUlpsActiveClkNot   (RxUlpsActiveClkNot),
	
	//To LVDS data lane 0
	.Rx_LP_D_P            (Rx_LP_D_P     ),
	.Rx_LP_D_N            (Rx_LP_D_N     ),
	.Rx_HS_D_0            (Rx_HS_D_0     ),
	.Rx_HS_D_1            (Rx_HS_D_1     ),
	.Rx_HS_D_2            (Rx_HS_D_2     ),
	.Rx_HS_D_3            (Rx_HS_D_3     ),
	.Rx_HS_D_4            (Rx_HS_D_4     ),
	.Rx_HS_D_5            (Rx_HS_D_5     ),
	.Rx_HS_D_6            (Rx_HS_D_6     ),
	.Rx_HS_D_7            (Rx_HS_D_7     ),
	.Rx_HS_enable_D       (Rx_HS_enable_D),
	.LVDS_termen_D        (LVDS_termen_D ),
	.fifo_rd_enable       (fifo_rd_enable),
	.fifo_rd_empty        (fifo_rd_empty ),
	.DLY_enable_D         (DLY_enable_D  ),
	.DLY_inc_D            (DLY_inc_D     ),
	.u_dly_enable_D       (u_dly_enable_D),
	.u_dly_inc_D          (u_dly_inc_D),	                   
	//To CSI2 lane 0      
    .RxUlpsEsc            (RxUlpsEsc),
    .RxUlpsActiveNot      (RxUlpsActiveNot),
    .RxErrEsc             (RxErrEsc),
    .RxErrControl         (RxErrControl),
    .RxErrSotSyncHS       (RxErrSotSyncHS),
    .RxDataHS_0           (RxDataHS_0), 
    .RxDataHS_1           (RxDataHS_1),
    .RxDataHS_2           (RxDataHS_2), 
    .RxDataHS_3           (RxDataHS_3),
    .RxDataHS_4           (RxDataHS_4), 
    .RxDataHS_5           (RxDataHS_5),
    .RxDataHS_6           (RxDataHS_6), 
    .RxDataHS_7           (RxDataHS_7),
    .RxValidHS            (RxValidHS), 
    .RxActiveHS           (),
    .RxSyncHS             (RxSyncHS),
    .RxSkewCalHS          (RxSkewCalHS),
    .RxStopState          (RxStopState),
    //LPDT mode only supported in DSI
    .RxLPDTEsc            (),
    .RxValidEsc           (),
    .RxDataEsc_0          (),
    .RxDataEsc_1          (),
    .RxDataEsc_2          (),
    .RxDataEsc_3          (),
    .RxDataEsc_4          (),
    .RxDataEsc_5          (),
    .RxDataEsc_6          (),
    .RxDataEsc_7          ()
);

`IP_MODULE_NAME(efx_csi2_rx_top) #(
    .HS_DATA_WIDTH         (8),
    .tINIT_NS              (tINIT_NS),
    .CLOCK_FREQ_MHZ        (CLOCK_FREQ_MHZ),
    .NUM_DATA_LANE         (NUM_DATA_LANE),
    .PACK_TYPE             (PACK_TYPE),
    .AREGISTER             (AREGISTER),
    .ENABLE_VCX            (ENABLE_VCX),
    .FRAME_MODE            (FRAME_MODE),
    .ASYNC_STAGE            (ASYNC_STAGE),
    .PIXEL_FIFO_DEPTH      (PIXEL_FIFO_DEPTH)
) csi2_rx_top_inst (
    .reset_n              (reset_n),
    .clk                  (clk),				
    .reset_byte_HS_n      (reset_byte_HS_n),
    .clk_byte_HS          (clk_byte_HS),
    .reset_pixel_n        (reset_pixel_n),
    .clk_pixel            (clk_pixel),
    .axi_clk              (axi_clk),
    .axi_reset_n          (axi_reset_n),
    .axi_awaddr           (axi_awaddr),
    .axi_awvalid          (axi_awvalid),
    .axi_awready          (axi_awready),
    .axi_wdata            (axi_wdata),
    .axi_wvalid           (axi_wvalid),
    .axi_wready           (axi_wready),                        
    .axi_bvalid           (axi_bvalid),
    .axi_bready           (axi_bready),
    .axi_araddr           (axi_araddr),
    .axi_arvalid          (axi_arvalid),
    .axi_arready          (axi_arready),
    .axi_rdata            (axi_rdata),
    .axi_rvalid           (axi_rvalid),
    .axi_rready           (axi_rready),
    
	.RxUlpsClkNot         (RxUlpsClkNot),
	.RxUlpsActiveClkNot   (RxUlpsActiveClkNot),
    .RxErrEsc             (RxErrEsc),
    .RxClkEsc             ({NUM_DATA_LANE{1'b0}}),
    .RxErrControl         (RxErrControl),
    .RxErrSotSyncHS       (RxErrSotSyncHS),
    .RxUlpsEsc            (RxUlpsEsc),
    .RxUlpsActiveNot      (RxUlpsActiveNot),
    .RxSkewCalHS          (RxSkewCalHS),
    .RxStopState          (RxStopState),
    .RxSyncHS             (RxSyncHS),
    .RxDataHS0            (RxDataHS_0),
    .RxDataHS1            (RxDataHS_1),  
    .RxDataHS2            (RxDataHS_2),
    .RxDataHS3            (RxDataHS_3),
    .RxDataHS4            (RxDataHS_4),
    .RxDataHS5            (RxDataHS_5),
    .RxDataHS6            (RxDataHS_6),
    .RxDataHS7            (RxDataHS_7),
    .RxValidHS0           (RxValidHS_0),
    .RxValidHS1           (RxValidHS_1),
    .RxValidHS2           (RxValidHS_2),
    .RxValidHS3           (RxValidHS_3),
    .RxValidHS4           (RxValidHS_4),
    .RxValidHS5           (RxValidHS_5),
    .RxValidHS6           (RxValidHS_6),
    .RxValidHS7           (RxValidHS_7),
    
    .hsync_vc0            (hsync_vc0),
    .hsync_vc1            (hsync_vc1),
    .hsync_vc2            (hsync_vc2),
    .hsync_vc3            (hsync_vc3),
    .vsync_vc0            (vsync_vc0),
    .vsync_vc1            (vsync_vc1),
    .vsync_vc2            (vsync_vc2),
    .vsync_vc3            (vsync_vc3), 
                          
    .hsync_vc4            (hsync_vc4),
    .hsync_vc5            (hsync_vc5),
    .hsync_vc6            (hsync_vc6),
    .hsync_vc7            (hsync_vc7),
    .hsync_vc8            (hsync_vc8),
    .hsync_vc9            (hsync_vc9),
    .hsync_vc10           (hsync_vc10),
    .hsync_vc11           (hsync_vc11),
    .hsync_vc12           (hsync_vc12),
    .hsync_vc13           (hsync_vc13),
    .hsync_vc14           (hsync_vc14),
    .hsync_vc15           (hsync_vc15),
    .vsync_vc4            (vsync_vc4),
    .vsync_vc5            (vsync_vc5),
    .vsync_vc6            (vsync_vc6),
    .vsync_vc7            (vsync_vc7),
    .vsync_vc8            (vsync_vc8),
    .vsync_vc9            (vsync_vc9),
    .vsync_vc10           (vsync_vc10),
    .vsync_vc11           (vsync_vc11),
    .vsync_vc12           (vsync_vc12),
    .vsync_vc13           (vsync_vc13),
    .vsync_vc14           (vsync_vc14),
    .vsync_vc15           (vsync_vc15),
    .vc                   (vc),
    .vcx                  (vcx),
    .word_count           (word_count),
    .shortpkt_data_field  (shortpkt_data_field),
    .datatype             (datatype),  
    .pixel_per_clk        (pixel_per_clk),
    .pixel_data           (pixel_data), 
    .pixel_data_valid     (pixel_data_valid),
`ifdef MIPI_CSI2_RX_DEBUG
    .mipi_debug_in        (mipi_debug_in    ),
    .mipi_debug_out       (mipi_debug_out   ),
`endif 
`ifdef MIPI_CSI2_RX_PIXEL_SIDEBAND
    .pixel_line_num       (pixel_line_num   ),
    .pixel_frame_num      (pixel_frame_num  ),
    .pixel_datatype       (pixel_datatype   ),
    .pixel_wordcount      (pixel_wordcount  ),
    .pixel_vc             (pixel_vc         ),
    .pixel_vcx            (pixel_vcx        ),
`endif 
    .irq                  (irq)
);


endmodule


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
AowdX9l0THyA81zNjivKQCvTwWXEvq+/17Q75lVq56oUvJgXrLSgfBvjIXHGvHtr
rLsZwgKrtPKiubkPbqVHP2Va7e1wmcWY5Qj/kaBjtFSx2MXFiILg8KsIR6GMxAcH
PWxUqEIHvLKvQdoUYMcTFFSwjaufKCuw1t+BVmUmVkyhXqZI56q4ETCmH5fcGArI
eiaqMa08jLdTbOgm0beJG3t5LOLrjf8wtFjdKapxG2XUUSG8DSC2wHbnZg2QUqie
mHpNoJuKRCgLV2cMvE9KSsVLpvkvL5C0zp4PdEELWjJMncavD3WUNH+PHyDY10oY
+UnJdeYNG8mPaxphaWYIGg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 3568 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
lAcgCs97drRhvY+hsni3MnNWG9jmVm47lHABxx4RdLY/Mvy3yXrw2pwiw/kp7o4N
DUtf9/wOV58imRoR2ivbfOSx7+FnWvdlJZlb5f3t3JB/RbYxwHItxCPiBBt/jq7m
WEKKBkbPeGLWfpt1piPQVeRXLMW6mhuwERJ83UyKkQWZkT1ypmNDQi8LECWnKzWm
kL09wRpfMWxxD0ezY9AnXGBMwIRPpRwq0p0yZjnfUuv+8kdeb4T+YAugzhzAAUwX
MjsmSAv733I1rgp/Dwne//+8Aw8uxwbglxpMVRqRK+Y+7Uvy43n1mY5c8IpEF7eR
7JimWsKtRbGLk5w5yB7TfA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 10032 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
BtZ0yiSUFHhu54JBH/mVFdXX4nrkd3qJO2CFAKQGI6bBChyCHsjDZrXH7WzE1rFH
vqDd56Oq3IyAWuiZQkoED+45RCS6MIy2GLPTgWX+DWaO3acqKkXMkccYqMaUwrY8
pETubv561TkDRKEkeBWwFh/gIETaHMiglLMF5P3zJM1RFX8hpRJZtLEzAm8NlR3L
fA7t4LkkYbD/yX2zWrCa7743HF3jCc9SXl6usdB3hdF1VL0gDfeLiq3QTRaVZzaI
Tl2j+EKyCcJgU2c4cJRndHq4oE5cGffP1ttq+rKX/oLh0JgUhvA7GaL3xA7Di6BV
pE7nGAjyvXUqxW0wY1nErQ==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 8800 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
D1KexZGsP308OxTskX5ZjHTXb98deWZ+pWxQJdLP7F20/v2iKPQD19Whxfb7IOb6
cFdkOyk9CR4T/*********************************+NBGhq2bGgbWu+p/ql
NMpAq5RTMC3PrM4Q27NLuUBCU60BnUzZiG1OVQuKtS8Ct5bCi2R5YRqxdwJ8r6mz
bi74dfnGnHENspP3MGV/Sz+6UwxBqIfBT6RZqxX2SXJrQDUIOymPE37g2CsMXlOF
fyZONTkGmoByhcyuXA+/T1052XRviwEo3BIybQ5fSMJ+pB19zPngGbc/xcf2smQf
fZTnIBPT/6fmgnYsg5R44A==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 14016 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


`timescale 1 ns / 1 ps
//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
eaaIP7AFYFSqfM/vFQOfuU+fOxFf3LncAI0jOuT2s5I5WEV9t0EKP3/gvhkEW4QP
gESdSUnnyUpHlzqYLTY3Ls60p9wDf4wDpjv70UNABq86oO6Xdg/tjjoakbrukYCm
5eZgVpyA9gjPqImVu+D7KmytXyuC/FHmVYWQfRSzb2HYuO6tpFVJHpDV8e7RTPeV
RgW2vasoNQHPRUYiYG7azDfNkRlltcapImg/VCqTSJyo/qZScFMP6uD7T/fyg27s
hWrLxCI6dhtles9DOwaVGChgk5nvXC56vL89T6llKIswTu111Gra5IwD2Fx8mSsf
2HwUpSQPfsh1ugrC3hsqXA==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 832 )
`pragma protect data_block
Kk8i9aU5eyjkGgP6bWWD9yHpmnUCJvMW922u4y/K9UkMaMB5TrkaBv8I9GG8r/RC
sQslWvZ9Mwx1isIW47ksTtJxTt8E+53YUBcZD1+lyqrqZjbL5H8DYceD4DyhxNmC
FmMxUpcVV34Dcy/x27+f0uZVoXK9qwy0DdXX/4wqLV1XSA0oQt1uLX+3qy9HhYR6
ZOhEv9Xjmj+/jW+18H7rKx4F+c0NnyIfVbMqRW1khJ7VsPmRIwjifKmfS/IAOUMJ
QmmBrwWAcz0S32t8Js0Zk8oqgXA7Fs5QtkV+suXhbq0/K60b07L5L1B5jpPzKaQ0
oJ8N1kCIk4X2e+OV43oEaZkdRHhJ9kZnq3Ji9RJoSQQrVJjBBJ3k+V3PMYWQntpd
0+juZZR/M9KGIgjhwxLEzay7FcSXHIV4qfJknFWoj/rFV27Ze5K/P31G25nt4WfV
o9P49wBPP978VuTPbaYxYLn3S4yfNNCGQmH1Z1u1fcLOOiujlcIiUlckZlUeTon/
sL6Q7Ekq2CfJnhNbnOaVasKuv5p17RviqYhWipEB+935AXhniLJVEsH7XXFabZ1P
HK5mP4oXFSF4uTmHZoXHz0cpxJidhjhb7n9K91MbkbW+84vOE+2yeJoWpodZkVym
WzUmJ7B0KMgViyQ6R3HHpa3o4m1sVrhCnfV0z4OrHX46Uk9tZcM/vAqvOze6LUst
Edi/0V10dLpCLuyg90nwsO8ZaYW5azJS8rtCPwwAiThC5oG6KSqVvyS6uOACcspd
a7n5gG498rZ7V82LqhH63ts3Sr0qLwPsu6Wi4klEt98dP7ymu5HGy8H//RMPOsBX
g4RQx9NidB5tL5hDyADJeuIA2SGayH/JVuRP8Ck+W/BIlT9sloDcqIaRdGPTpHA7
9LcsVnPCNj9LKp4K/lRhWuXdH9lTW+bl7dwYFGKtAKb3IdsMHYlUQJ6x+0xoc1JB
zFuaChJWD5QRaOzRU8sZxTvHjJ2O/5g5epayWSMUnVir/ol3N/zjpUeVg7S7CZMD
z/3DPn7NoVUFGNrDVRYzzgx46MjmHPmSE3Hf8wVmQn7RlbWoLC1dy9cWeajmTpAN
FbfSgHN0Eh1hUIyQQi4VYA==
`pragma protect end_protected

//pragma protect end


//pragma protect
//pragma protect begin

/* Encryption Envelope */

`pragma protect begin_protected
`pragma protect version = 1
`pragma protect encrypt_agent = "QuestaSim" , encrypt_agent_info = "2021.1"
`pragma protect key_keyowner = "Aldec" , key_keyname = "ALDEC15_001"
`pragma protect key_method = "rsa"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 256 )
`pragma protect key_block
yCqwjeXHo2Syg6VO+R8bTb23hWWlSuLCoxGXQ7buistEjBJTwSk2+rHIDkNPx6UK
pTmntPYWf+7QCplyo+HhQUsGtKmI6HuiMsfQvsgw7zwWXKe3d7shJ3hCzeRvSf1J
AfTJTZsk9ZC/wvzNGLj6WDc7P4wMLkiriV8G0NIfNQ7WVMp2XwzMomr3JLF7g+pI
q2q2GxD6U+/ygRgLGBd1JFIBKsxBvDiUM7pcrtMhhwpysd58Y4yVYSW8BdgtbtOi
GUTaljAz9hq5/eu+5wcM+/i4qBVxzeSE3MeNTT+7G88KwSYkM6XrDUkNJ3a6ATeP
ycuIDc+DZGuBqR5lXq8nLg==
`pragma protect data_method = "aes256-cbc"
`pragma protect encoding = ( enctype = "base64" , line_length = 64 , bytes = 6336 )
`pragma protect data_block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`pragma protect end_protected

//pragma protect end


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_top) # (
    parameter FAMILY             = "TRION",       // New Param
    parameter SYNC_CLK           = 0,
    parameter BYPASS_RESET_SYNC  = 0,             // New Param
    parameter SYNC_STAGE         = 2,             // New Param
    parameter MODE               = "STANDARD",
    parameter DEPTH              = 512,           // Reverted (Equivalent to WDATA_DEPTH) 
    parameter DATA_WIDTH         = 32,            // Reverted (Equivalent to WDATA_WIDTH)
    parameter PIPELINE_REG       = 1,             // Reverted (By default is ON)
    parameter OPTIONAL_FLAGS     = 1,             // Reverted
    parameter OUTPUT_REG         = 0,
    parameter PROGRAMMABLE_FULL  = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_FULL_ASSERT   = 27,
    parameter PROG_FULL_NEGATE   = 23,
    parameter PROGRAMMABLE_EMPTY = "STATIC_DUAL", // Set to "NONE" if not require this feature
    parameter PROG_EMPTY_ASSERT  = 5,
    parameter PROG_EMPTY_NEGATE  = 7,
    parameter ALMOST_FLAG        = OPTIONAL_FLAGS,
    parameter HANDSHAKE_FLAG     = OPTIONAL_FLAGS,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter WADDR_WIDTH        = depth2width(DEPTH),
    parameter RDATA_WIDTH        = rdwidthcompute(ASYM_WIDTH_RATIO,DATA_WIDTH),
    parameter RD_DEPTH           = rddepthcompute(DEPTH,DATA_WIDTH,RDATA_WIDTH),
    parameter RADDR_WIDTH        = depth2width(RD_DEPTH),
    parameter ENDIANESS		      = 0,
    parameter OVERFLOW_PROTECT   = 1,
    parameter UNDERFLOW_PROTECT  = 1,
    parameter RAM_STYLE          = "block_ram"

)(
    input  wire                   a_rst_i,
    input  wire                   a_wr_rst_i,
    input  wire                   a_rd_rst_i,
    input  wire                   clk_i,
    input  wire                   wr_clk_i,
    input  wire                   rd_clk_i,
    input  wire                   wr_en_i,
    input  wire                   rd_en_i,
    input  wire [DATA_WIDTH-1:0]  wdata,
    output wire                   almost_full_o,
    output wire                   prog_full_o,
    output wire                   full_o,
    output wire                   overflow_o,
    output wire                   wr_ack_o,
    output wire [WADDR_WIDTH :0]  datacount_o,
    output wire [WADDR_WIDTH :0]  wr_datacount_o,
    output wire                   empty_o,
    output wire                   almost_empty_o,
    output wire                   prog_empty_o,
    output wire                   underflow_o,
    output wire                   rd_valid_o,
    output wire [RDATA_WIDTH-1:0] rdata,
    output wire [RADDR_WIDTH :0]  rd_datacount_o,
    output wire                   rst_busy
); 

localparam WR_DEPTH      = DEPTH;
localparam WDATA_WIDTH   = DATA_WIDTH;
localparam RAM_MUX_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? 32 :
                           (RDATA_WIDTH <= WDATA_WIDTH/16) ? 16 :
                           (RDATA_WIDTH <= WDATA_WIDTH/8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH/4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH/2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH)    ? 1  :
                           (RDATA_WIDTH <= WDATA_WIDTH*2)  ? 2  :
                           (RDATA_WIDTH <= WDATA_WIDTH*4)  ? 4  :
                           (RDATA_WIDTH <= WDATA_WIDTH*8)  ? 8  :
                           (RDATA_WIDTH <= WDATA_WIDTH*16) ? 16 : 32;           

wire                   wr_rst_int;
wire                   rd_rst_int;
wire                   wr_en_int;
wire                   rd_en_int;
wire [WADDR_WIDTH-1:0] waddr;
wire [RADDR_WIDTH-1:0] raddr;
wire                   wr_clk_int;
wire                   rd_clk_int;
wire [WADDR_WIDTH :0]  wr_datacount_int;
wire [RADDR_WIDTH :0]  rd_datacount_int;

generate
    if (ASYM_WIDTH_RATIO == 4) begin
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
            assign datacount_o    = wr_datacount_int;
            assign wr_datacount_o = {(WADDR_WIDTH+1){1'b0}};
            assign rd_datacount_o = {(RADDR_WIDTH+1){1'b0}};
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
            assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
            assign wr_datacount_o = wr_datacount_int;
            assign rd_datacount_o = rd_datacount_int;
        end
    end
    else begin
        assign datacount_o    = {(WADDR_WIDTH+1){1'b0}};
        assign wr_datacount_o = wr_datacount_int;
        assign rd_datacount_o = rd_datacount_int;
        if (SYNC_CLK) begin
            assign wr_clk_int     = clk_i;    
            assign rd_clk_int     = clk_i;
        end
        else begin
            assign wr_clk_int     = wr_clk_i;    
            assign rd_clk_int     = rd_clk_i;
        end
    end    

    if (!SYNC_CLK) begin
        //(* async_reg = "true" *) reg [1:0] wr_rst;
        //(* async_reg = "true" *) reg [1:0] rd_rst;
        //
        //always @ (posedge wr_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        wr_rst <= 2'b11;
        //    else 
        //        wr_rst <= {wr_rst[0],1'b0};            
        //end
        //
        //always @ (posedge rd_clk_int or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        rd_rst <= 2'b11;
        //    else 
        //        rd_rst <= {rd_rst[0],1'b0};            
        //end
        

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_wr_rst_i;
            assign rd_rst_int = a_rd_rst_i; 
            assign rst_busy   = 1'b0;
        end 
        else begin
            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_wr_rst (
               .clk   (wr_clk_int),
               .reset (a_rst_i),
               .d_o   (wr_rst_int)
            );

            `IP_MODULE_NAME(efx_resetsync) #(
               .ACTIVE_LOW (0)
            ) efx_resetsync_rd_rst (
               .clk   (rd_clk_int),
               .reset (a_rst_i),
               .d_o   (rd_rst_int)
            );
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end

    end
    else begin
        //(* async_reg = "true" *) reg [1:0] a_rst;
        //
        //always @ (posedge clk_i or posedge a_rst_i) begin
        //    if (a_rst_i) 
        //        a_rst <= 2'b11;
        //    else 
        //        a_rst <= {a_rst[0],1'b0};            
        //end
        wire   a_rst;

        `IP_MODULE_NAME(efx_resetsync) #(
           .ACTIVE_LOW (0)
        ) efx_resetsync_a_rst (
           .clk   (clk_i),
           .reset (a_rst_i),
           .d_o   (a_rst)
        );

        if (BYPASS_RESET_SYNC) begin
            assign wr_rst_int = a_rst_i;
            assign rd_rst_int = a_rst_i;
            assign rst_busy   = 1'b0;
        end
        else begin
            assign wr_rst_int = a_rst;
            assign rd_rst_int = a_rst;   
            assign rst_busy   = wr_rst_int | rd_rst_int;
        end
    end 
endgenerate

`IP_MODULE_NAME(efx_fifo_ram) # (
    .FAMILY        (FAMILY),
    .WR_DEPTH      (WR_DEPTH),
    .RD_DEPTH      (RD_DEPTH),
    .WDATA_WIDTH   (WDATA_WIDTH),
    .RDATA_WIDTH   (RDATA_WIDTH),
    .WADDR_WIDTH   (WADDR_WIDTH),
    .RADDR_WIDTH   (RADDR_WIDTH),
    .OUTPUT_REG    (OUTPUT_REG),
    .RAM_MUX_RATIO (RAM_MUX_RATIO),
    .ENDIANESS	    (ENDIANESS),
    .RAM_STYLE     (RAM_STYLE)
) xefx_fifo_ram (
    .wdata (wdata), 
    .waddr (waddr),
    .raddr (raddr), 
    .we    (wr_en_int), 
    .re    (rd_en_int),
    .wclk  (wr_clk_int),
    .rclk  (rd_clk_int),
    .rdata (rdata)
);

`IP_MODULE_NAME(efx_fifo_ctl) # (
    .SYNC_CLK           (SYNC_CLK),
    .SYNC_STAGE         (SYNC_STAGE),
    .MODE               (MODE),
    .WR_DEPTH           (WR_DEPTH),
    .WADDR_WIDTH        (WADDR_WIDTH),
    .RADDR_WIDTH        (RADDR_WIDTH),
    .ASYM_WIDTH_RATIO   (ASYM_WIDTH_RATIO),
    .RAM_MUX_RATIO      (RAM_MUX_RATIO),
    .PIPELINE_REG       (PIPELINE_REG),
    .ALMOST_FLAG        (ALMOST_FLAG),
    .PROGRAMMABLE_FULL  (PROGRAMMABLE_FULL),
    .PROG_FULL_ASSERT   (PROG_FULL_ASSERT),
    .PROG_FULL_NEGATE   (PROG_FULL_NEGATE),
    .PROGRAMMABLE_EMPTY (PROGRAMMABLE_EMPTY),
    .PROG_EMPTY_ASSERT  (PROG_EMPTY_ASSERT),
    .PROG_EMPTY_NEGATE  (PROG_EMPTY_NEGATE),
    .OUTPUT_REG         (OUTPUT_REG),
    .HANDSHAKE_FLAG     (HANDSHAKE_FLAG),
    .OVERFLOW_PROTECT   (OVERFLOW_PROTECT),
    .UNDERFLOW_PROTECT  (UNDERFLOW_PROTECT)
) xefx_fifo_ctl (
    .wr_rst          (wr_rst_int),
    .rd_rst          (rd_rst_int),
    .wclk            (wr_clk_int),
    .rclk            (rd_clk_int),
    .we              (wr_en_i),
    .re              (rd_en_i),
    .wr_full         (full_o),
    .wr_ack          (wr_ack_o),
    .rd_empty        (empty_o),
    .wr_almost_full  (almost_full_o),
    .rd_almost_empty (almost_empty_o),
    .wr_prog_full    (prog_full_o),
    .rd_prog_empty   (prog_empty_o),
    .wr_en_int       (wr_en_int),
    .rd_en_int       (rd_en_int),
    .waddr           (waddr),
    .raddr           (raddr),
    .wr_datacount    (wr_datacount_int),
    .rd_datacount    (rd_datacount_int),
    .rd_vld          (rd_valid_o),
    .wr_overflow     (overflow_o),
    .rd_underflow    (underflow_o)
);

function integer depth2width;
input [31:0] depth;
begin : fnDepth2Width
    if (depth > 1) begin
        depth = depth - 1;
        for (depth2width=0; depth>0; depth2width = depth2width + 1)
            depth = depth>>1;
        end
    else
       depth2width = 0;
end
endfunction 

function integer width2depth;
input [31:0] width;
begin : fnWidth2Depth
    width2depth = width**2;
end
endfunction

function integer rdwidthcompute;
input [31:0] asym_option;
input [31:0] wr_width;
begin : RdWidthCompute
    rdwidthcompute = (asym_option==0)? wr_width/16 :
                     (asym_option==1)? wr_width/8  :
                     (asym_option==2)? wr_width/4  :
                     (asym_option==3)? wr_width/2  :
                     (asym_option==4)? wr_width/1  :
                     (asym_option==5)? wr_width*2  :
                     (asym_option==6)? wr_width*4  :
                     (asym_option==7)? wr_width*8  :
                     (asym_option==8)? wr_width*16 : wr_width/1;
end
endfunction

function integer rddepthcompute;
input [31:0] wr_depth;
input [31:0] wr_width;
input [31:0] rd_width;
begin : RdDepthCompute
    rddepthcompute = (wr_depth * wr_width) / rd_width;
end
endfunction

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ram) #(
    parameter FAMILY        = "TRION",
    parameter WR_DEPTH      = 512,
    parameter RD_DEPTH      = 512,
    parameter WDATA_WIDTH   = 8,
    parameter RDATA_WIDTH   = 8,
    parameter WADDR_WIDTH   = 9,
    parameter RADDR_WIDTH   = 9,
    parameter OUTPUT_REG    = 1,
    parameter RAM_MUX_RATIO = 4,
    parameter ENDIANESS 	 = 0, //0: Big endian (default)   1: Little endian 
    parameter RAM_STYLE     = "block_ram"
) (
    input  wire                     wclk,
    input  wire                     rclk,
    input  wire                     we,
    input  wire                     re,
    input  wire [(WDATA_WIDTH-1):0] wdata,
    input  wire [(WADDR_WIDTH-1):0] waddr,
    input  wire [(RADDR_WIDTH-1):0] raddr,
    output wire [(RDATA_WIDTH-1):0] rdata
);

localparam MEM_DEPTH         = (WR_DEPTH > RD_DEPTH) ? WR_DEPTH : RD_DEPTH;
localparam MEM_DATA_WIDTH    = (WDATA_WIDTH > RDATA_WIDTH) ? RDATA_WIDTH : WDATA_WIDTH;   
localparam LSB_WIDTH         = (WADDR_WIDTH > RADDR_WIDTH) ? (WADDR_WIDTH - RADDR_WIDTH) : (RADDR_WIDTH - WADDR_WIDTH);
localparam RDATA_WDATA_RATIO = (RDATA_WIDTH <= WDATA_WIDTH/32) ? "ONE_THIRTYTWO" :
                               (RDATA_WIDTH <= WDATA_WIDTH/16) ? "ONE_SIXTEENTH" :
                               (RDATA_WIDTH <= WDATA_WIDTH/8)  ? "ONE_EIGHTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/4)  ? "ONE_FOURTH"    :
                               (RDATA_WIDTH <= WDATA_WIDTH/2)  ? "ONE_HALF"      :
                               (RDATA_WIDTH <= WDATA_WIDTH)    ? "ONE"           :
                               (RDATA_WIDTH <= WDATA_WIDTH*2)  ? "TWO_TIMES"     :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "FOUR_TIMES"    :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "EIGHT_TIMES"   :
                               (RDATA_WIDTH <= WDATA_WIDTH*4)  ? "SIXTEEN_TIMES" : "THIRTYTWO_TIMES";

(* syn_ramstyle = RAM_STYLE *) reg [MEM_DATA_WIDTH-1:0] ram[MEM_DEPTH-1:0];
reg [RDATA_WIDTH-1:0]    r_rdata_1P;
reg [RDATA_WIDTH-1:0]    r_rdata_2P;

wire                     re_int;

generate
    if (FAMILY == "TRION") begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                end
                r_rdata_2P <= r_rdata_1P;
            end
        end
        
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            if (ENDIANESS == 0) begin 
            	integer i;
            	always @ (posedge wclk) begin
                	for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                    end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                end
                always @ (posedge rclk) begin 
                	if (re_int) begin
                		r_rdata_1P <= ram[raddr];
                	end
                		r_rdata_2P <= r_rdata_1P;
                end
            end
        end
        
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  ||RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
            //integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end 
            end
            else begin //endianess == 1
            	always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
        		always @ (posedge rclk) begin
        			for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    	end
                        	r_rdata_2P <= r_rdata_1P;
                    end
                end
            end
        end
        if (OUTPUT_REG) begin
            assign re_int = re;
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end
    end
    else begin
        if (RDATA_WDATA_RATIO == "ONE") begin
            always @ (posedge wclk) begin
                if (we)
                    ram[waddr] <= wdata;
            end
    
            always @ (posedge rclk) begin
                if (re_int) begin
                    r_rdata_1P <= ram[raddr];
                    r_rdata_2P <= r_rdata_1P;
                end
            end
        end 
        else if (RDATA_WDATA_RATIO == "ONE_THIRTYTWO" || RDATA_WDATA_RATIO == "ONE_SIXTEENTH" || RDATA_WDATA_RATIO == "ONE_EIGHTH" || RDATA_WDATA_RATIO == "ONE_FOURTH"  || RDATA_WDATA_RATIO == "ONE_HALF" ) begin
            //integer i;
            if (ENDIANESS == 0) begin
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
            
            else begin //endianess == 1
            	integer i;
            	always @ (posedge wclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (we) begin
                            	ram[{waddr,LSB_WIDTH'(i)}] <= wdata[((WDATA_WIDTH/RAM_MUX_RATIO)*i) +: WDATA_WIDTH/RAM_MUX_RATIO];
                        end
                    end
                 end
               	always @ (posedge rclk) begin 
               		if (re_int) begin
               			r_rdata_1P <= ram[raddr];
               			r_rdata_2P <= r_rdata_1P;
               		end
               	end
            end
        end
        else if (RDATA_WDATA_RATIO == "TWO_TIMES" || RDATA_WDATA_RATIO == "FOUR_TIMES"  || RDATA_WDATA_RATIO == "EIGHT_TIMES"  || RDATA_WDATA_RATIO == "SIXTEEN_TIMES"  || RDATA_WDATA_RATIO == "THIRTYTWO_TIMES" ) begin
        	//integer i;
        	if (ENDIANESS == 0) begin
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(RAM_MUX_RATIO-1-i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
            
            else begin //endianess ==1
        		always @ (posedge wclk) begin
        			if (we)
        				ram[waddr] <= wdata;
        		end
        		integer i;
            	always @ (posedge rclk) begin
            		for (i=0; i<RAM_MUX_RATIO; i=i+1) begin
                    	if (re_int) begin
                    		r_rdata_1P[((RDATA_WIDTH/RAM_MUX_RATIO)*i) +: RDATA_WIDTH/RAM_MUX_RATIO] <= ram[{raddr,LSB_WIDTH'(i)}];
                    		r_rdata_2P                                                               <= r_rdata_1P;
                    	end
                    end
                end 
            end
        end
        // NIC
        //if (MODE == "STANDARD") begin     
        //    if (OUTPUT_REG) begin
        //        reg re_r;
        //        always @ (posedge rclk) begin
        //            re_r <= re;
        //        end
        //        assign re_int = re | re_r;        
        //        assign rdata  = r_rdata_2P;            
        //    end
        //    else begin
        //        assign re_int = re;
        //        assign rdata  = r_rdata_1P;
        //    end
        //end
        //else begin
        //    assign re_int = re;
        //    assign rdata  = r_rdata_1P;
        //end    
        if (OUTPUT_REG) begin
            reg re_r;
            always @ (posedge rclk) begin
                re_r <= re;
            end
            assign re_int = re | re_r;        
            assign rdata  = r_rdata_2P;            
        end
        else begin
            assign re_int = re;
            assign rdata  = r_rdata_1P;
        end    
    end
endgenerate

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2021 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   gray2bin.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Gray to Binary Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_gray2bin)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] bin_o,
 // input
 input [WIDTH-1:0] gray_i);

//---------------------------------------------------------------------
// Recursive Module
// Description: reduction xor
generate
   if (WIDTH > 1) begin
      wire [1:0] bin_1;
      assign bin_1 = {gray_i[WIDTH-1], gray_i[WIDTH-1]^gray_i[WIDTH-2]};
      if (WIDTH == 2) begin
	 assign bin_o = bin_1;
      end
      else begin
	 assign bin_o[WIDTH-1] = bin_1[1];
	 `IP_MODULE_NAME(efx_fifo_gray2bin) #(.WIDTH(WIDTH-1)) u_gray2bin (.bin_o(bin_o[WIDTH-2:0]), .gray_i({bin_1[0], gray_i[WIDTH-3:0]}));
      end
   end
   else /* if (WIDTH == 1) */
     assign bin_o = gray_i;
endgenerate
   
endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   pipe_reg.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Parallel Pipelining Shift Register
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_datasync) #(
    parameter STAGE = 32,
    parameter WIDTH = 4
) (
 input  wire             clk_i,
 input  wire [WIDTH-1:0] d_i,
 output wire [WIDTH-1:0] d_o
);

(* async_reg = "true" *) reg [WIDTH-1:0] pipe_reg [STAGE-1:0];
integer i;

always @(posedge clk_i) begin
  for (i=STAGE-1; i>0; i = i - 1) begin
    pipe_reg[i] <= pipe_reg[i-1];
  end
  pipe_reg[0] <= d_i;
end
assign d_o = pipe_reg[STAGE-1];
   

endmodule

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////



// synopsys translate_off
`timescale 1 ns / 1 ps													
// synopsys translate_on

module `IP_MODULE_NAME(efx_resetsync) #(
    parameter ASYNC_STAGE = 2,
    parameter ACTIVE_LOW = 1
) (
    input  wire             clk,
    input  wire             reset,
    output wire             d_o
);


generate
   if (ACTIVE_LOW == 1) begin: active_low
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (1),
         .RST_VALUE  (0)
      ) efx_resetsync_active_low (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b1),
         .d_o     (d_o)
      );
   end
   else begin: active_high
      `IP_MODULE_NAME(efx_asyncreg) #(
         .WIDTH      (1),
         .ACTIVE_LOW (0),
         .RST_VALUE  (1)
      ) efx_resetsync_active_high (
         .clk     (clk),
         .reset_n (reset),
         .d_i     (1'b0),
         .d_o     (d_o)
      );
   end
endgenerate

endmodule


/////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2021 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   simple_dual_port_ram_fifo.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      EFX FIFO
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
//
// *******************************

module `IP_MODULE_NAME(efx_fifo_ctl) # (
    parameter SYNC_CLK           = 1,
    parameter SYNC_STAGE         = 2,
    parameter MODE               = "STANDARD",
    parameter WR_DEPTH           = 512,
    parameter WADDR_WIDTH        = 9,
    parameter RADDR_WIDTH        = 9,
    parameter ASYM_WIDTH_RATIO   = 4,
    parameter RAM_MUX_RATIO      = 1,
    parameter PIPELINE_REG       = 1,
    parameter ALMOST_FLAG        = 1,
    parameter PROGRAMMABLE_FULL  = "NONE",
    parameter PROG_FULL_ASSERT   = 0,
    parameter PROG_FULL_NEGATE   = 0,
    parameter PROGRAMMABLE_EMPTY = "NONE",
    parameter PROG_EMPTY_ASSERT  = 0,
    parameter PROG_EMPTY_NEGATE  = 0,
    parameter OUTPUT_REG         = 0,
    parameter HANDSHAKE_FLAG     = 1, 
    parameter OVERFLOW_PROTECT   = 0,
    parameter UNDERFLOW_PROTECT  = 0
)(
    input   wire                   wr_rst,
    input   wire                   rd_rst,
    input   wire                   wclk,
    input   wire                   rclk,
    input   wire                   we,
    input   wire                   re,
    output  wire                   wr_full,
    output  reg                    wr_ack,
    output  wire                   wr_almost_full,
    output  wire                   rd_empty,
    output  wire                   rd_almost_empty,
    output  wire                   wr_prog_full,
    output  wire                   rd_prog_empty,
    output  wire                   wr_en_int,
    output  wire                   rd_en_int,
    output  wire [WADDR_WIDTH-1:0] waddr,
    output  wire [RADDR_WIDTH-1:0] raddr,
    output  wire [WADDR_WIDTH:0]   wr_datacount,
    output  wire [RADDR_WIDTH:0]   rd_datacount,
    output  wire                   rd_vld,
    output  reg                    wr_overflow,
    output  reg                    rd_underflow
);

reg  [WADDR_WIDTH:0] waddr_cntr;
reg  [WADDR_WIDTH:0] waddr_cntr_r;
reg  [RADDR_WIDTH:0] raddr_cntr;
reg                  rd_valid;

wire [WADDR_WIDTH:0] waddr_int;
wire [RADDR_WIDTH:0] raddr_int;
wire                 rd_empty_int;
wire [WADDR_WIDTH:0] wr_datacount_int;
wire [RADDR_WIDTH:0] rd_datacount_int;

assign waddr     = waddr_cntr[WADDR_WIDTH-1:0];
// NIC
wire [RADDR_WIDTH:0] ram_raddr;
assign raddr     = (MODE == "FWFT") ? ram_raddr[RADDR_WIDTH-1:0] : raddr_cntr[RADDR_WIDTH-1:0];
//assign raddr     = raddr_cntr[RADDR_WIDTH-1:0];
//assign wr_en_int = we & ~wr_full;
assign wr_en_int = OVERFLOW_PROTECT ? we & ~wr_full : we;

assign wr_datacount  = wr_datacount_int;
assign rd_datacount  = ASYM_WIDTH_RATIO == 4 && SYNC_CLK ? wr_datacount_int : rd_datacount_int;


generate
    if (MODE == "FWFT") begin    
        // NIC
        //assign rd_en_int     = (~rd_empty_int & rd_empty) | (re & ~rd_empty_int);
        //assign rd_empty      = rd_empty_fwft;

        assign rd_en_int     = 1'b1;
        //assign rd_empty      = rd_empty_int;
        
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        init_set <= 1'b1;
        //    end
        //    else if (~init_set & rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //    else if (~rd_empty_int) begin
        //        init_set <= 1'b0;            
        //    end
        //    else if (rd_empty) begin
        //        init_set <= 1'b1;            
        //    end
        //end
        // NIC 
        //always @ (posedge rclk or posedge rd_rst) begin
        //    if (rd_rst) begin
        //        rd_empty_fwft <= 1'b1;
        //    end
        //    else if (rd_en_int) begin
        //        rd_empty_fwft <= 1'b0;            
        //    end
        //    else if (re) begin
        //        rd_empty_fwft <= 1'b1;            
        //    end
        //end          
        
        //if (FAMILY == "TRION") begin
        if (OUTPUT_REG) begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid <= 1'b0;
                end
                else begin
                    rd_valid <= ~rd_empty;
                end
            end
            assign rd_vld = rd_valid;                
        end    
        else begin
            assign rd_vld = ~rd_empty;                
        end
        
        assign rd_empty = rd_empty_int;
    end
    else begin
        assign rd_en_int     = UNDERFLOW_PROTECT ? re & ~rd_empty_int : re;
        assign rd_empty      = rd_empty_int;
        
        if (OUTPUT_REG) begin
            reg rd_valid_r;
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid_r <= 'h0;
                    rd_valid   <= 'h0;
                end
                else begin
                    {rd_valid,rd_valid_r} <= {rd_valid_r,rd_en_int};
                end
            end
            assign rd_vld = rd_valid;            
        end
        else begin
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    rd_valid  <= 'h0;
                end
                else begin
                    rd_valid <= rd_en_int;
                end
            end
            assign rd_vld = rd_valid;
        end
    end
    
    if (ALMOST_FLAG) begin
        assign wr_almost_full   = wr_datacount >= WR_DEPTH-1;
        assign rd_almost_empty  = rd_datacount <= 'd1;
    end
    else begin
        assign wr_almost_full   = 1'b0;
        assign rd_almost_empty  = 1'b0;        
    end
    
    if (PROGRAMMABLE_FULL == "STATIC_SINGLE") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_datacount >= PROG_FULL_ASSERT;
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end  
    end
    else if (PROGRAMMABLE_FULL == "STATIC_DUAL") begin
        reg    wr_prog_full_int;
        assign wr_prog_full  = wr_prog_full_int ? wr_datacount >= PROG_FULL_NEGATE : wr_datacount >= PROG_FULL_ASSERT;   

        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_prog_full_int  <= 1'b0;
            end
            else begin
                wr_prog_full_int  <= wr_prog_full;
            end
        end              
    end
    else begin
        assign wr_prog_full  = 1'b0; 
    end
        
    if (PROGRAMMABLE_EMPTY == "STATIC_SINGLE") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty  = rd_datacount <= PROG_EMPTY_ASSERT;
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end
    end
    else if (PROGRAMMABLE_EMPTY == "STATIC_DUAL") begin
        reg    rd_prog_empty_int;
        assign rd_prog_empty = rd_prog_empty_int ? (rd_datacount <= PROG_EMPTY_NEGATE) : (rd_datacount <= PROG_EMPTY_ASSERT);
            
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_prog_empty_int <= 1'b1;
            end
            else begin
                rd_prog_empty_int <= rd_prog_empty;
            end
        end  
    end
    else begin
        assign rd_prog_empty  = 1'b0; 
    end
    
    if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_ack <= 1'b0;
            end
            else begin
                // NIC
                //wr_ack <= wr_en_int & ~wr_overflow;
                wr_ack <= OVERFLOW_PROTECT ? wr_en_int & ~wr_overflow : wr_en_int;
            end
        end
    end

    if (OVERFLOW_PROTECT) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else if (we && wr_full) begin
                wr_overflow <= 1'b1;
            end
            else begin
                wr_overflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                wr_overflow <= 1'b0;
            end
            else begin
                wr_overflow <= we && wr_full ? 1'b1 : wr_overflow;
            end
        end
    end

    if (UNDERFLOW_PROTECT) begin     
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else if (re && rd_empty) begin
                rd_underflow <= 1'b1;
            end
            else begin
                rd_underflow <= 1'b0;            
            end
        end
    end
    else if (HANDSHAKE_FLAG) begin
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                rd_underflow <= 1'b0;
            end
            else begin
                rd_underflow <= re && rd_empty ? 1'b1 : rd_underflow;
            end
        end
    end
    
    localparam RATIO_WIDTH = (RADDR_WIDTH >= WADDR_WIDTH)? RADDR_WIDTH - WADDR_WIDTH : WADDR_WIDTH - RADDR_WIDTH;
    
    if (ASYM_WIDTH_RATIO < 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:RATIO_WIDTH]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:RATIO_WIDTH];
        assign wr_datacount_int = waddr_cntr - (raddr_int/RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int*RAM_MUX_RATIO)-raddr_cntr;
    end
    // NIC
    else if (ASYM_WIDTH_RATIO == 4) begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:0] == raddr_int[RADDR_WIDTH-1:0]);
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:0] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - raddr_int;
        assign rd_datacount_int = waddr_int - raddr_cntr;
    end
    else begin
        assign wr_full          = (waddr_cntr[WADDR_WIDTH]^raddr_int[RADDR_WIDTH]) & (waddr_cntr[WADDR_WIDTH-1:RATIO_WIDTH] == raddr_int[RADDR_WIDTH-1:0]);
        // NIC
        //assign rd_empty_int     = (waddr_int- raddr_cntr*RAM_MUX_RATIO) < RAM_MUX_RATIO;
        assign rd_empty_int     = waddr_int[WADDR_WIDTH:RATIO_WIDTH] == raddr_cntr[RADDR_WIDTH:0];
        assign wr_datacount_int = waddr_cntr - (raddr_int*RAM_MUX_RATIO);
        assign rd_datacount_int = (waddr_int/RAM_MUX_RATIO)-raddr_cntr;
    end
endgenerate

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr   <= 'h0;
    end
    else if (wr_en_int) begin
        waddr_cntr   <= waddr_cntr + 1'b1;
    end
end

always @ (posedge wclk or posedge wr_rst) begin
    if (wr_rst) begin
        waddr_cntr_r  <= 'h0;
    end
    else begin
        waddr_cntr_r  <= waddr_cntr;
    end
end

always @ (posedge rclk or posedge rd_rst) begin
    if (rd_rst) begin
        raddr_cntr   <= 'h0;
    end
    // NIC
    //else if (rd_en_int) begin
    else begin
        //raddr_cntr   <= raddr_cntr + 1'b1;
        //raddr_cntr   <= raddr_cntr + (re & ~rd_empty_int);
        raddr_cntr   <= ram_raddr;
    end
end
// NIC
assign ram_raddr = raddr_cntr + (UNDERFLOW_PROTECT ? re & ~rd_empty_int : re);


generate
    if (SYNC_CLK) begin : sync_clk
       if (MODE == "FWFT") begin
          assign waddr_int = waddr_cntr_r;
          assign raddr_int        = raddr_cntr;
       end
       else begin
          assign waddr_int        = waddr_cntr;
          assign raddr_int        = raddr_cntr;
       end
    end
    else begin : async_clk
        reg  [RADDR_WIDTH:0] raddr_cntr_gry_r;
        reg  [WADDR_WIDTH:0] waddr_cntr_gry_r;
        
        wire [RADDR_WIDTH:0] raddr_cntr_gry;
        wire [RADDR_WIDTH:0] raddr_cntr_gry_sync;
        wire [RADDR_WIDTH:0] raddr_cntr_sync_g2b;
        wire [WADDR_WIDTH:0] waddr_cntr_gry;
        wire [WADDR_WIDTH:0] waddr_cntr_gry_sync;
        wire [WADDR_WIDTH:0] waddr_cntr_sync_g2b;

        if (PIPELINE_REG) begin
            reg  [RADDR_WIDTH:0] raddr_cntr_sync_g2b_r;    
            reg  [WADDR_WIDTH:0] waddr_cntr_sync_g2b_r;        
        
            assign waddr_int        = waddr_cntr_sync_g2b_r;
            assign raddr_int        = raddr_cntr_sync_g2b_r;
            
            always @ (posedge wclk or posedge wr_rst) begin
                if (wr_rst) begin
                    raddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    raddr_cntr_sync_g2b_r <= raddr_cntr_sync_g2b;
                end
            end
            
            always @ (posedge rclk or posedge rd_rst) begin
                if (rd_rst) begin
                    waddr_cntr_sync_g2b_r <= 'h0;
                end
                else begin
                    waddr_cntr_sync_g2b_r <= waddr_cntr_sync_g2b;
                end
            end
        end
        else begin
            assign waddr_int        = waddr_cntr_sync_g2b;
            assign raddr_int        = raddr_cntr_sync_g2b;        
        end
      
        always @ (posedge rclk or posedge rd_rst) begin
            if (rd_rst) begin
                raddr_cntr_gry_r <= 'h0;
            end
            else begin
                raddr_cntr_gry_r <= raddr_cntr_gry;
            end
        end
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_bin2gray    (.bin_i(raddr_cntr), .gray_o(raddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (RADDR_WIDTH+1), .ACTIVE_LOW(0)) xrd2wr_addr_sync   (.clk(wclk), .reset_n(wr_rst), .d_i(raddr_cntr_gry_r), .d_o(raddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(RADDR_WIDTH+1)                                           ) xrd2wr_gray2bin    (.gray_i(raddr_cntr_gry_sync), .bin_o(raddr_cntr_sync_g2b));
        
        always @ (posedge wclk or posedge wr_rst) begin
            if (wr_rst) begin
                waddr_cntr_gry_r <= 'h0;
            end
            else begin
                waddr_cntr_gry_r <= waddr_cntr_gry;
            end
        end        
        `IP_MODULE_NAME(efx_fifo_bin2gray) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_bin2gray  (.bin_i(waddr_cntr), .gray_o(waddr_cntr_gry));
        `IP_MODULE_NAME(efx_asyncreg)      # (.ASYNC_STAGE(SYNC_STAGE), .WIDTH (WADDR_WIDTH+1), .ACTIVE_LOW(0)) wr2rd_addr_sync (.clk(rclk), .reset_n(rd_rst), .d_i(waddr_cntr_gry_r), .d_o(waddr_cntr_gry_sync));
        `IP_MODULE_NAME(efx_fifo_gray2bin) # (.WIDTH(WADDR_WIDTH+1)                                           ) wr2rd_gray2bin  (.gray_i(waddr_cntr_gry_sync), .bin_o(waddr_cntr_sync_g2b));        

    end
endgenerate
endmodule


////////////////////////////////////////////////////////////////////////////
//           _____       
//          / _______    Copyright (C) 2013-2020 Efinix Inc. All rights reserved.
//         / /       \   
//        / /  ..    /   bin2gray.v
//       / / .'     /    
//    __/ /.'      /     Description:
//   __   \       /      Binary to Gray Encoding Convertor
//  /_/ /\ \_____/ /     
// ____/  \_______/      
//
// *******************************
// Revisions:
// 1.0 Initial rev
//
// *******************************

`resetall
`timescale 1ns/1ps

module `IP_MODULE_NAME(efx_fifo_bin2gray)
#(parameter WIDTH=5)
(// outputs
 output wire [WIDTH-1:0] gray_o,
 // input
 input [WIDTH-1:0] bin_i
 );

//---------------------------------------------------------------------
// Function :   bit_xor
// Description: reduction xor
function bit_xor (
  input [31:0] nex_bit,
  input [31:0] curr_bit,
  input [WIDTH-1:0] xor_in);
  begin : fn_bit_xor
    bit_xor = xor_in[nex_bit] ^ xor_in[curr_bit];
  end
endfunction

// Convert Binary to Gray, bit by bit
generate 
begin
  genvar bit_idx;
  for(bit_idx=0; bit_idx<WIDTH-1; bit_idx=bit_idx+1) begin : gBinBits
    assign gray_o[bit_idx] = bit_xor(bit_idx+1, bit_idx, bin_i);
  end
  assign   gray_o[WIDTH-1] = bin_i[WIDTH-1];
end
endgenerate

endmodule 

////////////////////////////////////////////////////////////////////////////////
// Copyright (C) 2013-2020 Efinix Inc. All rights reserved.              
//
// This   document  contains  proprietary information  which   is        
// protected by  copyright. All rights  are reserved.  This notice       
// refers to original work by Efinix, Inc. which may be derivitive       
// of other work distributed under license of the authors.  In the       
// case of derivative work, nothing in this notice overrides the         
// original author's license agreement.  Where applicable, the           
// original license agreement is included in it's original               
// unmodified form immediately below this header.                        
//
// WARRANTY DISCLAIMER.                                                  
//     THE  DESIGN, CODE, OR INFORMATION ARE PROVIDED “AS IS” AND        
//     EFINIX MAKES NO WARRANTIES, EXPRESS OR IMPLIED WITH               
//     RESPECT THERETO, AND EXPRESSLY DISCLAIMS ANY IMPLIED WARRANTIES,  
//     INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF          
//     MERCHANTABILITY, NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR    
//     PURPOSE.  SOME STATES DO NOT ALLOW EXCLUSIONS OF AN IMPLIED       
//     WARRANTY, SO THIS DISCLAIMER MAY NOT APPLY TO LICENSEE.           
//
// LIMITATION OF LIABILITY.                                              
//     NOTWITHSTANDING ANYTHING TO THE CONTRARY, EXCEPT FOR BODILY       
//     INJURY, EFINIX SHALL NOT BE LIABLE WITH RESPECT TO ANY SUBJECT    
//     MATTER OF THIS AGREEMENT UNDER TORT, CONTRACT, STRICT LIABILITY   
//     OR ANY OTHER LEGAL OR EQUITABLE THEORY (I) FOR ANY INDIRECT,      
//     SPECIAL, INCIDENTAL, EXEMPLARY OR CONSEQUENTIAL DAMAGES OF ANY    
//     CHARACTER INCLUDING, WITHOUT LIMITATION, DAMAGES FOR LOSS OF      
//     GOODWILL, DATA OR PROFIT, WORK STOPPAGE, OR COMPUTER FAILURE OR   
//     MALFUNCTION, OR IN ANY EVENT (II) FOR ANY AMOUNT IN EXCESS, IN    
//     THE AGGREGATE, OF THE FEE PAID BY LICENSEE TO EFINIX HEREUNDER    
//     (OR, IF THE FEE HAS BEEN WAIVED, $100), EVEN IF EFINIX SHALL HAVE 
//     BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGES.  SOME STATES DO 
//     NOT ALLOW THE EXCLUSION OR LIMITATION OF INCIDENTAL OR            
//     CONSEQUENTIAL DAMAGES, SO THIS LIMITATION AND EXCLUSION MAY NOT   
//     APPLY TO LICENSEE.
//
////////////////////////////////////////////////////////////////////////////////

`undef IP_UUID
`undef IP_NAME_CONCAT
`undef IP_MODULE_NAME
